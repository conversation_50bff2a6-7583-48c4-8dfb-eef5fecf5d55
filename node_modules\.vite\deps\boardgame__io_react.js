import {
  Local,
  SocketIO
} from "./chunk-6QUVGBWT.js";
import {
  Client,
  LobbyClient,
  MCTSBot,
  require_setImmediate
} from "./chunk-J7D76I2G.js";
import {
  require_lodash,
  require_rfc6902
} from "./chunk-CT2AWXLA.js";
import {
  require_object_assign,
  require_prop_types
} from "./chunk-MYOAH3BB.js";
import {
  require_react
} from "./chunk-Y5IW5QF7.js";
import {
  __commonJS,
  __toESM
} from "./chunk-UV5CTPV7.js";

// node_modules/react-cookies/node_modules/cookie/index.js
var require_cookie = __commonJS({
  "node_modules/react-cookies/node_modules/cookie/index.js"(exports) {
    "use strict";
    exports.parse = parse;
    exports.serialize = serialize;
    var decode = decodeURIComponent;
    var encode = encodeURIComponent;
    var pairSplitRegExp = /; */;
    var fieldContentRegExp = /^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;
    function parse(str, options) {
      if (typeof str !== "string") {
        throw new TypeError("argument str must be a string");
      }
      var obj = {};
      var opt = options || {};
      var pairs = str.split(pairSplitRegExp);
      var dec = opt.decode || decode;
      for (var i = 0; i < pairs.length; i++) {
        var pair = pairs[i];
        var eq_idx = pair.indexOf("=");
        if (eq_idx < 0) {
          continue;
        }
        var key = pair.substr(0, eq_idx).trim();
        var val = pair.substr(++eq_idx, pair.length).trim();
        if ('"' == val[0]) {
          val = val.slice(1, -1);
        }
        if (void 0 == obj[key]) {
          obj[key] = tryDecode(val, dec);
        }
      }
      return obj;
    }
    function serialize(name, val, options) {
      var opt = options || {};
      var enc = opt.encode || encode;
      if (typeof enc !== "function") {
        throw new TypeError("option encode is invalid");
      }
      if (!fieldContentRegExp.test(name)) {
        throw new TypeError("argument name is invalid");
      }
      var value = enc(val);
      if (value && !fieldContentRegExp.test(value)) {
        throw new TypeError("argument val is invalid");
      }
      var str = name + "=" + value;
      if (null != opt.maxAge) {
        var maxAge = opt.maxAge - 0;
        if (isNaN(maxAge))
          throw new Error("maxAge should be a Number");
        str += "; Max-Age=" + Math.floor(maxAge);
      }
      if (opt.domain) {
        if (!fieldContentRegExp.test(opt.domain)) {
          throw new TypeError("option domain is invalid");
        }
        str += "; Domain=" + opt.domain;
      }
      if (opt.path) {
        if (!fieldContentRegExp.test(opt.path)) {
          throw new TypeError("option path is invalid");
        }
        str += "; Path=" + opt.path;
      }
      if (opt.expires) {
        if (typeof opt.expires.toUTCString !== "function") {
          throw new TypeError("option expires is invalid");
        }
        str += "; Expires=" + opt.expires.toUTCString();
      }
      if (opt.httpOnly) {
        str += "; HttpOnly";
      }
      if (opt.secure) {
        str += "; Secure";
      }
      if (opt.sameSite) {
        var sameSite = typeof opt.sameSite === "string" ? opt.sameSite.toLowerCase() : opt.sameSite;
        switch (sameSite) {
          case true:
            str += "; SameSite=Strict";
            break;
          case "lax":
            str += "; SameSite=Lax";
            break;
          case "strict":
            str += "; SameSite=Strict";
            break;
          default:
            throw new TypeError("option sameSite is invalid");
        }
      }
      return str;
    }
    function tryDecode(str, decode2) {
      try {
        return decode2(str);
      } catch (e) {
        return str;
      }
    }
  }
});

// node_modules/react-cookies/build/cookie.js
var require_cookie2 = __commonJS({
  "node_modules/react-cookies/build/cookie.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    var _typeof = typeof Symbol === "function" && typeof Symbol.iterator === "symbol" ? function(obj) {
      return typeof obj;
    } : function(obj) {
      return obj && typeof Symbol === "function" && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj;
    };
    exports.load = load;
    exports.loadAll = loadAll;
    exports.select = select;
    exports.save = save;
    exports.remove = remove;
    exports.setRawCookie = setRawCookie;
    exports.plugToRequest = plugToRequest;
    var _cookie = require_cookie();
    var _cookie2 = _interopRequireDefault(_cookie);
    var _objectAssign = require_object_assign();
    var _objectAssign2 = _interopRequireDefault(_objectAssign);
    function _interopRequireDefault(obj) {
      return obj && obj.__esModule ? obj : { default: obj };
    }
    var IS_NODE = typeof document === "undefined" || typeof process !== "undefined" && process.env && false;
    var _rawCookie = {};
    var _res = void 0;
    function _isResWritable() {
      return _res && !_res.headersSent;
    }
    function load(name, doNotParse) {
      var cookies = IS_NODE ? _rawCookie : _cookie2.default.parse(document.cookie);
      var cookieVal = cookies && cookies[name];
      if (typeof doNotParse === "undefined") {
        doNotParse = !cookieVal || cookieVal[0] !== "{" && cookieVal[0] !== "[";
      }
      if (!doNotParse) {
        try {
          cookieVal = JSON.parse(cookieVal);
        } catch (err) {
        }
      }
      return cookieVal;
    }
    function loadAll(doNotParse) {
      var cookies = IS_NODE ? _rawCookie : _cookie2.default.parse(document.cookie);
      var cookieVal = cookies;
      if (typeof doNotParse === "undefined") {
        doNotParse = !cookieVal || cookieVal[0] !== "{" && cookieVal[0] !== "[";
      }
      if (!doNotParse) {
        try {
          cookieVal = JSON.parse(cookieVal);
        } catch (err) {
        }
      }
      return cookieVal;
    }
    function select(regex) {
      var cookies = IS_NODE ? _rawCookie : _cookie2.default.parse(document.cookie);
      if (!cookies) {
        return {};
      }
      if (!regex) {
        return cookies;
      }
      return Object.keys(cookies).reduce(function(accumulator, name) {
        if (!regex.test(name)) {
          return accumulator;
        }
        var newCookie = {};
        newCookie[name] = cookies[name];
        return (0, _objectAssign2.default)({}, accumulator, newCookie);
      }, {});
    }
    function save(name, val, opt) {
      _rawCookie[name] = val;
      if ((typeof val === "undefined" ? "undefined" : _typeof(val)) === "object") {
        _rawCookie[name] = JSON.stringify(val);
      }
      if (!IS_NODE) {
        document.cookie = _cookie2.default.serialize(name, _rawCookie[name], opt);
      }
      if (_isResWritable() && _res.cookie) {
        _res.cookie(name, val, opt);
      }
    }
    function remove(name, opt) {
      delete _rawCookie[name];
      if (typeof opt === "undefined") {
        opt = {};
      } else if (typeof opt === "string") {
        opt = { path: opt };
      } else {
        opt = (0, _objectAssign2.default)({}, opt);
      }
      if (typeof document !== "undefined") {
        opt.expires = new Date(1970, 1, 1, 0, 0, 1);
        opt.maxAge = 0;
        document.cookie = _cookie2.default.serialize(name, "", opt);
      }
      if (_isResWritable() && _res.clearCookie) {
        _res.clearCookie(name, opt);
      }
    }
    function setRawCookie(rawCookie) {
      if (rawCookie) {
        _rawCookie = _cookie2.default.parse(rawCookie);
      } else {
        _rawCookie = {};
      }
    }
    function plugToRequest(req, res) {
      if (req.cookie) {
        _rawCookie = req.cookie;
      } else if (req.cookies) {
        _rawCookie = req.cookies;
      } else if (req.headers && req.headers.cookie) {
        setRawCookie(req.headers.cookie);
      } else {
        _rawCookie = {};
      }
      _res = res;
      return function unplug() {
        _res = null;
        _rawCookie = {};
      };
    }
    exports.default = {
      setRawCookie,
      load,
      loadAll,
      select,
      save,
      remove,
      plugToRequest
    };
  }
});

// node_modules/boardgame.io/dist/esm/react.js
var import_lodash = __toESM(require_lodash());
var import_rfc6902 = __toESM(require_rfc6902());
var import_setimmediate = __toESM(require_setImmediate());
var import_react = __toESM(require_react());
var import_prop_types = __toESM(require_prop_types());
var import_react_cookies = __toESM(require_cookie2());
function Client2(opts) {
  var _a;
  const { game, numPlayers, board, multiplayer, enhancer } = opts;
  let { loading, debug } = opts;
  if (loading === void 0) {
    const Loading = () => import_react.default.createElement("div", { className: "bgio-loading" }, "connecting...");
    loading = Loading;
  }
  return _a = class WrappedBoard extends import_react.default.Component {
    constructor(props) {
      super(props);
      if (debug === void 0) {
        debug = props.debug;
      }
      this.client = Client({
        game,
        debug,
        numPlayers,
        multiplayer,
        matchID: props.matchID,
        playerID: props.playerID,
        credentials: props.credentials,
        enhancer
      });
    }
    componentDidMount() {
      this.unsubscribe = this.client.subscribe(() => this.forceUpdate());
      this.client.start();
    }
    componentWillUnmount() {
      this.client.stop();
      this.unsubscribe();
    }
    componentDidUpdate(prevProps) {
      if (this.props.matchID != prevProps.matchID) {
        this.client.updateMatchID(this.props.matchID);
      }
      if (this.props.playerID != prevProps.playerID) {
        this.client.updatePlayerID(this.props.playerID);
      }
      if (this.props.credentials != prevProps.credentials) {
        this.client.updateCredentials(this.props.credentials);
      }
    }
    render() {
      const state = this.client.getState();
      if (state === null) {
        return import_react.default.createElement(loading);
      }
      let _board = null;
      if (board) {
        _board = import_react.default.createElement(board, {
          ...state,
          ...this.props,
          isMultiplayer: !!multiplayer,
          moves: this.client.moves,
          events: this.client.events,
          matchID: this.client.matchID,
          playerID: this.client.playerID,
          reset: this.client.reset,
          undo: this.client.undo,
          redo: this.client.redo,
          log: this.client.log,
          matchData: this.client.matchData,
          sendChatMessage: this.client.sendChatMessage,
          chatMessages: this.client.chatMessages
        });
      }
      return import_react.default.createElement("div", { className: "bgio-client" }, _board);
    }
  }, _a.propTypes = {
    // The ID of a game to connect to.
    // Only relevant in multiplayer.
    matchID: import_prop_types.default.string,
    // The ID of the player associated with this client.
    // Only relevant in multiplayer.
    playerID: import_prop_types.default.string,
    // This client's authentication credentials.
    // Only relevant in multiplayer.
    credentials: import_prop_types.default.string,
    // Enable / disable the Debug UI.
    debug: import_prop_types.default.any
  }, _a.defaultProps = {
    matchID: "default",
    playerID: null,
    credentials: null,
    debug: true
  }, _a;
}
var _LobbyConnectionImpl = class {
  constructor({ server, gameComponents, playerName, playerCredentials }) {
    this.client = new LobbyClient({ server });
    this.gameComponents = gameComponents;
    this.playerName = playerName || "Visitor";
    this.playerCredentials = playerCredentials;
    this.matches = [];
  }
  async refresh() {
    try {
      this.matches = [];
      const games = await this.client.listGames();
      for (const game of games) {
        if (!this._getGameComponents(game))
          continue;
        const { matches } = await this.client.listMatches(game);
        this.matches.push(...matches);
      }
    } catch (error) {
      throw new Error("failed to retrieve list of matches (" + error + ")");
    }
  }
  _getMatchInstance(matchID) {
    for (const inst of this.matches) {
      if (inst["matchID"] === matchID)
        return inst;
    }
  }
  _getGameComponents(gameName) {
    for (const comp of this.gameComponents) {
      if (comp.game.name === gameName)
        return comp;
    }
  }
  _findPlayer(playerName) {
    for (const inst of this.matches) {
      if (inst.players.some((player) => player.name === playerName))
        return inst;
    }
  }
  async join(gameName, matchID, playerID) {
    try {
      let inst = this._findPlayer(this.playerName);
      if (inst) {
        throw new Error("player has already joined " + inst.matchID);
      }
      inst = this._getMatchInstance(matchID);
      if (!inst) {
        throw new Error("game instance " + matchID + " not found");
      }
      const json = await this.client.joinMatch(gameName, matchID, {
        playerID,
        playerName: this.playerName
      });
      inst.players[Number.parseInt(playerID)].name = this.playerName;
      this.playerCredentials = json.playerCredentials;
    } catch (error) {
      throw new Error("failed to join match " + matchID + " (" + error + ")");
    }
  }
  async leave(gameName, matchID) {
    try {
      const inst = this._getMatchInstance(matchID);
      if (!inst)
        throw new Error("match instance not found");
      for (const player of inst.players) {
        if (player.name === this.playerName) {
          await this.client.leaveMatch(gameName, matchID, {
            playerID: player.id.toString(),
            credentials: this.playerCredentials
          });
          delete player.name;
          delete this.playerCredentials;
          return;
        }
      }
      throw new Error("player not found in match");
    } catch (error) {
      throw new Error("failed to leave match " + matchID + " (" + error + ")");
    }
  }
  async disconnect() {
    const inst = this._findPlayer(this.playerName);
    if (inst) {
      await this.leave(inst.gameName, inst.matchID);
    }
    this.matches = [];
    this.playerName = "Visitor";
  }
  async create(gameName, numPlayers) {
    try {
      const comp = this._getGameComponents(gameName);
      if (!comp)
        throw new Error("game not found");
      if (numPlayers < comp.game.minPlayers || numPlayers > comp.game.maxPlayers)
        throw new Error("invalid number of players " + numPlayers);
      await this.client.createMatch(gameName, { numPlayers });
    } catch (error) {
      throw new Error("failed to create match for " + gameName + " (" + error + ")");
    }
  }
};
function LobbyConnection(opts) {
  return new _LobbyConnectionImpl(opts);
}
var LobbyLoginForm = class extends import_react.default.Component {
  constructor() {
    super(...arguments);
    this.state = {
      playerName: this.props.playerName,
      nameErrorMsg: ""
    };
    this.onClickEnter = () => {
      if (this.state.playerName === "")
        return;
      this.props.onEnter(this.state.playerName);
    };
    this.onKeyPress = (event) => {
      if (event.key === "Enter") {
        this.onClickEnter();
      }
    };
    this.onChangePlayerName = (event) => {
      const name = event.target.value.trim();
      this.setState({
        playerName: name,
        nameErrorMsg: name.length > 0 ? "" : "empty player name"
      });
    };
  }
  render() {
    return import_react.default.createElement(
      "div",
      null,
      import_react.default.createElement("p", { className: "phase-title" }, "Choose a player name:"),
      import_react.default.createElement("input", { type: "text", value: this.state.playerName, onChange: this.onChangePlayerName, onKeyPress: this.onKeyPress }),
      import_react.default.createElement(
        "span",
        { className: "buttons" },
        import_react.default.createElement("button", { className: "buttons", onClick: this.onClickEnter }, "Enter")
      ),
      import_react.default.createElement("br", null),
      import_react.default.createElement(
        "span",
        { className: "error-msg" },
        this.state.nameErrorMsg,
        import_react.default.createElement("br", null)
      )
    );
  }
};
LobbyLoginForm.defaultProps = {
  playerName: ""
};
var LobbyMatchInstance = class extends import_react.default.Component {
  constructor() {
    super(...arguments);
    this._createSeat = (player) => {
      return player.name || "[free]";
    };
    this._createButtonJoin = (inst, seatId) => import_react.default.createElement("button", { key: "button-join-" + inst.matchID, onClick: () => this.props.onClickJoin(inst.gameName, inst.matchID, "" + seatId) }, "Join");
    this._createButtonLeave = (inst) => import_react.default.createElement("button", { key: "button-leave-" + inst.matchID, onClick: () => this.props.onClickLeave(inst.gameName, inst.matchID) }, "Leave");
    this._createButtonPlay = (inst, seatId) => import_react.default.createElement("button", { key: "button-play-" + inst.matchID, onClick: () => this.props.onClickPlay(inst.gameName, {
      matchID: inst.matchID,
      playerID: "" + seatId,
      numPlayers: inst.players.length
    }) }, "Play");
    this._createButtonSpectate = (inst) => import_react.default.createElement("button", { key: "button-spectate-" + inst.matchID, onClick: () => this.props.onClickPlay(inst.gameName, {
      matchID: inst.matchID,
      numPlayers: inst.players.length
    }) }, "Spectate");
    this._createInstanceButtons = (inst) => {
      const playerSeat = inst.players.find((player) => player.name === this.props.playerName);
      const freeSeat = inst.players.find((player) => !player.name);
      if (playerSeat && freeSeat) {
        return this._createButtonLeave(inst);
      }
      if (freeSeat) {
        return this._createButtonJoin(inst, freeSeat.id);
      }
      if (playerSeat) {
        return import_react.default.createElement("div", null, [
          this._createButtonPlay(inst, playerSeat.id),
          this._createButtonLeave(inst)
        ]);
      }
      return this._createButtonSpectate(inst);
    };
  }
  render() {
    const match = this.props.match;
    let status = "OPEN";
    if (!match.players.some((player) => !player.name)) {
      status = "RUNNING";
    }
    return import_react.default.createElement(
      "tr",
      { key: "line-" + match.matchID },
      import_react.default.createElement("td", { key: "cell-name-" + match.matchID }, match.gameName),
      import_react.default.createElement("td", { key: "cell-status-" + match.matchID }, status),
      import_react.default.createElement("td", { key: "cell-seats-" + match.matchID }, match.players.map((player) => this._createSeat(player)).join(", ")),
      import_react.default.createElement("td", { key: "cell-buttons-" + match.matchID }, this._createInstanceButtons(match))
    );
  }
};
var LobbyCreateMatchForm = class extends import_react.default.Component {
  constructor(props) {
    super(props);
    this.state = {
      selectedGame: 0,
      numPlayers: 2
    };
    this._createGameNameOption = (game, idx) => {
      return import_react.default.createElement("option", { key: "name-option-" + idx, value: idx }, game.game.name);
    };
    this._createNumPlayersOption = (idx) => {
      return import_react.default.createElement("option", { key: "num-option-" + idx, value: idx }, idx);
    };
    this._createNumPlayersRange = (game) => {
      return Array.from({ length: game.maxPlayers + 1 }).map((_, i) => i).slice(game.minPlayers);
    };
    this.onChangeNumPlayers = (event) => {
      this.setState({
        numPlayers: Number.parseInt(event.target.value)
      });
    };
    this.onChangeSelectedGame = (event) => {
      const idx = Number.parseInt(event.target.value);
      this.setState({
        selectedGame: idx,
        numPlayers: this.props.games[idx].game.minPlayers
      });
    };
    this.onClickCreate = () => {
      this.props.createMatch(this.props.games[this.state.selectedGame].game.name, this.state.numPlayers);
    };
    for (const game of props.games) {
      const matchDetails = game.game;
      if (!matchDetails.minPlayers) {
        matchDetails.minPlayers = 1;
      }
      if (!matchDetails.maxPlayers) {
        matchDetails.maxPlayers = 4;
      }
      console.assert(matchDetails.maxPlayers >= matchDetails.minPlayers);
    }
    this.state = {
      selectedGame: 0,
      numPlayers: props.games[0].game.minPlayers
    };
  }
  render() {
    return import_react.default.createElement(
      "div",
      null,
      import_react.default.createElement("select", { value: this.state.selectedGame, onChange: (evt) => this.onChangeSelectedGame(evt) }, this.props.games.map((game, index) => this._createGameNameOption(game, index))),
      import_react.default.createElement("span", null, "Players:"),
      import_react.default.createElement("select", { value: this.state.numPlayers, onChange: this.onChangeNumPlayers }, this._createNumPlayersRange(this.props.games[this.state.selectedGame].game).map((number) => this._createNumPlayersOption(number))),
      import_react.default.createElement(
        "span",
        { className: "buttons" },
        import_react.default.createElement("button", { onClick: this.onClickCreate }, "Create")
      )
    );
  }
};
var LobbyPhases;
(function(LobbyPhases2) {
  LobbyPhases2["ENTER"] = "enter";
  LobbyPhases2["PLAY"] = "play";
  LobbyPhases2["LIST"] = "list";
})(LobbyPhases || (LobbyPhases = {}));
var Lobby = class extends import_react.default.Component {
  constructor(props) {
    super(props);
    this.state = {
      phase: LobbyPhases.ENTER,
      playerName: "Visitor",
      runningMatch: null,
      errorMsg: "",
      credentialStore: {}
    };
    this._createConnection = (props2) => {
      const name = this.state.playerName;
      this.connection = LobbyConnection({
        server: props2.lobbyServer,
        gameComponents: props2.gameComponents,
        playerName: name,
        playerCredentials: this.state.credentialStore[name]
      });
    };
    this._updateCredentials = (playerName, credentials) => {
      this.setState((prevState) => {
        const store = Object.assign({}, prevState.credentialStore);
        store[playerName] = credentials;
        return { credentialStore: store };
      });
    };
    this._updateConnection = async () => {
      await this.connection.refresh();
      this.forceUpdate();
    };
    this._enterLobby = (playerName) => {
      this._startRefreshInterval();
      this.setState({ playerName, phase: LobbyPhases.LIST });
    };
    this._exitLobby = async () => {
      this._clearRefreshInterval();
      await this.connection.disconnect();
      this.setState({ phase: LobbyPhases.ENTER, errorMsg: "" });
    };
    this._createMatch = async (gameName, numPlayers) => {
      try {
        await this.connection.create(gameName, numPlayers);
        await this.connection.refresh();
        this.setState({});
      } catch (error) {
        this.setState({ errorMsg: error.message });
      }
    };
    this._joinMatch = async (gameName, matchID, playerID) => {
      try {
        await this.connection.join(gameName, matchID, playerID);
        await this.connection.refresh();
        this._updateCredentials(this.connection.playerName, this.connection.playerCredentials);
      } catch (error) {
        this.setState({ errorMsg: error.message });
      }
    };
    this._leaveMatch = async (gameName, matchID) => {
      try {
        await this.connection.leave(gameName, matchID);
        await this.connection.refresh();
        this._updateCredentials(this.connection.playerName, this.connection.playerCredentials);
      } catch (error) {
        this.setState({ errorMsg: error.message });
      }
    };
    this._startMatch = (gameName, matchOpts) => {
      const gameCode = this.connection._getGameComponents(gameName);
      if (!gameCode) {
        this.setState({
          errorMsg: "game " + gameName + " not supported"
        });
        return;
      }
      let multiplayer = void 0;
      if (matchOpts.numPlayers > 1) {
        multiplayer = this.props.gameServer ? SocketIO({ server: this.props.gameServer }) : SocketIO();
      }
      if (matchOpts.numPlayers == 1) {
        const maxPlayers = gameCode.game.maxPlayers;
        const bots = {};
        for (let i = 1; i < maxPlayers; i++) {
          bots[i + ""] = MCTSBot;
        }
        multiplayer = Local({ bots });
      }
      const app = this.props.clientFactory({
        game: gameCode.game,
        board: gameCode.board,
        debug: this.props.debug,
        multiplayer
      });
      const match = {
        app,
        matchID: matchOpts.matchID,
        playerID: matchOpts.numPlayers > 1 ? matchOpts.playerID : "0",
        credentials: this.connection.playerCredentials
      };
      this._clearRefreshInterval();
      this.setState({ phase: LobbyPhases.PLAY, runningMatch: match });
    };
    this._exitMatch = () => {
      this._startRefreshInterval();
      this.setState({ phase: LobbyPhases.LIST, runningMatch: null });
    };
    this._getPhaseVisibility = (phase) => {
      return this.state.phase !== phase ? "hidden" : "phase";
    };
    this.renderMatches = (matches, playerName) => {
      return matches.map((match) => {
        const { matchID, gameName, players } = match;
        return import_react.default.createElement(LobbyMatchInstance, { key: "instance-" + matchID, match: { matchID, gameName, players: Object.values(players) }, playerName, onClickJoin: this._joinMatch, onClickLeave: this._leaveMatch, onClickPlay: this._startMatch });
      });
    };
    this._createConnection(this.props);
  }
  componentDidMount() {
    const cookie = import_react_cookies.default.load("lobbyState") || {};
    if (cookie.phase && cookie.phase === LobbyPhases.PLAY) {
      cookie.phase = LobbyPhases.LIST;
    }
    if (cookie.phase && cookie.phase !== LobbyPhases.ENTER) {
      this._startRefreshInterval();
    }
    this.setState({
      phase: cookie.phase || LobbyPhases.ENTER,
      playerName: cookie.playerName || "Visitor",
      credentialStore: cookie.credentialStore || {}
    });
  }
  componentDidUpdate(prevProps, prevState) {
    const name = this.state.playerName;
    const creds = this.state.credentialStore[name];
    if (prevState.phase !== this.state.phase || prevState.credentialStore[name] !== creds || prevState.playerName !== name) {
      this._createConnection(this.props);
      this._updateConnection();
      const cookie = {
        phase: this.state.phase,
        playerName: name,
        credentialStore: this.state.credentialStore
      };
      import_react_cookies.default.save("lobbyState", cookie, { path: "/" });
    }
    if (prevProps.refreshInterval !== this.props.refreshInterval) {
      this._startRefreshInterval();
    }
  }
  componentWillUnmount() {
    this._clearRefreshInterval();
  }
  _startRefreshInterval() {
    this._clearRefreshInterval();
    this._currentInterval = setInterval(this._updateConnection, this.props.refreshInterval);
  }
  _clearRefreshInterval() {
    clearInterval(this._currentInterval);
  }
  render() {
    const { gameComponents, renderer } = this.props;
    const { errorMsg, playerName, phase, runningMatch } = this.state;
    if (renderer) {
      return renderer({
        errorMsg,
        gameComponents,
        matches: this.connection.matches,
        phase,
        playerName,
        runningMatch,
        handleEnterLobby: this._enterLobby,
        handleExitLobby: this._exitLobby,
        handleCreateMatch: this._createMatch,
        handleJoinMatch: this._joinMatch,
        handleLeaveMatch: this._leaveMatch,
        handleExitMatch: this._exitMatch,
        handleRefreshMatches: this._updateConnection,
        handleStartMatch: this._startMatch
      });
    }
    return import_react.default.createElement(
      "div",
      { id: "lobby-view", style: { padding: 50 } },
      import_react.default.createElement(
        "div",
        { className: this._getPhaseVisibility(LobbyPhases.ENTER) },
        import_react.default.createElement(LobbyLoginForm, { key: playerName, playerName, onEnter: this._enterLobby })
      ),
      import_react.default.createElement(
        "div",
        { className: this._getPhaseVisibility(LobbyPhases.LIST) },
        import_react.default.createElement(
          "p",
          null,
          "Welcome, ",
          playerName
        ),
        import_react.default.createElement(
          "div",
          { className: "phase-title", id: "match-creation" },
          import_react.default.createElement("span", null, "Create a match:"),
          import_react.default.createElement(LobbyCreateMatchForm, { games: gameComponents, createMatch: this._createMatch })
        ),
        import_react.default.createElement("p", { className: "phase-title" }, "Join a match:"),
        import_react.default.createElement(
          "div",
          { id: "instances" },
          import_react.default.createElement(
            "table",
            null,
            import_react.default.createElement("tbody", null, this.renderMatches(this.connection.matches, playerName))
          ),
          import_react.default.createElement(
            "span",
            { className: "error-msg" },
            errorMsg,
            import_react.default.createElement("br", null)
          )
        ),
        import_react.default.createElement("p", { className: "phase-title" }, "Matches that become empty are automatically deleted.")
      ),
      import_react.default.createElement(
        "div",
        { className: this._getPhaseVisibility(LobbyPhases.PLAY) },
        runningMatch && import_react.default.createElement(runningMatch.app, { matchID: runningMatch.matchID, playerID: runningMatch.playerID, credentials: runningMatch.credentials }),
        import_react.default.createElement(
          "div",
          { className: "buttons", id: "match-exit" },
          import_react.default.createElement("button", { onClick: this._exitMatch }, "Exit match")
        )
      ),
      import_react.default.createElement(
        "div",
        { className: "buttons", id: "lobby-exit" },
        import_react.default.createElement("button", { onClick: this._exitLobby }, "Exit lobby")
      )
    );
  }
};
Lobby.propTypes = {
  gameComponents: import_prop_types.default.array.isRequired,
  lobbyServer: import_prop_types.default.string,
  gameServer: import_prop_types.default.string,
  debug: import_prop_types.default.bool,
  clientFactory: import_prop_types.default.func,
  refreshInterval: import_prop_types.default.number
};
Lobby.defaultProps = {
  debug: false,
  clientFactory: Client2,
  refreshInterval: 2e3
};
export {
  Client2 as Client,
  Lobby
};
/*! Bundled license information:

cookie/index.js:
  (*!
   * cookie
   * Copyright(c) 2012-2014 Roman Shtylman
   * Copyright(c) 2015 Douglas Christopher Wilson
   * MIT Licensed
   *)
*/
//# sourceMappingURL=boardgame__io_react.js.map
