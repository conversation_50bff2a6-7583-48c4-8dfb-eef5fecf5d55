import { MapConfiguration, MapImportResult } from '../types/mapTypes';
import { GameState } from '../types/gameTypes';

export class MapImporter {
  static async importFromFile(file: File): Promise<MapImportResult> {
    try {
      const content = await this.readFileContent(file);
      const data = JSON.parse(content);

      // Determine file type and import accordingly
      if (this.isBoardgameIOFormat(data)) {
        return this.importFromBoardgameIO(data);
      } else if (this.isMapConfigurationFormat(data)) {
        return this.importFromMapConfiguration(data);
      } else {
        return {
          success: false,
          errors: ['Unknown file format'],
          warnings: [],
          missingAssets: [],
        };
      }
    } catch (error) {
      return {
        success: false,
        errors: [`Failed to parse file: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
        missingAssets: [],
      };
    }
  }

  static async importFromJSON(jsonString: string): Promise<MapImportResult> {
    try {
      const data = JSON.parse(jsonString);
      
      if (this.isMapConfigurationFormat(data)) {
        return this.importFromMapConfiguration(data);
      } else {
        return {
          success: false,
          errors: ['Invalid JSON format for map configuration'],
          warnings: [],
          missingAssets: [],
        };
      }
    } catch (error) {
      return {
        success: false,
        errors: [`Failed to parse JSON: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: [],
        missingAssets: [],
      };
    }
  }

  static importFromBoardgameIO(gameState: GameState): MapImportResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingAssets: string[] = [];

    try {
      // Convert boardgame.io format to map configuration
      const mapConfig: MapConfiguration = {
        id: `imported_${Date.now()}`,
        name: 'Imported Map',
        description: 'Map imported from boardgame.io format',
        boardSize: gameState.gameConfig?.boardSize || gameState.tiles.length,
        treasureChestPosition: gameState.gameConfig?.treasureChestPosition || 15,
        keysToWin: gameState.gameConfig?.keysToWin || 40,
        tiles: gameState.tiles.map(tile => ({
          id: tile.id,
          type: tile.type,
          position: tile.position || [0, 0, 0],
        })),
        terrain: {
          baseRadius: 32,
          radiusVariation: 12,
          heightVariation: 2,
          layers: [],
          customMeshes: [],
        },
        environment: {
          skybox: {
            type: 'gradient',
            topColor: '#87CEEB',
            bottomColor: '#F4A460',
          },
          fog: {
            enabled: true,
            color: '#F4A460',
            near: 50,
            far: 200,
          },
        },
        lighting: {
          ambientLight: {
            color: '#ffffff',
            intensity: 0.6,
          },
          directionalLight: {
            color: '#ffffff',
            intensity: 1.0,
            position: [10, 20, 10],
            castShadow: true,
            shadowMapSize: 2048,
          },
          additionalLights: [],
        },
        placedObjects: [],
        metadata: {
          created: Date.now(),
          modified: Date.now(),
          version: '1.0.0',
        },
      };

      // Validate the imported configuration
      const validation = this.validateImportedMap(mapConfig);
      errors.push(...validation.errors);
      warnings.push(...validation.warnings);

      return {
        success: errors.length === 0,
        configuration: mapConfig,
        errors,
        warnings,
        missingAssets,
      };
    } catch (error) {
      return {
        success: false,
        errors: [`Failed to convert boardgame.io format: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings,
        missingAssets,
      };
    }
  }

  static importFromMapConfiguration(data: any): MapImportResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const missingAssets: string[] = [];

    try {
      // Validate required fields
      if (!data.id) {
        data.id = `imported_${Date.now()}`;
        warnings.push('Map ID was missing, generated new ID');
      }

      if (!data.name) {
        data.name = 'Imported Map';
        warnings.push('Map name was missing, using default name');
      }

      if (!data.tiles || !Array.isArray(data.tiles)) {
        errors.push('Map must have a tiles array');
      }

      if (!data.metadata) {
        data.metadata = {
          created: Date.now(),
          modified: Date.now(),
          version: '1.0.0',
        };
        warnings.push('Metadata was missing, using defaults');
      }

      // Ensure all required properties exist with defaults
      const mapConfig: MapConfiguration = {
        id: data.id,
        name: data.name,
        description: data.description || '',
        boardSize: data.boardSize || data.tiles?.length || 60,
        treasureChestPosition: data.treasureChestPosition || 15,
        keysToWin: data.keysToWin || 40,
        tiles: data.tiles || [],
        terrain: data.terrain || {
          baseRadius: 32,
          radiusVariation: 12,
          heightVariation: 2,
          layers: [],
          customMeshes: [],
        },
        environment: data.environment || {
          skybox: {
            type: 'gradient',
            topColor: '#87CEEB',
            bottomColor: '#F4A460',
          },
          fog: {
            enabled: true,
            color: '#F4A460',
            near: 50,
            far: 200,
          },
        },
        lighting: data.lighting || {
          ambientLight: {
            color: '#ffffff',
            intensity: 0.6,
          },
          directionalLight: {
            color: '#ffffff',
            intensity: 1.0,
            position: [10, 20, 10],
            castShadow: true,
            shadowMapSize: 2048,
          },
          additionalLights: [],
        },
        placedObjects: data.placedObjects || [],
        metadata: data.metadata,
      };

      // Check for missing assets
      const availableAssets = this.getAvailableAssetIds();
      mapConfig.placedObjects.forEach(obj => {
        if (!availableAssets.includes(obj.assetId)) {
          missingAssets.push(obj.assetId);
        }
      });

      if (missingAssets.length > 0) {
        warnings.push(`Some assets are missing: ${missingAssets.join(', ')}`);
      }

      // Validate the imported configuration
      const validation = this.validateImportedMap(mapConfig);
      errors.push(...validation.errors);
      warnings.push(...validation.warnings);

      return {
        success: errors.length === 0,
        configuration: mapConfig,
        errors,
        warnings,
        missingAssets,
      };
    } catch (error) {
      return {
        success: false,
        errors: [`Failed to import map configuration: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings,
        missingAssets,
      };
    }
  }

  private static readFileContent(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          resolve(e.target.result as string);
        } else {
          reject(new Error('Failed to read file'));
        }
      };
      reader.onerror = () => reject(new Error('File reading error'));
      reader.readAsText(file);
    });
  }

  private static isBoardgameIOFormat(data: any): data is GameState {
    return data && 
           typeof data === 'object' && 
           Array.isArray(data.tiles) &&
           data.tiles.every((tile: any) => 
             typeof tile.id === 'number' && 
             typeof tile.type === 'string'
           );
  }

  private static isMapConfigurationFormat(data: any): boolean {
    return data && 
           typeof data === 'object' && 
           typeof data.id === 'string' &&
           Array.isArray(data.tiles) &&
           data.terrain &&
           data.environment &&
           data.lighting;
  }

  private static validateImportedMap(mapConfig: MapConfiguration): { errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Basic validation
    if (mapConfig.tiles.length === 0) {
      errors.push('Map has no tiles');
    }

    if (mapConfig.boardSize <= 0) {
      errors.push('Board size must be greater than 0');
    }

    if (mapConfig.keysToWin <= 0) {
      errors.push('Keys to win must be greater than 0');
    }

    // Check tile IDs are sequential
    const tileIds = mapConfig.tiles.map(tile => tile.id).sort((a, b) => a - b);
    for (let i = 0; i < tileIds.length; i++) {
      if (tileIds[i] !== i + 1) {
        warnings.push('Tile IDs are not sequential, this might cause issues');
        break;
      }
    }

    // Check for treasure chest
    const treasureChests = mapConfig.tiles.filter(tile => tile.type === 'treasure_chest');
    if (treasureChests.length === 0) {
      errors.push('Map must have a treasure chest tile');
    } else if (treasureChests.length > 1) {
      errors.push('Map should have only one treasure chest tile');
    }

    return { errors, warnings };
  }

  private static getAvailableAssetIds(): string[] {
    // This would normally come from the asset registry
    // For now, return the default assets
    return [
      'tile_basic', 'tile_healing', 'tile_damage', 'tile_key', 'tile_treasure_chest',
      'rock_small', 'rock_medium', 'rock_large',
      'cactus_small', 'cactus_large', 'palm_tree',
      'sand_dune', 'oasis_water', 'desert_grass', 'bone_pile',
      'point_light', 'spot_light'
    ];
  }

  static createSampleMap(): MapConfiguration {
    return {
      id: 'sample_desert_map',
      name: 'Sample Desert Map',
      description: 'A sample desert-themed map for testing',
      boardSize: 60,
      treasureChestPosition: 30,
      keysToWin: 40,
      tiles: [
        { id: 1, type: 'basic', position: [0, 0, 0] },
        { id: 30, type: 'treasure_chest', position: [15, 0, 0] },
        // Add more sample tiles as needed
      ],
      terrain: {
        baseRadius: 32,
        radiusVariation: 12,
        heightVariation: 2,
        layers: [],
        customMeshes: [],
      },
      environment: {
        skybox: {
          type: 'gradient',
          topColor: '#87CEEB',
          bottomColor: '#F4A460',
        },
        fog: {
          enabled: true,
          color: '#F4A460',
          near: 50,
          far: 200,
        },
      },
      lighting: {
        ambientLight: {
          color: '#ffffff',
          intensity: 0.6,
        },
        directionalLight: {
          color: '#ffffff',
          intensity: 1.0,
          position: [10, 20, 10],
          castShadow: true,
          shadowMapSize: 2048,
        },
        additionalLights: [],
      },
      placedObjects: [
        {
          id: 'obj_1',
          assetId: 'palm_tree',
          position: [0, 0, 10],
          rotation: [0, 0, 0],
          scale: [1, 1, 1],
          properties: {},
        },
        {
          id: 'obj_2',
          assetId: 'rock_medium',
          position: [10, 0, 5],
          rotation: [0, 0.5, 0],
          scale: [1.2, 1.2, 1.2],
          properties: {},
        },
      ],
      metadata: {
        created: Date.now(),
        modified: Date.now(),
        version: '1.0.0',
        author: 'System',
      },
    };
  }
}
