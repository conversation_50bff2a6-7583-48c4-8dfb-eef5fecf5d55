import React from 'react';
import { TerrainConfiguration } from '../../types/editorTypes';

interface EditorTerrainProps {
  configuration: TerrainConfiguration;
  editable: boolean;
}

export const EditorTerrain: React.FC<EditorTerrainProps> = ({
  configuration,
  editable,
}) => {
  return (
    <group>
      {/* Main Desert Ground - Large sandy terrain */}
      <mesh position={[0, -2.5, 0]} receiveShadow>
        <cylinderGeometry args={[50, 50, 4, 64]} />
        <meshStandardMaterial
          color="#F4A460"
          roughness={0.9}
          metalness={0.0}
        />
      </mesh>

      {/* Inner Desert Area - Slightly elevated */}
      <mesh position={[0, -0.2, 0]} receiveShadow>
        <cylinderGeometry args={[25, 25, 1, 32]} />
        <meshStandardMaterial
          color="#D2B48C"
          roughness={0.9}
          metalness={0.0}
        />
      </mesh>

      {/* Custom terrain meshes */}
      {configuration.customMeshes.map((mesh) => (
        <mesh
          key={mesh.id}
          position={mesh.position}
          rotation={mesh.rotation}
          scale={mesh.scale}
          receiveShadow
          castShadow
        >
          {/* This would be replaced with actual loaded geometry */}
          <boxGeometry args={[1, 1, 1]} />
          <meshStandardMaterial color="#D2B48C" />
        </mesh>
      ))}

      {/* Terrain editing indicators (only visible when editable) */}
      {editable && (
        <group>
          {/* Grid overlay for terrain editing */}
          <mesh position={[0, 0.1, 0]} rotation={[-Math.PI / 2, 0, 0]}>
            <planeGeometry args={[100, 100, 50, 50]} />
            <meshBasicMaterial 
              color="#ffffff" 
              transparent 
              opacity={0.1} 
              wireframe 
            />
          </mesh>
        </group>
      )}
    </group>
  );
};
