{"version": 3, "sources": ["../../lodash.isplainobject/index.js", "../../rfc6902/pointer.js", "../../rfc6902/util.js", "../../rfc6902/diff.js", "../../rfc6902/patch.js", "../../rfc6902/index.js", "../../@babel/runtime/helpers/esm/typeof.js", "../../@babel/runtime/helpers/esm/toPrimitive.js", "../../@babel/runtime/helpers/esm/toPropertyKey.js", "../../@babel/runtime/helpers/esm/defineProperty.js", "../../@babel/runtime/helpers/esm/objectSpread2.js", "../../boardgame.io/node_modules/redux/es/redux.js", "../../immer/src/utils/errors.ts", "../../immer/src/utils/common.ts", "../../immer/src/utils/plugins.ts", "../../immer/src/core/scope.ts", "../../immer/src/core/finalize.ts", "../../immer/src/core/proxy.ts", "../../immer/src/core/immerClass.ts", "../../immer/src/core/current.ts", "../../immer/src/plugins/es5.ts", "../../immer/src/plugins/patches.ts", "../../immer/src/plugins/mapset.ts", "../../immer/src/plugins/all.ts", "../../immer/src/immer.ts", "../../immer/src/utils/env.ts", "../../boardgame.io/dist/esm/plugin-random-087f861e.js", "../../boardgame.io/dist/esm/turn-order-8cc4909b.js", "../../boardgame.io/dist/esm/reducer-24ea3e4c.js", "../../boardgame.io/dist/esm/initialize-7316768f.js", "../../boardgame.io/dist/esm/transport-ce07b771.js"], "sourcesContent": ["/**\n * lodash (Custom Build) <https://lodash.com/>\n * Build: `lodash modularize exports=\"npm\" -o ./`\n * Copyright jQuery Foundation and other contributors <https://jquery.org/>\n * Released under MIT license <https://lodash.com/license>\n * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>\n * Copyright <PERSON>, DocumentCloud and Investigative Reporters & Editors\n */\n\n/** `Object#toString` result references. */\nvar objectTag = '[object Object]';\n\n/**\n * Checks if `value` is a host object in IE < 9.\n *\n * @private\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a host object, else `false`.\n */\nfunction isHostObject(value) {\n  // Many host objects are `Object` objects that can coerce to strings\n  // despite having improperly defined `toString` methods.\n  var result = false;\n  if (value != null && typeof value.toString != 'function') {\n    try {\n      result = !!(value + '');\n    } catch (e) {}\n  }\n  return result;\n}\n\n/**\n * Creates a unary function that invokes `func` with its argument transformed.\n *\n * @private\n * @param {Function} func The function to wrap.\n * @param {Function} transform The argument transform.\n * @returns {Function} Returns the new function.\n */\nfunction overArg(func, transform) {\n  return function(arg) {\n    return func(transform(arg));\n  };\n}\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to infer the `Object` constructor. */\nvar objectCtorString = funcToString.call(Object);\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar objectToString = objectProto.toString;\n\n/** Built-in value references. */\nvar getPrototype = overArg(Object.getPrototypeOf, Object);\n\n/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\nfunction isObjectLike(value) {\n  return !!value && typeof value == 'object';\n}\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nfunction isPlainObject(value) {\n  if (!isObjectLike(value) ||\n      objectToString.call(value) != objectTag || isHostObject(value)) {\n    return false;\n  }\n  var proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return (typeof Ctor == 'function' &&\n    Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString);\n}\n\nmodule.exports = isPlainObject;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Pointer = void 0;\n/**\nUnescape token part of a JSON Pointer string\n\n`token` should *not* contain any '/' characters.\n\n> Evaluation of each reference token begins by decoding any escaped\n> character sequence.  This is performed by first transforming any\n> occurrence of the sequence '~1' to '/', and then transforming any\n> occurrence of the sequence '~0' to '~'.  By performing the\n> substitutions in this order, an implementation avoids the error of\n> turning '~01' first into '~1' and then into '/', which would be\n> incorrect (the string '~01' correctly becomes '~1' after\n> transformation).\n\nHere's my take:\n\n~1 is unescaped with higher priority than ~0 because it is a lower-order escape character.\nI say \"lower order\" because '/' needs escaping due to the JSON Pointer serialization technique.\nWhereas, '~' is escaped because escaping '/' uses the '~' character.\n*/\nfunction unescape(token) {\n    return token.replace(/~1/g, '/').replace(/~0/g, '~');\n}\n/** Escape token part of a JSON Pointer string\n\n> '~' needs to be encoded as '~0' and '/'\n> needs to be encoded as '~1' when these characters appear in a\n> reference token.\n\nThis is the exact inverse of `unescape()`, so the reverse replacements must take place in reverse order.\n*/\nfunction escape(token) {\n    return token.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n/**\nJSON Pointer representation\n*/\nvar Pointer = /** @class */ (function () {\n    function Pointer(tokens) {\n        if (tokens === void 0) { tokens = ['']; }\n        this.tokens = tokens;\n    }\n    /**\n    `path` *must* be a properly escaped string.\n    */\n    Pointer.fromJSON = function (path) {\n        var tokens = path.split('/').map(unescape);\n        if (tokens[0] !== '')\n            throw new Error(\"Invalid JSON Pointer: \".concat(path));\n        return new Pointer(tokens);\n    };\n    Pointer.prototype.toString = function () {\n        return this.tokens.map(escape).join('/');\n    };\n    /**\n    Returns an object with 'parent', 'key', and 'value' properties.\n    In the special case that this Pointer's path == \"\",\n    this object will be {parent: null, key: '', value: object}.\n    Otherwise, parent and key will have the property such that parent[key] == value.\n    */\n    Pointer.prototype.evaluate = function (object) {\n        var parent = null;\n        var key = '';\n        var value = object;\n        for (var i = 1, l = this.tokens.length; i < l; i++) {\n            parent = value;\n            key = this.tokens[i];\n            if (key == '__proto__' || key == 'constructor' || key == 'prototype') {\n                continue;\n            }\n            // not sure if this the best way to handle non-existant paths...\n            value = (parent || {})[key];\n        }\n        return { parent: parent, key: key, value: value };\n    };\n    Pointer.prototype.get = function (object) {\n        return this.evaluate(object).value;\n    };\n    Pointer.prototype.set = function (object, value) {\n        var endpoint = this.evaluate(object);\n        if (endpoint.parent) {\n            endpoint.parent[endpoint.key] = value;\n        }\n    };\n    Pointer.prototype.push = function (token) {\n        // mutable\n        this.tokens.push(token);\n    };\n    /**\n    `token` should be a String. It'll be coerced to one anyway.\n  \n    immutable (shallowly)\n    */\n    Pointer.prototype.add = function (token) {\n        var tokens = this.tokens.concat(String(token));\n        return new Pointer(tokens);\n    };\n    return Pointer;\n}());\nexports.Pointer = Pointer;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.clone = exports.objectType = exports.hasOwnProperty = void 0;\nexports.hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction objectType(object) {\n    if (object === undefined) {\n        return 'undefined';\n    }\n    if (object === null) {\n        return 'null';\n    }\n    if (Array.isArray(object)) {\n        return 'array';\n    }\n    return typeof object;\n}\nexports.objectType = objectType;\nfunction isNonPrimitive(value) {\n    // loose-equality checking for null is faster than strict checking for each of null/undefined/true/false\n    // checking null first, then calling typeof, is faster than vice-versa\n    return value != null && typeof value == 'object';\n}\n/**\nRecursively copy a value.\n\n@param source - should be a JavaScript primitive, Array, Date, or (plain old) Object.\n@returns copy of source where every Array and Object have been recursively\n         reconstructed from their constituent elements\n*/\nfunction clone(source) {\n    if (!isNonPrimitive(source)) {\n        // short-circuiting is faster than a single return\n        return source;\n    }\n    // x.constructor == Array is the fastest way to check if x is an Array\n    if (source.constructor == Array) {\n        // construction via imperative for-loop is faster than source.map(arrayVsObject)\n        var length_1 = source.length;\n        // setting the Array length during construction is faster than just `[]` or `new Array()`\n        var arrayTarget = new Array(length_1);\n        for (var i = 0; i < length_1; i++) {\n            arrayTarget[i] = clone(source[i]);\n        }\n        return arrayTarget;\n    }\n    // Date\n    if (source.constructor == Date) {\n        var dateTarget = new Date(+source);\n        return dateTarget;\n    }\n    // Object\n    var objectTarget = {};\n    // declaring the variable (with const) inside the loop is faster\n    for (var key in source) {\n        // hasOwnProperty costs a bit of performance, but it's semantically necessary\n        // using a global helper is MUCH faster than calling source.hasOwnProperty(key)\n        if (exports.hasOwnProperty.call(source, key)) {\n            objectTarget[key] = clone(source[key]);\n        }\n    }\n    return objectTarget;\n}\nexports.clone = clone;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.diffAny = exports.diffObjects = exports.diffArrays = exports.intersection = exports.subtract = exports.isDestructive = void 0;\nvar util_1 = require(\"./util\");\nfunction isDestructive(_a) {\n    var op = _a.op;\n    return op === 'remove' || op === 'replace' || op === 'copy' || op === 'move';\n}\nexports.isDestructive = isDestructive;\n/**\nList the keys in `minuend` that are not in `subtrahend`.\n\nA key is only considered if it is both 1) an own-property (o.hasOwnProperty(k))\nof the object, and 2) has a value that is not undefined. This is to match JSON\nsemantics, where JSON object serialization drops keys with undefined values.\n\n@param minuend Object of interest\n@param subtrahend Object of comparison\n@returns Array of keys that are in `minuend` but not in `subtrahend`.\n*/\nfunction subtract(minuend, subtrahend) {\n    // initialize empty object; we only care about the keys, the values can be anything\n    var obj = {};\n    // build up obj with all the properties of minuend\n    for (var add_key in minuend) {\n        if (util_1.hasOwnProperty.call(minuend, add_key) && minuend[add_key] !== undefined) {\n            obj[add_key] = 1;\n        }\n    }\n    // now delete all the properties of subtrahend from obj\n    // (deleting a missing key has no effect)\n    for (var del_key in subtrahend) {\n        if (util_1.hasOwnProperty.call(subtrahend, del_key) && subtrahend[del_key] !== undefined) {\n            delete obj[del_key];\n        }\n    }\n    // finally, extract whatever keys remain in obj\n    return Object.keys(obj);\n}\nexports.subtract = subtract;\n/**\nList the keys that shared by all `objects`.\n\nThe semantics of what constitutes a \"key\" is described in {@link subtract}.\n\n@param objects Array of objects to compare\n@returns Array of keys that are in (\"own-properties\" of) every object in `objects`.\n*/\nfunction intersection(objects) {\n    var length = objects.length;\n    // prepare empty counter to keep track of how many objects each key occurred in\n    var counter = {};\n    // go through each object and increment the counter for each key in that object\n    for (var i = 0; i < length; i++) {\n        var object = objects[i];\n        for (var key in object) {\n            if (util_1.hasOwnProperty.call(object, key) && object[key] !== undefined) {\n                counter[key] = (counter[key] || 0) + 1;\n            }\n        }\n    }\n    // now delete all keys from the counter that were not seen in every object\n    for (var key in counter) {\n        if (counter[key] < length) {\n            delete counter[key];\n        }\n    }\n    // finally, extract whatever keys remain in the counter\n    return Object.keys(counter);\n}\nexports.intersection = intersection;\nfunction isArrayAdd(array_operation) {\n    return array_operation.op === 'add';\n}\nfunction isArrayRemove(array_operation) {\n    return array_operation.op === 'remove';\n}\nfunction appendArrayOperation(base, operation) {\n    return {\n        // the new operation must be pushed on the end\n        operations: base.operations.concat(operation),\n        cost: base.cost + 1,\n    };\n}\n/**\nCalculate the shortest sequence of operations to get from `input` to `output`,\nusing a dynamic programming implementation of the Levenshtein distance algorithm.\n\nTo get from the input ABC to the output AZ we could just delete all the input\nand say \"insert A, insert Z\" and be done with it. That's what we do if the\ninput is empty. But we can be smarter.\n\n          output\n               A   Z\n               -   -\n          [0]  1   2\ninput A |  1  [0]  1\n      B |  2  [1]  1\n      C |  3   2  [2]\n\n1) start at 0,0 (+0)\n2) keep A (+0)\n3) remove B (+1)\n4) replace C with Z (+1)\n\nIf the `input` (source) is empty, they'll all be in the top row, resulting in an\narray of 'add' operations.\nIf the `output` (target) is empty, everything will be in the left column,\nresulting in an array of 'remove' operations.\n\n@returns A list of add/remove/replace operations.\n*/\nfunction diffArrays(input, output, ptr, diff) {\n    if (diff === void 0) { diff = diffAny; }\n    // set up cost matrix (very simple initialization: just a map)\n    var memo = {\n        '0,0': { operations: [], cost: 0 },\n    };\n    /**\n    Calculate the cheapest sequence of operations required to get from\n    input.slice(0, i) to output.slice(0, j).\n    There may be other valid sequences with the same cost, but none cheaper.\n  \n    @param i The row in the layout above\n    @param j The column in the layout above\n    @returns An object containing a list of operations, along with the total cost\n             of applying them (+1 for each add/remove/replace operation)\n    */\n    function dist(i, j) {\n        // memoized\n        var memo_key = \"\".concat(i, \",\").concat(j);\n        var memoized = memo[memo_key];\n        if (memoized === undefined) {\n            // TODO: this !diff(...).length usage could/should be lazy\n            if (i > 0 && j > 0 && !diff(input[i - 1], output[j - 1], ptr.add(String(i - 1))).length) {\n                // equal (no operations => no cost)\n                memoized = dist(i - 1, j - 1);\n            }\n            else {\n                var alternatives = [];\n                if (i > 0) {\n                    // NOT topmost row\n                    var remove_base = dist(i - 1, j);\n                    var remove_operation = {\n                        op: 'remove',\n                        index: i - 1,\n                    };\n                    alternatives.push(appendArrayOperation(remove_base, remove_operation));\n                }\n                if (j > 0) {\n                    // NOT leftmost column\n                    var add_base = dist(i, j - 1);\n                    var add_operation = {\n                        op: 'add',\n                        index: i - 1,\n                        value: output[j - 1],\n                    };\n                    alternatives.push(appendArrayOperation(add_base, add_operation));\n                }\n                if (i > 0 && j > 0) {\n                    // TABLE MIDDLE\n                    // supposing we replaced it, compute the rest of the costs:\n                    var replace_base = dist(i - 1, j - 1);\n                    // okay, the general plan is to replace it, but we can be smarter,\n                    // recursing into the structure and replacing only part of it if\n                    // possible, but to do so we'll need the original value\n                    var replace_operation = {\n                        op: 'replace',\n                        index: i - 1,\n                        original: input[i - 1],\n                        value: output[j - 1],\n                    };\n                    alternatives.push(appendArrayOperation(replace_base, replace_operation));\n                }\n                // the only other case, i === 0 && j === 0, has already been memoized\n                // the meat of the algorithm:\n                // sort by cost to find the lowest one (might be several ties for lowest)\n                // [4, 6, 7, 1, 2].sort((a, b) => a - b) -> [ 1, 2, 4, 6, 7 ]\n                var best = alternatives.sort(function (a, b) { return a.cost - b.cost; })[0];\n                memoized = best;\n            }\n            memo[memo_key] = memoized;\n        }\n        return memoized;\n    }\n    // handle weird objects masquerading as Arrays that don't have proper length\n    // properties by using 0 for everything but positive numbers\n    var input_length = (isNaN(input.length) || input.length <= 0) ? 0 : input.length;\n    var output_length = (isNaN(output.length) || output.length <= 0) ? 0 : output.length;\n    var array_operations = dist(input_length, output_length).operations;\n    var padded_operations = array_operations.reduce(function (_a, array_operation) {\n        var operations = _a[0], padding = _a[1];\n        if (isArrayAdd(array_operation)) {\n            var padded_index = array_operation.index + 1 + padding;\n            var index_token = padded_index < (input_length + padding) ? String(padded_index) : '-';\n            var operation = {\n                op: array_operation.op,\n                path: ptr.add(index_token).toString(),\n                value: array_operation.value,\n            };\n            // padding++ // maybe only if array_operation.index > -1 ?\n            return [operations.concat(operation), padding + 1];\n        }\n        else if (isArrayRemove(array_operation)) {\n            var operation = {\n                op: array_operation.op,\n                path: ptr.add(String(array_operation.index + padding)).toString(),\n            };\n            // padding--\n            return [operations.concat(operation), padding - 1];\n        }\n        else { // replace\n            var replace_ptr = ptr.add(String(array_operation.index + padding));\n            var replace_operations = diff(array_operation.original, array_operation.value, replace_ptr);\n            return [operations.concat.apply(operations, replace_operations), padding];\n        }\n    }, [[], 0])[0];\n    return padded_operations;\n}\nexports.diffArrays = diffArrays;\nfunction diffObjects(input, output, ptr, diff) {\n    if (diff === void 0) { diff = diffAny; }\n    // if a key is in input but not output -> remove it\n    var operations = [];\n    subtract(input, output).forEach(function (key) {\n        operations.push({ op: 'remove', path: ptr.add(key).toString() });\n    });\n    // if a key is in output but not input -> add it\n    subtract(output, input).forEach(function (key) {\n        operations.push({ op: 'add', path: ptr.add(key).toString(), value: output[key] });\n    });\n    // if a key is in both, diff it recursively\n    intersection([input, output]).forEach(function (key) {\n        operations.push.apply(operations, diff(input[key], output[key], ptr.add(key)));\n    });\n    return operations;\n}\nexports.diffObjects = diffObjects;\n/**\n`diffAny()` returns an empty array if `input` and `output` are materially equal\n(i.e., would produce equivalent JSON); otherwise it produces an array of patches\nthat would transform `input` into `output`.\n\n> Here, \"equal\" means that the value at the target location and the\n> value conveyed by \"value\" are of the same JSON type, and that they\n> are considered equal by the following rules for that type:\n> o  strings: are considered equal if they contain the same number of\n>    Unicode characters and their code points are byte-by-byte equal.\n> o  numbers: are considered equal if their values are numerically\n>    equal.\n> o  arrays: are considered equal if they contain the same number of\n>    values, and if each value can be considered equal to the value at\n>    the corresponding position in the other array, using this list of\n>    type-specific rules.\n> o  objects: are considered equal if they contain the same number of\n>    members, and if each member can be considered equal to a member in\n>    the other object, by comparing their keys (as strings) and their\n>    values (using this list of type-specific rules).\n> o  literals (false, true, and null): are considered equal if they are\n>    the same.\n*/\nfunction diffAny(input, output, ptr, diff) {\n    if (diff === void 0) { diff = diffAny; }\n    // strict equality handles literals, numbers, and strings (a sufficient but not necessary cause)\n    if (input === output) {\n        return [];\n    }\n    var input_type = (0, util_1.objectType)(input);\n    var output_type = (0, util_1.objectType)(output);\n    if (input_type == 'array' && output_type == 'array') {\n        return diffArrays(input, output, ptr, diff);\n    }\n    if (input_type == 'object' && output_type == 'object') {\n        return diffObjects(input, output, ptr, diff);\n    }\n    // at this point we know that input and output are materially different;\n    // could be array -> object, object -> array, boolean -> undefined,\n    // number -> string, or some other combination, but nothing that can be split\n    // up into multiple patches: so `output` must replace `input` wholesale.\n    return [{ op: 'replace', path: ptr.toString(), value: output }];\n}\nexports.diffAny = diffAny;\n", "\"use strict\";\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.apply = exports.InvalidOperationError = exports.test = exports.copy = exports.move = exports.replace = exports.remove = exports.add = exports.TestError = exports.MissingError = void 0;\nvar pointer_1 = require(\"./pointer\");\nvar util_1 = require(\"./util\");\nvar diff_1 = require(\"./diff\");\nvar MissingError = /** @class */ (function (_super) {\n    __extends(MissingError, _super);\n    function MissingError(path) {\n        var _this = _super.call(this, \"Value required at path: \".concat(path)) || this;\n        _this.path = path;\n        _this.name = 'MissingError';\n        return _this;\n    }\n    return MissingError;\n}(Error));\nexports.MissingError = MissingError;\nvar TestError = /** @class */ (function (_super) {\n    __extends(TestError, _super);\n    function TestError(actual, expected) {\n        var _this = _super.call(this, \"Test failed: \".concat(actual, \" != \").concat(expected)) || this;\n        _this.actual = actual;\n        _this.expected = expected;\n        _this.name = 'TestError';\n        return _this;\n    }\n    return TestError;\n}(Error));\nexports.TestError = TestError;\nfunction _add(object, key, value) {\n    if (Array.isArray(object)) {\n        // `key` must be an index\n        if (key == '-') {\n            object.push(value);\n        }\n        else {\n            var index = parseInt(key, 10);\n            object.splice(index, 0, value);\n        }\n    }\n    else {\n        object[key] = value;\n    }\n}\nfunction _remove(object, key) {\n    if (Array.isArray(object)) {\n        // '-' syntax doesn't make sense when removing\n        var index = parseInt(key, 10);\n        object.splice(index, 1);\n    }\n    else {\n        // not sure what the proper behavior is when path = ''\n        delete object[key];\n    }\n}\n/**\n>  o  If the target location specifies an array index, a new value is\n>     inserted into the array at the specified index.\n>  o  If the target location specifies an object member that does not\n>     already exist, a new member is added to the object.\n>  o  If the target location specifies an object member that does exist,\n>     that member's value is replaced.\n*/\nfunction add(object, operation) {\n    var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);\n    // it's not exactly a \"MissingError\" in the same way that `remove` is -- more like a MissingParent, or something\n    if (endpoint.parent === undefined) {\n        return new MissingError(operation.path);\n    }\n    _add(endpoint.parent, endpoint.key, (0, util_1.clone)(operation.value));\n    return null;\n}\nexports.add = add;\n/**\n> The \"remove\" operation removes the value at the target location.\n> The target location MUST exist for the operation to be successful.\n*/\nfunction remove(object, operation) {\n    // endpoint has parent, key, and value properties\n    var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);\n    if (endpoint.value === undefined) {\n        return new MissingError(operation.path);\n    }\n    // not sure what the proper behavior is when path = ''\n    _remove(endpoint.parent, endpoint.key);\n    return null;\n}\nexports.remove = remove;\n/**\n> The \"replace\" operation replaces the value at the target location\n> with a new value.  The operation object MUST contain a \"value\" member\n> whose content specifies the replacement value.\n> The target location MUST exist for the operation to be successful.\n\n> This operation is functionally identical to a \"remove\" operation for\n> a value, followed immediately by an \"add\" operation at the same\n> location with the replacement value.\n\nEven more simply, it's like the add operation with an existence check.\n*/\nfunction replace(object, operation) {\n    var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);\n    if (endpoint.parent === null) {\n        return new MissingError(operation.path);\n    }\n    // this existence check treats arrays as a special case\n    if (Array.isArray(endpoint.parent)) {\n        if (parseInt(endpoint.key, 10) >= endpoint.parent.length) {\n            return new MissingError(operation.path);\n        }\n    }\n    else if (endpoint.value === undefined) {\n        return new MissingError(operation.path);\n    }\n    endpoint.parent[endpoint.key] = (0, util_1.clone)(operation.value);\n    return null;\n}\nexports.replace = replace;\n/**\n> The \"move\" operation removes the value at a specified location and\n> adds it to the target location.\n> The operation object MUST contain a \"from\" member, which is a string\n> containing a JSON Pointer value that references the location in the\n> target document to move the value from.\n> This operation is functionally identical to a \"remove\" operation on\n> the \"from\" location, followed immediately by an \"add\" operation at\n> the target location with the value that was just removed.\n\n> The \"from\" location MUST NOT be a proper prefix of the \"path\"\n> location; i.e., a location cannot be moved into one of its children.\n\nTODO: throw if the check described in the previous paragraph fails.\n*/\nfunction move(object, operation) {\n    var from_endpoint = pointer_1.Pointer.fromJSON(operation.from).evaluate(object);\n    if (from_endpoint.value === undefined) {\n        return new MissingError(operation.from);\n    }\n    var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);\n    if (endpoint.parent === undefined) {\n        return new MissingError(operation.path);\n    }\n    _remove(from_endpoint.parent, from_endpoint.key);\n    _add(endpoint.parent, endpoint.key, from_endpoint.value);\n    return null;\n}\nexports.move = move;\n/**\n> The \"copy\" operation copies the value at a specified location to the\n> target location.\n> The operation object MUST contain a \"from\" member, which is a string\n> containing a JSON Pointer value that references the location in the\n> target document to copy the value from.\n> The \"from\" location MUST exist for the operation to be successful.\n\n> This operation is functionally identical to an \"add\" operation at the\n> target location using the value specified in the \"from\" member.\n\nAlternatively, it's like 'move' without the 'remove'.\n*/\nfunction copy(object, operation) {\n    var from_endpoint = pointer_1.Pointer.fromJSON(operation.from).evaluate(object);\n    if (from_endpoint.value === undefined) {\n        return new MissingError(operation.from);\n    }\n    var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);\n    if (endpoint.parent === undefined) {\n        return new MissingError(operation.path);\n    }\n    _add(endpoint.parent, endpoint.key, (0, util_1.clone)(from_endpoint.value));\n    return null;\n}\nexports.copy = copy;\n/**\n> The \"test\" operation tests that a value at the target location is\n> equal to a specified value.\n> The operation object MUST contain a \"value\" member that conveys the\n> value to be compared to the target location's value.\n> The target location MUST be equal to the \"value\" value for the\n> operation to be considered successful.\n*/\nfunction test(object, operation) {\n    var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);\n    // TODO: this diffAny(...).length usage could/should be lazy\n    if ((0, diff_1.diffAny)(endpoint.value, operation.value, new pointer_1.Pointer()).length) {\n        return new TestError(endpoint.value, operation.value);\n    }\n    return null;\n}\nexports.test = test;\nvar InvalidOperationError = /** @class */ (function (_super) {\n    __extends(InvalidOperationError, _super);\n    function InvalidOperationError(operation) {\n        var _this = _super.call(this, \"Invalid operation: \".concat(operation.op)) || this;\n        _this.operation = operation;\n        _this.name = 'InvalidOperationError';\n        return _this;\n    }\n    return InvalidOperationError;\n}(Error));\nexports.InvalidOperationError = InvalidOperationError;\n/**\nSwitch on `operation.op`, applying the corresponding patch function for each\ncase to `object`.\n*/\nfunction apply(object, operation) {\n    // not sure why TypeScript can't infer typesafety of:\n    //   {add, remove, replace, move, copy, test}[operation.op](object, operation)\n    // (seems like a bug)\n    switch (operation.op) {\n        case 'add': return add(object, operation);\n        case 'remove': return remove(object, operation);\n        case 'replace': return replace(object, operation);\n        case 'move': return move(object, operation);\n        case 'copy': return copy(object, operation);\n        case 'test': return test(object, operation);\n    }\n    return new InvalidOperationError(operation);\n}\nexports.apply = apply;\n", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.createTests = exports.createPatch = exports.applyPatch = exports.Pointer = void 0;\nvar pointer_1 = require(\"./pointer\");\nObject.defineProperty(exports, \"Pointer\", { enumerable: true, get: function () { return pointer_1.Pointer; } });\nvar patch_1 = require(\"./patch\");\nvar diff_1 = require(\"./diff\");\n/**\nApply a 'application/json-patch+json'-type patch to an object.\n\n`patch` *must* be an array of operations.\n\n> Operation objects MUST have exactly one \"op\" member, whose value\n> indicates the operation to perform.  Its value MUST be one of \"add\",\n> \"remove\", \"replace\", \"move\", \"copy\", or \"test\"; other values are\n> errors.\n\nThis method mutates the target object in-place.\n\n@returns list of results, one for each operation: `null` indicated success,\n         otherwise, the result will be an instance of one of the Error classes:\n         MissingError, InvalidOperationError, or TestError.\n*/\nfunction applyPatch(object, patch) {\n    return patch.map(function (operation) { return (0, patch_1.apply)(object, operation); });\n}\nexports.applyPatch = applyPatch;\nfunction wrapVoidableDiff(diff) {\n    function wrappedDiff(input, output, ptr) {\n        var custom_patch = diff(input, output, ptr);\n        // ensure an array is always returned\n        return Array.isArray(custom_patch) ? custom_patch : (0, diff_1.diffAny)(input, output, ptr, wrappedDiff);\n    }\n    return wrappedDiff;\n}\n/**\nProduce a 'application/json-patch+json'-type patch to get from one object to\nanother.\n\nThis does not alter `input` or `output` unless they have a property getter with\nside-effects (which is not a good idea anyway).\n\n`diff` is called on each pair of comparable non-primitive nodes in the\n`input`/`output` object trees, producing nested patches. Return `undefined`\nto fall back to default behaviour.\n\nReturns list of operations to perform on `input` to produce `output`.\n*/\nfunction createPatch(input, output, diff) {\n    var ptr = new pointer_1.Pointer();\n    // a new Pointer gets a default path of [''] if not specified\n    return (diff ? wrapVoidableDiff(diff) : diff_1.diffAny)(input, output, ptr);\n}\nexports.createPatch = createPatch;\n/**\nCreate a test operation based on `input`'s current evaluation of the JSON\nPointer `path`; if such a pointer cannot be resolved, returns undefined.\n*/\nfunction createTest(input, path) {\n    var endpoint = pointer_1.Pointer.fromJSON(path).evaluate(input);\n    if (endpoint !== undefined) {\n        return { op: 'test', path: path, value: endpoint.value };\n    }\n}\n/**\nProduce an 'application/json-patch+json'-type list of tests, to verify that\nexisting values in an object are identical to the those captured at some\ncheckpoint (whenever this function is called).\n\nThis does not alter `input` or `output` unless they have a property getter with\nside-effects (which is not a good idea anyway).\n\nReturns list of test operations.\n*/\nfunction createTests(input, patch) {\n    var tests = new Array();\n    patch.filter(diff_1.isDestructive).forEach(function (operation) {\n        var pathTest = createTest(input, operation.path);\n        if (pathTest)\n            tests.push(pathTest);\n        if ('from' in operation) {\n            var fromTest = createTest(input, operation.from);\n            if (fromTest)\n                tests.push(fromTest);\n        }\n    });\n    return tests;\n}\nexports.createTests = createTests;\n", "export default function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}", "import _typeof from \"./typeof.js\";\nexport default function toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nexport default function toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : String(i);\n}", "import toPropertyKey from \"./toPropertyKey.js\";\nexport default function _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}", "import defineProperty from \"./defineProperty.js\";\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nexport default function _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}", "import _objectSpread from '@babel/runtime/helpers/esm/objectSpread2';\n\n/**\n * Adapted from React: https://github.com/facebook/react/blob/master/packages/shared/formatProdErrorMessage.js\n *\n * Do not require this module directly! Use normal throw error calls. These messages will be replaced with error codes\n * during build.\n * @param {number} code\n */\nfunction formatProdErrorMessage(code) {\n  return \"Minified Redux error #\" + code + \"; visit https://redux.js.org/Errors?code=\" + code + \" for the full message or \" + 'use the non-minified dev environment for full errors. ';\n}\n\n// Inlined version of the `symbol-observable` polyfill\nvar $$observable = (function () {\n  return typeof Symbol === 'function' && Symbol.observable || '@@observable';\n})();\n\n/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\nvar randomString = function randomString() {\n  return Math.random().toString(36).substring(7).split('').join('.');\n};\n\nvar ActionTypes = {\n  INIT: \"@@redux/INIT\" + randomString(),\n  REPLACE: \"@@redux/REPLACE\" + randomString(),\n  PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {\n    return \"@@redux/PROBE_UNKNOWN_ACTION\" + randomString();\n  }\n};\n\n/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nfunction isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = obj;\n\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n\n  return Object.getPrototypeOf(obj) === proto;\n}\n\n// Inlined / shortened version of `kindOf` from https://github.com/jonschlinkert/kind-of\nfunction miniKindOf(val) {\n  if (val === void 0) return 'undefined';\n  if (val === null) return 'null';\n  var type = typeof val;\n\n  switch (type) {\n    case 'boolean':\n    case 'string':\n    case 'number':\n    case 'symbol':\n    case 'function':\n      {\n        return type;\n      }\n  }\n\n  if (Array.isArray(val)) return 'array';\n  if (isDate(val)) return 'date';\n  if (isError(val)) return 'error';\n  var constructorName = ctorName(val);\n\n  switch (constructorName) {\n    case 'Symbol':\n    case 'Promise':\n    case 'WeakMap':\n    case 'WeakSet':\n    case 'Map':\n    case 'Set':\n      return constructorName;\n  } // other\n\n\n  return type.slice(8, -1).toLowerCase().replace(/\\s/g, '');\n}\n\nfunction ctorName(val) {\n  return typeof val.constructor === 'function' ? val.constructor.name : null;\n}\n\nfunction isError(val) {\n  return val instanceof Error || typeof val.message === 'string' && val.constructor && typeof val.constructor.stackTraceLimit === 'number';\n}\n\nfunction isDate(val) {\n  if (val instanceof Date) return true;\n  return typeof val.toDateString === 'function' && typeof val.getDate === 'function' && typeof val.setDate === 'function';\n}\n\nfunction kindOf(val) {\n  var typeOfVal = typeof val;\n\n  if (process.env.NODE_ENV !== 'production') {\n    typeOfVal = miniKindOf(val);\n  }\n\n  return typeOfVal;\n}\n\n/**\n * @deprecated\n *\n * **We recommend using the `configureStore` method\n * of the `@reduxjs/toolkit` package**, which replaces `createStore`.\n *\n * Redux Toolkit is our recommended approach for writing Redux logic today,\n * including store setup, reducers, data fetching, and more.\n *\n * **For more details, please read this Redux docs page:**\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * `configureStore` from Redux Toolkit is an improved version of `createStore` that\n * simplifies setup and helps avoid common bugs.\n *\n * You should not be using the `redux` core package by itself today, except for learning purposes.\n * The `createStore` method from the core `redux` package will not be removed, but we encourage\n * all users to migrate to using Redux Toolkit for all Redux code.\n *\n * If you want to use `createStore` without this visual deprecation warning, use\n * the `legacy_createStore` import instead:\n *\n * `import { legacy_createStore as createStore} from 'redux'`\n *\n */\n\nfunction createStore(reducer, preloadedState, enhancer) {\n  var _ref2;\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(0) : 'It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.');\n  }\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = preloadedState;\n    preloadedState = undefined;\n  }\n\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(1) : \"Expected the enhancer to be a function. Instead, received: '\" + kindOf(enhancer) + \"'\");\n    }\n\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n\n  if (typeof reducer !== 'function') {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(2) : \"Expected the root reducer to be a function. Instead, received: '\" + kindOf(reducer) + \"'\");\n  }\n\n  var currentReducer = reducer;\n  var currentState = preloadedState;\n  var currentListeners = [];\n  var nextListeners = currentListeners;\n  var isDispatching = false;\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice();\n    }\n  }\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n\n\n  function getState() {\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(3) : 'You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n\n    return currentState;\n  }\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n\n\n  function subscribe(listener) {\n    if (typeof listener !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(4) : \"Expected the listener to be a function. Instead, received: '\" + kindOf(listener) + \"'\");\n    }\n\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(5) : 'You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n    }\n\n    var isSubscribed = true;\n    ensureCanMutateNextListeners();\n    nextListeners.push(listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n\n      if (isDispatching) {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(6) : 'You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api/store#subscribelistener for more details.');\n      }\n\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      var index = nextListeners.indexOf(listener);\n      nextListeners.splice(index, 1);\n      currentListeners = null;\n    };\n  }\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n\n\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(7) : \"Actions must be plain objects. Instead, the actual type was: '\" + kindOf(action) + \"'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.\");\n    }\n\n    if (typeof action.type === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(8) : 'Actions may not have an undefined \"type\" property. You may have misspelled an action type string constant.');\n    }\n\n    if (isDispatching) {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(9) : 'Reducers may not dispatch actions.');\n    }\n\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n\n    var listeners = currentListeners = nextListeners;\n\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n\n    return action;\n  }\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n\n\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== 'function') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(10) : \"Expected the nextReducer to be a function. Instead, received: '\" + kindOf(nextReducer));\n    }\n\n    currentReducer = nextReducer; // This action has a similiar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n\n    dispatch({\n      type: ActionTypes.REPLACE\n    });\n  }\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n\n\n  function observable() {\n    var _ref;\n\n    var outerSubscribe = subscribe;\n    return _ref = {\n      /**\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe: function subscribe(observer) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(11) : \"Expected the observer to be an object. Instead, received: '\" + kindOf(observer) + \"'\");\n        }\n\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState());\n          }\n        }\n\n        observeState();\n        var unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe: unsubscribe\n        };\n      }\n    }, _ref[$$observable] = function () {\n      return this;\n    }, _ref;\n  } // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n\n\n  dispatch({\n    type: ActionTypes.INIT\n  });\n  return _ref2 = {\n    dispatch: dispatch,\n    subscribe: subscribe,\n    getState: getState,\n    replaceReducer: replaceReducer\n  }, _ref2[$$observable] = observable, _ref2;\n}\n/**\n * Creates a Redux store that holds the state tree.\n *\n * **We recommend using `configureStore` from the\n * `@reduxjs/toolkit` package**, which replaces `createStore`:\n * **https://redux.js.org/introduction/why-rtk-is-redux-today**\n *\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\n\nvar legacy_createStore = createStore;\n\n/**\n * Prints a warning in the console if it exists.\n *\n * @param {String} message The warning message.\n * @returns {void}\n */\nfunction warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n\n}\n\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  var reducerKeys = Object.keys(reducers);\n  var argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n\n  if (!isPlainObject(inputState)) {\n    return \"The \" + argumentName + \" has unexpected type of \\\"\" + kindOf(inputState) + \"\\\". Expected argument to be an object with the following \" + (\"keys: \\\"\" + reducerKeys.join('\", \"') + \"\\\"\");\n  }\n\n  var unexpectedKeys = Object.keys(inputState).filter(function (key) {\n    return !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key];\n  });\n  unexpectedKeys.forEach(function (key) {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n\n  if (unexpectedKeys.length > 0) {\n    return \"Unexpected \" + (unexpectedKeys.length > 1 ? 'keys' : 'key') + \" \" + (\"\\\"\" + unexpectedKeys.join('\", \"') + \"\\\" found in \" + argumentName + \". \") + \"Expected to find one of the known reducer keys instead: \" + (\"\\\"\" + reducerKeys.join('\", \"') + \"\\\". Unexpected keys will be ignored.\");\n  }\n}\n\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(function (key) {\n    var reducer = reducers[key];\n    var initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n\n    if (typeof initialState === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(12) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined during initialization. \" + \"If the state passed to the reducer is undefined, you must \" + \"explicitly return the initial state. The initial state may \" + \"not be undefined. If you don't want to set a value for this reducer, \" + \"you can use null instead of undefined.\");\n    }\n\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(13) : \"The slice reducer for key \\\"\" + key + \"\\\" returned undefined when probed with a random type. \" + (\"Don't try to handle '\" + ActionTypes.INIT + \"' or other actions in \\\"redux/*\\\" \") + \"namespace. They are considered private. Instead, you must return the \" + \"current state for any unknown actions, unless it is undefined, \" + \"in which case you must return the initial state, regardless of the \" + \"action type. The initial state may not be undefined, but can be null.\");\n    }\n  });\n}\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\n\n\nfunction combineReducers(reducers) {\n  var reducerKeys = Object.keys(reducers);\n  var finalReducers = {};\n\n  for (var i = 0; i < reducerKeys.length; i++) {\n    var key = reducerKeys[i];\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(\"No reducer provided for key \\\"\" + key + \"\\\"\");\n      }\n    }\n\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n\n  var finalReducerKeys = Object.keys(finalReducers); // This is used to make sure we don't warn about the same\n  // keys multiple times.\n\n  var unexpectedKeyCache;\n\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n\n  var shapeAssertionError;\n\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n\n  return function combination(state, action) {\n    if (state === void 0) {\n      state = {};\n    }\n\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      var warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n\n    var hasChanged = false;\n    var nextState = {};\n\n    for (var _i = 0; _i < finalReducerKeys.length; _i++) {\n      var _key = finalReducerKeys[_i];\n      var reducer = finalReducers[_key];\n      var previousStateForKey = state[_key];\n      var nextStateForKey = reducer(previousStateForKey, action);\n\n      if (typeof nextStateForKey === 'undefined') {\n        var actionType = action && action.type;\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(14) : \"When called with an action of type \" + (actionType ? \"\\\"\" + String(actionType) + \"\\\"\" : '(unknown type)') + \", the slice reducer for key \\\"\" + _key + \"\\\" returned undefined. \" + \"To ignore an action, you must explicitly return the previous state. \" + \"If you want this reducer to hold no value, you can return null instead of undefined.\");\n      }\n\n      nextState[_key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n\n    hasChanged = hasChanged || finalReducerKeys.length !== Object.keys(state).length;\n    return hasChanged ? nextState : state;\n  };\n}\n\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function () {\n    return dispatch(actionCreator.apply(this, arguments));\n  };\n}\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param {Function|Object} actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use ES6 `import * as`\n * syntax. You may also pass a single function.\n *\n * @param {Function} dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns {Function|Object} The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\n\n\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(16) : \"bindActionCreators expected an object or a function, but instead received: '\" + kindOf(actionCreators) + \"'. \" + \"Did you write \\\"import ActionCreators from\\\" instead of \\\"import * as ActionCreators from\\\"?\");\n  }\n\n  var boundActionCreators = {};\n\n  for (var key in actionCreators) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n\n  return boundActionCreators;\n}\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\nfunction compose() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(void 0, arguments));\n    };\n  });\n}\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\n\nfunction applyMiddleware() {\n  for (var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++) {\n    middlewares[_key] = arguments[_key];\n  }\n\n  return function (createStore) {\n    return function () {\n      var store = createStore.apply(void 0, arguments);\n\n      var _dispatch = function dispatch() {\n        throw new Error(process.env.NODE_ENV === \"production\" ? formatProdErrorMessage(15) : 'Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n      };\n\n      var middlewareAPI = {\n        getState: store.getState,\n        dispatch: function dispatch() {\n          return _dispatch.apply(void 0, arguments);\n        }\n      };\n      var chain = middlewares.map(function (middleware) {\n        return middleware(middlewareAPI);\n      });\n      _dispatch = compose.apply(void 0, chain)(store.dispatch);\n      return _objectSpread(_objectSpread({}, store), {}, {\n        dispatch: _dispatch\n      });\n    };\n  };\n}\n\nexport { ActionTypes as __DO_NOT_USE__ActionTypes, applyMiddleware, bindActionCreators, combineReducers, compose, createStore, legacy_createStore };\n", "const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ProxyType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n", "// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n", "// Inlined version of <PERSON><PERSON> from https://github.com/davidbau/seedrandom.\r\n// Converted to Typescript October 2020.\r\nclass Alea {\r\n    constructor(seed) {\r\n        const mash = Mash();\r\n        // Apply the seeding algorithm from Baagoe.\r\n        this.c = 1;\r\n        this.s0 = mash(' ');\r\n        this.s1 = mash(' ');\r\n        this.s2 = mash(' ');\r\n        this.s0 -= mash(seed);\r\n        if (this.s0 < 0) {\r\n            this.s0 += 1;\r\n        }\r\n        this.s1 -= mash(seed);\r\n        if (this.s1 < 0) {\r\n            this.s1 += 1;\r\n        }\r\n        this.s2 -= mash(seed);\r\n        if (this.s2 < 0) {\r\n            this.s2 += 1;\r\n        }\r\n    }\r\n    next() {\r\n        const t = 2091639 * this.s0 + this.c * 2.3283064365386963e-10; // 2^-32\r\n        this.s0 = this.s1;\r\n        this.s1 = this.s2;\r\n        return (this.s2 = t - (this.c = Math.trunc(t)));\r\n    }\r\n}\r\nfunction Mash() {\r\n    let n = 0xefc8249d;\r\n    const mash = function (data) {\r\n        const str = data.toString();\r\n        for (let i = 0; i < str.length; i++) {\r\n            n += str.charCodeAt(i);\r\n            let h = 0.02519603282416938 * n;\r\n            n = h >>> 0;\r\n            h -= n;\r\n            h *= n;\r\n            n = h >>> 0;\r\n            h -= n;\r\n            n += h * 0x100000000; // 2^32\r\n        }\r\n        return (n >>> 0) * 2.3283064365386963e-10; // 2^-32\r\n    };\r\n    return mash;\r\n}\r\nfunction copy(f, t) {\r\n    t.c = f.c;\r\n    t.s0 = f.s0;\r\n    t.s1 = f.s1;\r\n    t.s2 = f.s2;\r\n    return t;\r\n}\r\nfunction alea(seed, state) {\r\n    const xg = new Alea(seed);\r\n    const prng = xg.next.bind(xg);\r\n    if (state)\r\n        copy(state, xg);\r\n    prng.state = () => copy(xg, {});\r\n    return prng;\r\n}\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Random\r\n *\r\n * Calls that require a pseudorandom number generator.\r\n * Uses a seed from ctx, and also persists the PRNG\r\n * state in ctx so that moves can stay pure.\r\n */\r\nclass Random {\r\n    /**\r\n     * constructor\r\n     * @param {object} ctx - The ctx object to initialize from.\r\n     */\r\n    constructor(state) {\r\n        // If we are on the client, the seed is not present.\r\n        // Just use a temporary seed to execute the move without\r\n        // crashing it. The move state itself is discarded,\r\n        // so the actual value doesn't matter.\r\n        this.state = state || { seed: '0' };\r\n        this.used = false;\r\n    }\r\n    /**\r\n     * Generates a new seed from the current date / time.\r\n     */\r\n    static seed() {\r\n        return Date.now().toString(36).slice(-10);\r\n    }\r\n    isUsed() {\r\n        return this.used;\r\n    }\r\n    getState() {\r\n        return this.state;\r\n    }\r\n    /**\r\n     * Generate a random number.\r\n     */\r\n    _random() {\r\n        this.used = true;\r\n        const R = this.state;\r\n        const seed = R.prngstate ? '' : R.seed;\r\n        const rand = alea(seed, R.prngstate);\r\n        const number = rand();\r\n        this.state = {\r\n            ...R,\r\n            prngstate: rand.state(),\r\n        };\r\n        return number;\r\n    }\r\n    api() {\r\n        const random = this._random.bind(this);\r\n        const SpotValue = {\r\n            D4: 4,\r\n            D6: 6,\r\n            D8: 8,\r\n            D10: 10,\r\n            D12: 12,\r\n            D20: 20,\r\n        };\r\n        // Generate functions for predefined dice values D4 - D20.\r\n        const predefined = {};\r\n        for (const key in SpotValue) {\r\n            const spotvalue = SpotValue[key];\r\n            predefined[key] = (diceCount) => {\r\n                return diceCount === undefined\r\n                    ? Math.floor(random() * spotvalue) + 1\r\n                    : Array.from({ length: diceCount }).map(() => Math.floor(random() * spotvalue) + 1);\r\n            };\r\n        }\r\n        function Die(spotvalue = 6, diceCount) {\r\n            return diceCount === undefined\r\n                ? Math.floor(random() * spotvalue) + 1\r\n                : Array.from({ length: diceCount }).map(() => Math.floor(random() * spotvalue) + 1);\r\n        }\r\n        return {\r\n            /**\r\n             * Similar to Die below, but with fixed spot values.\r\n             * Supports passing a diceCount\r\n             *    if not defined, defaults to 1 and returns the value directly.\r\n             *    if defined, returns an array containing the random dice values.\r\n             *\r\n             * D4: (diceCount) => value\r\n             * D6: (diceCount) => value\r\n             * D8: (diceCount) => value\r\n             * D10: (diceCount) => value\r\n             * D12: (diceCount) => value\r\n             * D20: (diceCount) => value\r\n             */\r\n            ...predefined,\r\n            /**\r\n             * Roll a die of specified spot value.\r\n             *\r\n             * @param {number} spotvalue - The die dimension (default: 6).\r\n             * @param {number} diceCount - number of dice to throw.\r\n             *                             if not defined, defaults to 1 and returns the value directly.\r\n             *                             if defined, returns an array containing the random dice values.\r\n             */\r\n            Die,\r\n            /**\r\n             * Generate a random number between 0 and 1.\r\n             */\r\n            Number: () => {\r\n                return random();\r\n            },\r\n            /**\r\n             * Shuffle an array.\r\n             *\r\n             * @param {Array} deck - The array to shuffle. Does not mutate\r\n             *                       the input, but returns the shuffled array.\r\n             */\r\n            Shuffle: (deck) => {\r\n                const clone = [...deck];\r\n                let sourceIndex = deck.length;\r\n                let destinationIndex = 0;\r\n                const shuffled = Array.from({ length: sourceIndex });\r\n                while (sourceIndex) {\r\n                    const randomIndex = Math.trunc(sourceIndex * random());\r\n                    shuffled[destinationIndex++] = clone[randomIndex];\r\n                    clone[randomIndex] = clone[--sourceIndex];\r\n                }\r\n                return shuffled;\r\n            },\r\n            _private: this,\r\n        };\r\n    }\r\n}\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nconst RandomPlugin = {\r\n    name: 'random',\r\n    noClient: ({ api }) => {\r\n        return api._private.isUsed();\r\n    },\r\n    flush: ({ api }) => {\r\n        return api._private.getState();\r\n    },\r\n    api: ({ data }) => {\r\n        const random = new Random(data);\r\n        return random.api();\r\n    },\r\n    setup: ({ game }) => {\r\n        let { seed } = game;\r\n        if (seed === undefined) {\r\n            seed = Random.seed();\r\n        }\r\n        return { seed };\r\n    },\r\n    playerView: () => undefined,\r\n};\n\nexport { RandomPlugin as R, alea as a };\n", "import produce from 'immer';\nimport { R as RandomPlugin } from './plugin-random-087f861e.js';\nimport isPlainObject from 'lodash.isplainobject';\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nconst MAKE_MOVE = 'MAKE_MOVE';\r\nconst GAME_EVENT = 'GAME_EVENT';\r\nconst REDO = 'REDO';\r\nconst RESET = 'RESET';\r\nconst SYNC = 'SYNC';\r\nconst UNDO = 'UNDO';\r\nconst UPDATE = 'UPDATE';\r\nconst PATCH = 'PATCH';\r\nconst PLUGIN = 'PLUGIN';\r\nconst STRIP_TRANSIENTS = 'STRIP_TRANSIENTS';\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Generate a move to be dispatched to the game move reducer.\r\n *\r\n * @param {string} type - The move type.\r\n * @param {Array}  args - Additional arguments.\r\n * @param {string}  playerID - The ID of the player making this action.\r\n * @param {string}  credentials - (optional) The credentials for the player making this action.\r\n */\r\nconst makeMove = (type, args, playerID, credentials) => ({\r\n    type: MAKE_MOVE,\r\n    payload: { type, args, playerID, credentials },\r\n});\r\n/**\r\n * Generate a game event to be dispatched to the flow reducer.\r\n *\r\n * @param {string} type - The event type.\r\n * @param {Array}  args - Additional arguments.\r\n * @param {string}  playerID - The ID of the player making this action.\r\n * @param {string}  credentials - (optional) The credentials for the player making this action.\r\n */\r\nconst gameEvent = (type, args, playerID, credentials) => ({\r\n    type: GAME_EVENT,\r\n    payload: { type, args, playerID, credentials },\r\n});\r\n/**\r\n * Generate an automatic game event that is a side-effect of a move.\r\n * @param {string} type - The event type.\r\n * @param {Array}  args - Additional arguments.\r\n * @param {string}  playerID - The ID of the player making this action.\r\n * @param {string}  credentials - (optional) The credentials for the player making this action.\r\n */\r\nconst automaticGameEvent = (type, args, playerID, credentials) => ({\r\n    type: GAME_EVENT,\r\n    payload: { type, args, playerID, credentials },\r\n    automatic: true,\r\n});\r\nconst sync = (info) => ({\r\n    type: SYNC,\r\n    state: info.state,\r\n    log: info.log,\r\n    initialState: info.initialState,\r\n    clientOnly: true,\r\n});\r\n/**\r\n * Used to update the Redux store's state with patch in response to\r\n * an action coming from another player.\r\n * @param prevStateID previous stateID\r\n * @param stateID stateID after this patch\r\n * @param {Operation[]} patch - The patch to apply.\r\n * @param {LogEntry[]} deltalog - A log delta.\r\n */\r\nconst patch = (prevStateID, stateID, patch, deltalog) => ({\r\n    type: PATCH,\r\n    prevStateID,\r\n    stateID,\r\n    patch,\r\n    deltalog,\r\n    clientOnly: true,\r\n});\r\n/**\r\n * Used to update the Redux store's state in response to\r\n * an action coming from another player.\r\n * @param {object} state - The state to restore.\r\n * @param {Array} deltalog - A log delta.\r\n */\r\nconst update = (state, deltalog) => ({\r\n    type: UPDATE,\r\n    state,\r\n    deltalog,\r\n    clientOnly: true,\r\n});\r\n/**\r\n * Used to reset the game state.\r\n * @param {object} state - The initial state.\r\n */\r\nconst reset = (state) => ({\r\n    type: RESET,\r\n    state,\r\n    clientOnly: true,\r\n});\r\n/**\r\n * Used to undo the last move.\r\n * @param {string}  playerID - The ID of the player making this action.\r\n * @param {string}  credentials - (optional) The credentials for the player making this action.\r\n */\r\nconst undo = (playerID, credentials) => ({\r\n    type: UNDO,\r\n    payload: { type: null, args: null, playerID, credentials },\r\n});\r\n/**\r\n * Used to redo the last undone move.\r\n * @param {string}  playerID - The ID of the player making this action.\r\n * @param {string}  credentials - (optional) The credentials for the player making this action.\r\n */\r\nconst redo = (playerID, credentials) => ({\r\n    type: REDO,\r\n    payload: { type: null, args: null, playerID, credentials },\r\n});\r\n/**\r\n * Allows plugins to define their own actions and intercept them.\r\n */\r\nconst plugin = (type, args, playerID, credentials) => ({\r\n    type: PLUGIN,\r\n    payload: { type, args, playerID, credentials },\r\n});\r\n/**\r\n * Private action used to strip transient metadata (e.g. errors) from the game\r\n * state.\r\n */\r\nconst stripTransients = () => ({\r\n    type: STRIP_TRANSIENTS,\r\n});\n\nvar ActionCreators = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  makeMove: makeMove,\n  gameEvent: gameEvent,\n  automaticGameEvent: automaticGameEvent,\n  sync: sync,\n  patch: patch,\n  update: update,\n  reset: reset,\n  undo: undo,\n  redo: redo,\n  plugin: plugin,\n  stripTransients: stripTransients\n});\n\n/**\r\n * Moves can return this when they want to indicate\r\n * that the combination of arguments is illegal and\r\n * the move ought to be discarded.\r\n */\r\nconst INVALID_MOVE = 'INVALID_MOVE';\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Plugin that allows using Immer to make immutable changes\r\n * to G by just mutating it.\r\n */\r\nconst ImmerPlugin = {\r\n    name: 'plugin-immer',\r\n    fnWrap: (move) => (context, ...args) => {\r\n        let isInvalid = false;\r\n        const newG = produce(context.G, (G) => {\r\n            const result = move({ ...context, G }, ...args);\r\n            if (result === INVALID_MOVE) {\r\n                isInvalid = true;\r\n                return;\r\n            }\r\n            return result;\r\n        });\r\n        if (isInvalid)\r\n            return INVALID_MOVE;\r\n        return newG;\r\n    },\r\n};\n\nvar GameMethod;\r\n(function (GameMethod) {\r\n    GameMethod[\"MOVE\"] = \"MOVE\";\r\n    GameMethod[\"GAME_ON_END\"] = \"GAME_ON_END\";\r\n    GameMethod[\"PHASE_ON_BEGIN\"] = \"PHASE_ON_BEGIN\";\r\n    GameMethod[\"PHASE_ON_END\"] = \"PHASE_ON_END\";\r\n    GameMethod[\"TURN_ON_BEGIN\"] = \"TURN_ON_BEGIN\";\r\n    GameMethod[\"TURN_ON_MOVE\"] = \"TURN_ON_MOVE\";\r\n    GameMethod[\"TURN_ON_END\"] = \"TURN_ON_END\";\r\n})(GameMethod || (GameMethod = {}));\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nvar Errors;\r\n(function (Errors) {\r\n    Errors[\"CalledOutsideHook\"] = \"Events must be called from moves or the `onBegin`, `onEnd`, and `onMove` hooks.\\nThis error probably means you called an event from other game code, like an `endIf` trigger or one of the `turn.order` methods.\";\r\n    Errors[\"EndTurnInOnEnd\"] = \"`endTurn` is disallowed in `onEnd` hooks \\u2014 the turn is already ending.\";\r\n    Errors[\"MaxTurnEndings\"] = \"Maximum number of turn endings exceeded for this update.\\nThis likely means game code is triggering an infinite loop.\";\r\n    Errors[\"PhaseEventInOnEnd\"] = \"`setPhase` & `endPhase` are disallowed in a phase\\u2019s `onEnd` hook \\u2014 the phase is already ending.\\nIf you\\u2019re trying to dynamically choose the next phase when a phase ends, use the phase\\u2019s `next` trigger.\";\r\n    Errors[\"StageEventInOnEnd\"] = \"`setStage`, `endStage` & `setActivePlayers` are disallowed in `onEnd` hooks.\";\r\n    Errors[\"StageEventInPhaseBegin\"] = \"`setStage`, `endStage` & `setActivePlayers` are disallowed in a phase\\u2019s `onBegin` hook.\\nUse `setActivePlayers` in a `turn.onBegin` hook or declare stages with `turn.activePlayers` instead.\";\r\n    Errors[\"StageEventInTurnBegin\"] = \"`setStage` & `endStage` are disallowed in `turn.onBegin`.\\nUse `setActivePlayers` or declare stages with `turn.activePlayers` instead.\";\r\n})(Errors || (Errors = {}));\r\n/**\r\n * Events\r\n */\r\nclass Events {\r\n    constructor(flow, ctx, playerID) {\r\n        this.flow = flow;\r\n        this.playerID = playerID;\r\n        this.dispatch = [];\r\n        this.initialTurn = ctx.turn;\r\n        this.updateTurnContext(ctx, undefined);\r\n        // This is an arbitrarily large upper threshold, which could be made\r\n        // configurable via a game option if the need arises.\r\n        this.maxEndedTurnsPerAction = ctx.numPlayers * 100;\r\n    }\r\n    api() {\r\n        const events = {\r\n            _private: this,\r\n        };\r\n        for (const type of this.flow.eventNames) {\r\n            events[type] = (...args) => {\r\n                this.dispatch.push({\r\n                    type,\r\n                    args,\r\n                    phase: this.currentPhase,\r\n                    turn: this.currentTurn,\r\n                    calledFrom: this.currentMethod,\r\n                    // Used to capture a stack trace in case it is needed later.\r\n                    error: new Error('Events Plugin Error'),\r\n                });\r\n            };\r\n        }\r\n        return events;\r\n    }\r\n    isUsed() {\r\n        return this.dispatch.length > 0;\r\n    }\r\n    updateTurnContext(ctx, methodType) {\r\n        this.currentPhase = ctx.phase;\r\n        this.currentTurn = ctx.turn;\r\n        this.currentMethod = methodType;\r\n    }\r\n    unsetCurrentMethod() {\r\n        this.currentMethod = undefined;\r\n    }\r\n    /**\r\n     * Updates ctx with the triggered events.\r\n     * @param {object} state - The state object { G, ctx }.\r\n     */\r\n    update(state) {\r\n        const initialState = state;\r\n        const stateWithError = ({ stack }, message) => ({\r\n            ...initialState,\r\n            plugins: {\r\n                ...initialState.plugins,\r\n                events: {\r\n                    ...initialState.plugins.events,\r\n                    data: { error: message + '\\n' + stack },\r\n                },\r\n            },\r\n        });\r\n        EventQueue: for (let i = 0; i < this.dispatch.length; i++) {\r\n            const event = this.dispatch[i];\r\n            const turnHasEnded = event.turn !== state.ctx.turn;\r\n            // This protects against potential infinite loops if specific events are called on hooks.\r\n            // The moment we exceed the defined threshold, we just bail out of all phases.\r\n            const endedTurns = this.currentTurn - this.initialTurn;\r\n            if (endedTurns >= this.maxEndedTurnsPerAction) {\r\n                return stateWithError(event.error, Errors.MaxTurnEndings);\r\n            }\r\n            if (event.calledFrom === undefined) {\r\n                return stateWithError(event.error, Errors.CalledOutsideHook);\r\n            }\r\n            // Stop processing events once the game has finished.\r\n            if (state.ctx.gameover)\r\n                break EventQueue;\r\n            switch (event.type) {\r\n                case 'endStage':\r\n                case 'setStage':\r\n                case 'setActivePlayers': {\r\n                    switch (event.calledFrom) {\r\n                        // Disallow all stage events in onEnd and phase.onBegin hooks.\r\n                        case GameMethod.TURN_ON_END:\r\n                        case GameMethod.PHASE_ON_END:\r\n                            return stateWithError(event.error, Errors.StageEventInOnEnd);\r\n                        case GameMethod.PHASE_ON_BEGIN:\r\n                            return stateWithError(event.error, Errors.StageEventInPhaseBegin);\r\n                        // Disallow setStage & endStage in turn.onBegin hooks.\r\n                        case GameMethod.TURN_ON_BEGIN:\r\n                            if (event.type === 'setActivePlayers')\r\n                                break;\r\n                            return stateWithError(event.error, Errors.StageEventInTurnBegin);\r\n                    }\r\n                    // If the turn already ended, don't try to process stage events.\r\n                    if (turnHasEnded)\r\n                        continue EventQueue;\r\n                    break;\r\n                }\r\n                case 'endTurn': {\r\n                    if (event.calledFrom === GameMethod.TURN_ON_END ||\r\n                        event.calledFrom === GameMethod.PHASE_ON_END) {\r\n                        return stateWithError(event.error, Errors.EndTurnInOnEnd);\r\n                    }\r\n                    // If the turn already ended some other way,\r\n                    // don't try to end the turn again.\r\n                    if (turnHasEnded)\r\n                        continue EventQueue;\r\n                    break;\r\n                }\r\n                case 'endPhase':\r\n                case 'setPhase': {\r\n                    if (event.calledFrom === GameMethod.PHASE_ON_END) {\r\n                        return stateWithError(event.error, Errors.PhaseEventInOnEnd);\r\n                    }\r\n                    // If the phase already ended some other way,\r\n                    // don't try to end the phase again.\r\n                    if (event.phase !== state.ctx.phase)\r\n                        continue EventQueue;\r\n                    break;\r\n                }\r\n            }\r\n            const action = automaticGameEvent(event.type, event.args, this.playerID);\r\n            state = this.flow.processEvent(state, action);\r\n        }\r\n        return state;\r\n    }\r\n}\n\n/*\r\n * Copyright 2020 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nconst EventsPlugin = {\r\n    name: 'events',\r\n    noClient: ({ api }) => api._private.isUsed(),\r\n    isInvalid: ({ data }) => data.error || false,\r\n    // Update the events plugin’s internal turn context each time a move\r\n    // or hook is called. This allows events called after turn or phase\r\n    // endings to dispatch the current turn and phase correctly.\r\n    fnWrap: (method, methodType) => (context, ...args) => {\r\n        const api = context.events;\r\n        if (api)\r\n            api._private.updateTurnContext(context.ctx, methodType);\r\n        const G = method(context, ...args);\r\n        if (api)\r\n            api._private.unsetCurrentMethod();\r\n        return G;\r\n    },\r\n    dangerouslyFlushRawState: ({ state, api }) => api._private.update(state),\r\n    api: ({ game, ctx, playerID }) => new Events(game.flow, ctx, playerID).api(),\r\n};\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Plugin that makes it possible to add metadata to log entries.\r\n * During a move, you can set metadata using ctx.log.setMetadata and it will be\r\n * available on the log entry for that move.\r\n */\r\nconst LogPlugin = {\r\n    name: 'log',\r\n    flush: () => ({}),\r\n    api: ({ data }) => {\r\n        return {\r\n            setMetadata: (metadata) => {\r\n                data.metadata = metadata;\r\n            },\r\n        };\r\n    },\r\n    setup: () => ({}),\r\n};\n\n/**\r\n * Check if a value can be serialized (e.g. using `JSON.stringify`).\r\n * Adapted from: https://stackoverflow.com/a/30712764/3829557\r\n */\r\nfunction isSerializable(value) {\r\n    // Primitives are OK.\r\n    if (value === undefined ||\r\n        value === null ||\r\n        typeof value === 'boolean' ||\r\n        typeof value === 'number' ||\r\n        typeof value === 'string') {\r\n        return true;\r\n    }\r\n    // A non-primitive value that is neither a POJO or an array cannot be serialized.\r\n    if (!isPlainObject(value) && !Array.isArray(value)) {\r\n        return false;\r\n    }\r\n    // Recurse entries if the value is an object or array.\r\n    for (const key in value) {\r\n        if (!isSerializable(value[key]))\r\n            return false;\r\n    }\r\n    return true;\r\n}\r\n/**\r\n * Plugin that checks whether state is serializable, in order to avoid\r\n * network serialization bugs.\r\n */\r\nconst SerializablePlugin = {\r\n    name: 'plugin-serializable',\r\n    fnWrap: (move) => (context, ...args) => {\r\n        const result = move(context, ...args);\r\n        // Check state in non-production environments.\r\n        if (process.env.NODE_ENV !== 'production' && !isSerializable(result)) {\r\n            throw new Error('Move state is not JSON-serialiazable.\\n' +\r\n                'See https://boardgame.io/documentation/#/?id=state for more information.');\r\n        }\r\n        return result;\r\n    },\r\n};\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nconst production = process.env.NODE_ENV === 'production';\r\nconst logfn = production ? () => { } : (...msg) => console.log(...msg);\r\nconst errorfn = (...msg) => console.error(...msg);\r\nfunction info(msg) {\r\n    logfn(`INFO: ${msg}`);\r\n}\r\nfunction error(error) {\r\n    errorfn('ERROR:', error);\r\n}\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * List of plugins that are always added.\r\n */\r\nconst CORE_PLUGINS = [ImmerPlugin, RandomPlugin, LogPlugin, SerializablePlugin];\r\nconst DEFAULT_PLUGINS = [...CORE_PLUGINS, EventsPlugin];\r\n/**\r\n * Allow plugins to intercept actions and process them.\r\n */\r\nconst ProcessAction = (state, action, opts) => {\r\n    // TODO(#723): Extend error handling to plugins.\r\n    opts.game.plugins\r\n        .filter((plugin) => plugin.action !== undefined)\r\n        .filter((plugin) => plugin.name === action.payload.type)\r\n        .forEach((plugin) => {\r\n        const name = plugin.name;\r\n        const pluginState = state.plugins[name] || { data: {} };\r\n        const data = plugin.action(pluginState.data, action.payload);\r\n        state = {\r\n            ...state,\r\n            plugins: {\r\n                ...state.plugins,\r\n                [name]: { ...pluginState, data },\r\n            },\r\n        };\r\n    });\r\n    return state;\r\n};\r\n/**\r\n * The APIs created by various plugins are stored in the plugins\r\n * section of the state object:\r\n *\r\n * {\r\n *   G: {},\r\n *   ctx: {},\r\n *   plugins: {\r\n *     plugin-a: {\r\n *       data: {},  // this is generated by the plugin at Setup / Flush.\r\n *       api: {},   // this is ephemeral and generated by Enhance.\r\n *     }\r\n *   }\r\n * }\r\n *\r\n * This function retrieves plugin APIs and returns them as an object\r\n * for consumption as used by move contexts.\r\n */\r\nconst GetAPIs = ({ plugins }) => Object.entries(plugins || {}).reduce((apis, [name, { api }]) => {\r\n    apis[name] = api;\r\n    return apis;\r\n}, {});\r\n/**\r\n * Applies the provided plugins to the given move / flow function.\r\n *\r\n * @param methodToWrap - The move function or hook to apply the plugins to.\r\n * @param methodType - The type of the move or hook being wrapped.\r\n * @param plugins - The list of plugins.\r\n */\r\nconst FnWrap = (methodToWrap, methodType, plugins) => {\r\n    return [...CORE_PLUGINS, ...plugins, EventsPlugin]\r\n        .filter((plugin) => plugin.fnWrap !== undefined)\r\n        .reduce((method, { fnWrap }) => fnWrap(method, methodType), methodToWrap);\r\n};\r\n/**\r\n * Allows the plugin to generate its initial state.\r\n */\r\nconst Setup = (state, opts) => {\r\n    [...DEFAULT_PLUGINS, ...opts.game.plugins]\r\n        .filter((plugin) => plugin.setup !== undefined)\r\n        .forEach((plugin) => {\r\n        const name = plugin.name;\r\n        const data = plugin.setup({\r\n            G: state.G,\r\n            ctx: state.ctx,\r\n            game: opts.game,\r\n        });\r\n        state = {\r\n            ...state,\r\n            plugins: {\r\n                ...state.plugins,\r\n                [name]: { data },\r\n            },\r\n        };\r\n    });\r\n    return state;\r\n};\r\n/**\r\n * Invokes the plugin before a move or event.\r\n * The API that the plugin generates is stored inside\r\n * the `plugins` section of the state (which is subsequently\r\n * merged into ctx).\r\n */\r\nconst Enhance = (state, opts) => {\r\n    [...DEFAULT_PLUGINS, ...opts.game.plugins]\r\n        .filter((plugin) => plugin.api !== undefined)\r\n        .forEach((plugin) => {\r\n        const name = plugin.name;\r\n        const pluginState = state.plugins[name] || { data: {} };\r\n        const api = plugin.api({\r\n            G: state.G,\r\n            ctx: state.ctx,\r\n            data: pluginState.data,\r\n            game: opts.game,\r\n            playerID: opts.playerID,\r\n        });\r\n        state = {\r\n            ...state,\r\n            plugins: {\r\n                ...state.plugins,\r\n                [name]: { ...pluginState, api },\r\n            },\r\n        };\r\n    });\r\n    return state;\r\n};\r\n/**\r\n * Allows plugins to update their state after a move / event.\r\n */\r\nconst Flush = (state, opts) => {\r\n    // We flush the events plugin first, then custom plugins and the core plugins.\r\n    // This means custom plugins cannot use the events API but will be available in event hooks.\r\n    // Note that plugins are flushed in reverse, to allow custom plugins calling each other.\r\n    [...CORE_PLUGINS, ...opts.game.plugins, EventsPlugin]\r\n        .reverse()\r\n        .forEach((plugin) => {\r\n        const name = plugin.name;\r\n        const pluginState = state.plugins[name] || { data: {} };\r\n        if (plugin.flush) {\r\n            const newData = plugin.flush({\r\n                G: state.G,\r\n                ctx: state.ctx,\r\n                game: opts.game,\r\n                api: pluginState.api,\r\n                data: pluginState.data,\r\n            });\r\n            state = {\r\n                ...state,\r\n                plugins: {\r\n                    ...state.plugins,\r\n                    [plugin.name]: { data: newData },\r\n                },\r\n            };\r\n        }\r\n        else if (plugin.dangerouslyFlushRawState) {\r\n            state = plugin.dangerouslyFlushRawState({\r\n                state,\r\n                game: opts.game,\r\n                api: pluginState.api,\r\n                data: pluginState.data,\r\n            });\r\n            // Remove everything other than data.\r\n            const data = state.plugins[name].data;\r\n            state = {\r\n                ...state,\r\n                plugins: {\r\n                    ...state.plugins,\r\n                    [plugin.name]: { data },\r\n                },\r\n            };\r\n        }\r\n    });\r\n    return state;\r\n};\r\n/**\r\n * Allows plugins to indicate if they should not be materialized on the client.\r\n * This will cause the client to discard the state update and wait for the\r\n * master instead.\r\n */\r\nconst NoClient = (state, opts) => {\r\n    return [...DEFAULT_PLUGINS, ...opts.game.plugins]\r\n        .filter((plugin) => plugin.noClient !== undefined)\r\n        .map((plugin) => {\r\n        const name = plugin.name;\r\n        const pluginState = state.plugins[name];\r\n        if (pluginState) {\r\n            return plugin.noClient({\r\n                G: state.G,\r\n                ctx: state.ctx,\r\n                game: opts.game,\r\n                api: pluginState.api,\r\n                data: pluginState.data,\r\n            });\r\n        }\r\n        return false;\r\n    })\r\n        .includes(true);\r\n};\r\n/**\r\n * Allows plugins to indicate if the entire action should be thrown out\r\n * as invalid. This will cancel the entire state update.\r\n */\r\nconst IsInvalid = (state, opts) => {\r\n    const firstInvalidReturn = [...DEFAULT_PLUGINS, ...opts.game.plugins]\r\n        .filter((plugin) => plugin.isInvalid !== undefined)\r\n        .map((plugin) => {\r\n        const { name } = plugin;\r\n        const pluginState = state.plugins[name];\r\n        const message = plugin.isInvalid({\r\n            G: state.G,\r\n            ctx: state.ctx,\r\n            game: opts.game,\r\n            data: pluginState && pluginState.data,\r\n        });\r\n        return message ? { plugin: name, message } : false;\r\n    })\r\n        .find((value) => value);\r\n    return firstInvalidReturn || false;\r\n};\r\n/**\r\n * Update plugin state after move/event & check if plugins consider the update to be valid.\r\n * @returns Tuple of `[updatedState]` or `[originalState, invalidError]`.\r\n */\r\nconst FlushAndValidate = (state, opts) => {\r\n    const updatedState = Flush(state, opts);\r\n    const isInvalid = IsInvalid(updatedState, opts);\r\n    if (!isInvalid)\r\n        return [updatedState];\r\n    const { plugin, message } = isInvalid;\r\n    error(`${plugin} plugin declared action invalid:\\n${message}`);\r\n    return [state, isInvalid];\r\n};\r\n/**\r\n * Allows plugins to customize their data for specific players.\r\n * For example, a plugin may want to share no data with the client, or\r\n * want to keep some player data secret from opponents.\r\n */\r\nconst PlayerView = ({ G, ctx, plugins = {} }, { game, playerID }) => {\r\n    [...DEFAULT_PLUGINS, ...game.plugins].forEach(({ name, playerView }) => {\r\n        if (!playerView)\r\n            return;\r\n        const { data } = plugins[name] || { data: {} };\r\n        const newData = playerView({ G, ctx, game, data, playerID });\r\n        plugins = {\r\n            ...plugins,\r\n            [name]: { data: newData },\r\n        };\r\n    });\r\n    return plugins;\r\n};\n\n/**\r\n * Adjust the given options to use the new minMoves/maxMoves if a legacy moveLimit was given\r\n * @param options The options object to apply backwards compatibility to\r\n * @param enforceMinMoves Use moveLimit to set both minMoves and maxMoves\r\n */\r\nfunction supportDeprecatedMoveLimit(options, enforceMinMoves = false) {\r\n    if (options.moveLimit) {\r\n        if (enforceMinMoves) {\r\n            options.minMoves = options.moveLimit;\r\n        }\r\n        options.maxMoves = options.moveLimit;\r\n        delete options.moveLimit;\r\n    }\r\n}\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nfunction SetActivePlayers(ctx, arg) {\r\n    let activePlayers = {};\r\n    let _prevActivePlayers = [];\r\n    let _nextActivePlayers = null;\r\n    let _activePlayersMinMoves = {};\r\n    let _activePlayersMaxMoves = {};\r\n    if (Array.isArray(arg)) {\r\n        // support a simple array of player IDs as active players\r\n        const value = {};\r\n        arg.forEach((v) => (value[v] = Stage.NULL));\r\n        activePlayers = value;\r\n    }\r\n    else {\r\n        // process active players argument object\r\n        // stages previously did not enforce minMoves, this behaviour is kept intentionally\r\n        supportDeprecatedMoveLimit(arg);\r\n        if (arg.next) {\r\n            _nextActivePlayers = arg.next;\r\n        }\r\n        if (arg.revert) {\r\n            _prevActivePlayers = [\r\n                ...ctx._prevActivePlayers,\r\n                {\r\n                    activePlayers: ctx.activePlayers,\r\n                    _activePlayersMinMoves: ctx._activePlayersMinMoves,\r\n                    _activePlayersMaxMoves: ctx._activePlayersMaxMoves,\r\n                    _activePlayersNumMoves: ctx._activePlayersNumMoves,\r\n                },\r\n            ];\r\n        }\r\n        if (arg.currentPlayer !== undefined) {\r\n            ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, ctx.currentPlayer, arg.currentPlayer);\r\n        }\r\n        if (arg.others !== undefined) {\r\n            for (let i = 0; i < ctx.playOrder.length; i++) {\r\n                const id = ctx.playOrder[i];\r\n                if (id !== ctx.currentPlayer) {\r\n                    ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, id, arg.others);\r\n                }\r\n            }\r\n        }\r\n        if (arg.all !== undefined) {\r\n            for (let i = 0; i < ctx.playOrder.length; i++) {\r\n                const id = ctx.playOrder[i];\r\n                ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, id, arg.all);\r\n            }\r\n        }\r\n        if (arg.value) {\r\n            for (const id in arg.value) {\r\n                ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, id, arg.value[id]);\r\n            }\r\n        }\r\n        if (arg.minMoves) {\r\n            for (const id in activePlayers) {\r\n                if (_activePlayersMinMoves[id] === undefined) {\r\n                    _activePlayersMinMoves[id] = arg.minMoves;\r\n                }\r\n            }\r\n        }\r\n        if (arg.maxMoves) {\r\n            for (const id in activePlayers) {\r\n                if (_activePlayersMaxMoves[id] === undefined) {\r\n                    _activePlayersMaxMoves[id] = arg.maxMoves;\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (Object.keys(activePlayers).length === 0) {\r\n        activePlayers = null;\r\n    }\r\n    if (Object.keys(_activePlayersMinMoves).length === 0) {\r\n        _activePlayersMinMoves = null;\r\n    }\r\n    if (Object.keys(_activePlayersMaxMoves).length === 0) {\r\n        _activePlayersMaxMoves = null;\r\n    }\r\n    const _activePlayersNumMoves = {};\r\n    for (const id in activePlayers) {\r\n        _activePlayersNumMoves[id] = 0;\r\n    }\r\n    return {\r\n        ...ctx,\r\n        activePlayers,\r\n        _activePlayersMinMoves,\r\n        _activePlayersMaxMoves,\r\n        _activePlayersNumMoves,\r\n        _prevActivePlayers,\r\n        _nextActivePlayers,\r\n    };\r\n}\r\n/**\r\n * Update activePlayers, setting it to previous, next or null values\r\n * when it becomes empty.\r\n * @param ctx\r\n */\r\nfunction UpdateActivePlayersOnceEmpty(ctx) {\r\n    let { activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, _activePlayersNumMoves, _prevActivePlayers, _nextActivePlayers, } = ctx;\r\n    if (activePlayers && Object.keys(activePlayers).length === 0) {\r\n        if (_nextActivePlayers) {\r\n            ctx = SetActivePlayers(ctx, _nextActivePlayers);\r\n            ({\r\n                activePlayers,\r\n                _activePlayersMinMoves,\r\n                _activePlayersMaxMoves,\r\n                _activePlayersNumMoves,\r\n                _prevActivePlayers,\r\n            } = ctx);\r\n        }\r\n        else if (_prevActivePlayers.length > 0) {\r\n            const lastIndex = _prevActivePlayers.length - 1;\r\n            ({\r\n                activePlayers,\r\n                _activePlayersMinMoves,\r\n                _activePlayersMaxMoves,\r\n                _activePlayersNumMoves,\r\n            } = _prevActivePlayers[lastIndex]);\r\n            _prevActivePlayers = _prevActivePlayers.slice(0, lastIndex);\r\n        }\r\n        else {\r\n            activePlayers = null;\r\n            _activePlayersMinMoves = null;\r\n            _activePlayersMaxMoves = null;\r\n        }\r\n    }\r\n    return {\r\n        ...ctx,\r\n        activePlayers,\r\n        _activePlayersMinMoves,\r\n        _activePlayersMaxMoves,\r\n        _activePlayersNumMoves,\r\n        _prevActivePlayers,\r\n    };\r\n}\r\n/**\r\n * Apply an active player argument to the given player ID\r\n * @param {Object} activePlayers\r\n * @param {Object} _activePlayersMinMoves\r\n * @param {Object} _activePlayersMaxMoves\r\n * @param {String} playerID The player to apply the parameter to\r\n * @param {(String|Object)} arg An active player argument\r\n */\r\nfunction ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, playerID, arg) {\r\n    if (typeof arg !== 'object' || arg === Stage.NULL) {\r\n        arg = { stage: arg };\r\n    }\r\n    if (arg.stage !== undefined) {\r\n        // stages previously did not enforce minMoves, this behaviour is kept intentionally\r\n        supportDeprecatedMoveLimit(arg);\r\n        activePlayers[playerID] = arg.stage;\r\n        if (arg.minMoves)\r\n            _activePlayersMinMoves[playerID] = arg.minMoves;\r\n        if (arg.maxMoves)\r\n            _activePlayersMaxMoves[playerID] = arg.maxMoves;\r\n    }\r\n}\r\n/**\r\n * Converts a playOrderPos index into its value in playOrder.\r\n * @param {Array} playOrder - An array of player ID's.\r\n * @param {number} playOrderPos - An index into the above.\r\n */\r\nfunction getCurrentPlayer(playOrder, playOrderPos) {\r\n    // convert to string in case playOrder is set to number[]\r\n    return playOrder[playOrderPos] + '';\r\n}\r\n/**\r\n * Called at the start of a turn to initialize turn order state.\r\n *\r\n * TODO: This is called inside StartTurn, which is called from\r\n * both UpdateTurn and StartPhase (so it's called at the beginning\r\n * of a new phase as well as between turns). We should probably\r\n * split it into two.\r\n */\r\nfunction InitTurnOrderState(state, turn) {\r\n    let { G, ctx } = state;\r\n    const { numPlayers } = ctx;\r\n    const pluginAPIs = GetAPIs(state);\r\n    const context = { ...pluginAPIs, G, ctx };\r\n    const order = turn.order;\r\n    let playOrder = [...Array.from({ length: numPlayers })].map((_, i) => i + '');\r\n    if (order.playOrder !== undefined) {\r\n        playOrder = order.playOrder(context);\r\n    }\r\n    const playOrderPos = order.first(context);\r\n    const posType = typeof playOrderPos;\r\n    if (posType !== 'number') {\r\n        error(`invalid value returned by turn.order.first — expected number got ${posType} “${playOrderPos}”.`);\r\n    }\r\n    const currentPlayer = getCurrentPlayer(playOrder, playOrderPos);\r\n    ctx = { ...ctx, currentPlayer, playOrderPos, playOrder };\r\n    ctx = SetActivePlayers(ctx, turn.activePlayers || {});\r\n    return ctx;\r\n}\r\n/**\r\n * Called at the end of each turn to update the turn order state.\r\n * @param {object} G - The game object G.\r\n * @param {object} ctx - The game object ctx.\r\n * @param {object} turn - A turn object for this phase.\r\n * @param {string} endTurnArg - An optional argument to endTurn that\r\n                                may specify the next player.\r\n */\r\nfunction UpdateTurnOrderState(state, currentPlayer, turn, endTurnArg) {\r\n    const order = turn.order;\r\n    let { G, ctx } = state;\r\n    let playOrderPos = ctx.playOrderPos;\r\n    let endPhase = false;\r\n    if (endTurnArg && endTurnArg !== true) {\r\n        if (typeof endTurnArg !== 'object') {\r\n            error(`invalid argument to endTurn: ${endTurnArg}`);\r\n        }\r\n        Object.keys(endTurnArg).forEach((arg) => {\r\n            switch (arg) {\r\n                case 'remove':\r\n                    currentPlayer = getCurrentPlayer(ctx.playOrder, playOrderPos);\r\n                    break;\r\n                case 'next':\r\n                    playOrderPos = ctx.playOrder.indexOf(endTurnArg.next);\r\n                    currentPlayer = endTurnArg.next;\r\n                    break;\r\n                default:\r\n                    error(`invalid argument to endTurn: ${arg}`);\r\n            }\r\n        });\r\n    }\r\n    else {\r\n        const pluginAPIs = GetAPIs(state);\r\n        const context = { ...pluginAPIs, G, ctx };\r\n        const t = order.next(context);\r\n        const type = typeof t;\r\n        if (t !== undefined && type !== 'number') {\r\n            error(`invalid value returned by turn.order.next — expected number or undefined got ${type} “${t}”.`);\r\n        }\r\n        if (t === undefined) {\r\n            endPhase = true;\r\n        }\r\n        else {\r\n            playOrderPos = t;\r\n            currentPlayer = getCurrentPlayer(ctx.playOrder, playOrderPos);\r\n        }\r\n    }\r\n    ctx = {\r\n        ...ctx,\r\n        playOrderPos,\r\n        currentPlayer,\r\n    };\r\n    return { endPhase, ctx };\r\n}\r\n/**\r\n * Set of different turn orders possible in a phase.\r\n * These are meant to be passed to the `turn` setting\r\n * in the flow objects.\r\n *\r\n * Each object defines the first player when the phase / game\r\n * begins, and also a function `next` to determine who the\r\n * next player is when the turn ends.\r\n *\r\n * The phase ends if next() returns undefined.\r\n */\r\nconst TurnOrder = {\r\n    /**\r\n     * DEFAULT\r\n     *\r\n     * The default round-robin turn order.\r\n     */\r\n    DEFAULT: {\r\n        first: ({ ctx }) => ctx.turn === 0\r\n            ? ctx.playOrderPos\r\n            : (ctx.playOrderPos + 1) % ctx.playOrder.length,\r\n        next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length,\r\n    },\r\n    /**\r\n     * RESET\r\n     *\r\n     * Similar to DEFAULT, but starts from 0 each time.\r\n     */\r\n    RESET: {\r\n        first: () => 0,\r\n        next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length,\r\n    },\r\n    /**\r\n     * CONTINUE\r\n     *\r\n     * Similar to DEFAULT, but starts with the player who ended the last phase.\r\n     */\r\n    CONTINUE: {\r\n        first: ({ ctx }) => ctx.playOrderPos,\r\n        next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length,\r\n    },\r\n    /**\r\n     * ONCE\r\n     *\r\n     * Another round-robin turn order, but goes around just once.\r\n     * The phase ends after all players have played.\r\n     */\r\n    ONCE: {\r\n        first: () => 0,\r\n        next: ({ ctx }) => {\r\n            if (ctx.playOrderPos < ctx.playOrder.length - 1) {\r\n                return ctx.playOrderPos + 1;\r\n            }\r\n        },\r\n    },\r\n    /**\r\n     * CUSTOM\r\n     *\r\n     * Identical to DEFAULT, but also sets playOrder at the\r\n     * beginning of the phase.\r\n     *\r\n     * @param {Array} playOrder - The play order.\r\n     */\r\n    CUSTOM: (playOrder) => ({\r\n        playOrder: () => playOrder,\r\n        first: () => 0,\r\n        next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length,\r\n    }),\r\n    /**\r\n     * CUSTOM_FROM\r\n     *\r\n     * Identical to DEFAULT, but also sets playOrder at the\r\n     * beginning of the phase to a value specified by a field\r\n     * in G.\r\n     *\r\n     * @param {string} playOrderField - Field in G.\r\n     */\r\n    CUSTOM_FROM: (playOrderField) => ({\r\n        playOrder: ({ G }) => G[playOrderField],\r\n        first: () => 0,\r\n        next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length,\r\n    }),\r\n};\r\nconst Stage = {\r\n    NULL: null,\r\n};\r\nconst ActivePlayers = {\r\n    /**\r\n     * ALL\r\n     *\r\n     * The turn stays with one player, but any player can play (in any order)\r\n     * until the phase ends.\r\n     */\r\n    ALL: { all: Stage.NULL },\r\n    /**\r\n     * ALL_ONCE\r\n     *\r\n     * The turn stays with one player, but any player can play (once, and in any order).\r\n     * This is typically used in a phase where you want to elicit a response\r\n     * from every player in the game.\r\n     */\r\n    ALL_ONCE: { all: Stage.NULL, minMoves: 1, maxMoves: 1 },\r\n    /**\r\n     * OTHERS\r\n     *\r\n     * The turn stays with one player, and every *other* player can play (in any order)\r\n     * until the phase ends.\r\n     */\r\n    OTHERS: { others: Stage.NULL },\r\n    /**\r\n     * OTHERS_ONCE\r\n     *\r\n     * The turn stays with one player, and every *other* player can play (once, and in any order).\r\n     * This is typically used in a phase where you want to elicit a response\r\n     * from every *other* player in the game.\r\n     */\r\n    OTHERS_ONCE: { others: Stage.NULL, minMoves: 1, maxMoves: 1 },\r\n};\n\nexport { ActionCreators as A, makeMove as B, ActivePlayers as C, Enhance as E, FnWrap as F, GameMethod as G, InitTurnOrderState as I, MAKE_MOVE as M, NoClient as N, PATCH as P, REDO as R, Stage as S, TurnOrder as T, UpdateTurnOrderState as U, GetAPIs as a, supportDeprecatedMoveLimit as b, SetActivePlayers as c, UpdateActivePlayersOnceEmpty as d, error as e, PLUGIN as f, gameEvent as g, ProcessAction as h, info as i, UNDO as j, SYNC as k, UPDATE as l, RESET as m, INVALID_MOVE as n, GAME_EVENT as o, STRIP_TRANSIENTS as p, FlushAndValidate as q, stripTransients as r, sync as s, Setup as t, reset as u, undo as v, redo as w, PlayerView as x, patch as y, update as z };\n", "import { e as error, G as GameMethod, a as GetAPIs, T as TurnOrder, b as supportDeprecatedMoveLimit, S as Stage, c as SetActivePlayers, i as info, F as FnWrap, I as InitTurnOrderState, U as UpdateTurnOrderState, d as UpdateActivePlayersOnceEmpty, g as gameEvent, P as PATCH, f as PLUGIN, h as ProcessAction, R as REDO, j as UNDO, k as SYNC, l as UPDATE, m as RESET, M as MAKE_MOVE, E as Enhance, n as INVALID_MOVE, N as NoClient, o as GAME_EVENT, p as STRIP_TRANSIENTS, q as FlushAndValidate, r as stripTransients } from './turn-order-8cc4909b.js';\nimport { applyPatch } from 'rfc6902';\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Flow\r\n *\r\n * Creates a reducer that updates ctx (analogous to how moves update G).\r\n */\r\nfunction Flow({ moves, phases, endIf, onEnd, turn, events, plugins, }) {\r\n    // Attach defaults.\r\n    if (moves === undefined) {\r\n        moves = {};\r\n    }\r\n    if (events === undefined) {\r\n        events = {};\r\n    }\r\n    if (plugins === undefined) {\r\n        plugins = [];\r\n    }\r\n    if (phases === undefined) {\r\n        phases = {};\r\n    }\r\n    if (!endIf)\r\n        endIf = () => undefined;\r\n    if (!onEnd)\r\n        onEnd = ({ G }) => G;\r\n    if (!turn)\r\n        turn = {};\r\n    const phaseMap = { ...phases };\r\n    if ('' in phaseMap) {\r\n        error('cannot specify phase with empty name');\r\n    }\r\n    phaseMap[''] = {};\r\n    const moveMap = {};\r\n    const moveNames = new Set();\r\n    let startingPhase = null;\r\n    Object.keys(moves).forEach((name) => moveNames.add(name));\r\n    const HookWrapper = (hook, hookType) => {\r\n        const withPlugins = FnWrap(hook, hookType, plugins);\r\n        return (state) => {\r\n            const pluginAPIs = GetAPIs(state);\r\n            return withPlugins({\r\n                ...pluginAPIs,\r\n                G: state.G,\r\n                ctx: state.ctx,\r\n                playerID: state.playerID,\r\n            });\r\n        };\r\n    };\r\n    const TriggerWrapper = (trigger) => {\r\n        return (state) => {\r\n            const pluginAPIs = GetAPIs(state);\r\n            return trigger({\r\n                ...pluginAPIs,\r\n                G: state.G,\r\n                ctx: state.ctx,\r\n            });\r\n        };\r\n    };\r\n    const wrapped = {\r\n        onEnd: HookWrapper(onEnd, GameMethod.GAME_ON_END),\r\n        endIf: TriggerWrapper(endIf),\r\n    };\r\n    for (const phase in phaseMap) {\r\n        const phaseConfig = phaseMap[phase];\r\n        if (phaseConfig.start === true) {\r\n            startingPhase = phase;\r\n        }\r\n        if (phaseConfig.moves !== undefined) {\r\n            for (const move of Object.keys(phaseConfig.moves)) {\r\n                moveMap[phase + '.' + move] = phaseConfig.moves[move];\r\n                moveNames.add(move);\r\n            }\r\n        }\r\n        if (phaseConfig.endIf === undefined) {\r\n            phaseConfig.endIf = () => undefined;\r\n        }\r\n        if (phaseConfig.onBegin === undefined) {\r\n            phaseConfig.onBegin = ({ G }) => G;\r\n        }\r\n        if (phaseConfig.onEnd === undefined) {\r\n            phaseConfig.onEnd = ({ G }) => G;\r\n        }\r\n        if (phaseConfig.turn === undefined) {\r\n            phaseConfig.turn = turn;\r\n        }\r\n        if (phaseConfig.turn.order === undefined) {\r\n            phaseConfig.turn.order = TurnOrder.DEFAULT;\r\n        }\r\n        if (phaseConfig.turn.onBegin === undefined) {\r\n            phaseConfig.turn.onBegin = ({ G }) => G;\r\n        }\r\n        if (phaseConfig.turn.onEnd === undefined) {\r\n            phaseConfig.turn.onEnd = ({ G }) => G;\r\n        }\r\n        if (phaseConfig.turn.endIf === undefined) {\r\n            phaseConfig.turn.endIf = () => false;\r\n        }\r\n        if (phaseConfig.turn.onMove === undefined) {\r\n            phaseConfig.turn.onMove = ({ G }) => G;\r\n        }\r\n        if (phaseConfig.turn.stages === undefined) {\r\n            phaseConfig.turn.stages = {};\r\n        }\r\n        // turns previously treated moveLimit as both minMoves and maxMoves, this behaviour is kept intentionally\r\n        supportDeprecatedMoveLimit(phaseConfig.turn, true);\r\n        for (const stage in phaseConfig.turn.stages) {\r\n            const stageConfig = phaseConfig.turn.stages[stage];\r\n            const moves = stageConfig.moves || {};\r\n            for (const move of Object.keys(moves)) {\r\n                const key = phase + '.' + stage + '.' + move;\r\n                moveMap[key] = moves[move];\r\n                moveNames.add(move);\r\n            }\r\n        }\r\n        phaseConfig.wrapped = {\r\n            onBegin: HookWrapper(phaseConfig.onBegin, GameMethod.PHASE_ON_BEGIN),\r\n            onEnd: HookWrapper(phaseConfig.onEnd, GameMethod.PHASE_ON_END),\r\n            endIf: TriggerWrapper(phaseConfig.endIf),\r\n        };\r\n        phaseConfig.turn.wrapped = {\r\n            onMove: HookWrapper(phaseConfig.turn.onMove, GameMethod.TURN_ON_MOVE),\r\n            onBegin: HookWrapper(phaseConfig.turn.onBegin, GameMethod.TURN_ON_BEGIN),\r\n            onEnd: HookWrapper(phaseConfig.turn.onEnd, GameMethod.TURN_ON_END),\r\n            endIf: TriggerWrapper(phaseConfig.turn.endIf),\r\n        };\r\n        if (typeof phaseConfig.next !== 'function') {\r\n            const { next } = phaseConfig;\r\n            phaseConfig.next = () => next || null;\r\n        }\r\n        phaseConfig.wrapped.next = TriggerWrapper(phaseConfig.next);\r\n    }\r\n    function GetPhase(ctx) {\r\n        return ctx.phase ? phaseMap[ctx.phase] : phaseMap[''];\r\n    }\r\n    function OnMove(state) {\r\n        return state;\r\n    }\r\n    function Process(state, events) {\r\n        const phasesEnded = new Set();\r\n        const turnsEnded = new Set();\r\n        for (let i = 0; i < events.length; i++) {\r\n            const { fn, arg, ...rest } = events[i];\r\n            // Detect a loop of EndPhase calls.\r\n            // This could potentially even be an infinite loop\r\n            // if the endIf condition of each phase blindly\r\n            // returns true. The moment we detect a single\r\n            // loop, we just bail out of all phases.\r\n            if (fn === EndPhase) {\r\n                turnsEnded.clear();\r\n                const phase = state.ctx.phase;\r\n                if (phasesEnded.has(phase)) {\r\n                    const ctx = { ...state.ctx, phase: null };\r\n                    return { ...state, ctx };\r\n                }\r\n                phasesEnded.add(phase);\r\n            }\r\n            // Process event.\r\n            const next = [];\r\n            state = fn(state, {\r\n                ...rest,\r\n                arg,\r\n                next,\r\n            });\r\n            if (fn === EndGame) {\r\n                break;\r\n            }\r\n            // Check if we should end the game.\r\n            const shouldEndGame = ShouldEndGame(state);\r\n            if (shouldEndGame) {\r\n                events.push({\r\n                    fn: EndGame,\r\n                    arg: shouldEndGame,\r\n                    turn: state.ctx.turn,\r\n                    phase: state.ctx.phase,\r\n                    automatic: true,\r\n                });\r\n                continue;\r\n            }\r\n            // Check if we should end the phase.\r\n            const shouldEndPhase = ShouldEndPhase(state);\r\n            if (shouldEndPhase) {\r\n                events.push({\r\n                    fn: EndPhase,\r\n                    arg: shouldEndPhase,\r\n                    turn: state.ctx.turn,\r\n                    phase: state.ctx.phase,\r\n                    automatic: true,\r\n                });\r\n                continue;\r\n            }\r\n            // Check if we should end the turn.\r\n            if ([OnMove, UpdateStage, UpdateActivePlayers].includes(fn)) {\r\n                const shouldEndTurn = ShouldEndTurn(state);\r\n                if (shouldEndTurn) {\r\n                    events.push({\r\n                        fn: EndTurn,\r\n                        arg: shouldEndTurn,\r\n                        turn: state.ctx.turn,\r\n                        phase: state.ctx.phase,\r\n                        automatic: true,\r\n                    });\r\n                    continue;\r\n                }\r\n            }\r\n            events.push(...next);\r\n        }\r\n        return state;\r\n    }\r\n    ///////////\r\n    // Start //\r\n    ///////////\r\n    function StartGame(state, { next }) {\r\n        next.push({ fn: StartPhase });\r\n        return state;\r\n    }\r\n    function StartPhase(state, { next }) {\r\n        let { G, ctx } = state;\r\n        const phaseConfig = GetPhase(ctx);\r\n        // Run any phase setup code provided by the user.\r\n        G = phaseConfig.wrapped.onBegin(state);\r\n        next.push({ fn: StartTurn });\r\n        return { ...state, G, ctx };\r\n    }\r\n    function StartTurn(state, { currentPlayer }) {\r\n        let { ctx } = state;\r\n        const phaseConfig = GetPhase(ctx);\r\n        // Initialize the turn order state.\r\n        if (currentPlayer) {\r\n            ctx = { ...ctx, currentPlayer };\r\n            if (phaseConfig.turn.activePlayers) {\r\n                ctx = SetActivePlayers(ctx, phaseConfig.turn.activePlayers);\r\n            }\r\n        }\r\n        else {\r\n            // This is only called at the beginning of the phase\r\n            // when there is no currentPlayer yet.\r\n            ctx = InitTurnOrderState(state, phaseConfig.turn);\r\n        }\r\n        const turn = ctx.turn + 1;\r\n        ctx = { ...ctx, turn, numMoves: 0, _prevActivePlayers: [] };\r\n        const G = phaseConfig.turn.wrapped.onBegin({ ...state, ctx });\r\n        return { ...state, G, ctx, _undo: [], _redo: [] };\r\n    }\r\n    ////////////\r\n    // Update //\r\n    ////////////\r\n    function UpdatePhase(state, { arg, next, phase }) {\r\n        const phaseConfig = GetPhase({ phase });\r\n        let { ctx } = state;\r\n        if (arg && arg.next) {\r\n            if (arg.next in phaseMap) {\r\n                ctx = { ...ctx, phase: arg.next };\r\n            }\r\n            else {\r\n                error('invalid phase: ' + arg.next);\r\n                return state;\r\n            }\r\n        }\r\n        else {\r\n            ctx = { ...ctx, phase: phaseConfig.wrapped.next(state) || null };\r\n        }\r\n        state = { ...state, ctx };\r\n        // Start the new phase.\r\n        next.push({ fn: StartPhase });\r\n        return state;\r\n    }\r\n    function UpdateTurn(state, { arg, currentPlayer, next }) {\r\n        let { G, ctx } = state;\r\n        const phaseConfig = GetPhase(ctx);\r\n        // Update turn order state.\r\n        const { endPhase, ctx: newCtx } = UpdateTurnOrderState(state, currentPlayer, phaseConfig.turn, arg);\r\n        ctx = newCtx;\r\n        state = { ...state, G, ctx };\r\n        if (endPhase) {\r\n            next.push({ fn: EndPhase, turn: ctx.turn, phase: ctx.phase });\r\n        }\r\n        else {\r\n            next.push({ fn: StartTurn, currentPlayer: ctx.currentPlayer });\r\n        }\r\n        return state;\r\n    }\r\n    function UpdateStage(state, { arg, playerID }) {\r\n        if (typeof arg === 'string' || arg === Stage.NULL) {\r\n            arg = { stage: arg };\r\n        }\r\n        if (typeof arg !== 'object')\r\n            return state;\r\n        // `arg` should be of type `StageArg`, loose typing as `any` here for historic reasons\r\n        // stages previously did not enforce minMoves, this behaviour is kept intentionally\r\n        supportDeprecatedMoveLimit(arg);\r\n        let { ctx } = state;\r\n        let { activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, _activePlayersNumMoves, } = ctx;\r\n        // Checking if stage is valid, even Stage.NULL\r\n        if (arg.stage !== undefined) {\r\n            if (activePlayers === null) {\r\n                activePlayers = {};\r\n            }\r\n            activePlayers[playerID] = arg.stage;\r\n            _activePlayersNumMoves[playerID] = 0;\r\n            if (arg.minMoves) {\r\n                if (_activePlayersMinMoves === null) {\r\n                    _activePlayersMinMoves = {};\r\n                }\r\n                _activePlayersMinMoves[playerID] = arg.minMoves;\r\n            }\r\n            if (arg.maxMoves) {\r\n                if (_activePlayersMaxMoves === null) {\r\n                    _activePlayersMaxMoves = {};\r\n                }\r\n                _activePlayersMaxMoves[playerID] = arg.maxMoves;\r\n            }\r\n        }\r\n        ctx = {\r\n            ...ctx,\r\n            activePlayers,\r\n            _activePlayersMinMoves,\r\n            _activePlayersMaxMoves,\r\n            _activePlayersNumMoves,\r\n        };\r\n        return { ...state, ctx };\r\n    }\r\n    function UpdateActivePlayers(state, { arg }) {\r\n        return { ...state, ctx: SetActivePlayers(state.ctx, arg) };\r\n    }\r\n    ///////////////\r\n    // ShouldEnd //\r\n    ///////////////\r\n    function ShouldEndGame(state) {\r\n        return wrapped.endIf(state);\r\n    }\r\n    function ShouldEndPhase(state) {\r\n        const phaseConfig = GetPhase(state.ctx);\r\n        return phaseConfig.wrapped.endIf(state);\r\n    }\r\n    function ShouldEndTurn(state) {\r\n        const phaseConfig = GetPhase(state.ctx);\r\n        // End the turn if the required number of moves has been made.\r\n        const currentPlayerMoves = state.ctx.numMoves || 0;\r\n        if (phaseConfig.turn.maxMoves &&\r\n            currentPlayerMoves >= phaseConfig.turn.maxMoves) {\r\n            return true;\r\n        }\r\n        return phaseConfig.turn.wrapped.endIf(state);\r\n    }\r\n    /////////\r\n    // End //\r\n    /////////\r\n    function EndGame(state, { arg, phase }) {\r\n        state = EndPhase(state, { phase });\r\n        if (arg === undefined) {\r\n            arg = true;\r\n        }\r\n        state = { ...state, ctx: { ...state.ctx, gameover: arg } };\r\n        // Run game end hook.\r\n        const G = wrapped.onEnd(state);\r\n        return { ...state, G };\r\n    }\r\n    function EndPhase(state, { arg, next, turn: initialTurn, automatic }) {\r\n        // End the turn first.\r\n        state = EndTurn(state, { turn: initialTurn, force: true, automatic: true });\r\n        const { phase, turn } = state.ctx;\r\n        if (next) {\r\n            next.push({ fn: UpdatePhase, arg, phase });\r\n        }\r\n        // If we aren't in a phase, there is nothing else to do.\r\n        if (phase === null) {\r\n            return state;\r\n        }\r\n        // Run any cleanup code for the phase that is about to end.\r\n        const phaseConfig = GetPhase(state.ctx);\r\n        const G = phaseConfig.wrapped.onEnd(state);\r\n        // Reset the phase.\r\n        const ctx = { ...state.ctx, phase: null };\r\n        // Add log entry.\r\n        const action = gameEvent('endPhase', arg);\r\n        const { _stateID } = state;\r\n        const logEntry = { action, _stateID, turn, phase };\r\n        if (automatic)\r\n            logEntry.automatic = true;\r\n        const deltalog = [...(state.deltalog || []), logEntry];\r\n        return { ...state, G, ctx, deltalog };\r\n    }\r\n    function EndTurn(state, { arg, next, turn: initialTurn, force, automatic, playerID }) {\r\n        // This is not the turn that EndTurn was originally\r\n        // called for. The turn was probably ended some other way.\r\n        if (initialTurn !== state.ctx.turn) {\r\n            return state;\r\n        }\r\n        const { currentPlayer, numMoves, phase, turn } = state.ctx;\r\n        const phaseConfig = GetPhase(state.ctx);\r\n        // Prevent ending the turn if minMoves haven't been reached.\r\n        const currentPlayerMoves = numMoves || 0;\r\n        if (!force &&\r\n            phaseConfig.turn.minMoves &&\r\n            currentPlayerMoves < phaseConfig.turn.minMoves) {\r\n            info(`cannot end turn before making ${phaseConfig.turn.minMoves} moves`);\r\n            return state;\r\n        }\r\n        // Run turn-end triggers.\r\n        const G = phaseConfig.turn.wrapped.onEnd(state);\r\n        if (next) {\r\n            next.push({ fn: UpdateTurn, arg, currentPlayer });\r\n        }\r\n        // Reset activePlayers.\r\n        let ctx = { ...state.ctx, activePlayers: null };\r\n        // Remove player from playerOrder\r\n        if (arg && arg.remove) {\r\n            playerID = playerID || currentPlayer;\r\n            const playOrder = ctx.playOrder.filter((i) => i != playerID);\r\n            const playOrderPos = ctx.playOrderPos > playOrder.length - 1 ? 0 : ctx.playOrderPos;\r\n            ctx = { ...ctx, playOrder, playOrderPos };\r\n            if (playOrder.length === 0) {\r\n                next.push({ fn: EndPhase, turn, phase });\r\n                return state;\r\n            }\r\n        }\r\n        // Create log entry.\r\n        const action = gameEvent('endTurn', arg);\r\n        const { _stateID } = state;\r\n        const logEntry = { action, _stateID, turn, phase };\r\n        if (automatic)\r\n            logEntry.automatic = true;\r\n        const deltalog = [...(state.deltalog || []), logEntry];\r\n        return { ...state, G, ctx, deltalog, _undo: [], _redo: [] };\r\n    }\r\n    function EndStage(state, { arg, next, automatic, playerID }) {\r\n        playerID = playerID || state.ctx.currentPlayer;\r\n        let { ctx, _stateID } = state;\r\n        let { activePlayers, _activePlayersNumMoves, _activePlayersMinMoves, _activePlayersMaxMoves, phase, turn, } = ctx;\r\n        const playerInStage = activePlayers !== null && playerID in activePlayers;\r\n        const phaseConfig = GetPhase(ctx);\r\n        if (!arg && playerInStage) {\r\n            const stage = phaseConfig.turn.stages[activePlayers[playerID]];\r\n            if (stage && stage.next) {\r\n                arg = stage.next;\r\n            }\r\n        }\r\n        // Checking if arg is a valid stage, even Stage.NULL\r\n        if (next) {\r\n            next.push({ fn: UpdateStage, arg, playerID });\r\n        }\r\n        // If player isn’t in a stage, there is nothing else to do.\r\n        if (!playerInStage)\r\n            return state;\r\n        // Prevent ending the stage if minMoves haven't been reached.\r\n        const currentPlayerMoves = _activePlayersNumMoves[playerID] || 0;\r\n        if (_activePlayersMinMoves &&\r\n            _activePlayersMinMoves[playerID] &&\r\n            currentPlayerMoves < _activePlayersMinMoves[playerID]) {\r\n            info(`cannot end stage before making ${_activePlayersMinMoves[playerID]} moves`);\r\n            return state;\r\n        }\r\n        // Remove player from activePlayers.\r\n        activePlayers = { ...activePlayers };\r\n        delete activePlayers[playerID];\r\n        if (_activePlayersMinMoves) {\r\n            // Remove player from _activePlayersMinMoves.\r\n            _activePlayersMinMoves = { ..._activePlayersMinMoves };\r\n            delete _activePlayersMinMoves[playerID];\r\n        }\r\n        if (_activePlayersMaxMoves) {\r\n            // Remove player from _activePlayersMaxMoves.\r\n            _activePlayersMaxMoves = { ..._activePlayersMaxMoves };\r\n            delete _activePlayersMaxMoves[playerID];\r\n        }\r\n        ctx = UpdateActivePlayersOnceEmpty({\r\n            ...ctx,\r\n            activePlayers,\r\n            _activePlayersMinMoves,\r\n            _activePlayersMaxMoves,\r\n        });\r\n        // Create log entry.\r\n        const action = gameEvent('endStage', arg);\r\n        const logEntry = { action, _stateID, turn, phase };\r\n        if (automatic)\r\n            logEntry.automatic = true;\r\n        const deltalog = [...(state.deltalog || []), logEntry];\r\n        return { ...state, ctx, deltalog };\r\n    }\r\n    /**\r\n     * Retrieves the relevant move that can be played by playerID.\r\n     *\r\n     * If ctx.activePlayers is set (i.e. one or more players are in some stage),\r\n     * then it attempts to find the move inside the stages config for\r\n     * that turn. If the stage for a player is '', then the player is\r\n     * allowed to make a move (as determined by the phase config), but\r\n     * isn't restricted to a particular set as defined in the stage config.\r\n     *\r\n     * If not, it then looks for the move inside the phase.\r\n     *\r\n     * If it doesn't find the move there, it looks at the global move definition.\r\n     *\r\n     * @param {object} ctx\r\n     * @param {string} name\r\n     * @param {string} playerID\r\n     */\r\n    function GetMove(ctx, name, playerID) {\r\n        const phaseConfig = GetPhase(ctx);\r\n        const stages = phaseConfig.turn.stages;\r\n        const { activePlayers } = ctx;\r\n        if (activePlayers &&\r\n            activePlayers[playerID] !== undefined &&\r\n            activePlayers[playerID] !== Stage.NULL &&\r\n            stages[activePlayers[playerID]] !== undefined &&\r\n            stages[activePlayers[playerID]].moves !== undefined) {\r\n            // Check if moves are defined for the player's stage.\r\n            const stage = stages[activePlayers[playerID]];\r\n            const moves = stage.moves;\r\n            if (name in moves) {\r\n                return moves[name];\r\n            }\r\n        }\r\n        else if (phaseConfig.moves) {\r\n            // Check if moves are defined for the current phase.\r\n            if (name in phaseConfig.moves) {\r\n                return phaseConfig.moves[name];\r\n            }\r\n        }\r\n        else if (name in moves) {\r\n            // Check for the move globally.\r\n            return moves[name];\r\n        }\r\n        return null;\r\n    }\r\n    function ProcessMove(state, action) {\r\n        const { playerID, type } = action;\r\n        const { currentPlayer, activePlayers, _activePlayersMaxMoves } = state.ctx;\r\n        const move = GetMove(state.ctx, type, playerID);\r\n        const shouldCount = !move || typeof move === 'function' || move.noLimit !== true;\r\n        let { numMoves, _activePlayersNumMoves } = state.ctx;\r\n        if (shouldCount) {\r\n            if (playerID === currentPlayer)\r\n                numMoves++;\r\n            if (activePlayers)\r\n                _activePlayersNumMoves[playerID]++;\r\n        }\r\n        state = {\r\n            ...state,\r\n            ctx: {\r\n                ...state.ctx,\r\n                numMoves,\r\n                _activePlayersNumMoves,\r\n            },\r\n        };\r\n        if (_activePlayersMaxMoves &&\r\n            _activePlayersNumMoves[playerID] >= _activePlayersMaxMoves[playerID]) {\r\n            state = EndStage(state, { playerID, automatic: true });\r\n        }\r\n        const phaseConfig = GetPhase(state.ctx);\r\n        const G = phaseConfig.turn.wrapped.onMove({ ...state, playerID });\r\n        state = { ...state, G };\r\n        const events = [{ fn: OnMove }];\r\n        return Process(state, events);\r\n    }\r\n    function SetStageEvent(state, playerID, arg) {\r\n        return Process(state, [{ fn: EndStage, arg, playerID }]);\r\n    }\r\n    function EndStageEvent(state, playerID) {\r\n        return Process(state, [{ fn: EndStage, playerID }]);\r\n    }\r\n    function SetActivePlayersEvent(state, _playerID, arg) {\r\n        return Process(state, [{ fn: UpdateActivePlayers, arg }]);\r\n    }\r\n    function SetPhaseEvent(state, _playerID, newPhase) {\r\n        return Process(state, [\r\n            {\r\n                fn: EndPhase,\r\n                phase: state.ctx.phase,\r\n                turn: state.ctx.turn,\r\n                arg: { next: newPhase },\r\n            },\r\n        ]);\r\n    }\r\n    function EndPhaseEvent(state) {\r\n        return Process(state, [\r\n            { fn: EndPhase, phase: state.ctx.phase, turn: state.ctx.turn },\r\n        ]);\r\n    }\r\n    function EndTurnEvent(state, _playerID, arg) {\r\n        return Process(state, [\r\n            { fn: EndTurn, turn: state.ctx.turn, phase: state.ctx.phase, arg },\r\n        ]);\r\n    }\r\n    function PassEvent(state, _playerID, arg) {\r\n        return Process(state, [\r\n            {\r\n                fn: EndTurn,\r\n                turn: state.ctx.turn,\r\n                phase: state.ctx.phase,\r\n                force: true,\r\n                arg,\r\n            },\r\n        ]);\r\n    }\r\n    function EndGameEvent(state, _playerID, arg) {\r\n        return Process(state, [\r\n            { fn: EndGame, turn: state.ctx.turn, phase: state.ctx.phase, arg },\r\n        ]);\r\n    }\r\n    const eventHandlers = {\r\n        endStage: EndStageEvent,\r\n        setStage: SetStageEvent,\r\n        endTurn: EndTurnEvent,\r\n        pass: PassEvent,\r\n        endPhase: EndPhaseEvent,\r\n        setPhase: SetPhaseEvent,\r\n        endGame: EndGameEvent,\r\n        setActivePlayers: SetActivePlayersEvent,\r\n    };\r\n    const enabledEventNames = [];\r\n    if (events.endTurn !== false) {\r\n        enabledEventNames.push('endTurn');\r\n    }\r\n    if (events.pass !== false) {\r\n        enabledEventNames.push('pass');\r\n    }\r\n    if (events.endPhase !== false) {\r\n        enabledEventNames.push('endPhase');\r\n    }\r\n    if (events.setPhase !== false) {\r\n        enabledEventNames.push('setPhase');\r\n    }\r\n    if (events.endGame !== false) {\r\n        enabledEventNames.push('endGame');\r\n    }\r\n    if (events.setActivePlayers !== false) {\r\n        enabledEventNames.push('setActivePlayers');\r\n    }\r\n    if (events.endStage !== false) {\r\n        enabledEventNames.push('endStage');\r\n    }\r\n    if (events.setStage !== false) {\r\n        enabledEventNames.push('setStage');\r\n    }\r\n    function ProcessEvent(state, action) {\r\n        const { type, playerID, args } = action.payload;\r\n        if (typeof eventHandlers[type] !== 'function')\r\n            return state;\r\n        return eventHandlers[type](state, playerID, ...(Array.isArray(args) ? args : [args]));\r\n    }\r\n    function IsPlayerActive(_G, ctx, playerID) {\r\n        if (ctx.activePlayers) {\r\n            return playerID in ctx.activePlayers;\r\n        }\r\n        return ctx.currentPlayer === playerID;\r\n    }\r\n    return {\r\n        ctx: (numPlayers) => ({\r\n            numPlayers,\r\n            turn: 0,\r\n            currentPlayer: '0',\r\n            playOrder: [...Array.from({ length: numPlayers })].map((_, i) => i + ''),\r\n            playOrderPos: 0,\r\n            phase: startingPhase,\r\n            activePlayers: null,\r\n        }),\r\n        init: (state) => {\r\n            return Process(state, [{ fn: StartGame }]);\r\n        },\r\n        isPlayerActive: IsPlayerActive,\r\n        eventHandlers,\r\n        eventNames: Object.keys(eventHandlers),\r\n        enabledEventNames,\r\n        moveMap,\r\n        moveNames: [...moveNames.values()],\r\n        processMove: ProcessMove,\r\n        processEvent: ProcessEvent,\r\n        getMove: GetMove,\r\n    };\r\n}\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nfunction IsProcessed(game) {\r\n    return game.processMove !== undefined;\r\n}\r\n/**\r\n * Helper to generate the game move reducer. The returned\r\n * reducer has the following signature:\r\n *\r\n * (G, action, ctx) => {}\r\n *\r\n * You can roll your own if you like, or use any Redux\r\n * addon to generate such a reducer.\r\n *\r\n * The convention used in this framework is to\r\n * have action.type contain the name of the move, and\r\n * action.args contain any additional arguments as an\r\n * Array.\r\n */\r\nfunction ProcessGameConfig(game) {\r\n    // The Game() function has already been called on this\r\n    // config object, so just pass it through.\r\n    if (IsProcessed(game)) {\r\n        return game;\r\n    }\r\n    if (game.name === undefined)\r\n        game.name = 'default';\r\n    if (game.deltaState === undefined)\r\n        game.deltaState = false;\r\n    if (game.disableUndo === undefined)\r\n        game.disableUndo = false;\r\n    if (game.setup === undefined)\r\n        game.setup = () => ({});\r\n    if (game.moves === undefined)\r\n        game.moves = {};\r\n    if (game.playerView === undefined)\r\n        game.playerView = ({ G }) => G;\r\n    if (game.plugins === undefined)\r\n        game.plugins = [];\r\n    game.plugins.forEach((plugin) => {\r\n        if (plugin.name === undefined) {\r\n            throw new Error('Plugin missing name attribute');\r\n        }\r\n        if (plugin.name.includes(' ')) {\r\n            throw new Error(plugin.name + ': Plugin name must not include spaces');\r\n        }\r\n    });\r\n    if (game.name.includes(' ')) {\r\n        throw new Error(game.name + ': Game name must not include spaces');\r\n    }\r\n    const flow = Flow(game);\r\n    return {\r\n        ...game,\r\n        flow,\r\n        moveNames: flow.moveNames,\r\n        pluginNames: game.plugins.map((p) => p.name),\r\n        processMove: (state, action) => {\r\n            let moveFn = flow.getMove(state.ctx, action.type, action.playerID);\r\n            if (IsLongFormMove(moveFn)) {\r\n                moveFn = moveFn.move;\r\n            }\r\n            if (moveFn instanceof Function) {\r\n                const fn = FnWrap(moveFn, GameMethod.MOVE, game.plugins);\r\n                let args = [];\r\n                if (action.args !== undefined) {\r\n                    args = Array.isArray(action.args) ? action.args : [action.args];\r\n                }\r\n                const context = {\r\n                    ...GetAPIs(state),\r\n                    G: state.G,\r\n                    ctx: state.ctx,\r\n                    playerID: action.playerID,\r\n                };\r\n                return fn(context, ...args);\r\n            }\r\n            error(`invalid move object: ${action.type}`);\r\n            return state.G;\r\n        },\r\n    };\r\n}\r\nfunction IsLongFormMove(move) {\r\n    return move instanceof Object && move.move !== undefined;\r\n}\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nvar UpdateErrorType;\r\n(function (UpdateErrorType) {\r\n    // The action’s credentials were missing or invalid\r\n    UpdateErrorType[\"UnauthorizedAction\"] = \"update/unauthorized_action\";\r\n    // The action’s matchID was not found\r\n    UpdateErrorType[\"MatchNotFound\"] = \"update/match_not_found\";\r\n    // Could not apply Patch operation (rfc6902).\r\n    UpdateErrorType[\"PatchFailed\"] = \"update/patch_failed\";\r\n})(UpdateErrorType || (UpdateErrorType = {}));\r\nvar ActionErrorType;\r\n(function (ActionErrorType) {\r\n    // The action contained a stale state ID\r\n    ActionErrorType[\"StaleStateId\"] = \"action/stale_state_id\";\r\n    // The requested move is unknown or not currently available\r\n    ActionErrorType[\"UnavailableMove\"] = \"action/unavailable_move\";\r\n    // The move declared it was invalid (INVALID_MOVE constant)\r\n    ActionErrorType[\"InvalidMove\"] = \"action/invalid_move\";\r\n    // The player making the action is not currently active\r\n    ActionErrorType[\"InactivePlayer\"] = \"action/inactive_player\";\r\n    // The game has finished\r\n    ActionErrorType[\"GameOver\"] = \"action/gameover\";\r\n    // The requested action is disabled (e.g. undo/redo, events)\r\n    ActionErrorType[\"ActionDisabled\"] = \"action/action_disabled\";\r\n    // The requested action is not currently possible\r\n    ActionErrorType[\"ActionInvalid\"] = \"action/action_invalid\";\r\n    // The requested action was declared invalid by a plugin\r\n    ActionErrorType[\"PluginActionInvalid\"] = \"action/plugin_invalid\";\r\n})(ActionErrorType || (ActionErrorType = {}));\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Check if the payload for the passed action contains a playerID.\r\n */\r\nconst actionHasPlayerID = (action) => action.payload.playerID !== null && action.payload.playerID !== undefined;\r\n/**\r\n * Returns true if a move can be undone.\r\n */\r\nconst CanUndoMove = (G, ctx, move) => {\r\n    function HasUndoable(move) {\r\n        return move.undoable !== undefined;\r\n    }\r\n    function IsFunction(undoable) {\r\n        return undoable instanceof Function;\r\n    }\r\n    if (!HasUndoable(move)) {\r\n        return true;\r\n    }\r\n    if (IsFunction(move.undoable)) {\r\n        return move.undoable({ G, ctx });\r\n    }\r\n    return move.undoable;\r\n};\r\n/**\r\n * Update the undo and redo stacks for a move or event.\r\n */\r\nfunction updateUndoRedoState(state, opts) {\r\n    if (opts.game.disableUndo)\r\n        return state;\r\n    const undoEntry = {\r\n        G: state.G,\r\n        ctx: state.ctx,\r\n        plugins: state.plugins,\r\n        playerID: opts.action.payload.playerID || state.ctx.currentPlayer,\r\n    };\r\n    if (opts.action.type === 'MAKE_MOVE') {\r\n        undoEntry.moveType = opts.action.payload.type;\r\n    }\r\n    return {\r\n        ...state,\r\n        _undo: [...state._undo, undoEntry],\r\n        // Always reset redo stack when making a move or event\r\n        _redo: [],\r\n    };\r\n}\r\n/**\r\n * Process state, adding the initial deltalog for this action.\r\n */\r\nfunction initializeDeltalog(state, action, move) {\r\n    // Create a log entry for this action.\r\n    const logEntry = {\r\n        action,\r\n        _stateID: state._stateID,\r\n        turn: state.ctx.turn,\r\n        phase: state.ctx.phase,\r\n    };\r\n    const pluginLogMetadata = state.plugins.log.data.metadata;\r\n    if (pluginLogMetadata !== undefined) {\r\n        logEntry.metadata = pluginLogMetadata;\r\n    }\r\n    if (typeof move === 'object' && move.redact === true) {\r\n        logEntry.redact = true;\r\n    }\r\n    else if (typeof move === 'object' && move.redact instanceof Function) {\r\n        logEntry.redact = move.redact({ G: state.G, ctx: state.ctx });\r\n    }\r\n    return {\r\n        ...state,\r\n        deltalog: [logEntry],\r\n    };\r\n}\r\n/**\r\n * Update plugin state after move/event & check if plugins consider the action to be valid.\r\n * @param state Current version of state in the reducer.\r\n * @param oldState State to revert to in case of error.\r\n * @param pluginOpts Plugin configuration options.\r\n * @returns Tuple of the new state updated after flushing plugins and the old\r\n * state augmented with an error if a plugin declared the action invalid.\r\n */\r\nfunction flushAndValidatePlugins(state, oldState, pluginOpts) {\r\n    const [newState, isInvalid] = FlushAndValidate(state, pluginOpts);\r\n    if (!isInvalid)\r\n        return [newState];\r\n    return [\r\n        newState,\r\n        WithError(oldState, ActionErrorType.PluginActionInvalid, isInvalid),\r\n    ];\r\n}\r\n/**\r\n * ExtractTransientsFromState\r\n *\r\n * Split out transients from the a TransientState\r\n */\r\nfunction ExtractTransients(transientState) {\r\n    if (!transientState) {\r\n        // We preserve null for the state for legacy callers, but the transient\r\n        // field should be undefined if not present to be consistent with the\r\n        // code path below.\r\n        return [null, undefined];\r\n    }\r\n    const { transients, ...state } = transientState;\r\n    return [state, transients];\r\n}\r\n/**\r\n * WithError\r\n *\r\n * Augment a State instance with transient error information.\r\n */\r\nfunction WithError(state, errorType, payload) {\r\n    const error = {\r\n        type: errorType,\r\n        payload,\r\n    };\r\n    return {\r\n        ...state,\r\n        transients: {\r\n            error,\r\n        },\r\n    };\r\n}\r\n/**\r\n * Middleware for processing TransientState associated with the reducer\r\n * returned by CreateGameReducer.\r\n * This should pretty much be used everywhere you want realistic state\r\n * transitions and error handling.\r\n */\r\nconst TransientHandlingMiddleware = (store) => (next) => (action) => {\r\n    const result = next(action);\r\n    switch (action.type) {\r\n        case STRIP_TRANSIENTS: {\r\n            return result;\r\n        }\r\n        default: {\r\n            const [, transients] = ExtractTransients(store.getState());\r\n            if (typeof transients !== 'undefined') {\r\n                store.dispatch(stripTransients());\r\n                // Dev Note: If parent middleware needs to correlate the spawned\r\n                // StripTransients action to the triggering action, instrument here.\r\n                //\r\n                // This is a bit tricky; for more details, see:\r\n                //   https://github.com/boardgameio/boardgame.io/pull/940#discussion_r636200648\r\n                return {\r\n                    ...result,\r\n                    transients,\r\n                };\r\n            }\r\n            return result;\r\n        }\r\n    }\r\n};\r\n/**\r\n * CreateGameReducer\r\n *\r\n * Creates the main game state reducer.\r\n */\r\nfunction CreateGameReducer({ game, isClient, }) {\r\n    game = ProcessGameConfig(game);\r\n    /**\r\n     * GameReducer\r\n     *\r\n     * Redux reducer that maintains the overall game state.\r\n     * @param {object} state - The state before the action.\r\n     * @param {object} action - A Redux action.\r\n     */\r\n    return (stateWithTransients = null, action) => {\r\n        let [state /*, transients */] = ExtractTransients(stateWithTransients);\r\n        switch (action.type) {\r\n            case STRIP_TRANSIENTS: {\r\n                // This action indicates that transient metadata in the state has been\r\n                // consumed and should now be stripped from the state..\r\n                return state;\r\n            }\r\n            case GAME_EVENT: {\r\n                state = { ...state, deltalog: [] };\r\n                // Process game events only on the server.\r\n                // These events like `endTurn` typically\r\n                // contain code that may rely on secret state\r\n                // and cannot be computed on the client.\r\n                if (isClient) {\r\n                    return state;\r\n                }\r\n                // Disallow events once the game is over.\r\n                if (state.ctx.gameover !== undefined) {\r\n                    error(`cannot call event after game end`);\r\n                    return WithError(state, ActionErrorType.GameOver);\r\n                }\r\n                // Ignore the event if the player isn't active.\r\n                if (actionHasPlayerID(action) &&\r\n                    !game.flow.isPlayerActive(state.G, state.ctx, action.payload.playerID)) {\r\n                    error(`disallowed event: ${action.payload.type}`);\r\n                    return WithError(state, ActionErrorType.InactivePlayer);\r\n                }\r\n                // Execute plugins.\r\n                state = Enhance(state, {\r\n                    game,\r\n                    isClient: false,\r\n                    playerID: action.payload.playerID,\r\n                });\r\n                // Process event.\r\n                let newState = game.flow.processEvent(state, action);\r\n                // Execute plugins.\r\n                let stateWithError;\r\n                [newState, stateWithError] = flushAndValidatePlugins(newState, state, {\r\n                    game,\r\n                    isClient: false,\r\n                });\r\n                if (stateWithError)\r\n                    return stateWithError;\r\n                // Update undo / redo state.\r\n                newState = updateUndoRedoState(newState, { game, action });\r\n                return { ...newState, _stateID: state._stateID + 1 };\r\n            }\r\n            case MAKE_MOVE: {\r\n                const oldState = (state = { ...state, deltalog: [] });\r\n                // Check whether the move is allowed at this time.\r\n                const move = game.flow.getMove(state.ctx, action.payload.type, action.payload.playerID || state.ctx.currentPlayer);\r\n                if (move === null) {\r\n                    error(`disallowed move: ${action.payload.type}`);\r\n                    return WithError(state, ActionErrorType.UnavailableMove);\r\n                }\r\n                // Don't run move on client if move says so.\r\n                if (isClient && move.client === false) {\r\n                    return state;\r\n                }\r\n                // Disallow moves once the game is over.\r\n                if (state.ctx.gameover !== undefined) {\r\n                    error(`cannot make move after game end`);\r\n                    return WithError(state, ActionErrorType.GameOver);\r\n                }\r\n                // Ignore the move if the player isn't active.\r\n                if (actionHasPlayerID(action) &&\r\n                    !game.flow.isPlayerActive(state.G, state.ctx, action.payload.playerID)) {\r\n                    error(`disallowed move: ${action.payload.type}`);\r\n                    return WithError(state, ActionErrorType.InactivePlayer);\r\n                }\r\n                // Execute plugins.\r\n                state = Enhance(state, {\r\n                    game,\r\n                    isClient,\r\n                    playerID: action.payload.playerID,\r\n                });\r\n                // Process the move.\r\n                const G = game.processMove(state, action.payload);\r\n                // The game declared the move as invalid.\r\n                if (G === INVALID_MOVE) {\r\n                    error(`invalid move: ${action.payload.type} args: ${action.payload.args}`);\r\n                    // TODO(#723): Marshal a nice error payload with the processed move.\r\n                    return WithError(state, ActionErrorType.InvalidMove);\r\n                }\r\n                const newState = { ...state, G };\r\n                // Some plugin indicated that it is not suitable to be\r\n                // materialized on the client (and must wait for the server\r\n                // response instead).\r\n                if (isClient && NoClient(newState, { game })) {\r\n                    return state;\r\n                }\r\n                state = newState;\r\n                // If we're on the client, just process the move\r\n                // and no triggers in multiplayer mode.\r\n                // These will be processed on the server, which\r\n                // will send back a state update.\r\n                if (isClient) {\r\n                    let stateWithError;\r\n                    [state, stateWithError] = flushAndValidatePlugins(state, oldState, {\r\n                        game,\r\n                        isClient: true,\r\n                    });\r\n                    if (stateWithError)\r\n                        return stateWithError;\r\n                    return {\r\n                        ...state,\r\n                        _stateID: state._stateID + 1,\r\n                    };\r\n                }\r\n                // On the server, construct the deltalog.\r\n                state = initializeDeltalog(state, action, move);\r\n                // Allow the flow reducer to process any triggers that happen after moves.\r\n                state = game.flow.processMove(state, action.payload);\r\n                let stateWithError;\r\n                [state, stateWithError] = flushAndValidatePlugins(state, oldState, {\r\n                    game,\r\n                });\r\n                if (stateWithError)\r\n                    return stateWithError;\r\n                // Update undo / redo state.\r\n                state = updateUndoRedoState(state, { game, action });\r\n                return {\r\n                    ...state,\r\n                    _stateID: state._stateID + 1,\r\n                };\r\n            }\r\n            case RESET:\r\n            case UPDATE:\r\n            case SYNC: {\r\n                return action.state;\r\n            }\r\n            case UNDO: {\r\n                state = { ...state, deltalog: [] };\r\n                if (game.disableUndo) {\r\n                    error('Undo is not enabled');\r\n                    return WithError(state, ActionErrorType.ActionDisabled);\r\n                }\r\n                const { G, ctx, _undo, _redo, _stateID } = state;\r\n                if (_undo.length < 2) {\r\n                    error(`No moves to undo`);\r\n                    return WithError(state, ActionErrorType.ActionInvalid);\r\n                }\r\n                const last = _undo[_undo.length - 1];\r\n                const restore = _undo[_undo.length - 2];\r\n                // Only allow players to undo their own moves.\r\n                if (actionHasPlayerID(action) &&\r\n                    action.payload.playerID !== last.playerID) {\r\n                    error(`Cannot undo other players' moves`);\r\n                    return WithError(state, ActionErrorType.ActionInvalid);\r\n                }\r\n                // If undoing a move, check it is undoable.\r\n                if (last.moveType) {\r\n                    const lastMove = game.flow.getMove(restore.ctx, last.moveType, last.playerID);\r\n                    if (!CanUndoMove(G, ctx, lastMove)) {\r\n                        error(`Move cannot be undone`);\r\n                        return WithError(state, ActionErrorType.ActionInvalid);\r\n                    }\r\n                }\r\n                state = initializeDeltalog(state, action);\r\n                return {\r\n                    ...state,\r\n                    G: restore.G,\r\n                    ctx: restore.ctx,\r\n                    plugins: restore.plugins,\r\n                    _stateID: _stateID + 1,\r\n                    _undo: _undo.slice(0, -1),\r\n                    _redo: [last, ..._redo],\r\n                };\r\n            }\r\n            case REDO: {\r\n                state = { ...state, deltalog: [] };\r\n                if (game.disableUndo) {\r\n                    error('Redo is not enabled');\r\n                    return WithError(state, ActionErrorType.ActionDisabled);\r\n                }\r\n                const { _undo, _redo, _stateID } = state;\r\n                if (_redo.length === 0) {\r\n                    error(`No moves to redo`);\r\n                    return WithError(state, ActionErrorType.ActionInvalid);\r\n                }\r\n                const first = _redo[0];\r\n                // Only allow players to redo their own undos.\r\n                if (actionHasPlayerID(action) &&\r\n                    action.payload.playerID !== first.playerID) {\r\n                    error(`Cannot redo other players' moves`);\r\n                    return WithError(state, ActionErrorType.ActionInvalid);\r\n                }\r\n                state = initializeDeltalog(state, action);\r\n                return {\r\n                    ...state,\r\n                    G: first.G,\r\n                    ctx: first.ctx,\r\n                    plugins: first.plugins,\r\n                    _stateID: _stateID + 1,\r\n                    _undo: [..._undo, first],\r\n                    _redo: _redo.slice(1),\r\n                };\r\n            }\r\n            case PLUGIN: {\r\n                // TODO(#723): Expose error semantics to plugin processing.\r\n                return ProcessAction(state, action, { game });\r\n            }\r\n            case PATCH: {\r\n                const oldState = state;\r\n                const newState = JSON.parse(JSON.stringify(oldState));\r\n                const patchError = applyPatch(newState, action.patch);\r\n                const hasError = patchError.some((entry) => entry !== null);\r\n                if (hasError) {\r\n                    error(`Patch ${JSON.stringify(action.patch)} apply failed`);\r\n                    return WithError(oldState, UpdateErrorType.PatchFailed, patchError);\r\n                }\r\n                else {\r\n                    return newState;\r\n                }\r\n            }\r\n            default: {\r\n                return state;\r\n            }\r\n        }\r\n    };\r\n}\n\nexport { CreateGameReducer as C, IsLongFormMove as I, ProcessGameConfig as P, TransientHandlingMiddleware as T };\n", "import { t as Setup, E as <PERSON>hance, a as GetAPIs, q as FlushAndValidate } from './turn-order-8cc4909b.js';\nimport { P as ProcessGameConfig } from './reducer-24ea3e4c.js';\n\n/*\r\n * Copyright 2020 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Creates the initial game state.\r\n */\r\nfunction InitializeGame({ game, numPlayers, setupData, }) {\r\n    game = ProcessGameConfig(game);\r\n    if (!numPlayers) {\r\n        numPlayers = 2;\r\n    }\r\n    const ctx = game.flow.ctx(numPlayers);\r\n    let state = {\r\n        // User managed state.\r\n        G: {},\r\n        // Framework managed state.\r\n        ctx,\r\n        // Plugin related state.\r\n        plugins: {},\r\n    };\r\n    // Run plugins over initial state.\r\n    state = Setup(state, { game });\r\n    state = Enhance(state, { game, playerID: undefined });\r\n    const pluginAPIs = GetAPIs(state);\r\n    state.G = game.setup({ ...pluginAPIs, ctx: state.ctx }, setupData);\r\n    let initial = {\r\n        ...state,\r\n        // List of {G, ctx} pairs that can be undone.\r\n        _undo: [],\r\n        // List of {G, ctx} pairs that can be redone.\r\n        _redo: [],\r\n        // A monotonically non-decreasing ID to ensure that\r\n        // state updates are only allowed from clients that\r\n        // are at the same version that the server.\r\n        _stateID: 0,\r\n    };\r\n    initial = game.flow.init(initial);\r\n    [initial] = FlushAndValidate(initial, { game });\r\n    // Initialize undo stack.\r\n    if (!game.disableUndo) {\r\n        initial._undo = [\r\n            {\r\n                G: initial.G,\r\n                ctx: initial.ctx,\r\n                plugins: initial.plugins,\r\n            },\r\n        ];\r\n    }\r\n    return initial;\r\n}\n\nexport { InitializeGame as I };\n", "/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nclass Transport {\r\n    constructor({ transportDataCallback, gameName, playerID, matchID, credentials, numPlayers, }) {\r\n        /** Callback to let the client know when the connection status has changed. */\r\n        this.connectionStatusCallback = () => { };\r\n        this.isConnected = false;\r\n        this.transportDataCallback = transportDataCallback;\r\n        this.gameName = gameName || 'default';\r\n        this.playerID = playerID || null;\r\n        this.matchID = matchID || 'default';\r\n        this.credentials = credentials;\r\n        this.numPlayers = numPlayers || 2;\r\n    }\r\n    /** Subscribe to connection state changes. */\r\n    subscribeToConnectionStatus(fn) {\r\n        this.connectionStatusCallback = fn;\r\n    }\r\n    /** Transport implementations should call this when they connect/disconnect. */\r\n    setConnectionStatus(isConnected) {\r\n        this.isConnected = isConnected;\r\n        this.connectionStatusCallback();\r\n    }\r\n    /** Transport implementations should call this when they receive data from a master. */\r\n    notifyClient(data) {\r\n        this.transportDataCallback(data);\r\n    }\r\n}\n\nexport { Transport as T };\n"], "mappings": ";;;;;;AAAA;AAAA;AAUA,QAAI,YAAY;AAShB,aAAS,aAAa,OAAO;AAG3B,UAAI,SAAS;AACb,UAAI,SAAS,QAAQ,OAAO,MAAM,YAAY,YAAY;AACxD,YAAI;AACF,mBAAS,CAAC,EAAE,QAAQ;AAAA,QACtB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AACA,aAAO;AAAA,IACT;AAUA,aAAS,QAAQ,MAAM,WAAW;AAChC,aAAO,SAAS,KAAK;AACnB,eAAO,KAAK,UAAU,GAAG,CAAC;AAAA,MAC5B;AAAA,IACF;AAGA,QAAI,YAAY,SAAS;AAAzB,QACI,cAAc,OAAO;AAGzB,QAAI,eAAe,UAAU;AAG7B,QAAI,iBAAiB,YAAY;AAGjC,QAAI,mBAAmB,aAAa,KAAK,MAAM;AAO/C,QAAI,iBAAiB,YAAY;AAGjC,QAAI,eAAe,QAAQ,OAAO,gBAAgB,MAAM;AA0BxD,aAAS,aAAa,OAAO;AAC3B,aAAO,CAAC,CAAC,SAAS,OAAO,SAAS;AAAA,IACpC;AA8BA,aAASA,eAAc,OAAO;AAC5B,UAAI,CAAC,aAAa,KAAK,KACnB,eAAe,KAAK,KAAK,KAAK,aAAa,aAAa,KAAK,GAAG;AAClE,eAAO;AAAA,MACT;AACA,UAAI,QAAQ,aAAa,KAAK;AAC9B,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,eAAe,KAAK,OAAO,aAAa,KAAK,MAAM;AAC9D,aAAQ,OAAO,QAAQ,cACrB,gBAAgB,QAAQ,aAAa,KAAK,IAAI,KAAK;AAAA,IACvD;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AC1IjB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU;AAqBlB,aAAS,SAAS,OAAO;AACrB,aAAO,MAAM,QAAQ,OAAO,GAAG,EAAE,QAAQ,OAAO,GAAG;AAAA,IACvD;AASA,aAAS,OAAO,OAAO;AACnB,aAAO,MAAM,QAAQ,MAAM,IAAI,EAAE,QAAQ,OAAO,IAAI;AAAA,IACxD;AAIA,QAAI;AAAA;AAAA,MAAyB,WAAY;AACrC,iBAASC,SAAQ,QAAQ;AACrB,cAAI,WAAW,QAAQ;AAAE,qBAAS,CAAC,EAAE;AAAA,UAAG;AACxC,eAAK,SAAS;AAAA,QAClB;AAIA,QAAAA,SAAQ,WAAW,SAAU,MAAM;AAC/B,cAAI,SAAS,KAAK,MAAM,GAAG,EAAE,IAAI,QAAQ;AACzC,cAAI,OAAO,CAAC,MAAM;AACd,kBAAM,IAAI,MAAM,yBAAyB,OAAO,IAAI,CAAC;AACzD,iBAAO,IAAIA,SAAQ,MAAM;AAAA,QAC7B;AACA,QAAAA,SAAQ,UAAU,WAAW,WAAY;AACrC,iBAAO,KAAK,OAAO,IAAI,MAAM,EAAE,KAAK,GAAG;AAAA,QAC3C;AAOA,QAAAA,SAAQ,UAAU,WAAW,SAAU,QAAQ;AAC3C,cAAI,SAAS;AACb,cAAI,MAAM;AACV,cAAI,QAAQ;AACZ,mBAASC,KAAI,GAAGC,KAAI,KAAK,OAAO,QAAQD,KAAIC,IAAGD,MAAK;AAChD,qBAAS;AACT,kBAAM,KAAK,OAAOA,EAAC;AACnB,gBAAI,OAAO,eAAe,OAAO,iBAAiB,OAAO,aAAa;AAClE;AAAA,YACJ;AAEA,qBAAS,UAAU,CAAC,GAAG,GAAG;AAAA,UAC9B;AACA,iBAAO,EAAE,QAAgB,KAAU,MAAa;AAAA,QACpD;AACA,QAAAD,SAAQ,UAAU,MAAM,SAAU,QAAQ;AACtC,iBAAO,KAAK,SAAS,MAAM,EAAE;AAAA,QACjC;AACA,QAAAA,SAAQ,UAAU,MAAM,SAAU,QAAQ,OAAO;AAC7C,cAAI,WAAW,KAAK,SAAS,MAAM;AACnC,cAAI,SAAS,QAAQ;AACjB,qBAAS,OAAO,SAAS,GAAG,IAAI;AAAA,UACpC;AAAA,QACJ;AACA,QAAAA,SAAQ,UAAU,OAAO,SAAU,OAAO;AAEtC,eAAK,OAAO,KAAK,KAAK;AAAA,QAC1B;AAMA,QAAAA,SAAQ,UAAU,MAAM,SAAU,OAAO;AACrC,cAAI,SAAS,KAAK,OAAO,OAAO,OAAO,KAAK,CAAC;AAC7C,iBAAO,IAAIA,SAAQ,MAAM;AAAA,QAC7B;AACA,eAAOA;AAAA,MACX,EAAE;AAAA;AACF,YAAQ,UAAU;AAAA;AAAA;;;ACtGlB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,QAAQ,QAAQ,aAAa,QAAQ,iBAAiB;AAC9D,YAAQ,iBAAiB,OAAO,UAAU;AAC1C,aAAS,WAAW,QAAQ;AACxB,UAAI,WAAW,QAAW;AACtB,eAAO;AAAA,MACX;AACA,UAAI,WAAW,MAAM;AACjB,eAAO;AAAA,MACX;AACA,UAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,eAAO;AAAA,MACX;AACA,aAAO,OAAO;AAAA,IAClB;AACA,YAAQ,aAAa;AACrB,aAAS,eAAe,OAAO;AAG3B,aAAO,SAAS,QAAQ,OAAO,SAAS;AAAA,IAC5C;AAQA,aAAS,MAAM,QAAQ;AACnB,UAAI,CAAC,eAAe,MAAM,GAAG;AAEzB,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,eAAe,OAAO;AAE7B,YAAI,WAAW,OAAO;AAEtB,YAAI,cAAc,IAAI,MAAM,QAAQ;AACpC,iBAASG,KAAI,GAAGA,KAAI,UAAUA,MAAK;AAC/B,sBAAYA,EAAC,IAAI,MAAM,OAAOA,EAAC,CAAC;AAAA,QACpC;AACA,eAAO;AAAA,MACX;AAEA,UAAI,OAAO,eAAe,MAAM;AAC5B,YAAI,aAAa,oBAAI,KAAK,CAAC,MAAM;AACjC,eAAO;AAAA,MACX;AAEA,UAAI,eAAe,CAAC;AAEpB,eAAS,OAAO,QAAQ;AAGpB,YAAI,QAAQ,eAAe,KAAK,QAAQ,GAAG,GAAG;AAC1C,uBAAa,GAAG,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,QACzC;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,YAAQ,QAAQ;AAAA;AAAA;;;AC9DhB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,UAAU,QAAQ,cAAc,QAAQ,aAAa,QAAQ,eAAe,QAAQ,WAAW,QAAQ,gBAAgB;AAC/H,QAAI,SAAS;AACb,aAAS,cAAc,IAAI;AACvB,UAAI,KAAK,GAAG;AACZ,aAAO,OAAO,YAAY,OAAO,aAAa,OAAO,UAAU,OAAO;AAAA,IAC1E;AACA,YAAQ,gBAAgB;AAYxB,aAAS,SAAS,SAAS,YAAY;AAEnC,UAAI,MAAM,CAAC;AAEX,eAAS,WAAW,SAAS;AACzB,YAAI,OAAO,eAAe,KAAK,SAAS,OAAO,KAAK,QAAQ,OAAO,MAAM,QAAW;AAChF,cAAI,OAAO,IAAI;AAAA,QACnB;AAAA,MACJ;AAGA,eAAS,WAAW,YAAY;AAC5B,YAAI,OAAO,eAAe,KAAK,YAAY,OAAO,KAAK,WAAW,OAAO,MAAM,QAAW;AACtF,iBAAO,IAAI,OAAO;AAAA,QACtB;AAAA,MACJ;AAEA,aAAO,OAAO,KAAK,GAAG;AAAA,IAC1B;AACA,YAAQ,WAAW;AASnB,aAAS,aAAa,SAAS;AAC3B,UAAI,SAAS,QAAQ;AAErB,UAAI,UAAU,CAAC;AAEf,eAASC,KAAI,GAAGA,KAAI,QAAQA,MAAK;AAC7B,YAAI,SAAS,QAAQA,EAAC;AACtB,iBAAS,OAAO,QAAQ;AACpB,cAAI,OAAO,eAAe,KAAK,QAAQ,GAAG,KAAK,OAAO,GAAG,MAAM,QAAW;AACtE,oBAAQ,GAAG,KAAK,QAAQ,GAAG,KAAK,KAAK;AAAA,UACzC;AAAA,QACJ;AAAA,MACJ;AAEA,eAAS,OAAO,SAAS;AACrB,YAAI,QAAQ,GAAG,IAAI,QAAQ;AACvB,iBAAO,QAAQ,GAAG;AAAA,QACtB;AAAA,MACJ;AAEA,aAAO,OAAO,KAAK,OAAO;AAAA,IAC9B;AACA,YAAQ,eAAe;AACvB,aAAS,WAAW,iBAAiB;AACjC,aAAO,gBAAgB,OAAO;AAAA,IAClC;AACA,aAAS,cAAc,iBAAiB;AACpC,aAAO,gBAAgB,OAAO;AAAA,IAClC;AACA,aAAS,qBAAqB,MAAM,WAAW;AAC3C,aAAO;AAAA;AAAA,QAEH,YAAY,KAAK,WAAW,OAAO,SAAS;AAAA,QAC5C,MAAM,KAAK,OAAO;AAAA,MACtB;AAAA,IACJ;AA6BA,aAAS,WAAW,OAAO,QAAQ,KAAK,MAAM;AAC1C,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAS;AAEvC,UAAI,OAAO;AAAA,QACP,OAAO,EAAE,YAAY,CAAC,GAAG,MAAM,EAAE;AAAA,MACrC;AAWA,eAAS,KAAKA,IAAGC,IAAG;AAEhB,YAAI,WAAW,GAAG,OAAOD,IAAG,GAAG,EAAE,OAAOC,EAAC;AACzC,YAAI,WAAW,KAAK,QAAQ;AAC5B,YAAI,aAAa,QAAW;AAExB,cAAID,KAAI,KAAKC,KAAI,KAAK,CAAC,KAAK,MAAMD,KAAI,CAAC,GAAG,OAAOC,KAAI,CAAC,GAAG,IAAI,IAAI,OAAOD,KAAI,CAAC,CAAC,CAAC,EAAE,QAAQ;AAErF,uBAAW,KAAKA,KAAI,GAAGC,KAAI,CAAC;AAAA,UAChC,OACK;AACD,gBAAI,eAAe,CAAC;AACpB,gBAAID,KAAI,GAAG;AAEP,kBAAI,cAAc,KAAKA,KAAI,GAAGC,EAAC;AAC/B,kBAAI,mBAAmB;AAAA,gBACnB,IAAI;AAAA,gBACJ,OAAOD,KAAI;AAAA,cACf;AACA,2BAAa,KAAK,qBAAqB,aAAa,gBAAgB,CAAC;AAAA,YACzE;AACA,gBAAIC,KAAI,GAAG;AAEP,kBAAI,WAAW,KAAKD,IAAGC,KAAI,CAAC;AAC5B,kBAAI,gBAAgB;AAAA,gBAChB,IAAI;AAAA,gBACJ,OAAOD,KAAI;AAAA,gBACX,OAAO,OAAOC,KAAI,CAAC;AAAA,cACvB;AACA,2BAAa,KAAK,qBAAqB,UAAU,aAAa,CAAC;AAAA,YACnE;AACA,gBAAID,KAAI,KAAKC,KAAI,GAAG;AAGhB,kBAAI,eAAe,KAAKD,KAAI,GAAGC,KAAI,CAAC;AAIpC,kBAAI,oBAAoB;AAAA,gBACpB,IAAI;AAAA,gBACJ,OAAOD,KAAI;AAAA,gBACX,UAAU,MAAMA,KAAI,CAAC;AAAA,gBACrB,OAAO,OAAOC,KAAI,CAAC;AAAA,cACvB;AACA,2BAAa,KAAK,qBAAqB,cAAc,iBAAiB,CAAC;AAAA,YAC3E;AAKA,gBAAI,OAAO,aAAa,KAAK,SAAUC,IAAGC,IAAG;AAAE,qBAAOD,GAAE,OAAOC,GAAE;AAAA,YAAM,CAAC,EAAE,CAAC;AAC3E,uBAAW;AAAA,UACf;AACA,eAAK,QAAQ,IAAI;AAAA,QACrB;AACA,eAAO;AAAA,MACX;AAGA,UAAI,eAAgB,MAAM,MAAM,MAAM,KAAK,MAAM,UAAU,IAAK,IAAI,MAAM;AAC1E,UAAI,gBAAiB,MAAM,OAAO,MAAM,KAAK,OAAO,UAAU,IAAK,IAAI,OAAO;AAC9E,UAAI,mBAAmB,KAAK,cAAc,aAAa,EAAE;AACzD,UAAI,oBAAoB,iBAAiB,OAAO,SAAU,IAAI,iBAAiB;AAC3E,YAAI,aAAa,GAAG,CAAC,GAAG,UAAU,GAAG,CAAC;AACtC,YAAI,WAAW,eAAe,GAAG;AAC7B,cAAI,eAAe,gBAAgB,QAAQ,IAAI;AAC/C,cAAI,cAAc,eAAgB,eAAe,UAAW,OAAO,YAAY,IAAI;AACnF,cAAI,YAAY;AAAA,YACZ,IAAI,gBAAgB;AAAA,YACpB,MAAM,IAAI,IAAI,WAAW,EAAE,SAAS;AAAA,YACpC,OAAO,gBAAgB;AAAA,UAC3B;AAEA,iBAAO,CAAC,WAAW,OAAO,SAAS,GAAG,UAAU,CAAC;AAAA,QACrD,WACS,cAAc,eAAe,GAAG;AACrC,cAAI,YAAY;AAAA,YACZ,IAAI,gBAAgB;AAAA,YACpB,MAAM,IAAI,IAAI,OAAO,gBAAgB,QAAQ,OAAO,CAAC,EAAE,SAAS;AAAA,UACpE;AAEA,iBAAO,CAAC,WAAW,OAAO,SAAS,GAAG,UAAU,CAAC;AAAA,QACrD,OACK;AACD,cAAI,cAAc,IAAI,IAAI,OAAO,gBAAgB,QAAQ,OAAO,CAAC;AACjE,cAAI,qBAAqB,KAAK,gBAAgB,UAAU,gBAAgB,OAAO,WAAW;AAC1F,iBAAO,CAAC,WAAW,OAAO,MAAM,YAAY,kBAAkB,GAAG,OAAO;AAAA,QAC5E;AAAA,MACJ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;AACb,aAAO;AAAA,IACX;AACA,YAAQ,aAAa;AACrB,aAAS,YAAY,OAAO,QAAQ,KAAK,MAAM;AAC3C,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAS;AAEvC,UAAI,aAAa,CAAC;AAClB,eAAS,OAAO,MAAM,EAAE,QAAQ,SAAU,KAAK;AAC3C,mBAAW,KAAK,EAAE,IAAI,UAAU,MAAM,IAAI,IAAI,GAAG,EAAE,SAAS,EAAE,CAAC;AAAA,MACnE,CAAC;AAED,eAAS,QAAQ,KAAK,EAAE,QAAQ,SAAU,KAAK;AAC3C,mBAAW,KAAK,EAAE,IAAI,OAAO,MAAM,IAAI,IAAI,GAAG,EAAE,SAAS,GAAG,OAAO,OAAO,GAAG,EAAE,CAAC;AAAA,MACpF,CAAC;AAED,mBAAa,CAAC,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AACjD,mBAAW,KAAK,MAAM,YAAY,KAAK,MAAM,GAAG,GAAG,OAAO,GAAG,GAAG,IAAI,IAAI,GAAG,CAAC,CAAC;AAAA,MACjF,CAAC;AACD,aAAO;AAAA,IACX;AACA,YAAQ,cAAc;AAwBtB,aAAS,QAAQ,OAAO,QAAQ,KAAK,MAAM;AACvC,UAAI,SAAS,QAAQ;AAAE,eAAO;AAAA,MAAS;AAEvC,UAAI,UAAU,QAAQ;AAClB,eAAO,CAAC;AAAA,MACZ;AACA,UAAI,cAAc,GAAG,OAAO,YAAY,KAAK;AAC7C,UAAI,eAAe,GAAG,OAAO,YAAY,MAAM;AAC/C,UAAI,cAAc,WAAW,eAAe,SAAS;AACjD,eAAO,WAAW,OAAO,QAAQ,KAAK,IAAI;AAAA,MAC9C;AACA,UAAI,cAAc,YAAY,eAAe,UAAU;AACnD,eAAO,YAAY,OAAO,QAAQ,KAAK,IAAI;AAAA,MAC/C;AAKA,aAAO,CAAC,EAAE,IAAI,WAAW,MAAM,IAAI,SAAS,GAAG,OAAO,OAAO,CAAC;AAAA,IAClE;AACA,YAAQ,UAAU;AAAA;AAAA;;;ACzRlB;AAAA;AAAA;AACA,QAAI,YAAa,WAAQ,QAAK,aAAe,2BAAY;AACrD,UAAI,gBAAgB,SAAUC,IAAGC,IAAG;AAChC,wBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUD,IAAGC,IAAG;AAAE,UAAAD,GAAE,YAAYC;AAAA,QAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,mBAASC,MAAKD;AAAG,gBAAI,OAAO,UAAU,eAAe,KAAKA,IAAGC,EAAC;AAAG,cAAAF,GAAEE,EAAC,IAAID,GAAEC,EAAC;AAAA,QAAG;AACpG,eAAO,cAAcF,IAAGC,EAAC;AAAA,MAC7B;AACA,aAAO,SAAUD,IAAGC,IAAG;AACnB,YAAI,OAAOA,OAAM,cAAcA,OAAM;AACjC,gBAAM,IAAI,UAAU,yBAAyB,OAAOA,EAAC,IAAI,+BAA+B;AAC5F,sBAAcD,IAAGC,EAAC;AAClB,iBAAS,KAAK;AAAE,eAAK,cAAcD;AAAA,QAAG;AACtC,QAAAA,GAAE,YAAYC,OAAM,OAAO,OAAO,OAAOA,EAAC,KAAK,GAAG,YAAYA,GAAE,WAAW,IAAI,GAAG;AAAA,MACtF;AAAA,IACJ,EAAG;AACH,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,QAAQ,QAAQ,wBAAwB,QAAQ,OAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ,UAAU,QAAQ,SAAS,QAAQ,MAAM,QAAQ,YAAY,QAAQ,eAAe;AACzL,QAAI,YAAY;AAChB,QAAI,SAAS;AACb,QAAI,SAAS;AACb,QAAI;AAAA;AAAA,MAA8B,SAAU,QAAQ;AAChD,kBAAUE,eAAc,MAAM;AAC9B,iBAASA,cAAa,MAAM;AACxB,cAAI,QAAQ,OAAO,KAAK,MAAM,2BAA2B,OAAO,IAAI,CAAC,KAAK;AAC1E,gBAAM,OAAO;AACb,gBAAM,OAAO;AACb,iBAAO;AAAA,QACX;AACA,eAAOA;AAAA,MACX,EAAE,KAAK;AAAA;AACP,YAAQ,eAAe;AACvB,QAAI;AAAA;AAAA,MAA2B,SAAU,QAAQ;AAC7C,kBAAUC,YAAW,MAAM;AAC3B,iBAASA,WAAU,QAAQ,UAAU;AACjC,cAAI,QAAQ,OAAO,KAAK,MAAM,gBAAgB,OAAO,QAAQ,MAAM,EAAE,OAAO,QAAQ,CAAC,KAAK;AAC1F,gBAAM,SAAS;AACf,gBAAM,WAAW;AACjB,gBAAM,OAAO;AACb,iBAAO;AAAA,QACX;AACA,eAAOA;AAAA,MACX,EAAE,KAAK;AAAA;AACP,YAAQ,YAAY;AACpB,aAAS,KAAK,QAAQ,KAAK,OAAO;AAC9B,UAAI,MAAM,QAAQ,MAAM,GAAG;AAEvB,YAAI,OAAO,KAAK;AACZ,iBAAO,KAAK,KAAK;AAAA,QACrB,OACK;AACD,cAAI,QAAQ,SAAS,KAAK,EAAE;AAC5B,iBAAO,OAAO,OAAO,GAAG,KAAK;AAAA,QACjC;AAAA,MACJ,OACK;AACD,eAAO,GAAG,IAAI;AAAA,MAClB;AAAA,IACJ;AACA,aAAS,QAAQ,QAAQ,KAAK;AAC1B,UAAI,MAAM,QAAQ,MAAM,GAAG;AAEvB,YAAI,QAAQ,SAAS,KAAK,EAAE;AAC5B,eAAO,OAAO,OAAO,CAAC;AAAA,MAC1B,OACK;AAED,eAAO,OAAO,GAAG;AAAA,MACrB;AAAA,IACJ;AASA,aAAS,IAAI,QAAQ,WAAW;AAC5B,UAAI,WAAW,UAAU,QAAQ,SAAS,UAAU,IAAI,EAAE,SAAS,MAAM;AAEzE,UAAI,SAAS,WAAW,QAAW;AAC/B,eAAO,IAAI,aAAa,UAAU,IAAI;AAAA,MAC1C;AACA,WAAK,SAAS,QAAQ,SAAS,MAAM,GAAG,OAAO,OAAO,UAAU,KAAK,CAAC;AACtE,aAAO;AAAA,IACX;AACA,YAAQ,MAAM;AAKd,aAAS,OAAO,QAAQ,WAAW;AAE/B,UAAI,WAAW,UAAU,QAAQ,SAAS,UAAU,IAAI,EAAE,SAAS,MAAM;AACzE,UAAI,SAAS,UAAU,QAAW;AAC9B,eAAO,IAAI,aAAa,UAAU,IAAI;AAAA,MAC1C;AAEA,cAAQ,SAAS,QAAQ,SAAS,GAAG;AACrC,aAAO;AAAA,IACX;AACA,YAAQ,SAAS;AAajB,aAAS,QAAQ,QAAQ,WAAW;AAChC,UAAI,WAAW,UAAU,QAAQ,SAAS,UAAU,IAAI,EAAE,SAAS,MAAM;AACzE,UAAI,SAAS,WAAW,MAAM;AAC1B,eAAO,IAAI,aAAa,UAAU,IAAI;AAAA,MAC1C;AAEA,UAAI,MAAM,QAAQ,SAAS,MAAM,GAAG;AAChC,YAAI,SAAS,SAAS,KAAK,EAAE,KAAK,SAAS,OAAO,QAAQ;AACtD,iBAAO,IAAI,aAAa,UAAU,IAAI;AAAA,QAC1C;AAAA,MACJ,WACS,SAAS,UAAU,QAAW;AACnC,eAAO,IAAI,aAAa,UAAU,IAAI;AAAA,MAC1C;AACA,eAAS,OAAO,SAAS,GAAG,KAAK,GAAG,OAAO,OAAO,UAAU,KAAK;AACjE,aAAO;AAAA,IACX;AACA,YAAQ,UAAU;AAgBlB,aAAS,KAAK,QAAQ,WAAW;AAC7B,UAAI,gBAAgB,UAAU,QAAQ,SAAS,UAAU,IAAI,EAAE,SAAS,MAAM;AAC9E,UAAI,cAAc,UAAU,QAAW;AACnC,eAAO,IAAI,aAAa,UAAU,IAAI;AAAA,MAC1C;AACA,UAAI,WAAW,UAAU,QAAQ,SAAS,UAAU,IAAI,EAAE,SAAS,MAAM;AACzE,UAAI,SAAS,WAAW,QAAW;AAC/B,eAAO,IAAI,aAAa,UAAU,IAAI;AAAA,MAC1C;AACA,cAAQ,cAAc,QAAQ,cAAc,GAAG;AAC/C,WAAK,SAAS,QAAQ,SAAS,KAAK,cAAc,KAAK;AACvD,aAAO;AAAA,IACX;AACA,YAAQ,OAAO;AAcf,aAASC,MAAK,QAAQ,WAAW;AAC7B,UAAI,gBAAgB,UAAU,QAAQ,SAAS,UAAU,IAAI,EAAE,SAAS,MAAM;AAC9E,UAAI,cAAc,UAAU,QAAW;AACnC,eAAO,IAAI,aAAa,UAAU,IAAI;AAAA,MAC1C;AACA,UAAI,WAAW,UAAU,QAAQ,SAAS,UAAU,IAAI,EAAE,SAAS,MAAM;AACzE,UAAI,SAAS,WAAW,QAAW;AAC/B,eAAO,IAAI,aAAa,UAAU,IAAI;AAAA,MAC1C;AACA,WAAK,SAAS,QAAQ,SAAS,MAAM,GAAG,OAAO,OAAO,cAAc,KAAK,CAAC;AAC1E,aAAO;AAAA,IACX;AACA,YAAQ,OAAOA;AASf,aAAS,KAAK,QAAQ,WAAW;AAC7B,UAAI,WAAW,UAAU,QAAQ,SAAS,UAAU,IAAI,EAAE,SAAS,MAAM;AAEzE,WAAK,GAAG,OAAO,SAAS,SAAS,OAAO,UAAU,OAAO,IAAI,UAAU,QAAQ,CAAC,EAAE,QAAQ;AACtF,eAAO,IAAI,UAAU,SAAS,OAAO,UAAU,KAAK;AAAA,MACxD;AACA,aAAO;AAAA,IACX;AACA,YAAQ,OAAO;AACf,QAAI;AAAA;AAAA,MAAuC,SAAU,QAAQ;AACzD,kBAAUC,wBAAuB,MAAM;AACvC,iBAASA,uBAAsB,WAAW;AACtC,cAAI,QAAQ,OAAO,KAAK,MAAM,sBAAsB,OAAO,UAAU,EAAE,CAAC,KAAK;AAC7E,gBAAM,YAAY;AAClB,gBAAM,OAAO;AACb,iBAAO;AAAA,QACX;AACA,eAAOA;AAAA,MACX,EAAE,KAAK;AAAA;AACP,YAAQ,wBAAwB;AAKhC,aAAS,MAAM,QAAQ,WAAW;AAI9B,cAAQ,UAAU,IAAI;AAAA,QAClB,KAAK;AAAO,iBAAO,IAAI,QAAQ,SAAS;AAAA,QACxC,KAAK;AAAU,iBAAO,OAAO,QAAQ,SAAS;AAAA,QAC9C,KAAK;AAAW,iBAAO,QAAQ,QAAQ,SAAS;AAAA,QAChD,KAAK;AAAQ,iBAAO,KAAK,QAAQ,SAAS;AAAA,QAC1C,KAAK;AAAQ,iBAAOD,MAAK,QAAQ,SAAS;AAAA,QAC1C,KAAK;AAAQ,iBAAO,KAAK,QAAQ,SAAS;AAAA,MAC9C;AACA,aAAO,IAAI,sBAAsB,SAAS;AAAA,IAC9C;AACA,YAAQ,QAAQ;AAAA;AAAA;;;AC1OhB;AAAA;AAAA;AACA,WAAO,eAAe,SAAS,cAAc,EAAE,OAAO,KAAK,CAAC;AAC5D,YAAQ,cAAc,QAAQ,cAAc,QAAQ,aAAa,QAAQ,UAAU;AACnF,QAAI,YAAY;AAChB,WAAO,eAAe,SAAS,WAAW,EAAE,YAAY,MAAM,KAAK,WAAY;AAAE,aAAO,UAAU;AAAA,IAAS,EAAE,CAAC;AAC9G,QAAI,UAAU;AACd,QAAI,SAAS;AAiBb,aAASE,YAAW,QAAQC,QAAO;AAC/B,aAAOA,OAAM,IAAI,SAAU,WAAW;AAAE,gBAAQ,GAAG,QAAQ,OAAO,QAAQ,SAAS;AAAA,MAAG,CAAC;AAAA,IAC3F;AACA,YAAQ,aAAaD;AACrB,aAAS,iBAAiB,MAAM;AAC5B,eAAS,YAAY,OAAO,QAAQ,KAAK;AACrC,YAAI,eAAe,KAAK,OAAO,QAAQ,GAAG;AAE1C,eAAO,MAAM,QAAQ,YAAY,IAAI,gBAAgB,GAAG,OAAO,SAAS,OAAO,QAAQ,KAAK,WAAW;AAAA,MAC3G;AACA,aAAO;AAAA,IACX;AAcA,aAAS,YAAY,OAAO,QAAQ,MAAM;AACtC,UAAI,MAAM,IAAI,UAAU,QAAQ;AAEhC,cAAQ,OAAO,iBAAiB,IAAI,IAAI,OAAO,SAAS,OAAO,QAAQ,GAAG;AAAA,IAC9E;AACA,YAAQ,cAAc;AAKtB,aAAS,WAAW,OAAO,MAAM;AAC7B,UAAI,WAAW,UAAU,QAAQ,SAAS,IAAI,EAAE,SAAS,KAAK;AAC9D,UAAI,aAAa,QAAW;AACxB,eAAO,EAAE,IAAI,QAAQ,MAAY,OAAO,SAAS,MAAM;AAAA,MAC3D;AAAA,IACJ;AAWA,aAAS,YAAY,OAAOC,QAAO;AAC/B,UAAI,QAAQ,IAAI,MAAM;AACtB,MAAAA,OAAM,OAAO,OAAO,aAAa,EAAE,QAAQ,SAAU,WAAW;AAC5D,YAAI,WAAW,WAAW,OAAO,UAAU,IAAI;AAC/C,YAAI;AACA,gBAAM,KAAK,QAAQ;AACvB,YAAI,UAAU,WAAW;AACrB,cAAI,WAAW,WAAW,OAAO,UAAU,IAAI;AAC/C,cAAI;AACA,kBAAM,KAAK,QAAQ;AAAA,QAC3B;AAAA,MACJ,CAAC;AACD,aAAO;AAAA,IACX;AACA,YAAQ,cAAc;AAAA;AAAA;;;ACxFP,SAAR,QAAyBC,IAAG;AACjC;AAEA,SAAO,UAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUA,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAG,QAAQA,EAAC;AACd;;;ACPe,SAAR,YAA6BC,IAAGC,IAAG;AACxC,MAAI,YAAY,QAAQD,EAAC,KAAK,CAACA;AAAG,WAAOA;AACzC,MAAI,IAAIA,GAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAIE,KAAI,EAAE,KAAKF,IAAGC,MAAK,SAAS;AAChC,QAAI,YAAY,QAAQC,EAAC;AAAG,aAAOA;AACnC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAaD,KAAI,SAAS,QAAQD,EAAC;AAC7C;;;ACRe,SAAR,cAA+BG,IAAG;AACvC,MAAIC,KAAI,YAAYD,IAAG,QAAQ;AAC/B,SAAO,YAAY,QAAQC,EAAC,IAAIA,KAAI,OAAOA,EAAC;AAC9C;;;ACJe,SAAR,gBAAiC,KAAK,KAAK,OAAO;AACvD,QAAM,cAAc,GAAG;AACvB,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AACA,SAAO;AACT;;;ACbA,SAAS,QAAQ,GAAGC,IAAG;AACrB,MAAIC,KAAI,OAAO,KAAK,CAAC;AACrB,MAAI,OAAO,uBAAuB;AAChC,QAAIC,KAAI,OAAO,sBAAsB,CAAC;AACtC,IAAAF,OAAME,KAAIA,GAAE,OAAO,SAAUF,IAAG;AAC9B,aAAO,OAAO,yBAAyB,GAAGA,EAAC,EAAE;AAAA,IAC/C,CAAC,IAAIC,GAAE,KAAK,MAAMA,IAAGC,EAAC;AAAA,EACxB;AACA,SAAOD;AACT;AACe,SAAR,eAAgC,GAAG;AACxC,WAASD,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,QAAIC,KAAI,QAAQ,UAAUD,EAAC,IAAI,UAAUA,EAAC,IAAI,CAAC;AAC/C,IAAAA,KAAI,IAAI,QAAQ,OAAOC,EAAC,GAAG,IAAE,EAAE,QAAQ,SAAUD,IAAG;AAClD,sBAAe,GAAGA,IAAGC,GAAED,EAAC,CAAC;AAAA,IAC3B,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,GAAG,OAAO,0BAA0BC,EAAC,CAAC,IAAI,QAAQ,OAAOA,EAAC,CAAC,EAAE,QAAQ,SAAUD,IAAG;AAChJ,aAAO,eAAe,GAAGA,IAAG,OAAO,yBAAyBC,IAAGD,EAAC,CAAC;AAAA,IACnE,CAAC;AAAA,EACH;AACA,SAAO;AACT;;;ACPA,IAAI,eAAgB,WAAY;AAC9B,SAAO,OAAO,WAAW,cAAc,OAAO,cAAc;AAC9D,EAAG;AAQH,IAAI,eAAe,SAASG,gBAAe;AACzC,SAAO,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC,EAAE,MAAM,EAAE,EAAE,KAAK,GAAG;AACnE;AAEA,IAAI,cAAc;AAAA,EAChB,MAAM,iBAAiB,aAAa;AAAA,EACpC,SAAS,oBAAoB,aAAa;AAAA,EAC1C,sBAAsB,SAAS,uBAAuB;AACpD,WAAO,iCAAiC,aAAa;AAAA,EACvD;AACF;AAMA,SAAS,cAAc,KAAK;AAC1B,MAAI,OAAO,QAAQ,YAAY,QAAQ;AAAM,WAAO;AACpD,MAAI,QAAQ;AAEZ,SAAO,OAAO,eAAe,KAAK,MAAM,MAAM;AAC5C,YAAQ,OAAO,eAAe,KAAK;AAAA,EACrC;AAEA,SAAO,OAAO,eAAe,GAAG,MAAM;AACxC;AAGA,SAAS,WAAW,KAAK;AACvB,MAAI,QAAQ;AAAQ,WAAO;AAC3B,MAAI,QAAQ;AAAM,WAAO;AACzB,MAAI,OAAO,OAAO;AAElB,UAAQ,MAAM;AAAA,IACZ,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,YACH;AACE,aAAO;AAAA,IACT;AAAA,EACJ;AAEA,MAAI,MAAM,QAAQ,GAAG;AAAG,WAAO;AAC/B,MAAI,OAAO,GAAG;AAAG,WAAO;AACxB,MAAI,QAAQ,GAAG;AAAG,WAAO;AACzB,MAAI,kBAAkB,SAAS,GAAG;AAElC,UAAQ,iBAAiB;AAAA,IACvB,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,EACX;AAGA,SAAO,KAAK,MAAM,GAAG,EAAE,EAAE,YAAY,EAAE,QAAQ,OAAO,EAAE;AAC1D;AAEA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,IAAI,gBAAgB,aAAa,IAAI,YAAY,OAAO;AACxE;AAEA,SAAS,QAAQ,KAAK;AACpB,SAAO,eAAe,SAAS,OAAO,IAAI,YAAY,YAAY,IAAI,eAAe,OAAO,IAAI,YAAY,oBAAoB;AAClI;AAEA,SAAS,OAAO,KAAK;AACnB,MAAI,eAAe;AAAM,WAAO;AAChC,SAAO,OAAO,IAAI,iBAAiB,cAAc,OAAO,IAAI,YAAY,cAAc,OAAO,IAAI,YAAY;AAC/G;AAEA,SAAS,OAAO,KAAK;AACnB,MAAI,YAAY,OAAO;AAEvB,MAAI,MAAuC;AACzC,gBAAY,WAAW,GAAG;AAAA,EAC5B;AAEA,SAAO;AACT;AA4BA,SAAS,YAAY,SAAS,gBAAgB,UAAU;AACtD,MAAI;AAEJ,MAAI,OAAO,mBAAmB,cAAc,OAAO,aAAa,cAAc,OAAO,aAAa,cAAc,OAAO,UAAU,CAAC,MAAM,YAAY;AAClJ,UAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,kQAA4Q;AAAA,EAClW;AAEA,MAAI,OAAO,mBAAmB,cAAc,OAAO,aAAa,aAAa;AAC3E,eAAW;AACX,qBAAiB;AAAA,EACnB;AAEA,MAAI,OAAO,aAAa,aAAa;AACnC,QAAI,OAAO,aAAa,YAAY;AAClC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,iEAAiE,OAAO,QAAQ,IAAI,GAAG;AAAA,IAC7K;AAEA,WAAO,SAAS,WAAW,EAAE,SAAS,cAAc;AAAA,EACtD;AAEA,MAAI,OAAO,YAAY,YAAY;AACjC,UAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,qEAAqE,OAAO,OAAO,IAAI,GAAG;AAAA,EAChL;AAEA,MAAI,iBAAiB;AACrB,MAAI,eAAe;AACnB,MAAI,mBAAmB,CAAC;AACxB,MAAI,gBAAgB;AACpB,MAAI,gBAAgB;AASpB,WAAS,+BAA+B;AACtC,QAAI,kBAAkB,kBAAkB;AACtC,sBAAgB,iBAAiB,MAAM;AAAA,IACzC;AAAA,EACF;AAQA,WAAS,WAAW;AAClB,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,sMAAgN;AAAA,IACtS;AAEA,WAAO;AAAA,EACT;AA0BA,WAAS,UAAU,UAAU;AAC3B,QAAI,OAAO,aAAa,YAAY;AAClC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,iEAAiE,OAAO,QAAQ,IAAI,GAAG;AAAA,IAC7K;AAEA,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,iTAAgU;AAAA,IACtZ;AAEA,QAAI,eAAe;AACnB,iCAA6B;AAC7B,kBAAc,KAAK,QAAQ;AAC3B,WAAO,SAAS,cAAc;AAC5B,UAAI,CAAC,cAAc;AACjB;AAAA,MACF;AAEA,UAAI,eAAe;AACjB,cAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,sJAA2J;AAAA,MACjP;AAEA,qBAAe;AACf,mCAA6B;AAC7B,UAAI,QAAQ,cAAc,QAAQ,QAAQ;AAC1C,oBAAc,OAAO,OAAO,CAAC;AAC7B,yBAAmB;AAAA,IACrB;AAAA,EACF;AA4BA,WAAS,SAAS,QAAQ;AACxB,QAAI,CAAC,cAAc,MAAM,GAAG;AAC1B,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,mEAAmE,OAAO,MAAM,IAAI,4UAA4U;AAAA,IACtf;AAEA,QAAI,OAAO,OAAO,SAAS,aAAa;AACtC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,4GAA4G;AAAA,IAClM;AAEA,QAAI,eAAe;AACjB,YAAM,IAAI,MAAM,QAAwC,uBAAuB,CAAC,IAAI,oCAAoC;AAAA,IAC1H;AAEA,QAAI;AACF,sBAAgB;AAChB,qBAAe,eAAe,cAAc,MAAM;AAAA,IACpD,UAAE;AACA,sBAAgB;AAAA,IAClB;AAEA,QAAI,YAAY,mBAAmB;AAEnC,aAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,UAAI,WAAW,UAAUA,EAAC;AAC1B,eAAS;AAAA,IACX;AAEA,WAAO;AAAA,EACT;AAaA,WAAS,eAAe,aAAa;AACnC,QAAI,OAAO,gBAAgB,YAAY;AACrC,YAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,oEAAoE,OAAO,WAAW,CAAC;AAAA,IAC9K;AAEA,qBAAiB;AAKjB,aAAS;AAAA,MACP,MAAM,YAAY;AAAA,IACpB,CAAC;AAAA,EACH;AASA,WAAS,aAAa;AACpB,QAAI;AAEJ,QAAI,iBAAiB;AACrB,WAAO,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASZ,WAAW,SAASC,WAAU,UAAU;AACtC,YAAI,OAAO,aAAa,YAAY,aAAa,MAAM;AACrD,gBAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,gEAAgE,OAAO,QAAQ,IAAI,GAAG;AAAA,QAC7K;AAEA,iBAAS,eAAe;AACtB,cAAI,SAAS,MAAM;AACjB,qBAAS,KAAK,SAAS,CAAC;AAAA,UAC1B;AAAA,QACF;AAEA,qBAAa;AACb,YAAI,cAAc,eAAe,YAAY;AAC7C,eAAO;AAAA,UACL;AAAA,QACF;AAAA,MACF;AAAA,IACF,GAAG,KAAK,YAAY,IAAI,WAAY;AAClC,aAAO;AAAA,IACT,GAAG;AAAA,EACL;AAKA,WAAS;AAAA,IACP,MAAM,YAAY;AAAA,EACpB,CAAC;AACD,SAAO,QAAQ;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,MAAM,YAAY,IAAI,YAAY;AACvC;AA6PA,SAAS,UAAU;AACjB,WAAS,OAAO,UAAU,QAAQ,QAAQ,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACxF,UAAM,IAAI,IAAI,UAAU,IAAI;AAAA,EAC9B;AAEA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,SAAU,KAAK;AACpB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,MAAM,WAAW,GAAG;AACtB,WAAO,MAAM,CAAC;AAAA,EAChB;AAEA,SAAO,MAAM,OAAO,SAAUC,IAAGC,IAAG;AAClC,WAAO,WAAY;AACjB,aAAOD,GAAEC,GAAE,MAAM,QAAQ,SAAS,CAAC;AAAA,IACrC;AAAA,EACF,CAAC;AACH;AAmBA,SAAS,kBAAkB;AACzB,WAAS,OAAO,UAAU,QAAQ,cAAc,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC9F,gBAAY,IAAI,IAAI,UAAU,IAAI;AAAA,EACpC;AAEA,SAAO,SAAUC,cAAa;AAC5B,WAAO,WAAY;AACjB,UAAI,QAAQA,aAAY,MAAM,QAAQ,SAAS;AAE/C,UAAI,YAAY,SAAS,WAAW;AAClC,cAAM,IAAI,MAAM,QAAwC,uBAAuB,EAAE,IAAI,wHAA6H;AAAA,MACpN;AAEA,UAAI,gBAAgB;AAAA,QAClB,UAAU,MAAM;AAAA,QAChB,UAAU,SAAS,WAAW;AAC5B,iBAAO,UAAU,MAAM,QAAQ,SAAS;AAAA,QAC1C;AAAA,MACF;AACA,UAAI,QAAQ,YAAY,IAAI,SAAU,YAAY;AAChD,eAAO,WAAW,aAAa;AAAA,MACjC,CAAC;AACD,kBAAY,QAAQ,MAAM,QAAQ,KAAK,EAAE,MAAM,QAAQ;AACvD,aAAO,eAAc,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACjD,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;SClpBgBC,EAAIC,IAAAA;AAAAA,WAAAA,KAAAA,UAAAA,QAA+BC,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,IAAAA,GAAAA,IAAAA,IAAAA;AAAAA,IAAAA,GAAAA,IAAAA,CAAAA,IAAAA,UAAAA,CAAAA;AAAAA,MAAAA,MACrC;AAAA,QACNC,KAAIC,EAAOH,EAAAA,GACXI,KAAOF,KAEG,cAAA,OAANA,KACPA,GAAEG,MAAM,MAAMJ,EAAAA,IACdC,KAHA,uBAAuBF;AAAAA,UAIhBM,MAAAA,aAAiBF,EAAAA;EAAAA;AAAAA,QAElBE,MAAAA,gCACqBN,MAC7BC,GAAKM,SAAS,MAAMN,GAAKO,IAAI,SAAAC,IAAAA;AAAAA,WAAAA,MAASA,KAAAA;EAAAA,CAAAA,EAAMC,KAAK,GAAA,IAAO,MAAA,kDAAA;AAAA;AAAA,SCvC3CC,EAAQC,IAAAA;AAAAA,SAAAA,CAAAA,CACdA,MAAAA,CAAAA,CAAWA,GAAMC,CAAAA;AAAAA;AAAAA,SAKXC,EAAYF,IAAAA;AAAAA,MAAAA;AAAAA,SAAAA,CAAAA,CACtBA,OAAAA,SAawBA,IAAAA;AAAAA,QAAAA,CACxBA,MAA0B,YAAA,OAAVA;AAAoB,aAAA;AAAO,QAC1CG,KAAQC,OAAOC,eAAeL,EAAAA;AAAAA,QACtB,SAAVG;AAAAA,aAAAA;AACI,QAEFG,KACLF,OAAOG,eAAeC,KAAKL,IAAO,aAAA,KAAkBA,GAAMM;AAAAA,WAEvDH,OAASF,UAGG,cAAA,OAARE,MACPI,SAASC,SAASH,KAAKF,EAAAA,MAAUM;EAAAA,EAxBnBZ,EAAAA,KACda,MAAMC,QAAQd,EAAAA,KAAAA,CAAAA,CACZA,GAAMe,CAAAA,KAAAA,CAAAA,EAAAA,UAAAA,KACNf,GAAMS,gBAAAA,WAAAA,KAAAA,SAANO,GAAoBD,CAAAA,MACtBE,EAAMjB,EAAAA,KACNkB,EAAMlB,EAAAA;AAAAA;AA0DR,SAAgBmB,EAAKC,IAAUC,IAAWC,IAAAA;AAAAA,aAAAA,OAAAA,KAAAA,QAAiB,MACtDC,EAAYH,EAAAA,KACbE,KAAiBE,OAAOC,OAAOC,IAASN,EAAAA,EAAKO,QAAQ,SAAAC,GAAAA;AACjDN,IAAAA,MAAiC,YAAA,OAARM,KAAkBP,GAAKO,GAAKR,GAAIQ,CAAAA,GAAMR,EAAAA;EAAAA,CAAAA,IAGrEA,GAAIO,QAAQ,SAACE,IAAYC,GAAAA;AAAAA,WAAeT,GAAKS,GAAOD,IAAOT,EAAAA;EAAAA,CAAAA;AAAAA;AAAAA,SAK7CG,EAAYQ,IAAAA;AAAAA,MAErBC,KAAgCD,GAAME,CAAAA;AAAAA,SACrCD,KACJA,GAAME,IAAQ,IACbF,GAAME,IAAQ,IACbF,GAAME,IACRC,MAAMC,QAAQL,EAAAA,IAAAA,IAEdM,EAAMN,EAAAA,IAAAA,IAENO,EAAMP,EAAAA,IAAAA,IAAAA;AAAAA;AAAAA,SAMMQ,EAAIR,IAAYS,IAAAA;AAAAA,SAAAA,MACxBjB,EAAYQ,EAAAA,IAChBA,GAAMQ,IAAIC,EAAAA,IACVhB,OAAOiB,UAAUC,eAAeC,KAAKZ,IAAOS,EAAAA;AAAAA;AAAAA,SAIhCI,EAAIb,IAA2BS,IAAAA;AAAAA,SAAAA,MAEvCjB,EAAYQ,EAAAA,IAA0BA,GAAMa,IAAIJ,EAAAA,IAAQT,GAAMS,EAAAA;AAAAA;AAItE,SAAgBK,EAAId,IAAYe,IAA6BC,IAAAA;AAAAA,MACtDC,IAAIzB,EAAYQ,EAAAA;AAAAA,QAClBiB,IAAoBjB,GAAMc,IAAIC,IAAgBC,EAAAA,IAAAA,MACzCC,IACRjB,GAAMkB,IAAIF,EAAAA,IACJhB,GAAMe,EAAAA,IAAkBC;AAAAA;AAAAA,SAIhBG,EAAGC,IAAQC,IAAAA;AAAAA,SAEtBD,OAAMC,KACI,MAAND,MAAW,IAAIA,MAAM,IAAIC,KAEzBD,MAAMA,MAAKC,MAAMA;AAAAA;AAAAA,SAKVf,EAAMgB,IAAAA;AAAAA,SACdC,KAAUD,cAAkBE;AAAAA;AAAAA,SAIpBjB,EAAMe,IAAAA;AAAAA,SACdG,KAAUH,cAAkBI;AAAAA;AAAAA,SAGpBC,EAAO1B,IAAAA;AAAAA,SACfA,GAAM2B,KAAS3B,GAAM4B;AAAAA;AAAAA,SAIbC,EAAYC,IAAAA;AAAAA,MACvB3B,MAAMC,QAAQ0B,EAAAA;AAAO,WAAO3B,MAAMM,UAAUsB,MAAMpB,KAAKmB,EAAAA;AAAAA,MACrDE,KAAcC,GAA0BH,EAAAA;AAAAA,SACvCE,GAAY/B,CAAAA;AAAAA,WACfR,KAAOC,GAAQsC,EAAAA,GACVE,IAAI,GAAGA,IAAIzC,GAAK0C,QAAQD,KAAK;AAAA,QAC/BtC,KAAWH,GAAKyC,CAAAA,GAChBE,KAAOJ,GAAYpC,EAAAA;AAAAA,cACrBwC,GAAKC,aACRD,GAAKC,WAAAA,MACLD,GAAKE,eAAAA,QAKFF,GAAKxB,OAAOwB,GAAKvB,SACpBmB,GAAYpC,EAAAA,IAAO,EAClB0C,cAAAA,MACAD,UAAAA,MACAE,YAAYH,GAAKG,YACjBxB,OAAOe,GAAKlC,EAAAA,EAAAA;EAAAA;AAAAA,SAGRJ,OAAOgD,OAAOhD,OAAOiD,eAAeX,EAAAA,GAAOE,EAAAA;AAAAA;AAAAA,SAWnCU,EAAUtD,IAAUuD,GAAAA;AAAAA,SAAAA,WAAAA,MAAAA,IAAAA,QAC/BC,EAASxD,EAAAA,KAAQyD,EAAQzD,EAAAA,KAAAA,CAAS0D,EAAY1D,EAAAA,MAC9CG,EAAYH,EAAAA,IAAO,MACtBA,GAAIyB,MAAMzB,GAAI6B,MAAM7B,GAAI2D,QAAQ3D,GAAI4D,SAASC,IAE9CzD,OAAOkD,OAAOtD,EAAAA,GACVuD,KAAMxD,EAAKC,IAAK,SAACQ,IAAKmB,IAAAA;AAAAA,WAAU2B,EAAO3B,IAAAA,IAAO;EAAA,GAAA,IAAO,IALM3B;AAAAA;AAShE,SAAS6D,IAAAA;AACRC,IAAI,CAAA;AAAA;AAAA,SAGWN,EAASxD,IAAAA;AAAAA,SACb,QAAPA,MAA8B,YAAA,OAARA,MAEnBI,OAAOoD,SAASxD,EAAAA;AAAAA;AAAAA,SCxKR+D,EACfC,IAAAA;AAAAA,MAEMC,KAASC,GAAQF,EAAAA;AAAAA,SAClBC,MACJH,EAAI,IAAIE,EAAAA,GAGFC;AAAAA;AC3BR,SAAgBE,IAAAA;AAAAA,SACCC,KAAcC,EAAI,CAAA,GAC3BD;AAAAA;AAAAA,SAkBQE,EACfC,IACAC,IAAAA;AAEIA,EAAAA,OACHC,EAAU,SAAA,GACVF,GAAMG,IAAW,CAAA,GACjBH,GAAMI,IAAkB,CAAA,GACxBJ,GAAMK,IAAiBJ;AAAAA;AAAAA,SAITK,EAAYN,IAAAA;AAC3BO,IAAWP,EAAAA,GACXA,GAAMQ,EAAQC,QAAQC,CAAAA,GAEtBV,GAAMQ,IAAU;AAAA;AAAA,SAGDD,EAAWP,IAAAA;AACtBA,EAAAA,OAAUH,MACbA,IAAeG,GAAMW;AAAAA;AAAAA,SAIPC,EAAWC,IAAAA;AAAAA,SAClBhB,IArCD,EACNW,GAAS,CAAA,GACTG,GAmCkCd,GAlClCiB,GAkCgDD,IA/BhDE,GAAAA,MACAC,GAAoB,EAAA;AAAA;AAiCtB,SAASN,EAAYO,IAAAA;AAAAA,MACdC,KAAoBD,GAAME,CAAAA;AAAAA,QAE/BD,GAAME,KAAAA,MACNF,GAAME,IAENF,GAAMG,EAAAA,IACFH,GAAMI,IAAAA;AAAW;AAAA,SC9DPC,EAAcC,IAAaxB,GAAAA;AAC1CA,IAAMgB,IAAqBhB,EAAMQ,EAAQiB;AAAAA,MACnCC,KAAY1B,EAAMQ,EAAS,CAAA,GAC3BmB,KAAAA,WAAaH,MAAwBA,OAAWE;AAAAA,SACjD1B,EAAMc,EAAOc,KACjB1B,EAAU,KAAA,EAAO2B,EAAiB7B,GAAOwB,IAAQG,EAAAA,GAC9CA,MACCD,GAAUP,CAAAA,EAAaW,MAC1BxB,EAAYN,CAAAA,GACZF,EAAI,CAAA,IAEDiC,EAAYP,EAAAA,MAEfA,KAASQ,EAAShC,GAAOwB,EAAAA,GACpBxB,EAAMW,KAASsB,EAAYjC,GAAOwB,EAAAA,IAEpCxB,EAAMG,KACTD,EAAU,SAAA,EAAWgC,EACpBR,GAAUP,CAAAA,EAAagB,GACvBX,IACAxB,EAAMG,GACNH,EAAMI,CAAAA,KAKRoB,KAASQ,EAAShC,GAAO0B,IAAW,CAAA,CAAA,GAErCpB,EAAYN,CAAAA,GACRA,EAAMG,KACTH,EAAMK,EAAgBL,EAAMG,GAAUH,EAAMI,CAAAA,GAEtCoB,OAAWY,IAAUZ,KAAAA;AAASa;AAGtC,SAASL,EAASM,IAAuBC,IAAYC,IAAAA;AAAAA,MAEhDC,EAASF,EAAAA;AAAQ,WAAOA;AAAAA,MAEtBrB,IAAoBqB,GAAMpB,CAAAA;AAAAA,MAAAA,CAE3BD;AAAAA,WACJwB,EACCH,IACA,SAACI,IAAKC,IAAAA;AAAAA,aACLC,EAAiBP,IAAWpB,GAAOqB,IAAOI,IAAKC,IAAYJ,EAAAA;IAAAA,GAAAA,IAC5D,GAEMD;AAAAA,MAGJrB,EAAM4B,MAAWR;AAAW,WAAOC;AAAAA,MAAAA,CAElCrB,EAAMY;AAAAA,WACVG,EAAYK,IAAWpB,EAAMiB,GAAAA,IAAO,GAC7BjB,EAAMiB;AAAAA,MAAAA,CAGTjB,EAAM6B,GAAY;AACtB7B,MAAM6B,IAAAA,MACN7B,EAAM4B,EAAO9B;AAAAA,QACPQ,KAAAA,MAELN,EAAME,KAAAA,MAAiCF,EAAME,IACzCF,EAAM8B,IAAQC,EAAY/B,EAAMgC,CAAAA,IACjChC,EAAM8B,GAKNG,KAAa3B,IACb4B,KAAAA;AAAQ,UACRlC,EAAME,MACT+B,KAAa,IAAIE,IAAI7B,EAAAA,GACrBA,GAAO8B,MAAAA,GACPF,KAAAA,OAEDV,EAAKS,IAAY,SAACR,IAAKC,IAAAA;AAAAA,aACtBC,EAAiBP,IAAWpB,GAAOM,IAAQmB,IAAKC,IAAYJ,IAAMY,EAAAA;IAAAA,CAAAA,GAGnEnB,EAAYK,IAAWd,IAAAA,KAAQ,GAE3BgB,MAAQF,GAAUnC,KACrBD,EAAU,SAAA,EAAWqD,EACpBrC,GACAsB,IACAF,GAAUnC,GACVmC,GAAUlC,CAAAA;EAAAA;AAAAA,SAINc,EAAM8B;AAAAA;AAGd,SAASH,EACRP,GACAkB,IACAC,IACAC,IACAd,IACAe,IACAC,IAAAA;AAAAA,MAEehB,OAAea,MAAc3D,EAAI,CAAA,GAC5C+D,EAAQjB,EAAAA,GAAa;AAAA,QASlBkB,KAAM9B,EAASM,GAAWM,IAP/Be,MACAH,MAAAA,MACAA,GAAapC,KAAAA,CACZ2C,EAAKP,GAA8CQ,GAAYN,EAAAA,IAC7DC,GAAUM,OAAOP,EAAAA,IAAAA,MACjBrB;AAAAA,QAGJ6B,EAAIT,IAAcC,IAAMI,EAAAA,GAAAA,CAGpBD,EAAQC,EAAAA;AAEL;AADNxB,MAAUvB,IAAAA;EAAiB;AAElB6C,IAAAA,MACVH,GAAaU,IAAIvB,EAAAA;AAAAA,MAGdb,EAAYa,EAAAA,KAAAA,CAAgBH,EAASG,EAAAA,GAAa;AAAA,QAAA,CAChDN,EAAUxB,EAAOsD,KAAe9B,EAAUtB,IAAqB;AAAA;AAQpEgB,MAASM,GAAWM,EAAAA,GAEfY,MAAgBA,GAAYV,EAAOnC,KACvCsB,EAAYK,GAAWM,EAAAA;EAAAA;AAAAA;AAI1B,SAASX,EAAYjC,IAAmBuC,IAAY8B,IAAAA;AAAAA,aAAAA,OAAAA,KAAAA,QAAO,CAErDrE,GAAMW,KAAWX,GAAMc,EAAOsD,KAAepE,GAAMe,KACvDuD,EAAO/B,IAAO8B,EAAAA;AAAAA;ACqEhB,SAASE,EAAKtD,IAAgByC,IAAAA;AAAAA,MACvBxC,KAAQD,GAAME,CAAAA;AAAAA,UACLD,KAAQsD,EAAOtD,EAAAA,IAASD,IACzByC,EAAAA;AAAAA;AAcf,SAASe,EACRC,IACAhB,IAAAA;AAAAA,MAGMA,MAAQgB;AAAAA,aACVC,KAAQC,OAAOC,eAAeH,EAAAA,GAC3BC,MAAO;AAAA,UACPG,IAAOF,OAAOG,yBAAyBJ,IAAOjB,EAAAA;AAAAA,UAChDoB;AAAM,eAAOA;AACjBH,MAAAA,KAAQC,OAAOC,eAAeF,EAAAA;IAAAA;AAAAA;AAAAA,SAKhBK,EAAY9D,IAAAA;AACtBA,EAAAA,GAAMY,MACVZ,GAAMY,IAAAA,MACFZ,GAAMP,KACTqE,EAAY9D,GAAMP,CAAAA;AAAAA;AAAAA,SAKLsE,EAAY/D,IAAAA;AACtBA,EAAAA,GAAM8B,MACV9B,GAAM8B,IAAQC,EAAY/B,GAAMiB,CAAAA;AAAAA;ACtDlC,SAAgB+C,EACfrE,IACA0B,IACA4C,IAAAA;AAAAA,MAGMlE,IAAiBmE,EAAM7C,EAAAA,IAC1BrC,EAAU,QAAA,EAAUmF,EAAU9C,IAAO4C,EAAAA,IACrC/B,EAAMb,EAAAA,IACNrC,EAAU,QAAA,EAAUoF,EAAU/C,IAAO4C,EAAAA,IACrCtE,GAAMe,IAAAA,SDvLT2D,IACAJ,IAAAA;AAAAA,QAEMK,KAAUC,MAAMD,QAAQD,EAAAA,GACxBrE,KAAoB,EACzBE,GAAOoE,KAAAA,IAAkC,GAEzC1C,GAAQqC,KAASA,GAAOrC,IAASlD,EAAAA,GAEjCkC,GAAAA,OAEAiB,GAAAA,OAEAiB,GAAW,CAAA,GAEXrD,GAASwE,IAEThD,GAAOoD,IAEPrC,GAAQ,MAERF,GAAO,MAEP3B,GAAS,MACTqE,GAAAA,MAAW,GASRC,KAAYzE,IACZ0E,KAA2CC;AAC3CL,IAAAA,OACHG,KAAS,CAACzE,EAAAA,GACV0E,KAAQE;AAAAA,QAAAA,KAGeC,MAAMC,UAAUL,IAAQC,EAAAA,GAAzCK,KAAAA,GAAAA,QAAQC,KAAAA,GAAAA;AAAAA,WACfhF,GAAMgC,IAASgD,IACfhF,GAAMG,IAAU4E,IACTC;EAAAA,EC6Ia3D,IAAO4C,EAAAA,IACxBjF,EAAU,KAAA,EAAOiG,EAAgB5D,IAAO4C,EAAAA;AAAAA,UAE7BA,KAASA,GAAOrC,IAASlD,EAAAA,GACjCY,EAAQ4F,KAAKnF,CAAAA,GACZA;AAAAA;AAAAA,SC9NQoF,EAAQ9D,GAAAA;AAAAA,SAClBsB,EAAQtB,CAAAA,KAAQzC,EAAI,IAAIyC,CAAAA,GAI9B,SAAS+D,GAAY/D,IAAAA;AAAAA,QAAAA,CACfR,EAAYQ,EAAAA;AAAQ,aAAOA;AAAAA,QAE5BgE,IADErF,KAAgCqB,GAAMpB,CAAAA,GAEtCqF,KAAWC,EAAYlE,EAAAA;AAAAA,QACzBrB,IAAO;AAAA,UAAA,CAERA,GAAMY,MACNZ,GAAME,IAAQ,KAAA,CAAMlB,EAAU,KAAA,EAAOwG,EAAYxF,EAAAA;AAElD,eAAOA,GAAMiB;AAEdjB,MAAAA,GAAM6B,IAAAA,MACNwD,KAAOI,EAAWpE,IAAOiE,EAAAA,GACzBtF,GAAM6B,IAAAA;IAAa;AAEnBwD,MAAAA,KAAOI,EAAWpE,IAAOiE,EAAAA;AAAAA,WAG1B9D,EAAK6D,IAAM,SAAC5D,IAAKC,IAAAA;AACZ1B,MAAAA,MAAS0F,EAAI1F,GAAMiB,GAAOQ,EAAAA,MAASC,MACvCsB,EAAIqC,IAAM5D,IAAK2D,GAAY1D,EAAAA,CAAAA;IAAAA,CAAAA,GAAAA,MAGrB4D,KAA4B,IAAInD,IAAIkD,EAAAA,IAAQA;EAAAA,EA3BhChE,CAAAA;AAAAA;AA8BpB,SAASoE,EAAWpE,IAAYiE,IAAAA;AAAAA,UAEvBA,IAAAA;IAAAA,KAAAA;AAAAA,aAEC,IAAIK,IAAItE,EAAAA;IAAAA,KAAAA;AAAAA,aAGRkD,MAAMqB,KAAKvE,EAAAA;EAAAA;AAAAA,SAEbU,EAAYV,EAAAA;AAAAA;AKkDZwE,IAAAA;AAAAA,ITnFJC;ASmFID,ICvGFE,IACa,eAAA,OAAXC,UAAiD,YAAA,OAAhBA,OAAO,GAAA;ADsGxCH,ICrGKI,IAAwB,eAAA,OAARC;ADqGrBL,ICpGKM,IAAwB,eAAA,OAARC;ADoGrBP,ICnGKQ,IACK,eAAA,OAAVC,SAAAA,WACAA,MAAMC,aACM,eAAA,OAAZC;ADgGAX,IC3FKY,IAAmBV,IAC7BC,OAAOU,IAAI,eAAA,MAAA,IAAA,CAAA,GACR,eAAA,IAAA,MAAkB;ADyFhBb,IC/EKc,IAA2BZ,IACrCC,OAAOU,IAAI,iBAAA,IACV;AD6EIb,IC3EKe,IAA6Bb,IACvCC,OAAOU,IAAI,aAAA,IACV;ADyEIG,IZ5GFC,IAAS,EAAA,GACX,iBAAA,GACA,gDAAA,GACA,yDAAA,GAAA,SACDC,IAAAA;AAAAA,SAEA,yHACAA;AAAAA,GAAAA,GAGC,qHAAA,GACA,qCAAA,GACA,gEAAA,GACA,mEAAA,GACA,4FAAA,GACA,6EAAA,IACC,wCAAA,IACA,4DAAA,IACA,4DAAA,IACA,8CAAA,IACA,uEAAA,IAAA,SACDC,IAAAA;AAAAA,SACK,+CAA+CA;AAAAA,GAAAA,IAEnD,uCAAA,IAAA,SACDC,IAAAA;AAAAA,SACK,kCAAkCA;AAAAA,GAAAA,IAAAA,SAEvCC,IAAAA;AAAAA,SAAAA,qBACwBA,KAAAA,oFAAyFA,KAAAA;AAAAA,GAAAA,IAEhH,6EAAA,IAAA,SACDC,IAAAA;AAAAA,SAAAA,wJAC2JA,KAAAA;AAAAA,GAAAA,IAAAA,SAE3JA,IAAAA;AAAAA,SAAAA,qCACwCA;AAAAA,GAAAA,IAAAA,SAExCA,IAAAA;AAAAA,SAAAA,sCACyCA;AAAAA,GAAAA,IAExC,wFAAA;AYmEGN,IXzEFO,IAAmBC,KAAAA,OAAOC,UAAUC;AWyElCV,IX7CKW,KACO,eAAA,OAAZC,WAA2BA,QAAQD,UACvCC,QAAQD,UAAAA,WACDH,OAAOK,wBACd,SAAAC,IAAAA;AAAAA,SACAN,OAAOO,oBAAoBD,EAAAA,EAAKE,OAC/BR,OAAOK,sBAAsBC,EAAAA,CAAAA;AAAAA,IAEHN,OAAOO;AWqC9Bf,IXnCKiB,KACZT,OAAOS,6BACP,SAAmCC,IAAAA;AAAAA,MAE5BC,KAAW,CAAA;AAAA,SACjBR,GAAQO,EAAAA,EAAQE,QAAQ,SAAAC,IAAAA;AACvBF,IAAAA,GAAIE,EAAAA,IAAOb,OAAOc,yBAAyBJ,IAAQG,EAAAA;EAAAA,CAAAA,GAE7CF;AAAAA;AW2BDnB,IV9FFuB,KA4BF,CAAA;AUkEIvB,IPTKwB,KAAwC,EACpDC,KAAAA,SAAIC,IAAOC,IAAAA;AAAAA,MACNA,OAASC;AAAa,WAAOF;AAAAA,MAE3BG,IAASC,EAAOJ,EAAAA;AAAAA,MAAAA,CACjBK,EAAIF,GAAQF,EAAAA;AAAAA,WAwInB,SAA2BD,IAAmBG,IAAaF,IAAAA;AAAAA,UAAAA,IACpDK,KAAOC,EAAuBJ,IAAQF,EAAAA;AAAAA,aACrCK,KACJ,WAAWA,KACVA,GAAKhC,QAAAA,UAAAA,KAGLgC,GAAKP,QAAAA,WAAAA,KAAAA,SAALS,GAAUC,KAAKT,GAAMU,CAAAA,IAAAA;IACtBC,EA9IwBX,IAAOG,GAAQF,EAAAA;AAAAA,MAEnC3B,KAAQ6B,EAAOF,EAAAA;AAAAA,SACjBD,GAAMY,KAAAA,CAAeC,EAAYvC,EAAAA,IAC7BA,KAIJA,OAAUwC,EAAKd,GAAMe,GAAOd,EAAAA,KAC/Be,EAAYhB,EAAAA,GACJA,GAAMiB,EAAOhB,EAAAA,IAAeiB,EACnClB,GAAMmB,EAAOC,GACb9C,IACA0B,EAAAA,KAGK1B;AAAAA,GAER+B,KAAAA,SAAIL,IAAOC,IAAAA;AAAAA,SACHA,MAAQG,EAAOJ,EAAAA;AAAAA,GAEvBf,SAAAA,SAAQe,IAAAA;AAAAA,SACAd,QAAQD,QAAQmB,EAAOJ,EAAAA,CAAAA;AAAAA,GAE/BqB,KAAAA,SACCrB,IACAC,IACA3B,IAAAA;AAAAA,MAEMgC,IAAOC,EAAuBH,EAAOJ,EAAAA,GAAQC,EAAAA;AAAAA,MAC/CK,QAAAA,IAAAA,SAAAA,EAAMe;AAAAA,WAGTf,EAAKe,IAAIZ,KAAKT,GAAMU,GAAQpC,EAAAA,GAAAA;AACrB,MAAA,CAEH0B,GAAMsB,GAAW;AAAA,QAGfC,KAAUT,EAAKV,EAAOJ,EAAAA,GAAQC,EAAAA,GAE9BuB,KAAiCD,QAAAA,KAAAA,SAAAA,GAAUrB,CAAAA;AAAAA,QAC7CsB,MAAgBA,GAAaT,MAAUzC;AAAAA,aAC1C0B,GAAMiB,EAAOhB,EAAAA,IAAQ3B,IACrB0B,GAAMyB,EAAUxB,EAAAA,IAAAA,OAAQ;AACjB,QAEJyB,EAAGpD,IAAOiD,EAAAA,MAAAA,WAAajD,MAAuB+B,EAAIL,GAAMe,GAAOd,EAAAA;AAClE,aAAA;AACDe,MAAYhB,EAAAA,GACZ2B,EAAY3B,EAAAA;EAAAA;AAAAA,SAIXA,GAAMiB,EAAOhB,EAAAA,MAAU3B,OAAAA,WAEtBA,MAAuB2B,MAAQD,GAAMiB,MAEtCW,OAAOC,MAAMvD,EAAAA,KAAUsD,OAAOC,MAAM7B,GAAMiB,EAAOhB,EAAAA,CAAAA,MAKnDD,GAAMiB,EAAOhB,EAAAA,IAAQ3B,IACrB0B,GAAMyB,EAAUxB,EAAAA,IAAAA,OAAQ;AAJhB,GAOT6B,gBAAAA,SAAe9B,IAAOC,IAAAA;AAAAA,SAAAA,WAEjBa,EAAKd,GAAMe,GAAOd,EAAAA,KAAuBA,MAAQD,GAAMe,KAC1Df,GAAMyB,EAAUxB,EAAAA,IAAAA,OAChBe,EAAYhB,EAAAA,GACZ2B,EAAY3B,EAAAA,KAAAA,OAGLA,GAAMyB,EAAUxB,EAAAA,GAGpBD,GAAMiB,KAAAA,OAAcjB,GAAMiB,EAAMhB,EAAAA,GAAAA;AAC7B,GAIRL,0BAAAA,SAAyBI,IAAOC,IAAAA;AAAAA,MACzB8B,KAAQ3B,EAAOJ,EAAAA,GACfM,IAAOpB,QAAQU,yBAAyBmC,IAAO9B,EAAAA;AAAAA,SAChDK,IACE,EACN0B,UAAAA,MACAC,cAAAA,MAAcjC,GAAMkC,KAA2C,aAATjC,IACtDkC,YAAY7B,EAAK6B,YACjB7D,OAAOyD,GAAM9B,EAAAA,EAAAA,IALIK;AAAAA,GAQnB8B,gBAAAA,WAAAA;AACCC,IAAI,EAAA;AAAA,GAELC,gBAAAA,SAAetC,IAAAA;AAAAA,SACPlB,OAAOwD,eAAetC,GAAMe,CAAAA;AAAAA,GAEpCwB,gBAAAA,WAAAA;AACCF,IAAI,EAAA;AAAA,EAAA;AOnGE/D,IP2GFkE,KAA8C,CAAA;AACpDC,EAAK3C,IAAa,SAACH,IAAK+C,IAAAA;AAEvBF,KAAW7C,EAAAA,IAAO,WAAA;AAAA,WACjBgD,UAAU,CAAA,IAAKA,UAAU,CAAA,EAAG,CAAA,GACrBD,GAAGE,MAAMC,MAAMF,SAAAA;EAAAA;AAAAA,CAAAA,GAGxBH,GAAWV,iBAAiB,SAAS9B,IAAOC,IAAAA;AAAAA,SAC5B4B,MAAMiB,SAAS7C,EAAAA,CAAAA,KAAeoC,EAAI,EAAA,GAE1CG,GAAWnB,IAAKZ,KAAKoC,MAAM7C,IAAOC,IAAAA,MAAMU;AAAAA,GAEhD6B,GAAWnB,MAAM,SAASrB,IAAOC,IAAM3B,GAAAA;AAAAA,SACd,aAAT2B,MAAqB4B,MAAMiB,SAAS7C,EAAAA,CAAAA,KAAeoC,EAAI,EAAA,GAC/DvC,GAAYuB,IAAKZ,KAAKoC,MAAM7C,GAAM,CAAA,GAAIC,IAAM3B,GAAO0B,GAAM,CAAA,CAAA;AAAA;AAAA,ICpMpD+C,KAAb,WAAA;AAAA,WAAA,EAKaC,IAAAA;AAAAA,QAAAA,KAAAA;AAAAA,SAAAA,IAJWC,GAAAA,KAAAA,IAAAA,MAEA,KAAA,UA4BH,SAACC,IAAWC,IAAcC,IAAAA;AAAAA,UAEzB,cAAA,OAATF,MAAyC,cAAA,OAAXC,IAAuB;AAAA,YACzDE,KAAcF;AACpBA,QAAAA,KAASD;AAAAA,YAEHI,KAAOC;AAAAA,eACN,SAENL,IAAAA;AAAAA,cAAAA,KAAAA;AAAAA,qBAAAA,OAAAA,KAAOG;AAAAA,mBAAAA,KAAAA,UAAAA,QACJG,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,KAAAA,GAAAA,KAAAA,IAAAA;AAAAA,YAAAA,GAAAA,KAAAA,CAAAA,IAAAA,UAAAA,EAAAA;AAAAA,iBAEIF,GAAKG,QAAQP,IAAM,SAACQ,IAAAA;AAAAA,gBAAAA;AAAAA,oBAAAA,KAAmBP,IAAO1C,KAAAA,MAAAA,IAAAA,CAAKkD,IAAMD,EAAAA,EAAAA,OAAUF,EAAAA,CAAAA;UAAAA,CAAAA;QAAAA;MAAAA;AAAAA,UAQxEI;AAAAA,UAJkB,cAAA,OAAXT,MAAuBd,EAAI,CAAA,GAAA,WAClCe,MAAwD,cAAA,OAAlBA,MACzCf,EAAI,CAAA,GAKDxB,EAAYqC,EAAAA,GAAO;AAAA,YAChBW,KAAQC,EAAWP,EAAAA,GACnBQ,KAAQ7C,EAAYqC,IAAML,IAAAA,MAAMvC,GAClCqD,KAAAA;AAAW,YAAA;AAEdJ,UAAAA,KAAST,GAAOY,EAAAA,GAChBC,KAAAA;QAAW,UAAA;AAGPA,UAAAA,KAAUC,EAAYJ,EAAAA,IACrBK,EAAWL,EAAAA;QAAAA;AAAAA,eAEM,eAAA,OAAZM,WAA2BP,cAAkBO,UAChDP,GAAOQ,KACb,SAAAR,IAAAA;AAAAA,iBACCS,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAcV,IAAQC,EAAAA;QAAAA,GAE9B,SAAAU,IAAAA;AAAAA,gBACCN,EAAYJ,EAAAA,GACNU;QAAAA,CAAAA,KAITF,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAcV,IAAQC,EAAAA;MAAAA;AACvB,UAAA,CAAKX,MAAwB,YAAA,OAATA,IAAmB;AAAA,YAAA,YAC7CU,KAAST,GAAOD,EAAAA,OACUU,KAASV,KAC/BU,OAAWY,MAASZ,KAAAA,SACpBL,GAAKkB,KAAaC,EAAOd,IAAAA,IAAQ,GACjCR,IAAe;AAAA,cACZuB,KAAa,CAAA,GACbC,KAAc,CAAA;AACpBC,YAAU,SAAA,EAAWC,EAA4B5B,IAAMU,IAAQe,IAAGC,EAAAA,GAClExB,GAAcuB,IAAGC,EAAAA;QAAAA;AAAAA,eAEXhB;MAAAA;AACDvB,QAAI,IAAIa,EAAAA;IAAAA,GAAAA,KAAAA,qBAG0B,SAACA,IAAWC,IAAAA;AAAAA,UAEjC,cAAA,OAATD;AAAAA,eACH,SAAClD,IAAAA;AAAAA,mBAAAA,KAAAA,UAAAA,QAAewD,KAAAA,MAAAA,KAAAA,IAAAA,KAAAA,IAAAA,CAAAA,GAAAA,KAAAA,GAAAA,KAAAA,IAAAA;AAAAA,YAAAA,GAAAA,KAAAA,CAAAA,IAAAA,UAAAA,EAAAA;AAAAA,iBACtBD,GAAKwB,mBAAmB/E,IAAO,SAAC0D,IAAAA;AAAAA,mBAAeR,GAAAA,MAAAA,QAAAA,CAAKQ,EAAAA,EAAAA,OAAUF,EAAAA,CAAAA;UAAAA,CAAAA;QAAAA;AAAAA,UAG5DwB,IAAkBC,IAChBrB,KAASL,GAAKE,QAAQP,IAAMC,IAAQ,SAACwB,IAAYC,IAAAA;AACtDI,QAAAA,KAAUL,IACVM,KAAiBL;MAAAA,CAAAA;AAAAA,aAGK,eAAA,OAAZT,WAA2BP,cAAkBO,UAChDP,GAAOQ,KAAK,SAAAc,IAAAA;AAAAA,eAAa,CAACA,IAAWF,IAAUC,EAAAA;MAAAA,CAAAA,IAEhD,CAACrB,IAAQoB,IAAUC,EAAAA;IAAAA,GAzGQ,aAAA,QAAvBjC,QAAAA,KAAAA,SAAAA,GAAQmC,eAClBtC,KAAKuC,cAAcpC,GAAQmC,UAAAA,GACM,aAAA,QAAvBnC,QAAAA,KAAAA,SAAAA,GAAQqC,eAClBxC,KAAKyC,cAActC,GAAQqC,UAAAA;EAAAA;AAAAA,MAAAA,KAAAA,EAAAA;AAAAA,SAAAA,GAyG7BE,cAAA,SAAiCrC,IAAAA;AAC3BrC,MAAYqC,EAAAA,KAAOb,EAAI,CAAA,GACxBmD,EAAQtC,EAAAA,MAAOA,KAAO3B,EAAQ2B,EAAAA;AAAAA,QAC5BW,KAAQC,EAAWjB,IAAAA,GACnBkB,KAAQ7C,EAAY2B,MAAMK,IAAAA,MAAMvC;AAAAA,WACtCoD,GAAM7D,CAAAA,EAAauF,IAAAA,MACnBvB,EAAWL,EAAAA,GACJE;EAAAA,GAAAA,GAGR2B,cAAA,SACChC,IACAN,IAAAA;AAAAA,QAEMpD,KAAoB0D,MAAUA,GAAcxD,CAAAA;AAAAA,IAE5CF,MAAUA,GAAMyF,KAAWpD,EAAI,CAAA,GAChCrC,GAAMY,KAAYyB,EAAI,EAAA;AAAA,QAEZwB,KAAS7D,GAAjBmB;AAAAA,WACPkD,EAAkBR,IAAOT,EAAAA,GAClBkB,EAAAA,QAAyBT,EAAAA;EAAAA,GAAAA,GAQjCyB,gBAAA,SAAchH,IAAAA;AAAAA,SACRmG,IAAcnG;EAAAA,GAAAA,GASpB8G,gBAAA,SAAc9G,IAAAA;AACTA,IAAAA,MAAAA,CAAU2E,KACbZ,EAAI,EAAA,GAAA,KAEAsD,IAAcrH;EAAAA,GAAAA,GAGpBsH,eAAA,SAAkC1C,IAAS8B,IAAAA;AAAAA,QAGtCa;AAAAA,SACCA,KAAIb,GAAQc,SAAS,GAAGD,MAAK,GAAGA,MAAK;AAAA,UACnCE,KAAQf,GAAQa,EAAAA;AAAAA,UACI,MAAtBE,GAAMtH,KAAKqH,UAA6B,cAAbC,GAAMrH,IAAkB;AACtDwE,QAAAA,KAAO6C,GAAMzH;AAAAA;MAAAA;IAAAA;AAMXuH,IAAAA,KAAAA,OACHb,KAAUA,GAAQgB,MAAMH,KAAI,CAAA;AAAA,QAGvBI,KAAmBpB,EAAU,SAAA,EAAWqB;AAAAA,WAC1CV,EAAQtC,EAAAA,IAEJ+C,GAAiB/C,IAAM8B,EAAAA,IAGxBnC,KAAKY,QAAQP,IAAM,SAACQ,IAAAA;AAAAA,aAC1BuC,GAAiBvC,IAAOsB,EAAAA;IAAAA,CAAAA;EAAAA,GAAAA;AAAAA,EAxL3B;ADoMiE,IOhN3DmB,KAAQ,IAAIpD;APgN+C,IO3LpDU,KAAoB0C,GAAM1C;AP2L0B,IOpLpDsB,KAA0CoB,GAAMpB,mBAAmBqB,KAC/ED,EAAAA;APmLgE,IO3KpDb,KAAgBa,GAAMb,cAAcc,KAAKD,EAAAA;AP2KW,IOnKpDf,KAAgBe,GAAMf,cAAcgB,KAAKD,EAAAA;APmKW,IO5JpDP,KAAeO,GAAMP,aAAaQ,KAAKD,EAAAA;AP4Ja,IOtJpDZ,KAAcY,GAAMZ,YAAYa,KAAKD,EAAAA;APsJe,IO5IpDT,KAAcS,GAAMT,YAAYU,KAAKD,EAAAA;AAAAA,IAAAA,oBAAAA;;;AExFlD,IAAM,OAAN,MAAW;AAAA,EACP,YAAY,MAAM;AACd,UAAM,OAAO,KAAK;AAElB,SAAK,IAAI;AACT,SAAK,KAAK,KAAK,GAAG;AAClB,SAAK,KAAK,KAAK,GAAG;AAClB,SAAK,KAAK,KAAK,GAAG;AAClB,SAAK,MAAM,KAAK,IAAI;AACpB,QAAI,KAAK,KAAK,GAAG;AACb,WAAK,MAAM;AAAA,IACf;AACA,SAAK,MAAM,KAAK,IAAI;AACpB,QAAI,KAAK,KAAK,GAAG;AACb,WAAK,MAAM;AAAA,IACf;AACA,SAAK,MAAM,KAAK,IAAI;AACpB,QAAI,KAAK,KAAK,GAAG;AACb,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AAAA,EACA,OAAO;AACH,UAAME,KAAI,UAAU,KAAK,KAAK,KAAK,IAAI;AACvC,SAAK,KAAK,KAAK;AACf,SAAK,KAAK,KAAK;AACf,WAAQ,KAAK,KAAKA,MAAK,KAAK,IAAI,KAAK,MAAMA,EAAC;AAAA,EAChD;AACJ;AACA,SAAS,OAAO;AACZ,MAAIC,KAAI;AACR,QAAM,OAAO,SAAU,MAAM;AACzB,UAAM,MAAM,KAAK,SAAS;AAC1B,aAASC,KAAI,GAAGA,KAAI,IAAI,QAAQA,MAAK;AACjC,MAAAD,MAAK,IAAI,WAAWC,EAAC;AACrB,UAAIC,KAAI,sBAAsBF;AAC9B,MAAAA,KAAIE,OAAM;AACV,MAAAA,MAAKF;AACL,MAAAE,MAAKF;AACL,MAAAA,KAAIE,OAAM;AACV,MAAAA,MAAKF;AACL,MAAAA,MAAKE,KAAI;AAAA,IACb;AACA,YAAQF,OAAM,KAAK;AAAA,EACvB;AACA,SAAO;AACX;AACA,SAAS,KAAKG,IAAGJ,IAAG;AAChB,EAAAA,GAAE,IAAII,GAAE;AACR,EAAAJ,GAAE,KAAKI,GAAE;AACT,EAAAJ,GAAE,KAAKI,GAAE;AACT,EAAAJ,GAAE,KAAKI,GAAE;AACT,SAAOJ;AACX;AACA,SAAS,KAAK,MAAM,OAAO;AACvB,QAAM,KAAK,IAAI,KAAK,IAAI;AACxB,QAAM,OAAO,GAAG,KAAK,KAAK,EAAE;AAC5B,MAAI;AACA,SAAK,OAAO,EAAE;AAClB,OAAK,QAAQ,MAAM,KAAK,IAAI,CAAC,CAAC;AAC9B,SAAO;AACX;AAgBA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA;AAAA,EAKT,YAAY,OAAO;AAKf,SAAK,QAAQ,SAAS,EAAE,MAAM,IAAI;AAClC,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO;AACV,WAAO,KAAK,IAAI,EAAE,SAAS,EAAE,EAAE,MAAM,GAAG;AAAA,EAC5C;AAAA,EACA,SAAS;AACL,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,WAAW;AACP,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,SAAK,OAAO;AACZ,UAAMK,KAAI,KAAK;AACf,UAAM,OAAOA,GAAE,YAAY,KAAKA,GAAE;AAClC,UAAM,OAAO,KAAK,MAAMA,GAAE,SAAS;AACnC,UAAM,SAAS,KAAK;AACpB,SAAK,QAAQ;AAAA,MACT,GAAGA;AAAA,MACH,WAAW,KAAK,MAAM;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA,EACA,MAAM;AACF,UAAM,SAAS,KAAK,QAAQ,KAAK,IAAI;AACrC,UAAM,YAAY;AAAA,MACd,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACT;AAEA,UAAM,aAAa,CAAC;AACpB,eAAW,OAAO,WAAW;AACzB,YAAM,YAAY,UAAU,GAAG;AAC/B,iBAAW,GAAG,IAAI,CAAC,cAAc;AAC7B,eAAO,cAAc,SACf,KAAK,MAAM,OAAO,IAAI,SAAS,IAAI,IACnC,MAAM,KAAK,EAAE,QAAQ,UAAU,CAAC,EAAE,IAAI,MAAM,KAAK,MAAM,OAAO,IAAI,SAAS,IAAI,CAAC;AAAA,MAC1F;AAAA,IACJ;AACA,aAAS,IAAI,YAAY,GAAG,WAAW;AACnC,aAAO,cAAc,SACf,KAAK,MAAM,OAAO,IAAI,SAAS,IAAI,IACnC,MAAM,KAAK,EAAE,QAAQ,UAAU,CAAC,EAAE,IAAI,MAAM,KAAK,MAAM,OAAO,IAAI,SAAS,IAAI,CAAC;AAAA,IAC1F;AACA,WAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAcH,GAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASH;AAAA;AAAA;AAAA;AAAA,MAIA,QAAQ,MAAM;AACV,eAAO,OAAO;AAAA,MAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAOA,SAAS,CAAC,SAAS;AACf,cAAM,QAAQ,CAAC,GAAG,IAAI;AACtB,YAAI,cAAc,KAAK;AACvB,YAAI,mBAAmB;AACvB,cAAM,WAAW,MAAM,KAAK,EAAE,QAAQ,YAAY,CAAC;AACnD,eAAO,aAAa;AAChB,gBAAM,cAAc,KAAK,MAAM,cAAc,OAAO,CAAC;AACrD,mBAAS,kBAAkB,IAAI,MAAM,WAAW;AAChD,gBAAM,WAAW,IAAI,MAAM,EAAE,WAAW;AAAA,QAC5C;AACA,eAAO;AAAA,MACX;AAAA,MACA,UAAU;AAAA,IACd;AAAA,EACJ;AACJ;AASA,IAAM,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,UAAU,CAAC,EAAE,IAAI,MAAM;AACnB,WAAO,IAAI,SAAS,OAAO;AAAA,EAC/B;AAAA,EACA,OAAO,CAAC,EAAE,IAAI,MAAM;AAChB,WAAO,IAAI,SAAS,SAAS;AAAA,EACjC;AAAA,EACA,KAAK,CAAC,EAAE,KAAK,MAAM;AACf,UAAM,SAAS,IAAI,OAAO,IAAI;AAC9B,WAAO,OAAO,IAAI;AAAA,EACtB;AAAA,EACA,OAAO,CAAC,EAAE,KAAK,MAAM;AACjB,QAAI,EAAE,KAAK,IAAI;AACf,QAAI,SAAS,QAAW;AACpB,aAAO,OAAO,KAAK;AAAA,IACvB;AACA,WAAO,EAAE,KAAK;AAAA,EAClB;AAAA,EACA,YAAY,MAAM;AACtB;;;AC7NA,oBAA0B;AAS1B,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,mBAAmB;AAiBzB,IAAM,WAAW,CAAC,MAAM,MAAM,UAAU,iBAAiB;AAAA,EACrD,MAAM;AAAA,EACN,SAAS,EAAE,MAAM,MAAM,UAAU,YAAY;AACjD;AASA,IAAM,YAAY,CAAC,MAAM,MAAM,UAAU,iBAAiB;AAAA,EACtD,MAAM;AAAA,EACN,SAAS,EAAE,MAAM,MAAM,UAAU,YAAY;AACjD;AAQA,IAAM,qBAAqB,CAAC,MAAM,MAAM,UAAU,iBAAiB;AAAA,EAC/D,MAAM;AAAA,EACN,SAAS,EAAE,MAAM,MAAM,UAAU,YAAY;AAAA,EAC7C,WAAW;AACf;AACA,IAAM,OAAO,CAACC,WAAU;AAAA,EACpB,MAAM;AAAA,EACN,OAAOA,MAAK;AAAA,EACZ,KAAKA,MAAK;AAAA,EACV,cAAcA,MAAK;AAAA,EACnB,YAAY;AAChB;AASA,IAAM,QAAQ,CAAC,aAAa,SAASC,QAAO,cAAc;AAAA,EACtD,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA,OAAAA;AAAA,EACA;AAAA,EACA,YAAY;AAChB;AAOA,IAAM,SAAS,CAAC,OAAO,cAAc;AAAA,EACjC,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA,YAAY;AAChB;AAKA,IAAM,QAAQ,CAAC,WAAW;AAAA,EACtB,MAAM;AAAA,EACN;AAAA,EACA,YAAY;AAChB;AAMA,IAAM,OAAO,CAAC,UAAU,iBAAiB;AAAA,EACrC,MAAM;AAAA,EACN,SAAS,EAAE,MAAM,MAAM,MAAM,MAAM,UAAU,YAAY;AAC7D;AAMA,IAAM,OAAO,CAAC,UAAU,iBAAiB;AAAA,EACrC,MAAM;AAAA,EACN,SAAS,EAAE,MAAM,MAAM,MAAM,MAAM,UAAU,YAAY;AAC7D;AAIA,IAAM,SAAS,CAAC,MAAM,MAAM,UAAU,iBAAiB;AAAA,EACnD,MAAM;AAAA,EACN,SAAS,EAAE,MAAM,MAAM,UAAU,YAAY;AACjD;AAKA,IAAM,kBAAkB,OAAO;AAAA,EAC3B,MAAM;AACV;AAEA,IAAI,iBAA8B,OAAO,OAAO;AAAA,EAC9C,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;AAOD,IAAM,eAAe;AAarB,IAAM,cAAc;AAAA,EAChB,MAAM;AAAA,EACN,QAAQ,CAAC,SAAS,CAAC,YAAY,SAAS;AACpC,QAAI,YAAY;AAChB,UAAM,OAAO,kBAAQ,QAAQ,GAAG,CAACC,OAAM;AACnC,YAAM,SAAS,KAAK,EAAE,GAAG,SAAS,GAAAA,GAAE,GAAG,GAAG,IAAI;AAC9C,UAAI,WAAW,cAAc;AACzB,oBAAY;AACZ;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC;AACD,QAAI;AACA,aAAO;AACX,WAAO;AAAA,EACX;AACJ;AAEA,IAAI;AAAA,CACH,SAAUC,aAAY;AACnB,EAAAA,YAAW,MAAM,IAAI;AACrB,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,eAAe,IAAI;AAC9B,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,aAAa,IAAI;AAChC,GAAG,eAAe,aAAa,CAAC,EAAE;AASlC,IAAI;AAAA,CACH,SAAUC,SAAQ;AACf,EAAAA,QAAO,mBAAmB,IAAI;AAC9B,EAAAA,QAAO,gBAAgB,IAAI;AAC3B,EAAAA,QAAO,gBAAgB,IAAI;AAC3B,EAAAA,QAAO,mBAAmB,IAAI;AAC9B,EAAAA,QAAO,mBAAmB,IAAI;AAC9B,EAAAA,QAAO,wBAAwB,IAAI;AACnC,EAAAA,QAAO,uBAAuB,IAAI;AACtC,GAAG,WAAW,SAAS,CAAC,EAAE;AAI1B,IAAM,SAAN,MAAa;AAAA,EACT,YAAY,MAAM,KAAK,UAAU;AAC7B,SAAK,OAAO;AACZ,SAAK,WAAW;AAChB,SAAK,WAAW,CAAC;AACjB,SAAK,cAAc,IAAI;AACvB,SAAK,kBAAkB,KAAK,MAAS;AAGrC,SAAK,yBAAyB,IAAI,aAAa;AAAA,EACnD;AAAA,EACA,MAAM;AACF,UAAM,SAAS;AAAA,MACX,UAAU;AAAA,IACd;AACA,eAAW,QAAQ,KAAK,KAAK,YAAY;AACrC,aAAO,IAAI,IAAI,IAAI,SAAS;AACxB,aAAK,SAAS,KAAK;AAAA,UACf;AAAA,UACA;AAAA,UACA,OAAO,KAAK;AAAA,UACZ,MAAM,KAAK;AAAA,UACX,YAAY,KAAK;AAAA;AAAA,UAEjB,OAAO,IAAI,MAAM,qBAAqB;AAAA,QAC1C,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,KAAK,SAAS,SAAS;AAAA,EAClC;AAAA,EACA,kBAAkB,KAAK,YAAY;AAC/B,SAAK,eAAe,IAAI;AACxB,SAAK,cAAc,IAAI;AACvB,SAAK,gBAAgB;AAAA,EACzB;AAAA,EACA,qBAAqB;AACjB,SAAK,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACV,UAAM,eAAe;AACrB,UAAM,iBAAiB,CAAC,EAAE,MAAM,GAAG,aAAa;AAAA,MAC5C,GAAG;AAAA,MACH,SAAS;AAAA,QACL,GAAG,aAAa;AAAA,QAChB,QAAQ;AAAA,UACJ,GAAG,aAAa,QAAQ;AAAA,UACxB,MAAM,EAAE,OAAO,UAAU,OAAO,MAAM;AAAA,QAC1C;AAAA,MACJ;AAAA,IACJ;AACA;AAAY,eAASC,KAAI,GAAGA,KAAI,KAAK,SAAS,QAAQA,MAAK;AACvD,cAAM,QAAQ,KAAK,SAASA,EAAC;AAC7B,cAAM,eAAe,MAAM,SAAS,MAAM,IAAI;AAG9C,cAAM,aAAa,KAAK,cAAc,KAAK;AAC3C,YAAI,cAAc,KAAK,wBAAwB;AAC3C,iBAAO,eAAe,MAAM,OAAO,OAAO,cAAc;AAAA,QAC5D;AACA,YAAI,MAAM,eAAe,QAAW;AAChC,iBAAO,eAAe,MAAM,OAAO,OAAO,iBAAiB;AAAA,QAC/D;AAEA,YAAI,MAAM,IAAI;AACV,gBAAM;AACV,gBAAQ,MAAM,MAAM;AAAA,UAChB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,oBAAoB;AACrB,oBAAQ,MAAM,YAAY;AAAA,cAEtB,KAAK,WAAW;AAAA,cAChB,KAAK,WAAW;AACZ,uBAAO,eAAe,MAAM,OAAO,OAAO,iBAAiB;AAAA,cAC/D,KAAK,WAAW;AACZ,uBAAO,eAAe,MAAM,OAAO,OAAO,sBAAsB;AAAA,cAEpE,KAAK,WAAW;AACZ,oBAAI,MAAM,SAAS;AACf;AACJ,uBAAO,eAAe,MAAM,OAAO,OAAO,qBAAqB;AAAA,YACvE;AAEA,gBAAI;AACA,uBAAS;AACb;AAAA,UACJ;AAAA,UACA,KAAK,WAAW;AACZ,gBAAI,MAAM,eAAe,WAAW,eAChC,MAAM,eAAe,WAAW,cAAc;AAC9C,qBAAO,eAAe,MAAM,OAAO,OAAO,cAAc;AAAA,YAC5D;AAGA,gBAAI;AACA,uBAAS;AACb;AAAA,UACJ;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY;AACb,gBAAI,MAAM,eAAe,WAAW,cAAc;AAC9C,qBAAO,eAAe,MAAM,OAAO,OAAO,iBAAiB;AAAA,YAC/D;AAGA,gBAAI,MAAM,UAAU,MAAM,IAAI;AAC1B,uBAAS;AACb;AAAA,UACJ;AAAA,QACJ;AACA,cAAM,SAAS,mBAAmB,MAAM,MAAM,MAAM,MAAM,KAAK,QAAQ;AACvE,gBAAQ,KAAK,KAAK,aAAa,OAAO,MAAM;AAAA,MAChD;AACA,WAAO;AAAA,EACX;AACJ;AASA,IAAM,eAAe;AAAA,EACjB,MAAM;AAAA,EACN,UAAU,CAAC,EAAE,IAAI,MAAM,IAAI,SAAS,OAAO;AAAA,EAC3C,WAAW,CAAC,EAAE,KAAK,MAAM,KAAK,SAAS;AAAA;AAAA;AAAA;AAAA,EAIvC,QAAQ,CAAC,QAAQ,eAAe,CAAC,YAAY,SAAS;AAClD,UAAM,MAAM,QAAQ;AACpB,QAAI;AACA,UAAI,SAAS,kBAAkB,QAAQ,KAAK,UAAU;AAC1D,UAAMH,KAAI,OAAO,SAAS,GAAG,IAAI;AACjC,QAAI;AACA,UAAI,SAAS,mBAAmB;AACpC,WAAOA;AAAA,EACX;AAAA,EACA,0BAA0B,CAAC,EAAE,OAAO,IAAI,MAAM,IAAI,SAAS,OAAO,KAAK;AAAA,EACvE,KAAK,CAAC,EAAE,MAAM,KAAK,SAAS,MAAM,IAAI,OAAO,KAAK,MAAM,KAAK,QAAQ,EAAE,IAAI;AAC/E;AAcA,IAAM,YAAY;AAAA,EACd,MAAM;AAAA,EACN,OAAO,OAAO,CAAC;AAAA,EACf,KAAK,CAAC,EAAE,KAAK,MAAM;AACf,WAAO;AAAA,MACH,aAAa,CAAC,aAAa;AACvB,aAAK,WAAW;AAAA,MACpB;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,OAAO,OAAO,CAAC;AACnB;AAMA,SAAS,eAAe,OAAO;AAE3B,MAAI,UAAU,UACV,UAAU,QACV,OAAO,UAAU,aACjB,OAAO,UAAU,YACjB,OAAO,UAAU,UAAU;AAC3B,WAAO;AAAA,EACX;AAEA,MAAI,KAAC,cAAAI,SAAc,KAAK,KAAK,CAAC,MAAM,QAAQ,KAAK,GAAG;AAChD,WAAO;AAAA,EACX;AAEA,aAAW,OAAO,OAAO;AACrB,QAAI,CAAC,eAAe,MAAM,GAAG,CAAC;AAC1B,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAKA,IAAM,qBAAqB;AAAA,EACvB,MAAM;AAAA,EACN,QAAQ,CAAC,SAAS,CAAC,YAAY,SAAS;AACpC,UAAM,SAAS,KAAK,SAAS,GAAG,IAAI;AAEpC,QAA6C,CAAC,eAAe,MAAM,GAAG;AAClE,YAAM,IAAI,MAAM,iHAC8D;AAAA,IAClF;AACA,WAAO;AAAA,EACX;AACJ;AASA,IAAM,aAAa;AACnB,IAAM,QAAQ,aAAa,MAAM;AAAE,IAAI,IAAI,QAAQ,QAAQ,IAAI,GAAG,GAAG;AACrE,IAAM,UAAU,IAAI,QAAQ,QAAQ,MAAM,GAAG,GAAG;AAChD,SAAS,KAAK,KAAK;AACf,QAAM,SAAS,GAAG,EAAE;AACxB;AACA,SAAS,MAAMC,QAAO;AAClB,UAAQ,UAAUA,MAAK;AAC3B;AAYA,IAAM,eAAe,CAAC,aAAa,cAAc,WAAW,kBAAkB;AAC9E,IAAM,kBAAkB,CAAC,GAAG,cAAc,YAAY;AAItD,IAAM,gBAAgB,CAAC,OAAO,QAAQ,SAAS;AAE3C,OAAK,KAAK,QACL,OAAO,CAACC,YAAWA,QAAO,WAAW,MAAS,EAC9C,OAAO,CAACA,YAAWA,QAAO,SAAS,OAAO,QAAQ,IAAI,EACtD,QAAQ,CAACA,YAAW;AACrB,UAAM,OAAOA,QAAO;AACpB,UAAM,cAAc,MAAM,QAAQ,IAAI,KAAK,EAAE,MAAM,CAAC,EAAE;AACtD,UAAM,OAAOA,QAAO,OAAO,YAAY,MAAM,OAAO,OAAO;AAC3D,YAAQ;AAAA,MACJ,GAAG;AAAA,MACH,SAAS;AAAA,QACL,GAAG,MAAM;AAAA,QACT,CAAC,IAAI,GAAG,EAAE,GAAG,aAAa,KAAK;AAAA,MACnC;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAmBA,IAAM,UAAU,CAAC,EAAE,QAAQ,MAAM,OAAO,QAAQ,WAAW,CAAC,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;AAC7F,OAAK,IAAI,IAAI;AACb,SAAO;AACX,GAAG,CAAC,CAAC;AAQL,IAAM,SAAS,CAAC,cAAc,YAAY,YAAY;AAClD,SAAO,CAAC,GAAG,cAAc,GAAG,SAAS,YAAY,EAC5C,OAAO,CAACA,YAAWA,QAAO,WAAW,MAAS,EAC9C,OAAO,CAAC,QAAQ,EAAE,OAAO,MAAM,OAAO,QAAQ,UAAU,GAAG,YAAY;AAChF;AAIA,IAAM,QAAQ,CAAC,OAAO,SAAS;AAC3B,GAAC,GAAG,iBAAiB,GAAG,KAAK,KAAK,OAAO,EACpC,OAAO,CAACA,YAAWA,QAAO,UAAU,MAAS,EAC7C,QAAQ,CAACA,YAAW;AACrB,UAAM,OAAOA,QAAO;AACpB,UAAM,OAAOA,QAAO,MAAM;AAAA,MACtB,GAAG,MAAM;AAAA,MACT,KAAK,MAAM;AAAA,MACX,MAAM,KAAK;AAAA,IACf,CAAC;AACD,YAAQ;AAAA,MACJ,GAAG;AAAA,MACH,SAAS;AAAA,QACL,GAAG,MAAM;AAAA,QACT,CAAC,IAAI,GAAG,EAAE,KAAK;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAOA,IAAM,UAAU,CAAC,OAAO,SAAS;AAC7B,GAAC,GAAG,iBAAiB,GAAG,KAAK,KAAK,OAAO,EACpC,OAAO,CAACA,YAAWA,QAAO,QAAQ,MAAS,EAC3C,QAAQ,CAACA,YAAW;AACrB,UAAM,OAAOA,QAAO;AACpB,UAAM,cAAc,MAAM,QAAQ,IAAI,KAAK,EAAE,MAAM,CAAC,EAAE;AACtD,UAAM,MAAMA,QAAO,IAAI;AAAA,MACnB,GAAG,MAAM;AAAA,MACT,KAAK,MAAM;AAAA,MACX,MAAM,YAAY;AAAA,MAClB,MAAM,KAAK;AAAA,MACX,UAAU,KAAK;AAAA,IACnB,CAAC;AACD,YAAQ;AAAA,MACJ,GAAG;AAAA,MACH,SAAS;AAAA,QACL,GAAG,MAAM;AAAA,QACT,CAAC,IAAI,GAAG,EAAE,GAAG,aAAa,IAAI;AAAA,MAClC;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAIA,IAAM,QAAQ,CAAC,OAAO,SAAS;AAI3B,GAAC,GAAG,cAAc,GAAG,KAAK,KAAK,SAAS,YAAY,EAC/C,QAAQ,EACR,QAAQ,CAACA,YAAW;AACrB,UAAM,OAAOA,QAAO;AACpB,UAAM,cAAc,MAAM,QAAQ,IAAI,KAAK,EAAE,MAAM,CAAC,EAAE;AACtD,QAAIA,QAAO,OAAO;AACd,YAAM,UAAUA,QAAO,MAAM;AAAA,QACzB,GAAG,MAAM;AAAA,QACT,KAAK,MAAM;AAAA,QACX,MAAM,KAAK;AAAA,QACX,KAAK,YAAY;AAAA,QACjB,MAAM,YAAY;AAAA,MACtB,CAAC;AACD,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,GAAG,MAAM;AAAA,UACT,CAACA,QAAO,IAAI,GAAG,EAAE,MAAM,QAAQ;AAAA,QACnC;AAAA,MACJ;AAAA,IACJ,WACSA,QAAO,0BAA0B;AACtC,cAAQA,QAAO,yBAAyB;AAAA,QACpC;AAAA,QACA,MAAM,KAAK;AAAA,QACX,KAAK,YAAY;AAAA,QACjB,MAAM,YAAY;AAAA,MACtB,CAAC;AAED,YAAM,OAAO,MAAM,QAAQ,IAAI,EAAE;AACjC,cAAQ;AAAA,QACJ,GAAG;AAAA,QACH,SAAS;AAAA,UACL,GAAG,MAAM;AAAA,UACT,CAACA,QAAO,IAAI,GAAG,EAAE,KAAK;AAAA,QAC1B;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAMA,IAAM,WAAW,CAAC,OAAO,SAAS;AAC9B,SAAO,CAAC,GAAG,iBAAiB,GAAG,KAAK,KAAK,OAAO,EAC3C,OAAO,CAACA,YAAWA,QAAO,aAAa,MAAS,EAChD,IAAI,CAACA,YAAW;AACjB,UAAM,OAAOA,QAAO;AACpB,UAAM,cAAc,MAAM,QAAQ,IAAI;AACtC,QAAI,aAAa;AACb,aAAOA,QAAO,SAAS;AAAA,QACnB,GAAG,MAAM;AAAA,QACT,KAAK,MAAM;AAAA,QACX,MAAM,KAAK;AAAA,QACX,KAAK,YAAY;AAAA,QACjB,MAAM,YAAY;AAAA,MACtB,CAAC;AAAA,IACL;AACA,WAAO;AAAA,EACX,CAAC,EACI,SAAS,IAAI;AACtB;AAKA,IAAM,YAAY,CAAC,OAAO,SAAS;AAC/B,QAAM,qBAAqB,CAAC,GAAG,iBAAiB,GAAG,KAAK,KAAK,OAAO,EAC/D,OAAO,CAACA,YAAWA,QAAO,cAAc,MAAS,EACjD,IAAI,CAACA,YAAW;AACjB,UAAM,EAAE,KAAK,IAAIA;AACjB,UAAM,cAAc,MAAM,QAAQ,IAAI;AACtC,UAAM,UAAUA,QAAO,UAAU;AAAA,MAC7B,GAAG,MAAM;AAAA,MACT,KAAK,MAAM;AAAA,MACX,MAAM,KAAK;AAAA,MACX,MAAM,eAAe,YAAY;AAAA,IACrC,CAAC;AACD,WAAO,UAAU,EAAE,QAAQ,MAAM,QAAQ,IAAI;AAAA,EACjD,CAAC,EACI,KAAK,CAAC,UAAU,KAAK;AAC1B,SAAO,sBAAsB;AACjC;AAKA,IAAM,mBAAmB,CAAC,OAAO,SAAS;AACtC,QAAM,eAAe,MAAM,OAAO,IAAI;AACtC,QAAM,YAAY,UAAU,cAAc,IAAI;AAC9C,MAAI,CAAC;AACD,WAAO,CAAC,YAAY;AACxB,QAAM,EAAE,QAAAA,SAAQ,QAAQ,IAAI;AAC5B,QAAM,GAAGA,OAAM;AAAA,EAAqC,OAAO,EAAE;AAC7D,SAAO,CAAC,OAAO,SAAS;AAC5B;AAMA,IAAM,aAAa,CAAC,EAAE,GAAAN,IAAG,KAAK,UAAU,CAAC,EAAE,GAAG,EAAE,MAAM,SAAS,MAAM;AACjE,GAAC,GAAG,iBAAiB,GAAG,KAAK,OAAO,EAAE,QAAQ,CAAC,EAAE,MAAM,WAAW,MAAM;AACpE,QAAI,CAAC;AACD;AACJ,UAAM,EAAE,KAAK,IAAI,QAAQ,IAAI,KAAK,EAAE,MAAM,CAAC,EAAE;AAC7C,UAAM,UAAU,WAAW,EAAE,GAAAA,IAAG,KAAK,MAAM,MAAM,SAAS,CAAC;AAC3D,cAAU;AAAA,MACN,GAAG;AAAA,MACH,CAAC,IAAI,GAAG,EAAE,MAAM,QAAQ;AAAA,IAC5B;AAAA,EACJ,CAAC;AACD,SAAO;AACX;AAOA,SAAS,2BAA2B,SAAS,kBAAkB,OAAO;AAClE,MAAI,QAAQ,WAAW;AACnB,QAAI,iBAAiB;AACjB,cAAQ,WAAW,QAAQ;AAAA,IAC/B;AACA,YAAQ,WAAW,QAAQ;AAC3B,WAAO,QAAQ;AAAA,EACnB;AACJ;AASA,SAAS,iBAAiB,KAAK,KAAK;AAChC,MAAI,gBAAgB,CAAC;AACrB,MAAI,qBAAqB,CAAC;AAC1B,MAAI,qBAAqB;AACzB,MAAI,yBAAyB,CAAC;AAC9B,MAAI,yBAAyB,CAAC;AAC9B,MAAI,MAAM,QAAQ,GAAG,GAAG;AAEpB,UAAM,QAAQ,CAAC;AACf,QAAI,QAAQ,CAACO,OAAO,MAAMA,EAAC,IAAI,MAAM,IAAK;AAC1C,oBAAgB;AAAA,EACpB,OACK;AAGD,+BAA2B,GAAG;AAC9B,QAAI,IAAI,MAAM;AACV,2BAAqB,IAAI;AAAA,IAC7B;AACA,QAAI,IAAI,QAAQ;AACZ,2BAAqB;AAAA,QACjB,GAAG,IAAI;AAAA,QACP;AAAA,UACI,eAAe,IAAI;AAAA,UACnB,wBAAwB,IAAI;AAAA,UAC5B,wBAAwB,IAAI;AAAA,UAC5B,wBAAwB,IAAI;AAAA,QAChC;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,IAAI,kBAAkB,QAAW;AACjC,gCAA0B,eAAe,wBAAwB,wBAAwB,IAAI,eAAe,IAAI,aAAa;AAAA,IACjI;AACA,QAAI,IAAI,WAAW,QAAW;AAC1B,eAASJ,KAAI,GAAGA,KAAI,IAAI,UAAU,QAAQA,MAAK;AAC3C,cAAM,KAAK,IAAI,UAAUA,EAAC;AAC1B,YAAI,OAAO,IAAI,eAAe;AAC1B,oCAA0B,eAAe,wBAAwB,wBAAwB,IAAI,IAAI,MAAM;AAAA,QAC3G;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,IAAI,QAAQ,QAAW;AACvB,eAASA,KAAI,GAAGA,KAAI,IAAI,UAAU,QAAQA,MAAK;AAC3C,cAAM,KAAK,IAAI,UAAUA,EAAC;AAC1B,kCAA0B,eAAe,wBAAwB,wBAAwB,IAAI,IAAI,GAAG;AAAA,MACxG;AAAA,IACJ;AACA,QAAI,IAAI,OAAO;AACX,iBAAW,MAAM,IAAI,OAAO;AACxB,kCAA0B,eAAe,wBAAwB,wBAAwB,IAAI,IAAI,MAAM,EAAE,CAAC;AAAA,MAC9G;AAAA,IACJ;AACA,QAAI,IAAI,UAAU;AACd,iBAAW,MAAM,eAAe;AAC5B,YAAI,uBAAuB,EAAE,MAAM,QAAW;AAC1C,iCAAuB,EAAE,IAAI,IAAI;AAAA,QACrC;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,IAAI,UAAU;AACd,iBAAW,MAAM,eAAe;AAC5B,YAAI,uBAAuB,EAAE,MAAM,QAAW;AAC1C,iCAAuB,EAAE,IAAI,IAAI;AAAA,QACrC;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,OAAO,KAAK,aAAa,EAAE,WAAW,GAAG;AACzC,oBAAgB;AAAA,EACpB;AACA,MAAI,OAAO,KAAK,sBAAsB,EAAE,WAAW,GAAG;AAClD,6BAAyB;AAAA,EAC7B;AACA,MAAI,OAAO,KAAK,sBAAsB,EAAE,WAAW,GAAG;AAClD,6BAAyB;AAAA,EAC7B;AACA,QAAM,yBAAyB,CAAC;AAChC,aAAW,MAAM,eAAe;AAC5B,2BAAuB,EAAE,IAAI;AAAA,EACjC;AACA,SAAO;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAMA,SAAS,6BAA6B,KAAK;AACvC,MAAI,EAAE,eAAe,wBAAwB,wBAAwB,wBAAwB,oBAAoB,mBAAoB,IAAI;AACzI,MAAI,iBAAiB,OAAO,KAAK,aAAa,EAAE,WAAW,GAAG;AAC1D,QAAI,oBAAoB;AACpB,YAAM,iBAAiB,KAAK,kBAAkB;AAC9C,OAAC;AAAA,QACG;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,IAAI;AAAA,IACR,WACS,mBAAmB,SAAS,GAAG;AACpC,YAAM,YAAY,mBAAmB,SAAS;AAC9C,OAAC;AAAA,QACG;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACJ,IAAI,mBAAmB,SAAS;AAChC,2BAAqB,mBAAmB,MAAM,GAAG,SAAS;AAAA,IAC9D,OACK;AACD,sBAAgB;AAChB,+BAAyB;AACzB,+BAAyB;AAAA,IAC7B;AAAA,EACJ;AACA,SAAO;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AASA,SAAS,0BAA0B,eAAe,wBAAwB,wBAAwB,UAAU,KAAK;AAC7G,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM,MAAM;AAC/C,UAAM,EAAE,OAAO,IAAI;AAAA,EACvB;AACA,MAAI,IAAI,UAAU,QAAW;AAEzB,+BAA2B,GAAG;AAC9B,kBAAc,QAAQ,IAAI,IAAI;AAC9B,QAAI,IAAI;AACJ,6BAAuB,QAAQ,IAAI,IAAI;AAC3C,QAAI,IAAI;AACJ,6BAAuB,QAAQ,IAAI,IAAI;AAAA,EAC/C;AACJ;AAMA,SAAS,iBAAiB,WAAW,cAAc;AAE/C,SAAO,UAAU,YAAY,IAAI;AACrC;AASA,SAAS,mBAAmB,OAAO,MAAM;AACrC,MAAI,EAAE,GAAAH,IAAG,IAAI,IAAI;AACjB,QAAM,EAAE,WAAW,IAAI;AACvB,QAAM,aAAa,QAAQ,KAAK;AAChC,QAAM,UAAU,EAAE,GAAG,YAAY,GAAAA,IAAG,IAAI;AACxC,QAAM,QAAQ,KAAK;AACnB,MAAI,YAAY,CAAC,GAAG,MAAM,KAAK,EAAE,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI,CAACQ,IAAGL,OAAMA,KAAI,EAAE;AAC5E,MAAI,MAAM,cAAc,QAAW;AAC/B,gBAAY,MAAM,UAAU,OAAO;AAAA,EACvC;AACA,QAAM,eAAe,MAAM,MAAM,OAAO;AACxC,QAAM,UAAU,OAAO;AACvB,MAAI,YAAY,UAAU;AACtB,UAAM,oEAAoE,OAAO,KAAK,YAAY,IAAI;AAAA,EAC1G;AACA,QAAM,gBAAgB,iBAAiB,WAAW,YAAY;AAC9D,QAAM,EAAE,GAAG,KAAK,eAAe,cAAc,UAAU;AACvD,QAAM,iBAAiB,KAAK,KAAK,iBAAiB,CAAC,CAAC;AACpD,SAAO;AACX;AASA,SAAS,qBAAqB,OAAO,eAAe,MAAM,YAAY;AAClE,QAAM,QAAQ,KAAK;AACnB,MAAI,EAAE,GAAAH,IAAG,IAAI,IAAI;AACjB,MAAI,eAAe,IAAI;AACvB,MAAI,WAAW;AACf,MAAI,cAAc,eAAe,MAAM;AACnC,QAAI,OAAO,eAAe,UAAU;AAChC,YAAM,gCAAgC,UAAU,EAAE;AAAA,IACtD;AACA,WAAO,KAAK,UAAU,EAAE,QAAQ,CAAC,QAAQ;AACrC,cAAQ,KAAK;AAAA,QACT,KAAK;AACD,0BAAgB,iBAAiB,IAAI,WAAW,YAAY;AAC5D;AAAA,QACJ,KAAK;AACD,yBAAe,IAAI,UAAU,QAAQ,WAAW,IAAI;AACpD,0BAAgB,WAAW;AAC3B;AAAA,QACJ;AACI,gBAAM,gCAAgC,GAAG,EAAE;AAAA,MACnD;AAAA,IACJ,CAAC;AAAA,EACL,OACK;AACD,UAAM,aAAa,QAAQ,KAAK;AAChC,UAAM,UAAU,EAAE,GAAG,YAAY,GAAAA,IAAG,IAAI;AACxC,UAAMS,KAAI,MAAM,KAAK,OAAO;AAC5B,UAAM,OAAO,OAAOA;AACpB,QAAIA,OAAM,UAAa,SAAS,UAAU;AACtC,YAAM,gFAAgF,IAAI,KAAKA,EAAC,IAAI;AAAA,IACxG;AACA,QAAIA,OAAM,QAAW;AACjB,iBAAW;AAAA,IACf,OACK;AACD,qBAAeA;AACf,sBAAgB,iBAAiB,IAAI,WAAW,YAAY;AAAA,IAChE;AAAA,EACJ;AACA,QAAM;AAAA,IACF,GAAG;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AACA,SAAO,EAAE,UAAU,IAAI;AAC3B;AAYA,IAAM,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMd,SAAS;AAAA,IACL,OAAO,CAAC,EAAE,IAAI,MAAM,IAAI,SAAS,IAC3B,IAAI,gBACH,IAAI,eAAe,KAAK,IAAI,UAAU;AAAA,IAC7C,MAAM,CAAC,EAAE,IAAI,OAAO,IAAI,eAAe,KAAK,IAAI,UAAU;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AAAA,IACH,OAAO,MAAM;AAAA,IACb,MAAM,CAAC,EAAE,IAAI,OAAO,IAAI,eAAe,KAAK,IAAI,UAAU;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AAAA,IACN,OAAO,CAAC,EAAE,IAAI,MAAM,IAAI;AAAA,IACxB,MAAM,CAAC,EAAE,IAAI,OAAO,IAAI,eAAe,KAAK,IAAI,UAAU;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM;AAAA,IACF,OAAO,MAAM;AAAA,IACb,MAAM,CAAC,EAAE,IAAI,MAAM;AACf,UAAI,IAAI,eAAe,IAAI,UAAU,SAAS,GAAG;AAC7C,eAAO,IAAI,eAAe;AAAA,MAC9B;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,CAAC,eAAe;AAAA,IACpB,WAAW,MAAM;AAAA,IACjB,OAAO,MAAM;AAAA,IACb,MAAM,CAAC,EAAE,IAAI,OAAO,IAAI,eAAe,KAAK,IAAI,UAAU;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,CAAC,oBAAoB;AAAA,IAC9B,WAAW,CAAC,EAAE,GAAAT,GAAE,MAAMA,GAAE,cAAc;AAAA,IACtC,OAAO,MAAM;AAAA,IACb,MAAM,CAAC,EAAE,IAAI,OAAO,IAAI,eAAe,KAAK,IAAI,UAAU;AAAA,EAC9D;AACJ;AACA,IAAM,QAAQ;AAAA,EACV,MAAM;AACV;AACA,IAAM,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOlB,KAAK,EAAE,KAAK,MAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQvB,UAAU,EAAE,KAAK,MAAM,MAAM,UAAU,GAAG,UAAU,EAAE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOtD,QAAQ,EAAE,QAAQ,MAAM,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ7B,aAAa,EAAE,QAAQ,MAAM,MAAM,UAAU,GAAG,UAAU,EAAE;AAChE;;;AC/jCA,qBAA2B;AAc3B,SAAS,KAAK,EAAE,OAAO,QAAQ,OAAO,OAAO,MAAM,QAAQ,QAAS,GAAG;AAEnE,MAAI,UAAU,QAAW;AACrB,YAAQ,CAAC;AAAA,EACb;AACA,MAAI,WAAW,QAAW;AACtB,aAAS,CAAC;AAAA,EACd;AACA,MAAI,YAAY,QAAW;AACvB,cAAU,CAAC;AAAA,EACf;AACA,MAAI,WAAW,QAAW;AACtB,aAAS,CAAC;AAAA,EACd;AACA,MAAI,CAAC;AACD,YAAQ,MAAM;AAClB,MAAI,CAAC;AACD,YAAQ,CAAC,EAAE,GAAAU,GAAE,MAAMA;AACvB,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,QAAM,WAAW,EAAE,GAAG,OAAO;AAC7B,MAAI,MAAM,UAAU;AAChB,UAAM,sCAAsC;AAAA,EAChD;AACA,WAAS,EAAE,IAAI,CAAC;AAChB,QAAM,UAAU,CAAC;AACjB,QAAM,YAAY,oBAAI,IAAI;AAC1B,MAAI,gBAAgB;AACpB,SAAO,KAAK,KAAK,EAAE,QAAQ,CAAC,SAAS,UAAU,IAAI,IAAI,CAAC;AACxD,QAAM,cAAc,CAAC,MAAM,aAAa;AACpC,UAAM,cAAc,OAAO,MAAM,UAAU,OAAO;AAClD,WAAO,CAAC,UAAU;AACd,YAAM,aAAa,QAAQ,KAAK;AAChC,aAAO,YAAY;AAAA,QACf,GAAG;AAAA,QACH,GAAG,MAAM;AAAA,QACT,KAAK,MAAM;AAAA,QACX,UAAU,MAAM;AAAA,MACpB,CAAC;AAAA,IACL;AAAA,EACJ;AACA,QAAM,iBAAiB,CAAC,YAAY;AAChC,WAAO,CAAC,UAAU;AACd,YAAM,aAAa,QAAQ,KAAK;AAChC,aAAO,QAAQ;AAAA,QACX,GAAG;AAAA,QACH,GAAG,MAAM;AAAA,QACT,KAAK,MAAM;AAAA,MACf,CAAC;AAAA,IACL;AAAA,EACJ;AACA,QAAM,UAAU;AAAA,IACZ,OAAO,YAAY,OAAO,WAAW,WAAW;AAAA,IAChD,OAAO,eAAe,KAAK;AAAA,EAC/B;AACA,aAAW,SAAS,UAAU;AAC1B,UAAM,cAAc,SAAS,KAAK;AAClC,QAAI,YAAY,UAAU,MAAM;AAC5B,sBAAgB;AAAA,IACpB;AACA,QAAI,YAAY,UAAU,QAAW;AACjC,iBAAW,QAAQ,OAAO,KAAK,YAAY,KAAK,GAAG;AAC/C,gBAAQ,QAAQ,MAAM,IAAI,IAAI,YAAY,MAAM,IAAI;AACpD,kBAAU,IAAI,IAAI;AAAA,MACtB;AAAA,IACJ;AACA,QAAI,YAAY,UAAU,QAAW;AACjC,kBAAY,QAAQ,MAAM;AAAA,IAC9B;AACA,QAAI,YAAY,YAAY,QAAW;AACnC,kBAAY,UAAU,CAAC,EAAE,GAAAA,GAAE,MAAMA;AAAA,IACrC;AACA,QAAI,YAAY,UAAU,QAAW;AACjC,kBAAY,QAAQ,CAAC,EAAE,GAAAA,GAAE,MAAMA;AAAA,IACnC;AACA,QAAI,YAAY,SAAS,QAAW;AAChC,kBAAY,OAAO;AAAA,IACvB;AACA,QAAI,YAAY,KAAK,UAAU,QAAW;AACtC,kBAAY,KAAK,QAAQ,UAAU;AAAA,IACvC;AACA,QAAI,YAAY,KAAK,YAAY,QAAW;AACxC,kBAAY,KAAK,UAAU,CAAC,EAAE,GAAAA,GAAE,MAAMA;AAAA,IAC1C;AACA,QAAI,YAAY,KAAK,UAAU,QAAW;AACtC,kBAAY,KAAK,QAAQ,CAAC,EAAE,GAAAA,GAAE,MAAMA;AAAA,IACxC;AACA,QAAI,YAAY,KAAK,UAAU,QAAW;AACtC,kBAAY,KAAK,QAAQ,MAAM;AAAA,IACnC;AACA,QAAI,YAAY,KAAK,WAAW,QAAW;AACvC,kBAAY,KAAK,SAAS,CAAC,EAAE,GAAAA,GAAE,MAAMA;AAAA,IACzC;AACA,QAAI,YAAY,KAAK,WAAW,QAAW;AACvC,kBAAY,KAAK,SAAS,CAAC;AAAA,IAC/B;AAEA,+BAA2B,YAAY,MAAM,IAAI;AACjD,eAAW,SAAS,YAAY,KAAK,QAAQ;AACzC,YAAM,cAAc,YAAY,KAAK,OAAO,KAAK;AACjD,YAAMC,SAAQ,YAAY,SAAS,CAAC;AACpC,iBAAW,QAAQ,OAAO,KAAKA,MAAK,GAAG;AACnC,cAAM,MAAM,QAAQ,MAAM,QAAQ,MAAM;AACxC,gBAAQ,GAAG,IAAIA,OAAM,IAAI;AACzB,kBAAU,IAAI,IAAI;AAAA,MACtB;AAAA,IACJ;AACA,gBAAY,UAAU;AAAA,MAClB,SAAS,YAAY,YAAY,SAAS,WAAW,cAAc;AAAA,MACnE,OAAO,YAAY,YAAY,OAAO,WAAW,YAAY;AAAA,MAC7D,OAAO,eAAe,YAAY,KAAK;AAAA,IAC3C;AACA,gBAAY,KAAK,UAAU;AAAA,MACvB,QAAQ,YAAY,YAAY,KAAK,QAAQ,WAAW,YAAY;AAAA,MACpE,SAAS,YAAY,YAAY,KAAK,SAAS,WAAW,aAAa;AAAA,MACvE,OAAO,YAAY,YAAY,KAAK,OAAO,WAAW,WAAW;AAAA,MACjE,OAAO,eAAe,YAAY,KAAK,KAAK;AAAA,IAChD;AACA,QAAI,OAAO,YAAY,SAAS,YAAY;AACxC,YAAM,EAAE,KAAK,IAAI;AACjB,kBAAY,OAAO,MAAM,QAAQ;AAAA,IACrC;AACA,gBAAY,QAAQ,OAAO,eAAe,YAAY,IAAI;AAAA,EAC9D;AACA,WAAS,SAAS,KAAK;AACnB,WAAO,IAAI,QAAQ,SAAS,IAAI,KAAK,IAAI,SAAS,EAAE;AAAA,EACxD;AACA,WAAS,OAAO,OAAO;AACnB,WAAO;AAAA,EACX;AACA,WAAS,QAAQ,OAAOC,SAAQ;AAC5B,UAAM,cAAc,oBAAI,IAAI;AAC5B,UAAM,aAAa,oBAAI,IAAI;AAC3B,aAASC,KAAI,GAAGA,KAAID,QAAO,QAAQC,MAAK;AACpC,YAAM,EAAE,IAAAC,KAAI,KAAK,GAAG,KAAK,IAAIF,QAAOC,EAAC;AAMrC,UAAIC,QAAO,UAAU;AACjB,mBAAW,MAAM;AACjB,cAAM,QAAQ,MAAM,IAAI;AACxB,YAAI,YAAY,IAAI,KAAK,GAAG;AACxB,gBAAM,MAAM,EAAE,GAAG,MAAM,KAAK,OAAO,KAAK;AACxC,iBAAO,EAAE,GAAG,OAAO,IAAI;AAAA,QAC3B;AACA,oBAAY,IAAI,KAAK;AAAA,MACzB;AAEA,YAAM,OAAO,CAAC;AACd,cAAQA,IAAG,OAAO;AAAA,QACd,GAAG;AAAA,QACH;AAAA,QACA;AAAA,MACJ,CAAC;AACD,UAAIA,QAAO,SAAS;AAChB;AAAA,MACJ;AAEA,YAAM,gBAAgB,cAAc,KAAK;AACzC,UAAI,eAAe;AACf,QAAAF,QAAO,KAAK;AAAA,UACR,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,MAAM,MAAM,IAAI;AAAA,UAChB,OAAO,MAAM,IAAI;AAAA,UACjB,WAAW;AAAA,QACf,CAAC;AACD;AAAA,MACJ;AAEA,YAAM,iBAAiB,eAAe,KAAK;AAC3C,UAAI,gBAAgB;AAChB,QAAAA,QAAO,KAAK;AAAA,UACR,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,MAAM,MAAM,IAAI;AAAA,UAChB,OAAO,MAAM,IAAI;AAAA,UACjB,WAAW;AAAA,QACf,CAAC;AACD;AAAA,MACJ;AAEA,UAAI,CAAC,QAAQ,aAAa,mBAAmB,EAAE,SAASE,GAAE,GAAG;AACzD,cAAM,gBAAgB,cAAc,KAAK;AACzC,YAAI,eAAe;AACf,UAAAF,QAAO,KAAK;AAAA,YACR,IAAI;AAAA,YACJ,KAAK;AAAA,YACL,MAAM,MAAM,IAAI;AAAA,YAChB,OAAO,MAAM,IAAI;AAAA,YACjB,WAAW;AAAA,UACf,CAAC;AACD;AAAA,QACJ;AAAA,MACJ;AACA,MAAAA,QAAO,KAAK,GAAG,IAAI;AAAA,IACvB;AACA,WAAO;AAAA,EACX;AAIA,WAAS,UAAU,OAAO,EAAE,KAAK,GAAG;AAChC,SAAK,KAAK,EAAE,IAAI,WAAW,CAAC;AAC5B,WAAO;AAAA,EACX;AACA,WAAS,WAAW,OAAO,EAAE,KAAK,GAAG;AACjC,QAAI,EAAE,GAAAF,IAAG,IAAI,IAAI;AACjB,UAAM,cAAc,SAAS,GAAG;AAEhC,IAAAA,KAAI,YAAY,QAAQ,QAAQ,KAAK;AACrC,SAAK,KAAK,EAAE,IAAI,UAAU,CAAC;AAC3B,WAAO,EAAE,GAAG,OAAO,GAAAA,IAAG,IAAI;AAAA,EAC9B;AACA,WAAS,UAAU,OAAO,EAAE,cAAc,GAAG;AACzC,QAAI,EAAE,IAAI,IAAI;AACd,UAAM,cAAc,SAAS,GAAG;AAEhC,QAAI,eAAe;AACf,YAAM,EAAE,GAAG,KAAK,cAAc;AAC9B,UAAI,YAAY,KAAK,eAAe;AAChC,cAAM,iBAAiB,KAAK,YAAY,KAAK,aAAa;AAAA,MAC9D;AAAA,IACJ,OACK;AAGD,YAAM,mBAAmB,OAAO,YAAY,IAAI;AAAA,IACpD;AACA,UAAMK,QAAO,IAAI,OAAO;AACxB,UAAM,EAAE,GAAG,KAAK,MAAAA,OAAM,UAAU,GAAG,oBAAoB,CAAC,EAAE;AAC1D,UAAML,KAAI,YAAY,KAAK,QAAQ,QAAQ,EAAE,GAAG,OAAO,IAAI,CAAC;AAC5D,WAAO,EAAE,GAAG,OAAO,GAAAA,IAAG,KAAK,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE;AAAA,EACpD;AAIA,WAAS,YAAY,OAAO,EAAE,KAAK,MAAM,MAAM,GAAG;AAC9C,UAAM,cAAc,SAAS,EAAE,MAAM,CAAC;AACtC,QAAI,EAAE,IAAI,IAAI;AACd,QAAI,OAAO,IAAI,MAAM;AACjB,UAAI,IAAI,QAAQ,UAAU;AACtB,cAAM,EAAE,GAAG,KAAK,OAAO,IAAI,KAAK;AAAA,MACpC,OACK;AACD,cAAM,oBAAoB,IAAI,IAAI;AAClC,eAAO;AAAA,MACX;AAAA,IACJ,OACK;AACD,YAAM,EAAE,GAAG,KAAK,OAAO,YAAY,QAAQ,KAAK,KAAK,KAAK,KAAK;AAAA,IACnE;AACA,YAAQ,EAAE,GAAG,OAAO,IAAI;AAExB,SAAK,KAAK,EAAE,IAAI,WAAW,CAAC;AAC5B,WAAO;AAAA,EACX;AACA,WAAS,WAAW,OAAO,EAAE,KAAK,eAAe,KAAK,GAAG;AACrD,QAAI,EAAE,GAAAA,IAAG,IAAI,IAAI;AACjB,UAAM,cAAc,SAAS,GAAG;AAEhC,UAAM,EAAE,UAAU,KAAK,OAAO,IAAI,qBAAqB,OAAO,eAAe,YAAY,MAAM,GAAG;AAClG,UAAM;AACN,YAAQ,EAAE,GAAG,OAAO,GAAAA,IAAG,IAAI;AAC3B,QAAI,UAAU;AACV,WAAK,KAAK,EAAE,IAAI,UAAU,MAAM,IAAI,MAAM,OAAO,IAAI,MAAM,CAAC;AAAA,IAChE,OACK;AACD,WAAK,KAAK,EAAE,IAAI,WAAW,eAAe,IAAI,cAAc,CAAC;AAAA,IACjE;AACA,WAAO;AAAA,EACX;AACA,WAAS,YAAY,OAAO,EAAE,KAAK,SAAS,GAAG;AAC3C,QAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM,MAAM;AAC/C,YAAM,EAAE,OAAO,IAAI;AAAA,IACvB;AACA,QAAI,OAAO,QAAQ;AACf,aAAO;AAGX,+BAA2B,GAAG;AAC9B,QAAI,EAAE,IAAI,IAAI;AACd,QAAI,EAAE,eAAe,wBAAwB,wBAAwB,uBAAwB,IAAI;AAEjG,QAAI,IAAI,UAAU,QAAW;AACzB,UAAI,kBAAkB,MAAM;AACxB,wBAAgB,CAAC;AAAA,MACrB;AACA,oBAAc,QAAQ,IAAI,IAAI;AAC9B,6BAAuB,QAAQ,IAAI;AACnC,UAAI,IAAI,UAAU;AACd,YAAI,2BAA2B,MAAM;AACjC,mCAAyB,CAAC;AAAA,QAC9B;AACA,+BAAuB,QAAQ,IAAI,IAAI;AAAA,MAC3C;AACA,UAAI,IAAI,UAAU;AACd,YAAI,2BAA2B,MAAM;AACjC,mCAAyB,CAAC;AAAA,QAC9B;AACA,+BAAuB,QAAQ,IAAI,IAAI;AAAA,MAC3C;AAAA,IACJ;AACA,UAAM;AAAA,MACF,GAAG;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,WAAO,EAAE,GAAG,OAAO,IAAI;AAAA,EAC3B;AACA,WAAS,oBAAoB,OAAO,EAAE,IAAI,GAAG;AACzC,WAAO,EAAE,GAAG,OAAO,KAAK,iBAAiB,MAAM,KAAK,GAAG,EAAE;AAAA,EAC7D;AAIA,WAAS,cAAc,OAAO;AAC1B,WAAO,QAAQ,MAAM,KAAK;AAAA,EAC9B;AACA,WAAS,eAAe,OAAO;AAC3B,UAAM,cAAc,SAAS,MAAM,GAAG;AACtC,WAAO,YAAY,QAAQ,MAAM,KAAK;AAAA,EAC1C;AACA,WAAS,cAAc,OAAO;AAC1B,UAAM,cAAc,SAAS,MAAM,GAAG;AAEtC,UAAM,qBAAqB,MAAM,IAAI,YAAY;AACjD,QAAI,YAAY,KAAK,YACjB,sBAAsB,YAAY,KAAK,UAAU;AACjD,aAAO;AAAA,IACX;AACA,WAAO,YAAY,KAAK,QAAQ,MAAM,KAAK;AAAA,EAC/C;AAIA,WAAS,QAAQ,OAAO,EAAE,KAAK,MAAM,GAAG;AACpC,YAAQ,SAAS,OAAO,EAAE,MAAM,CAAC;AACjC,QAAI,QAAQ,QAAW;AACnB,YAAM;AAAA,IACV;AACA,YAAQ,EAAE,GAAG,OAAO,KAAK,EAAE,GAAG,MAAM,KAAK,UAAU,IAAI,EAAE;AAEzD,UAAMA,KAAI,QAAQ,MAAM,KAAK;AAC7B,WAAO,EAAE,GAAG,OAAO,GAAAA,GAAE;AAAA,EACzB;AACA,WAAS,SAAS,OAAO,EAAE,KAAK,MAAM,MAAM,aAAa,UAAU,GAAG;AAElE,YAAQ,QAAQ,OAAO,EAAE,MAAM,aAAa,OAAO,MAAM,WAAW,KAAK,CAAC;AAC1E,UAAM,EAAE,OAAO,MAAAK,MAAK,IAAI,MAAM;AAC9B,QAAI,MAAM;AACN,WAAK,KAAK,EAAE,IAAI,aAAa,KAAK,MAAM,CAAC;AAAA,IAC7C;AAEA,QAAI,UAAU,MAAM;AAChB,aAAO;AAAA,IACX;AAEA,UAAM,cAAc,SAAS,MAAM,GAAG;AACtC,UAAML,KAAI,YAAY,QAAQ,MAAM,KAAK;AAEzC,UAAM,MAAM,EAAE,GAAG,MAAM,KAAK,OAAO,KAAK;AAExC,UAAM,SAAS,UAAU,YAAY,GAAG;AACxC,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,WAAW,EAAE,QAAQ,UAAU,MAAAK,OAAM,MAAM;AACjD,QAAI;AACA,eAAS,YAAY;AACzB,UAAM,WAAW,CAAC,GAAI,MAAM,YAAY,CAAC,GAAI,QAAQ;AACrD,WAAO,EAAE,GAAG,OAAO,GAAAL,IAAG,KAAK,SAAS;AAAA,EACxC;AACA,WAAS,QAAQ,OAAO,EAAE,KAAK,MAAM,MAAM,aAAa,OAAO,WAAW,SAAS,GAAG;AAGlF,QAAI,gBAAgB,MAAM,IAAI,MAAM;AAChC,aAAO;AAAA,IACX;AACA,UAAM,EAAE,eAAe,UAAU,OAAO,MAAAK,MAAK,IAAI,MAAM;AACvD,UAAM,cAAc,SAAS,MAAM,GAAG;AAEtC,UAAM,qBAAqB,YAAY;AACvC,QAAI,CAAC,SACD,YAAY,KAAK,YACjB,qBAAqB,YAAY,KAAK,UAAU;AAChD,WAAK,iCAAiC,YAAY,KAAK,QAAQ,QAAQ;AACvE,aAAO;AAAA,IACX;AAEA,UAAML,KAAI,YAAY,KAAK,QAAQ,MAAM,KAAK;AAC9C,QAAI,MAAM;AACN,WAAK,KAAK,EAAE,IAAI,YAAY,KAAK,cAAc,CAAC;AAAA,IACpD;AAEA,QAAI,MAAM,EAAE,GAAG,MAAM,KAAK,eAAe,KAAK;AAE9C,QAAI,OAAO,IAAI,QAAQ;AACnB,iBAAW,YAAY;AACvB,YAAM,YAAY,IAAI,UAAU,OAAO,CAACG,OAAMA,MAAK,QAAQ;AAC3D,YAAM,eAAe,IAAI,eAAe,UAAU,SAAS,IAAI,IAAI,IAAI;AACvE,YAAM,EAAE,GAAG,KAAK,WAAW,aAAa;AACxC,UAAI,UAAU,WAAW,GAAG;AACxB,aAAK,KAAK,EAAE,IAAI,UAAU,MAAAE,OAAM,MAAM,CAAC;AACvC,eAAO;AAAA,MACX;AAAA,IACJ;AAEA,UAAM,SAAS,UAAU,WAAW,GAAG;AACvC,UAAM,EAAE,SAAS,IAAI;AACrB,UAAM,WAAW,EAAE,QAAQ,UAAU,MAAAA,OAAM,MAAM;AACjD,QAAI;AACA,eAAS,YAAY;AACzB,UAAM,WAAW,CAAC,GAAI,MAAM,YAAY,CAAC,GAAI,QAAQ;AACrD,WAAO,EAAE,GAAG,OAAO,GAAAL,IAAG,KAAK,UAAU,OAAO,CAAC,GAAG,OAAO,CAAC,EAAE;AAAA,EAC9D;AACA,WAAS,SAAS,OAAO,EAAE,KAAK,MAAM,WAAW,SAAS,GAAG;AACzD,eAAW,YAAY,MAAM,IAAI;AACjC,QAAI,EAAE,KAAK,SAAS,IAAI;AACxB,QAAI,EAAE,eAAe,wBAAwB,wBAAwB,wBAAwB,OAAO,MAAAK,MAAM,IAAI;AAC9G,UAAM,gBAAgB,kBAAkB,QAAQ,YAAY;AAC5D,UAAM,cAAc,SAAS,GAAG;AAChC,QAAI,CAAC,OAAO,eAAe;AACvB,YAAM,QAAQ,YAAY,KAAK,OAAO,cAAc,QAAQ,CAAC;AAC7D,UAAI,SAAS,MAAM,MAAM;AACrB,cAAM,MAAM;AAAA,MAChB;AAAA,IACJ;AAEA,QAAI,MAAM;AACN,WAAK,KAAK,EAAE,IAAI,aAAa,KAAK,SAAS,CAAC;AAAA,IAChD;AAEA,QAAI,CAAC;AACD,aAAO;AAEX,UAAM,qBAAqB,uBAAuB,QAAQ,KAAK;AAC/D,QAAI,0BACA,uBAAuB,QAAQ,KAC/B,qBAAqB,uBAAuB,QAAQ,GAAG;AACvD,WAAK,kCAAkC,uBAAuB,QAAQ,CAAC,QAAQ;AAC/E,aAAO;AAAA,IACX;AAEA,oBAAgB,EAAE,GAAG,cAAc;AACnC,WAAO,cAAc,QAAQ;AAC7B,QAAI,wBAAwB;AAExB,+BAAyB,EAAE,GAAG,uBAAuB;AACrD,aAAO,uBAAuB,QAAQ;AAAA,IAC1C;AACA,QAAI,wBAAwB;AAExB,+BAAyB,EAAE,GAAG,uBAAuB;AACrD,aAAO,uBAAuB,QAAQ;AAAA,IAC1C;AACA,UAAM,6BAA6B;AAAA,MAC/B,GAAG;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AAED,UAAM,SAAS,UAAU,YAAY,GAAG;AACxC,UAAM,WAAW,EAAE,QAAQ,UAAU,MAAAA,OAAM,MAAM;AACjD,QAAI;AACA,eAAS,YAAY;AACzB,UAAM,WAAW,CAAC,GAAI,MAAM,YAAY,CAAC,GAAI,QAAQ;AACrD,WAAO,EAAE,GAAG,OAAO,KAAK,SAAS;AAAA,EACrC;AAkBA,WAAS,QAAQ,KAAK,MAAM,UAAU;AAClC,UAAM,cAAc,SAAS,GAAG;AAChC,UAAM,SAAS,YAAY,KAAK;AAChC,UAAM,EAAE,cAAc,IAAI;AAC1B,QAAI,iBACA,cAAc,QAAQ,MAAM,UAC5B,cAAc,QAAQ,MAAM,MAAM,QAClC,OAAO,cAAc,QAAQ,CAAC,MAAM,UACpC,OAAO,cAAc,QAAQ,CAAC,EAAE,UAAU,QAAW;AAErD,YAAM,QAAQ,OAAO,cAAc,QAAQ,CAAC;AAC5C,YAAMJ,SAAQ,MAAM;AACpB,UAAI,QAAQA,QAAO;AACf,eAAOA,OAAM,IAAI;AAAA,MACrB;AAAA,IACJ,WACS,YAAY,OAAO;AAExB,UAAI,QAAQ,YAAY,OAAO;AAC3B,eAAO,YAAY,MAAM,IAAI;AAAA,MACjC;AAAA,IACJ,WACS,QAAQ,OAAO;AAEpB,aAAO,MAAM,IAAI;AAAA,IACrB;AACA,WAAO;AAAA,EACX;AACA,WAAS,YAAY,OAAO,QAAQ;AAChC,UAAM,EAAE,UAAU,KAAK,IAAI;AAC3B,UAAM,EAAE,eAAe,eAAe,uBAAuB,IAAI,MAAM;AACvE,UAAM,OAAO,QAAQ,MAAM,KAAK,MAAM,QAAQ;AAC9C,UAAM,cAAc,CAAC,QAAQ,OAAO,SAAS,cAAc,KAAK,YAAY;AAC5E,QAAI,EAAE,UAAU,uBAAuB,IAAI,MAAM;AACjD,QAAI,aAAa;AACb,UAAI,aAAa;AACb;AACJ,UAAI;AACA,+BAAuB,QAAQ;AAAA,IACvC;AACA,YAAQ;AAAA,MACJ,GAAG;AAAA,MACH,KAAK;AAAA,QACD,GAAG,MAAM;AAAA,QACT;AAAA,QACA;AAAA,MACJ;AAAA,IACJ;AACA,QAAI,0BACA,uBAAuB,QAAQ,KAAK,uBAAuB,QAAQ,GAAG;AACtE,cAAQ,SAAS,OAAO,EAAE,UAAU,WAAW,KAAK,CAAC;AAAA,IACzD;AACA,UAAM,cAAc,SAAS,MAAM,GAAG;AACtC,UAAMD,KAAI,YAAY,KAAK,QAAQ,OAAO,EAAE,GAAG,OAAO,SAAS,CAAC;AAChE,YAAQ,EAAE,GAAG,OAAO,GAAAA,GAAE;AACtB,UAAME,UAAS,CAAC,EAAE,IAAI,OAAO,CAAC;AAC9B,WAAO,QAAQ,OAAOA,OAAM;AAAA,EAChC;AACA,WAAS,cAAc,OAAO,UAAU,KAAK;AACzC,WAAO,QAAQ,OAAO,CAAC,EAAE,IAAI,UAAU,KAAK,SAAS,CAAC,CAAC;AAAA,EAC3D;AACA,WAAS,cAAc,OAAO,UAAU;AACpC,WAAO,QAAQ,OAAO,CAAC,EAAE,IAAI,UAAU,SAAS,CAAC,CAAC;AAAA,EACtD;AACA,WAAS,sBAAsB,OAAO,WAAW,KAAK;AAClD,WAAO,QAAQ,OAAO,CAAC,EAAE,IAAI,qBAAqB,IAAI,CAAC,CAAC;AAAA,EAC5D;AACA,WAAS,cAAc,OAAO,WAAW,UAAU;AAC/C,WAAO,QAAQ,OAAO;AAAA,MAClB;AAAA,QACI,IAAI;AAAA,QACJ,OAAO,MAAM,IAAI;AAAA,QACjB,MAAM,MAAM,IAAI;AAAA,QAChB,KAAK,EAAE,MAAM,SAAS;AAAA,MAC1B;AAAA,IACJ,CAAC;AAAA,EACL;AACA,WAAS,cAAc,OAAO;AAC1B,WAAO,QAAQ,OAAO;AAAA,MAClB,EAAE,IAAI,UAAU,OAAO,MAAM,IAAI,OAAO,MAAM,MAAM,IAAI,KAAK;AAAA,IACjE,CAAC;AAAA,EACL;AACA,WAAS,aAAa,OAAO,WAAW,KAAK;AACzC,WAAO,QAAQ,OAAO;AAAA,MAClB,EAAE,IAAI,SAAS,MAAM,MAAM,IAAI,MAAM,OAAO,MAAM,IAAI,OAAO,IAAI;AAAA,IACrE,CAAC;AAAA,EACL;AACA,WAAS,UAAU,OAAO,WAAW,KAAK;AACtC,WAAO,QAAQ,OAAO;AAAA,MAClB;AAAA,QACI,IAAI;AAAA,QACJ,MAAM,MAAM,IAAI;AAAA,QAChB,OAAO,MAAM,IAAI;AAAA,QACjB,OAAO;AAAA,QACP;AAAA,MACJ;AAAA,IACJ,CAAC;AAAA,EACL;AACA,WAAS,aAAa,OAAO,WAAW,KAAK;AACzC,WAAO,QAAQ,OAAO;AAAA,MAClB,EAAE,IAAI,SAAS,MAAM,MAAM,IAAI,MAAM,OAAO,MAAM,IAAI,OAAO,IAAI;AAAA,IACrE,CAAC;AAAA,EACL;AACA,QAAM,gBAAgB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,MAAM;AAAA,IACN,UAAU;AAAA,IACV,UAAU;AAAA,IACV,SAAS;AAAA,IACT,kBAAkB;AAAA,EACtB;AACA,QAAM,oBAAoB,CAAC;AAC3B,MAAI,OAAO,YAAY,OAAO;AAC1B,sBAAkB,KAAK,SAAS;AAAA,EACpC;AACA,MAAI,OAAO,SAAS,OAAO;AACvB,sBAAkB,KAAK,MAAM;AAAA,EACjC;AACA,MAAI,OAAO,aAAa,OAAO;AAC3B,sBAAkB,KAAK,UAAU;AAAA,EACrC;AACA,MAAI,OAAO,aAAa,OAAO;AAC3B,sBAAkB,KAAK,UAAU;AAAA,EACrC;AACA,MAAI,OAAO,YAAY,OAAO;AAC1B,sBAAkB,KAAK,SAAS;AAAA,EACpC;AACA,MAAI,OAAO,qBAAqB,OAAO;AACnC,sBAAkB,KAAK,kBAAkB;AAAA,EAC7C;AACA,MAAI,OAAO,aAAa,OAAO;AAC3B,sBAAkB,KAAK,UAAU;AAAA,EACrC;AACA,MAAI,OAAO,aAAa,OAAO;AAC3B,sBAAkB,KAAK,UAAU;AAAA,EACrC;AACA,WAAS,aAAa,OAAO,QAAQ;AACjC,UAAM,EAAE,MAAM,UAAU,KAAK,IAAI,OAAO;AACxC,QAAI,OAAO,cAAc,IAAI,MAAM;AAC/B,aAAO;AACX,WAAO,cAAc,IAAI,EAAE,OAAO,UAAU,GAAI,MAAM,QAAQ,IAAI,IAAI,OAAO,CAAC,IAAI,CAAE;AAAA,EACxF;AACA,WAAS,eAAe,IAAI,KAAK,UAAU;AACvC,QAAI,IAAI,eAAe;AACnB,aAAO,YAAY,IAAI;AAAA,IAC3B;AACA,WAAO,IAAI,kBAAkB;AAAA,EACjC;AACA,SAAO;AAAA,IACH,KAAK,CAAC,gBAAgB;AAAA,MAClB;AAAA,MACA,MAAM;AAAA,MACN,eAAe;AAAA,MACf,WAAW,CAAC,GAAG,MAAM,KAAK,EAAE,QAAQ,WAAW,CAAC,CAAC,EAAE,IAAI,CAACI,IAAGH,OAAMA,KAAI,EAAE;AAAA,MACvE,cAAc;AAAA,MACd,OAAO;AAAA,MACP,eAAe;AAAA,IACnB;AAAA,IACA,MAAM,CAAC,UAAU;AACb,aAAO,QAAQ,OAAO,CAAC,EAAE,IAAI,UAAU,CAAC,CAAC;AAAA,IAC7C;AAAA,IACA,gBAAgB;AAAA,IAChB;AAAA,IACA,YAAY,OAAO,KAAK,aAAa;AAAA,IACrC;AAAA,IACA;AAAA,IACA,WAAW,CAAC,GAAG,UAAU,OAAO,CAAC;AAAA,IACjC,aAAa;AAAA,IACb,cAAc;AAAA,IACd,SAAS;AAAA,EACb;AACJ;AASA,SAAS,YAAY,MAAM;AACvB,SAAO,KAAK,gBAAgB;AAChC;AAeA,SAAS,kBAAkB,MAAM;AAG7B,MAAI,YAAY,IAAI,GAAG;AACnB,WAAO;AAAA,EACX;AACA,MAAI,KAAK,SAAS;AACd,SAAK,OAAO;AAChB,MAAI,KAAK,eAAe;AACpB,SAAK,aAAa;AACtB,MAAI,KAAK,gBAAgB;AACrB,SAAK,cAAc;AACvB,MAAI,KAAK,UAAU;AACf,SAAK,QAAQ,OAAO,CAAC;AACzB,MAAI,KAAK,UAAU;AACf,SAAK,QAAQ,CAAC;AAClB,MAAI,KAAK,eAAe;AACpB,SAAK,aAAa,CAAC,EAAE,GAAAH,GAAE,MAAMA;AACjC,MAAI,KAAK,YAAY;AACjB,SAAK,UAAU,CAAC;AACpB,OAAK,QAAQ,QAAQ,CAACO,YAAW;AAC7B,QAAIA,QAAO,SAAS,QAAW;AAC3B,YAAM,IAAI,MAAM,+BAA+B;AAAA,IACnD;AACA,QAAIA,QAAO,KAAK,SAAS,GAAG,GAAG;AAC3B,YAAM,IAAI,MAAMA,QAAO,OAAO,uCAAuC;AAAA,IACzE;AAAA,EACJ,CAAC;AACD,MAAI,KAAK,KAAK,SAAS,GAAG,GAAG;AACzB,UAAM,IAAI,MAAM,KAAK,OAAO,qCAAqC;AAAA,EACrE;AACA,QAAM,OAAO,KAAK,IAAI;AACtB,SAAO;AAAA,IACH,GAAG;AAAA,IACH;AAAA,IACA,WAAW,KAAK;AAAA,IAChB,aAAa,KAAK,QAAQ,IAAI,CAACC,OAAMA,GAAE,IAAI;AAAA,IAC3C,aAAa,CAAC,OAAO,WAAW;AAC5B,UAAI,SAAS,KAAK,QAAQ,MAAM,KAAK,OAAO,MAAM,OAAO,QAAQ;AACjE,UAAI,eAAe,MAAM,GAAG;AACxB,iBAAS,OAAO;AAAA,MACpB;AACA,UAAI,kBAAkB,UAAU;AAC5B,cAAMJ,MAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,OAAO;AACvD,YAAI,OAAO,CAAC;AACZ,YAAI,OAAO,SAAS,QAAW;AAC3B,iBAAO,MAAM,QAAQ,OAAO,IAAI,IAAI,OAAO,OAAO,CAAC,OAAO,IAAI;AAAA,QAClE;AACA,cAAM,UAAU;AAAA,UACZ,GAAG,QAAQ,KAAK;AAAA,UAChB,GAAG,MAAM;AAAA,UACT,KAAK,MAAM;AAAA,UACX,UAAU,OAAO;AAAA,QACrB;AACA,eAAOA,IAAG,SAAS,GAAG,IAAI;AAAA,MAC9B;AACA,YAAM,wBAAwB,OAAO,IAAI,EAAE;AAC3C,aAAO,MAAM;AAAA,IACjB;AAAA,EACJ;AACJ;AACA,SAAS,eAAe,MAAM;AAC1B,SAAO,gBAAgB,UAAU,KAAK,SAAS;AACnD;AASA,IAAI;AAAA,CACH,SAAUK,kBAAiB;AAExB,EAAAA,iBAAgB,oBAAoB,IAAI;AAExC,EAAAA,iBAAgB,eAAe,IAAI;AAEnC,EAAAA,iBAAgB,aAAa,IAAI;AACrC,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAC5C,IAAI;AAAA,CACH,SAAUC,kBAAiB;AAExB,EAAAA,iBAAgB,cAAc,IAAI;AAElC,EAAAA,iBAAgB,iBAAiB,IAAI;AAErC,EAAAA,iBAAgB,aAAa,IAAI;AAEjC,EAAAA,iBAAgB,gBAAgB,IAAI;AAEpC,EAAAA,iBAAgB,UAAU,IAAI;AAE9B,EAAAA,iBAAgB,gBAAgB,IAAI;AAEpC,EAAAA,iBAAgB,eAAe,IAAI;AAEnC,EAAAA,iBAAgB,qBAAqB,IAAI;AAC7C,GAAG,oBAAoB,kBAAkB,CAAC,EAAE;AAY5C,IAAM,oBAAoB,CAAC,WAAW,OAAO,QAAQ,aAAa,QAAQ,OAAO,QAAQ,aAAa;AAItG,IAAM,cAAc,CAACV,IAAG,KAAK,SAAS;AAClC,WAAS,YAAYW,OAAM;AACvB,WAAOA,MAAK,aAAa;AAAA,EAC7B;AACA,WAAS,WAAW,UAAU;AAC1B,WAAO,oBAAoB;AAAA,EAC/B;AACA,MAAI,CAAC,YAAY,IAAI,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,KAAK,QAAQ,GAAG;AAC3B,WAAO,KAAK,SAAS,EAAE,GAAAX,IAAG,IAAI,CAAC;AAAA,EACnC;AACA,SAAO,KAAK;AAChB;AAIA,SAAS,oBAAoB,OAAO,MAAM;AACtC,MAAI,KAAK,KAAK;AACV,WAAO;AACX,QAAM,YAAY;AAAA,IACd,GAAG,MAAM;AAAA,IACT,KAAK,MAAM;AAAA,IACX,SAAS,MAAM;AAAA,IACf,UAAU,KAAK,OAAO,QAAQ,YAAY,MAAM,IAAI;AAAA,EACxD;AACA,MAAI,KAAK,OAAO,SAAS,aAAa;AAClC,cAAU,WAAW,KAAK,OAAO,QAAQ;AAAA,EAC7C;AACA,SAAO;AAAA,IACH,GAAG;AAAA,IACH,OAAO,CAAC,GAAG,MAAM,OAAO,SAAS;AAAA;AAAA,IAEjC,OAAO,CAAC;AAAA,EACZ;AACJ;AAIA,SAAS,mBAAmB,OAAO,QAAQ,MAAM;AAE7C,QAAM,WAAW;AAAA,IACb;AAAA,IACA,UAAU,MAAM;AAAA,IAChB,MAAM,MAAM,IAAI;AAAA,IAChB,OAAO,MAAM,IAAI;AAAA,EACrB;AACA,QAAM,oBAAoB,MAAM,QAAQ,IAAI,KAAK;AACjD,MAAI,sBAAsB,QAAW;AACjC,aAAS,WAAW;AAAA,EACxB;AACA,MAAI,OAAO,SAAS,YAAY,KAAK,WAAW,MAAM;AAClD,aAAS,SAAS;AAAA,EACtB,WACS,OAAO,SAAS,YAAY,KAAK,kBAAkB,UAAU;AAClE,aAAS,SAAS,KAAK,OAAO,EAAE,GAAG,MAAM,GAAG,KAAK,MAAM,IAAI,CAAC;AAAA,EAChE;AACA,SAAO;AAAA,IACH,GAAG;AAAA,IACH,UAAU,CAAC,QAAQ;AAAA,EACvB;AACJ;AASA,SAAS,wBAAwB,OAAO,UAAU,YAAY;AAC1D,QAAM,CAAC,UAAU,SAAS,IAAI,iBAAiB,OAAO,UAAU;AAChE,MAAI,CAAC;AACD,WAAO,CAAC,QAAQ;AACpB,SAAO;AAAA,IACH;AAAA,IACA,UAAU,UAAU,gBAAgB,qBAAqB,SAAS;AAAA,EACtE;AACJ;AAMA,SAAS,kBAAkB,gBAAgB;AACvC,MAAI,CAAC,gBAAgB;AAIjB,WAAO,CAAC,MAAM,MAAS;AAAA,EAC3B;AACA,QAAM,EAAE,YAAY,GAAG,MAAM,IAAI;AACjC,SAAO,CAAC,OAAO,UAAU;AAC7B;AAMA,SAAS,UAAU,OAAO,WAAW,SAAS;AAC1C,QAAMY,SAAQ;AAAA,IACV,MAAM;AAAA,IACN;AAAA,EACJ;AACA,SAAO;AAAA,IACH,GAAG;AAAA,IACH,YAAY;AAAA,MACR,OAAAA;AAAA,IACJ;AAAA,EACJ;AACJ;AAOA,IAAM,8BAA8B,CAAC,UAAU,CAAC,SAAS,CAAC,WAAW;AACjE,QAAM,SAAS,KAAK,MAAM;AAC1B,UAAQ,OAAO,MAAM;AAAA,IACjB,KAAK,kBAAkB;AACnB,aAAO;AAAA,IACX;AAAA,IACA,SAAS;AACL,YAAM,CAAC,EAAE,UAAU,IAAI,kBAAkB,MAAM,SAAS,CAAC;AACzD,UAAI,OAAO,eAAe,aAAa;AACnC,cAAM,SAAS,gBAAgB,CAAC;AAMhC,eAAO;AAAA,UACH,GAAG;AAAA,UACH;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAMA,SAAS,kBAAkB,EAAE,MAAM,SAAU,GAAG;AAC5C,SAAO,kBAAkB,IAAI;AAQ7B,SAAO,CAAC,sBAAsB,MAAM,WAAW;AAC3C,QAAI;AAAA,MAAC;AAAA;AAAA,IAAuB,IAAI,kBAAkB,mBAAmB;AACrE,YAAQ,OAAO,MAAM;AAAA,MACjB,KAAK,kBAAkB;AAGnB,eAAO;AAAA,MACX;AAAA,MACA,KAAK,YAAY;AACb,gBAAQ,EAAE,GAAG,OAAO,UAAU,CAAC,EAAE;AAKjC,YAAI,UAAU;AACV,iBAAO;AAAA,QACX;AAEA,YAAI,MAAM,IAAI,aAAa,QAAW;AAClC,gBAAM,kCAAkC;AACxC,iBAAO,UAAU,OAAO,gBAAgB,QAAQ;AAAA,QACpD;AAEA,YAAI,kBAAkB,MAAM,KACxB,CAAC,KAAK,KAAK,eAAe,MAAM,GAAG,MAAM,KAAK,OAAO,QAAQ,QAAQ,GAAG;AACxE,gBAAM,qBAAqB,OAAO,QAAQ,IAAI,EAAE;AAChD,iBAAO,UAAU,OAAO,gBAAgB,cAAc;AAAA,QAC1D;AAEA,gBAAQ,QAAQ,OAAO;AAAA,UACnB;AAAA,UACA,UAAU;AAAA,UACV,UAAU,OAAO,QAAQ;AAAA,QAC7B,CAAC;AAED,YAAI,WAAW,KAAK,KAAK,aAAa,OAAO,MAAM;AAEnD,YAAI;AACJ,SAAC,UAAU,cAAc,IAAI,wBAAwB,UAAU,OAAO;AAAA,UAClE;AAAA,UACA,UAAU;AAAA,QACd,CAAC;AACD,YAAI;AACA,iBAAO;AAEX,mBAAW,oBAAoB,UAAU,EAAE,MAAM,OAAO,CAAC;AACzD,eAAO,EAAE,GAAG,UAAU,UAAU,MAAM,WAAW,EAAE;AAAA,MACvD;AAAA,MACA,KAAK,WAAW;AACZ,cAAM,WAAY,QAAQ,EAAE,GAAG,OAAO,UAAU,CAAC,EAAE;AAEnD,cAAM,OAAO,KAAK,KAAK,QAAQ,MAAM,KAAK,OAAO,QAAQ,MAAM,OAAO,QAAQ,YAAY,MAAM,IAAI,aAAa;AACjH,YAAI,SAAS,MAAM;AACf,gBAAM,oBAAoB,OAAO,QAAQ,IAAI,EAAE;AAC/C,iBAAO,UAAU,OAAO,gBAAgB,eAAe;AAAA,QAC3D;AAEA,YAAI,YAAY,KAAK,WAAW,OAAO;AACnC,iBAAO;AAAA,QACX;AAEA,YAAI,MAAM,IAAI,aAAa,QAAW;AAClC,gBAAM,iCAAiC;AACvC,iBAAO,UAAU,OAAO,gBAAgB,QAAQ;AAAA,QACpD;AAEA,YAAI,kBAAkB,MAAM,KACxB,CAAC,KAAK,KAAK,eAAe,MAAM,GAAG,MAAM,KAAK,OAAO,QAAQ,QAAQ,GAAG;AACxE,gBAAM,oBAAoB,OAAO,QAAQ,IAAI,EAAE;AAC/C,iBAAO,UAAU,OAAO,gBAAgB,cAAc;AAAA,QAC1D;AAEA,gBAAQ,QAAQ,OAAO;AAAA,UACnB;AAAA,UACA;AAAA,UACA,UAAU,OAAO,QAAQ;AAAA,QAC7B,CAAC;AAED,cAAMZ,KAAI,KAAK,YAAY,OAAO,OAAO,OAAO;AAEhD,YAAIA,OAAM,cAAc;AACpB,gBAAM,iBAAiB,OAAO,QAAQ,IAAI,UAAU,OAAO,QAAQ,IAAI,EAAE;AAEzE,iBAAO,UAAU,OAAO,gBAAgB,WAAW;AAAA,QACvD;AACA,cAAM,WAAW,EAAE,GAAG,OAAO,GAAAA,GAAE;AAI/B,YAAI,YAAY,SAAS,UAAU,EAAE,KAAK,CAAC,GAAG;AAC1C,iBAAO;AAAA,QACX;AACA,gBAAQ;AAKR,YAAI,UAAU;AACV,cAAIa;AACJ,WAAC,OAAOA,eAAc,IAAI,wBAAwB,OAAO,UAAU;AAAA,YAC/D;AAAA,YACA,UAAU;AAAA,UACd,CAAC;AACD,cAAIA;AACA,mBAAOA;AACX,iBAAO;AAAA,YACH,GAAG;AAAA,YACH,UAAU,MAAM,WAAW;AAAA,UAC/B;AAAA,QACJ;AAEA,gBAAQ,mBAAmB,OAAO,QAAQ,IAAI;AAE9C,gBAAQ,KAAK,KAAK,YAAY,OAAO,OAAO,OAAO;AACnD,YAAI;AACJ,SAAC,OAAO,cAAc,IAAI,wBAAwB,OAAO,UAAU;AAAA,UAC/D;AAAA,QACJ,CAAC;AACD,YAAI;AACA,iBAAO;AAEX,gBAAQ,oBAAoB,OAAO,EAAE,MAAM,OAAO,CAAC;AACnD,eAAO;AAAA,UACH,GAAG;AAAA,UACH,UAAU,MAAM,WAAW;AAAA,QAC/B;AAAA,MACJ;AAAA,MACA,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,MAAM;AACP,eAAO,OAAO;AAAA,MAClB;AAAA,MACA,KAAK,MAAM;AACP,gBAAQ,EAAE,GAAG,OAAO,UAAU,CAAC,EAAE;AACjC,YAAI,KAAK,aAAa;AAClB,gBAAM,qBAAqB;AAC3B,iBAAO,UAAU,OAAO,gBAAgB,cAAc;AAAA,QAC1D;AACA,cAAM,EAAE,GAAAb,IAAG,KAAK,OAAO,OAAO,SAAS,IAAI;AAC3C,YAAI,MAAM,SAAS,GAAG;AAClB,gBAAM,kBAAkB;AACxB,iBAAO,UAAU,OAAO,gBAAgB,aAAa;AAAA,QACzD;AACA,cAAM,OAAO,MAAM,MAAM,SAAS,CAAC;AACnC,cAAM,UAAU,MAAM,MAAM,SAAS,CAAC;AAEtC,YAAI,kBAAkB,MAAM,KACxB,OAAO,QAAQ,aAAa,KAAK,UAAU;AAC3C,gBAAM,kCAAkC;AACxC,iBAAO,UAAU,OAAO,gBAAgB,aAAa;AAAA,QACzD;AAEA,YAAI,KAAK,UAAU;AACf,gBAAM,WAAW,KAAK,KAAK,QAAQ,QAAQ,KAAK,KAAK,UAAU,KAAK,QAAQ;AAC5E,cAAI,CAAC,YAAYA,IAAG,KAAK,QAAQ,GAAG;AAChC,kBAAM,uBAAuB;AAC7B,mBAAO,UAAU,OAAO,gBAAgB,aAAa;AAAA,UACzD;AAAA,QACJ;AACA,gBAAQ,mBAAmB,OAAO,MAAM;AACxC,eAAO;AAAA,UACH,GAAG;AAAA,UACH,GAAG,QAAQ;AAAA,UACX,KAAK,QAAQ;AAAA,UACb,SAAS,QAAQ;AAAA,UACjB,UAAU,WAAW;AAAA,UACrB,OAAO,MAAM,MAAM,GAAG,EAAE;AAAA,UACxB,OAAO,CAAC,MAAM,GAAG,KAAK;AAAA,QAC1B;AAAA,MACJ;AAAA,MACA,KAAK,MAAM;AACP,gBAAQ,EAAE,GAAG,OAAO,UAAU,CAAC,EAAE;AACjC,YAAI,KAAK,aAAa;AAClB,gBAAM,qBAAqB;AAC3B,iBAAO,UAAU,OAAO,gBAAgB,cAAc;AAAA,QAC1D;AACA,cAAM,EAAE,OAAO,OAAO,SAAS,IAAI;AACnC,YAAI,MAAM,WAAW,GAAG;AACpB,gBAAM,kBAAkB;AACxB,iBAAO,UAAU,OAAO,gBAAgB,aAAa;AAAA,QACzD;AACA,cAAM,QAAQ,MAAM,CAAC;AAErB,YAAI,kBAAkB,MAAM,KACxB,OAAO,QAAQ,aAAa,MAAM,UAAU;AAC5C,gBAAM,kCAAkC;AACxC,iBAAO,UAAU,OAAO,gBAAgB,aAAa;AAAA,QACzD;AACA,gBAAQ,mBAAmB,OAAO,MAAM;AACxC,eAAO;AAAA,UACH,GAAG;AAAA,UACH,GAAG,MAAM;AAAA,UACT,KAAK,MAAM;AAAA,UACX,SAAS,MAAM;AAAA,UACf,UAAU,WAAW;AAAA,UACrB,OAAO,CAAC,GAAG,OAAO,KAAK;AAAA,UACvB,OAAO,MAAM,MAAM,CAAC;AAAA,QACxB;AAAA,MACJ;AAAA,MACA,KAAK,QAAQ;AAET,eAAO,cAAc,OAAO,QAAQ,EAAE,KAAK,CAAC;AAAA,MAChD;AAAA,MACA,KAAK,OAAO;AACR,cAAM,WAAW;AACjB,cAAM,WAAW,KAAK,MAAM,KAAK,UAAU,QAAQ,CAAC;AACpD,cAAM,iBAAa,2BAAW,UAAU,OAAO,KAAK;AACpD,cAAM,WAAW,WAAW,KAAK,CAAC,UAAU,UAAU,IAAI;AAC1D,YAAI,UAAU;AACV,gBAAM,SAAS,KAAK,UAAU,OAAO,KAAK,CAAC,eAAe;AAC1D,iBAAO,UAAU,UAAU,gBAAgB,aAAa,UAAU;AAAA,QACtE,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,SAAS;AACL,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AACJ;;;AC9pCA,SAAS,eAAe,EAAE,MAAM,YAAY,UAAW,GAAG;AACtD,SAAO,kBAAkB,IAAI;AAC7B,MAAI,CAAC,YAAY;AACb,iBAAa;AAAA,EACjB;AACA,QAAM,MAAM,KAAK,KAAK,IAAI,UAAU;AACpC,MAAI,QAAQ;AAAA;AAAA,IAER,GAAG,CAAC;AAAA;AAAA,IAEJ;AAAA;AAAA,IAEA,SAAS,CAAC;AAAA,EACd;AAEA,UAAQ,MAAM,OAAO,EAAE,KAAK,CAAC;AAC7B,UAAQ,QAAQ,OAAO,EAAE,MAAM,UAAU,OAAU,CAAC;AACpD,QAAM,aAAa,QAAQ,KAAK;AAChC,QAAM,IAAI,KAAK,MAAM,EAAE,GAAG,YAAY,KAAK,MAAM,IAAI,GAAG,SAAS;AACjE,MAAI,UAAU;AAAA,IACV,GAAG;AAAA;AAAA,IAEH,OAAO,CAAC;AAAA;AAAA,IAER,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA,IAIR,UAAU;AAAA,EACd;AACA,YAAU,KAAK,KAAK,KAAK,OAAO;AAChC,GAAC,OAAO,IAAI,iBAAiB,SAAS,EAAE,KAAK,CAAC;AAE9C,MAAI,CAAC,KAAK,aAAa;AACnB,YAAQ,QAAQ;AAAA,MACZ;AAAA,QACI,GAAG,QAAQ;AAAA,QACX,KAAK,QAAQ;AAAA,QACb,SAAS,QAAQ;AAAA,MACrB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;ACjDA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,EAAE,uBAAuB,UAAU,UAAU,SAAS,aAAa,WAAY,GAAG;AAE1F,SAAK,2BAA2B,MAAM;AAAA,IAAE;AACxC,SAAK,cAAc;AACnB,SAAK,wBAAwB;AAC7B,SAAK,WAAW,YAAY;AAC5B,SAAK,WAAW,YAAY;AAC5B,SAAK,UAAU,WAAW;AAC1B,SAAK,cAAc;AACnB,SAAK,aAAa,cAAc;AAAA,EACpC;AAAA;AAAA,EAEA,4BAA4Bc,KAAI;AAC5B,SAAK,2BAA2BA;AAAA,EACpC;AAAA;AAAA,EAEA,oBAAoB,aAAa;AAC7B,SAAK,cAAc;AACnB,SAAK,yBAAyB;AAAA,EAClC;AAAA;AAAA,EAEA,aAAa,MAAM;AACf,SAAK,sBAAsB,IAAI;AAAA,EACnC;AACJ;", "names": ["isPlainObject", "Pointer", "i", "l", "i", "i", "j", "a", "b", "d", "b", "p", "Missing<PERSON><PERSON><PERSON>", "TestError", "copy", "InvalidOperationError", "applyPatch", "patch", "o", "t", "r", "i", "t", "i", "r", "t", "o", "randomString", "i", "subscribe", "a", "b", "createStore", "die", "error", "args", "e", "errors", "msg", "apply", "Error", "length", "map", "s", "join", "isDraft", "value", "DRAFT_STATE", "isDraftable", "proto", "Object", "getPrototypeOf", "Ctor", "hasOwnProperty", "call", "constructor", "Function", "toString", "objectCtorString", "Array", "isArray", "DRAFTABLE", "_value$constructor", "isMap", "isSet", "each", "obj", "iter", "enumerableOnly", "getArchtype", "Object", "keys", "ownKeys", "for<PERSON>ach", "key", "entry", "index", "thing", "state", "DRAFT_STATE", "type_", "Array", "isArray", "isMap", "isSet", "has", "prop", "prototype", "hasOwnProperty", "call", "get", "set", "propOrOldValue", "value", "t", "add", "is", "x", "y", "target", "hasMap", "Map", "hasSet", "Set", "latest", "copy_", "base_", "shallowCopy", "base", "slice", "descriptors", "getOwnPropertyDescriptors", "i", "length", "desc", "writable", "configurable", "enumerable", "create", "getPrototypeOf", "freeze", "deep", "isFrozen", "isDraft", "isDraftable", "clear", "delete", "dontMutateFrozenCollections", "die", "getPlugin", "pluginKey", "plugin", "plugins", "getCurrentScope", "currentScope", "die", "usePatchesInScope", "scope", "patchListener", "getPlugin", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "drafts_", "for<PERSON>ach", "revokeDraft", "parent_", "enterScope", "immer", "immer_", "canAutoFreeze_", "unfinalizedDrafts_", "draft", "state", "DRAFT_STATE", "type_", "revoke_", "revoked_", "processResult", "result", "length", "baseDraft", "isReplaced", "useProxies_", "willFinalizeES5_", "modified_", "isDraftable", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "base_", "NOTHING", "undefined", "rootScope", "value", "path", "isFrozen", "each", "key", "childValue", "finalizeProperty", "scope_", "finalized_", "copy_", "shallowCopy", "draft_", "resultEach", "isSet", "Set", "clear", "generatePatches_", "parentState", "targetObject", "prop", "rootPath", "targetIsSet", "isDraft", "res", "has", "assigned_", "concat", "set", "add", "autoFreeze_", "deep", "freeze", "peek", "latest", "getDescriptorFromProto", "source", "proto", "Object", "getPrototypeOf", "desc", "getOwnPropertyDescriptor", "<PERSON><PERSON><PERSON><PERSON>", "prepareCopy", "createProxy", "parent", "isMap", "proxyMap_", "proxySet_", "base", "isArray", "Array", "isManual_", "target", "traps", "objectTraps", "arrayTraps", "Proxy", "revocable", "revoke", "proxy", "createES5Proxy_", "push", "current", "currentImpl", "copy", "archType", "getArchtype", "hasChanges_", "copyHelper", "get", "Map", "from", "value", "currentScope", "hasSymbol", "Symbol", "hasMap", "Map", "hasSet", "Set", "hasProxies", "Proxy", "revocable", "Reflect", "NOTHING", "for", "DRAFTABLE", "DRAFT_STATE", "value", "errors", "data", "path", "op", "plugin", "thing", "objectCtorString", "Object", "prototype", "constructor", "ownKeys", "Reflect", "getOwnPropertySymbols", "obj", "getOwnPropertyNames", "concat", "getOwnPropertyDescriptors", "target", "res", "for<PERSON>ach", "key", "getOwnPropertyDescriptor", "plugins", "objectTraps", "get", "state", "prop", "DRAFT_STATE", "source", "latest", "has", "desc", "getDescriptorFromProto", "_desc$get", "call", "draft_", "undefined", "finalized_", "isDraftable", "peek", "base_", "prepareCopy", "copy_", "createProxy", "scope_", "immer_", "set", "modified_", "current", "currentState", "assigned_", "is", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "deleteProperty", "owner", "writable", "configurable", "type_", "enumerable", "defineProperty", "die", "getPrototypeOf", "setPrototypeOf", "arrayTraps", "each", "fn", "arguments", "apply", "this", "parseInt", "Immer", "config", "hasProxies", "base", "recipe", "patchListener", "defaultBase", "self", "_this", "args", "produce", "draft", "_this2", "result", "scope", "enterScope", "proxy", "<PERSON><PERSON><PERSON><PERSON>", "revokeScope", "leaveScope", "Promise", "then", "usePatchesInScope", "processResult", "error", "NOTHING", "autoFreeze_", "freeze", "p", "ip", "getPlugin", "generateReplacementPatches_", "produceWithPatches", "patches", "inversePatches", "nextState", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "isDraft", "isManual_", "finishDraft", "useProxies_", "applyPatches", "i", "length", "patch", "slice", "applyPatchesImpl", "applyPatches_", "immer", "bind", "t", "n", "i", "h", "f", "R", "info", "patch", "G", "GameMethod", "Errors", "i", "isPlainObject", "error", "plugin", "v", "_", "t", "G", "moves", "events", "i", "fn", "turn", "_", "plugin", "p", "UpdateErrorType", "ActionErrorType", "move", "error", "stateWithError", "fn"]}