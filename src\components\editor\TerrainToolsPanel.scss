.terrain-tools-panel {
  padding: 1rem;
  background: #2a2a2a;
  color: #ffffff;
  border-radius: 8px;
  max-height: 80vh;
  overflow-y: auto;

  .panel-header {
    margin-bottom: 1.5rem;

    h3 {
      margin: 0 0 0.5rem 0;
      color: #4CAF50;
      font-size: 1.2rem;
    }

    p {
      margin: 0;
      color: #cccccc;
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }

  .tool-section,
  .brush-section,
  .presets-section,
  .material-section,
  .actions-section,
  .instructions-section {
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #444;

    &:last-child {
      border-bottom: none;
      margin-bottom: 0;
    }

    h4 {
      margin: 0 0 0.75rem 0;
      color: #ffffff;
      font-size: 1rem;
      font-weight: 600;
    }
  }

  .tool-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .tool-button {
    padding: 0.75rem;
    background: #3a3a3a;
    border: 2px solid #555;
    border-radius: 6px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &:hover {
      background: #4a4a4a;
      border-color: #666;
    }

    &.active {
      background: #4CAF50;
      border-color: #4CAF50;
      color: #ffffff;
    }
  }

  .setting-group {
    margin-bottom: 1rem;

    label {
      display: block;
      margin-bottom: 0.5rem;
      color: #cccccc;
      font-size: 0.9rem;
      font-weight: 500;
    }
  }

  .slider-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .slider {
      flex: 1;
      height: 6px;
      background: #555;
      border-radius: 3px;
      outline: none;
      cursor: pointer;

      &::-webkit-slider-thumb {
        appearance: none;
        width: 18px;
        height: 18px;
        background: #4CAF50;
        border-radius: 50%;
        cursor: pointer;
      }

      &::-moz-range-thumb {
        width: 18px;
        height: 18px;
        background: #4CAF50;
        border-radius: 50%;
        border: none;
        cursor: pointer;
      }
    }

    .value {
      min-width: 3rem;
      text-align: right;
      color: #4CAF50;
      font-weight: 600;
      font-size: 0.9rem;
    }
  }

  .preset-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .preset-button {
    padding: 0.6rem;
    background: #3a3a3a;
    border: 1px solid #555;
    border-radius: 6px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.4rem;

    &:hover {
      background: #4a4a4a;
      border-color: #666;
    }
  }

  .color-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;

    .color-picker {
      width: 40px;
      height: 30px;
      border: 2px solid #555;
      border-radius: 4px;
      cursor: pointer;
      background: none;

      &::-webkit-color-swatch-wrapper {
        padding: 0;
      }

      &::-webkit-color-swatch {
        border: none;
        border-radius: 2px;
      }
    }

    .color-value {
      color: #cccccc;
      font-family: monospace;
      font-size: 0.85rem;
    }
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .action-button {
    padding: 0.75rem;
    background: #3a3a3a;
    border: 1px solid #555;
    border-radius: 6px;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;

    &:hover:not(:disabled) {
      background: #4a4a4a;
      border-color: #666;
    }

    &:disabled {
      background: #2a2a2a;
      border-color: #333;
      color: #666;
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  .instruction-list {
    margin: 0;
    padding-left: 1.2rem;
    color: #cccccc;

    li {
      margin-bottom: 0.4rem;
      font-size: 0.85rem;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  // Checkbox styling
  input[type="checkbox"] {
    margin-right: 0.5rem;
    accent-color: #4CAF50;
  }

  // Scrollbar styling
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #1a1a1a;
  }

  &::-webkit-scrollbar-thumb {
    background: #555;
    border-radius: 3px;

    &:hover {
      background: #666;
    }
  }
}
