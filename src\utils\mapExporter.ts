import { MapConfiguration, ExportOptions } from '../types/editorTypes';
import { GameState } from '../types/gameTypes';
import { initPlayers } from '../game';

export class MapExporter {
  static exportToBoardgameIO(mapConfig: MapConfiguration): GameState {
    // Convert editor map configuration to boardgame.io game state format
    return {
      players: {}, // Will be populated during game setup
      tiles: mapConfig.tiles.map(tile => ({
        id: tile.id,
        type: tile.type,
        position: tile.position,
      })),
      currentDiceRoll: 0,
      gameLog: [],
      gameConfig: {
        boardSize: mapConfig.boardSize,
        treasureChestPosition: mapConfig.treasureChestPosition,
        keysToWin: mapConfig.keysToWin,
      },
    };
  }

  static exportToJSON(mapConfig: MapConfiguration, options: ExportOptions = {
    format: 'json',
    includeAssets: true,
    optimizeForPerformance: false,
    compressionLevel: 0,
  }): string {
    const exportData = {
      ...mapConfig,
      exportOptions: options,
      exportedAt: Date.now(),
      version: '1.0.0',
    };

    if (!options.includeAssets) {
      // Remove asset references if not including assets
      exportData.placedObjects = exportData.placedObjects.map(obj => ({
        ...obj,
        assetId: obj.assetId, // Keep reference but assets won't be included
      }));
    }

    return JSON.stringify(exportData, null, options.optimizeForPerformance ? 0 : 2);
  }

  static exportToFile(mapConfig: MapConfiguration, format: 'json' | 'boardgame_io' = 'json'): void {
    let content: string;
    let filename: string;
    let mimeType: string;

    switch (format) {
      case 'boardgame_io':
        const gameState = this.exportToBoardgameIO(mapConfig);
        content = JSON.stringify(gameState, null, 2);
        filename = `${mapConfig.name.replace(/\s+/g, '_')}_boardgame.json`;
        mimeType = 'application/json';
        break;
      
      case 'json':
      default:
        content = this.exportToJSON(mapConfig);
        filename = `${mapConfig.name.replace(/\s+/g, '_')}_map.json`;
        mimeType = 'application/json';
        break;
    }

    // Create and trigger download
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  static generateThumbnail(mapConfig: MapConfiguration): Promise<string> {
    // This would generate a thumbnail image of the map
    // For now, return a placeholder
    return Promise.resolve('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="150"><rect width="200" height="150" fill="%23F4A460"/><text x="100" y="75" text-anchor="middle" fill="white" font-size="16">Map Thumbnail</text></svg>');
  }

  static validateMap(mapConfig: MapConfiguration): { isValid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check for required elements
    if (mapConfig.tiles.length === 0) {
      errors.push('Map must have at least one tile');
    }

    if (mapConfig.tiles.length < 20) {
      warnings.push('Map has very few tiles, consider adding more for better gameplay');
    }

    if (mapConfig.tiles.length > 120) {
      warnings.push('Map has many tiles, this might make games very long');
    }

    // Check for treasure chest
    const treasureChests = mapConfig.tiles.filter(tile => tile.type === 'treasure_chest');
    if (treasureChests.length === 0) {
      errors.push('Map must have exactly one treasure chest tile');
    } else if (treasureChests.length > 1) {
      errors.push('Map must have exactly one treasure chest tile, found multiple');
    }

    // Check treasure chest position matches configuration
    if (treasureChests.length === 1) {
      const treasureChest = treasureChests[0];
      if (treasureChest.id !== mapConfig.treasureChestPosition) {
        warnings.push(`Treasure chest position (${treasureChest.id}) doesn't match configuration (${mapConfig.treasureChestPosition})`);
      }
    }

    // Check for reasonable key distribution
    const keyTiles = mapConfig.tiles.filter(tile => tile.type === 'key');
    if (keyTiles.length === 0) {
      warnings.push('Map has no key tiles, players will not be able to collect keys');
    }

    const keysAvailable = keyTiles.length * 10; // Assuming each key tile gives 10 keys
    if (keysAvailable < mapConfig.keysToWin) {
      errors.push(`Not enough keys available (${keysAvailable}) to meet win condition (${mapConfig.keysToWin})`);
    }

    // Check for tile connectivity (basic check)
    if (mapConfig.tiles.length !== mapConfig.boardSize) {
      warnings.push(`Number of tiles (${mapConfig.tiles.length}) doesn't match board size (${mapConfig.boardSize})`);
    }

    // Performance checks
    if (mapConfig.placedObjects.length > 1000) {
      warnings.push('Map has many objects, this might impact performance');
    }

    const lightCount = mapConfig.lighting.additionalLights.length + 2; // ambient + directional + additional
    if (lightCount > 10) {
      warnings.push('Map has many lights, this might impact performance');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  static optimizeForPerformance(mapConfig: MapConfiguration): MapConfiguration {
    const optimized = { ...mapConfig };

    // Remove excessive objects
    if (optimized.placedObjects.length > 500) {
      console.warn('Reducing object count for performance');
      optimized.placedObjects = optimized.placedObjects.slice(0, 500);
    }

    // Limit lights
    if (optimized.lighting.additionalLights.length > 8) {
      console.warn('Reducing light count for performance');
      optimized.lighting.additionalLights = optimized.lighting.additionalLights.slice(0, 8);
    }

    // Simplify terrain if too complex
    if (optimized.terrain.customMeshes.length > 100) {
      console.warn('Reducing terrain complexity for performance');
      optimized.terrain.customMeshes = optimized.terrain.customMeshes.slice(0, 100);
    }

    return optimized;
  }

  static calculateMapStatistics(mapConfig: MapConfiguration) {
    const tileTypeDistribution = mapConfig.tiles.reduce((acc, tile) => {
      acc[tile.type] = (acc[tile.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Estimate performance metrics
    const triangleCount = mapConfig.tiles.length * 16 + // tiles (cylinder geometry)
                         mapConfig.placedObjects.length * 100 + // average object complexity
                         mapConfig.terrain.customMeshes.length * 200; // terrain meshes

    const lightCount = 2 + mapConfig.lighting.additionalLights.length; // ambient + directional + additional

    return {
      totalTiles: mapConfig.tiles.length,
      tileTypeDistribution,
      totalObjects: mapConfig.placedObjects.length,
      lightCount,
      estimatedTriangles: triangleCount,
      estimatedFPS: Math.max(30, 120 - (triangleCount / 1000)), // Rough estimate
      mapComplexity: triangleCount < 10000 ? 'Low' : triangleCount < 50000 ? 'Medium' : 'High',
    };
  }
}
