.editor-sidebar {
  display: flex;
  flex-direction: column;
  gap: 8px;
  height: 100%;
  overflow-y: auto;

  .sidebar-section {
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    overflow: hidden;

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      background: #f8f9fa;
      border-bottom: 1px solid #e9ecef;
      cursor: pointer;
      font-weight: 600;
      color: #2c3e50;
      font-size: 14px;
      transition: background-color 0.2s ease;

      &:hover {
        background: #e9ecef;
      }

      .expand-icon {
        font-size: 12px;
        transition: transform 0.2s ease;

        &.expanded {
          transform: rotate(180deg);
        }
      }
    }

    .section-content {
      padding: 16px;
    }
  }

  .info-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #f1f3f4;

    &:last-child {
      border-bottom: none;
    }

    .info-label {
      font-size: 13px;
      color: #6c757d;
      font-weight: 500;
    }

    .info-value {
      font-size: 13px;
      color: #2c3e50;
      font-weight: 600;
      text-align: right;
      max-width: 60%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .property-group {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }

    > label {
      display: block;
      font-size: 13px;
      font-weight: 600;
      color: #495057;
      margin-bottom: 6px;
    }

    .property-value {
      font-size: 13px;
      color: #2c3e50;
      padding: 6px 8px;
      background: #f8f9fa;
      border-radius: 4px;
      font-family: monospace;
    }
  }

  .vector-input {
    display: flex;
    gap: 6px;

    .vector-component {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 2px;

      label {
        font-size: 11px;
        color: #6c757d;
        font-weight: 600;
        text-align: center;
      }

      input {
        width: 100%;
        padding: 4px 6px;
        border: 1px solid #ced4da;
        border-radius: 3px;
        font-size: 12px;
        text-align: center;
        transition: border-color 0.2s ease;

        &:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.25);
        }
      }
    }
  }

  .scale-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .uniform-scale {
      display: flex;
      align-items: center;
      gap: 8px;

      label {
        font-size: 12px;
        color: #6c757d;
        font-weight: 600;
        min-width: 50px;
      }

      input {
        flex: 1;
        padding: 4px 8px;
        border: 1px solid #ced4da;
        border-radius: 3px;
        font-size: 12px;
        text-align: center;

        &:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.25);
        }
      }
    }
  }

  .quick-actions {
    display: flex;
    flex-direction: column;
    gap: 6px;

    .action-btn {
      padding: 6px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      background: white;
      color: #495057;
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: #f8f9fa;
        border-color: #007bff;
        color: #007bff;
      }

      &.danger {
        border-color: #dc3545;
        color: #dc3545;

        &:hover {
          background: #dc3545;
          color: white;
        }
      }
    }
  }

  .setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    label {
      font-size: 13px;
      color: #495057;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 6px;
      cursor: pointer;
    }

    input[type="number"] {
      width: 80px;
      padding: 4px 8px;
      border: 1px solid #ced4da;
      border-radius: 3px;
      font-size: 12px;
      text-align: center;

      &:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 1px rgba(0, 123, 255, 0.25);
      }
    }

    input[type="checkbox"] {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .editor-sidebar {
    gap: 6px;

    .sidebar-section {
      .section-header {
        padding: 10px 12px;
        font-size: 13px;
      }

      .section-content {
        padding: 12px;
      }
    }

    .info-item {
      .info-label,
      .info-value {
        font-size: 12px;
      }
    }

    .property-group {
      margin-bottom: 12px;

      > label {
        font-size: 12px;
        margin-bottom: 4px;
      }

      .property-value {
        font-size: 12px;
        padding: 4px 6px;
      }
    }

    .vector-input {
      gap: 4px;

      .vector-component {
        label {
          font-size: 10px;
        }

        input {
          padding: 3px 4px;
          font-size: 11px;
        }
      }
    }

    .quick-actions {
      gap: 4px;

      .action-btn {
        padding: 4px 8px;
        font-size: 11px;
      }
    }

    .setting-item {
      margin-bottom: 8px;

      label {
        font-size: 12px;
      }

      input[type="number"] {
        width: 60px;
        padding: 3px 6px;
        font-size: 11px;
      }

      input[type="checkbox"] {
        width: 14px;
        height: 14px;
      }
    }
  }
}

// Scrollbar styling
.editor-sidebar {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Animation for section expansion
.section-content {
  animation: slideDown 0.2s ease;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Focus states for better accessibility
.vector-component input:focus,
.uniform-scale input:focus,
.setting-item input:focus {
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

// Hover effects
.action-btn:active {
  transform: translateY(1px);
}

.section-header:active {
  background: #dee2e6;
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .editor-sidebar {
    .sidebar-section {
      background: #2d3748;
      border-color: #4a5568;

      .section-header {
        background: #4a5568;
        border-bottom-color: #718096;
        color: #e2e8f0;

        &:hover {
          background: #718096;
        }
      }

      .section-content {
        background: #2d3748;
      }
    }

    .info-item {
      border-bottom-color: #4a5568;

      .info-label {
        color: #a0aec0;
      }

      .info-value {
        color: #e2e8f0;
      }
    }

    .property-group {
      > label {
        color: #e2e8f0;
      }

      .property-value {
        background: #4a5568;
        color: #e2e8f0;
      }
    }

    .vector-component,
    .uniform-scale,
    .setting-item {
      label {
        color: #a0aec0;
      }

      input {
        background: #4a5568;
        border-color: #718096;
        color: #e2e8f0;

        &:focus {
          border-color: #3182ce;
        }
      }
    }

    .action-btn {
      background: #4a5568;
      border-color: #718096;
      color: #e2e8f0;

      &:hover {
        background: #718096;
        border-color: #3182ce;
        color: #bee3f8;
      }

      &.danger {
        border-color: #e53e3e;
        color: #feb2b2;

        &:hover {
          background: #e53e3e;
          color: white;
        }
      }
    }
  }
}
