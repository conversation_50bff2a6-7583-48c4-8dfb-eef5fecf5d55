import React from 'react';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { FullscreenGameBoard } from './FullscreenGameBoard';
import './SimpleGameBoard.scss';

interface SimpleGameBoardProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

const getTileIcon = (type: TileType): string => {
  switch (type) {
    case TileType.BASIC:
      return '●';
    case TileType.HEALING:
      return '+';
    case TileType.DAMAGE:
      return 'X';
    case TileType.KEY:
      return 'K';
    case TileType.TREASURE_CHEST:
      return 'T';
    default:
      return '●';
  }
};

const getTileColor = (type: TileType): string => {
  switch (type) {
    case TileType.BASIC:
      return '#f0f0f0';
    case TileType.HEALING:
      return '#90EE90';
    case TileType.DAMAGE:
      return '#FFB6C1';
    case TileType.KEY:
      return '#FFD700';
    case TileType.TREASURE_CHEST:
      return '#FF6347';
    default:
      return '#f0f0f0';
  }
};

export const SimpleGameBoard: React.FC<SimpleGameBoardProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  return (
    <FullscreenGameBoard
      G={G}
      ctx={ctx}
      moves={moves}
      playerID={playerID}
      isActive={isActive}
    />
  );
};
