import React, { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Text } from '@react-three/drei';
import { Mesh } from 'three';
import { Tile, TileType, Player } from '../../types/gameTypes';

interface GameTileProps {
  tile: Tile;
  players: Player[];
  isActive: boolean;
  moves: any;
  playerID: string | null;
}

const getTileColor = (type: TileType): string => {
  switch (type) {
    case TileType.BASIC:
      return '#ecf0f1';
    case TileType.HEALING:
      return '#2ecc71';
    case TileType.DAMAGE:
      return '#e74c3c';
    case TileType.KEY:
      return '#f1c40f';
    case TileType.TREASURE_CHEST:
      return '#9b59b6';
    default:
      return '#ecf0f1';
  }
};

const getTileIcon = (type: TileType): string => {
  switch (type) {
    case TileType.BASIC:
      return '●';
    case TileType.HEALING:
      return '+';
    case TileType.DAMAGE:
      return 'X';
    case TileType.KEY:
      return 'K';
    case TileType.TREASURE_CHEST:
      return 'T';
    default:
      return '●';
  }
};

export const GameTile: React.FC<GameTileProps> = ({
  tile,
  players,
  isActive,
  moves,
  playerID,
}) => {
  const meshRef = useRef<Mesh>(null);
  const [hovered, setHovered] = useState(false);

  const tileHeight = tile.type === TileType.TREASURE_CHEST ? 2 : 1;

  // Animate special tiles with stable positioning
  useFrame((state, delta) => {
    if (meshRef.current && tile.type !== TileType.BASIC) {
      meshRef.current.rotation.y += delta * 0.5;
      // Use a more stable animation that doesn't interfere with base position
      const baseY = tileHeight / 2;
      const animationOffset = Math.sin(state.clock.elapsedTime * 2) * 0.1;
      meshRef.current.position.y = baseY + animationOffset;
    }
  });

  const handleClick = () => {
    // Handle tile interactions if needed
    console.log(`Clicked tile ${tile.id} of type ${tile.type}`);
  };

  return (
    <group position={[tile.position.x, 0, tile.position.z]}>
      {/* Tile Base */}
      <mesh
        ref={meshRef}
        position={[0, tileHeight / 2, 0]}
        onClick={handleClick}
        onPointerOver={() => setHovered(true)}
        onPointerOut={() => setHovered(false)}
        castShadow
        receiveShadow
      >
        <cylinderGeometry args={[1.5, 1.5, tileHeight, 8]} />
        <meshStandardMaterial
          color={getTileColor(tile.type)}
          emissive={hovered ? '#333333' : '#000000'}
          emissiveIntensity={hovered ? 0.2 : 0}
        />
      </mesh>

      {/* Tile Number */}
      <Text
        position={[0, tileHeight + 0.5, 0]}
        fontSize={0.7}
        color="#2c3e50"
        anchorX="center"
        anchorY="middle"
        font="/fonts/roboto-mono-regular.woff"
        outlineWidth={0.03}
        outlineColor="#ffffff"
      >
        {tile.id}
      </Text>



      {/* Special Effects for Treasure Chest */}
      {tile.type === TileType.TREASURE_CHEST && (
        <>
          {/* Glowing particles effect */}
          <pointLight
            position={[0, tileHeight + 1, 0]}
            intensity={0.5}
            color="#f1c40f"
            distance={5}
          />

          {/* Rotating ring */}
          <mesh position={[0, tileHeight + 1.5, 0]} rotation={[Math.PI / 2, 0, 0]}>
            <torusGeometry args={[2, 0.1, 8, 16]} />
            <meshStandardMaterial
              color="#f1c40f"
              emissive="#f1c40f"
              emissiveIntensity={0.3}
            />
          </mesh>
        </>
      )}

      {/* Player Count Indicator */}
      {players.length > 0 && (
        <Text
          position={[0, tileHeight + 1.5, 0]}
          fontSize={0.3}
          color="#e74c3c"
          anchorX="center"
          anchorY="middle"
        >
          {players.length} player{players.length > 1 ? 's' : ''}
        </Text>
      )}
    </group>
  );
};
