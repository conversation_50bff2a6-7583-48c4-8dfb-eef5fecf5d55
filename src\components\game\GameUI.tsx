import React, { useState } from 'react';
import { GameState, Player } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import './GameUI.scss';

interface GameUIProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
  isMyTurn: boolean;
  canOpenChest: boolean;
  onRollDice: () => void;
  onOpenChest: () => void;
}

export const GameUI: React.FC<GameUIProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
  isMyTurn,
  canOpenChest,
  onRollDice,
  onOpenChest,
}) => {
  const [showRules, setShowRules] = useState(false);
  const [showLog, setShowLog] = useState(false);

  return (
    <div className="game-ui-overlay">
      {/* Top Bar */}
      <div className="top-bar">
        <div className="game-title">
          <h1>Brawl Party AI</h1>
        </div>

        <div className="turn-info">
          {!ctx.gameover && (
            <>
              <span className="current-turn">
                Current Turn: <strong>{G.players[ctx.currentPlayer]?.name}</strong>
              </span>
              {G.currentDiceRoll > 0 && (
                <span className="dice-result">
                  Dice: {G.currentDiceRoll}
                </span>
              )}
            </>
          )}
        </div>
      </div>

      {/* Left Sidebar - Players */}
      <div className="left-sidebar">
        <div className="panel players-panel">
          <h3>Players</h3>
          <div className="players-list">
            {Object.values(G.players).map((player: Player) => (
              <div
                key={player.ID}
                className={`player-card ${player.ID === ctx.currentPlayer ? 'current-player' : ''}`}
                style={{ borderColor: player.color }}
              >
                <div className="player-name">{player.name}</div>
                <div className="player-stats">
                  <div className="stat">
                    <span className="stat-icon">❤️</span>
                    <span className="stat-value">{player.health}/100</span>
                  </div>
                  <div className="stat">
                    <span className="stat-icon">🗝️</span>
                    <span className="stat-value">{player.keys}</span>
                  </div>
                  <div className="stat">
                    <span className="stat-icon">📍</span>
                    <span className="stat-value">Tile {player.position}</span>
                  </div>
                </div>
                {!player.isAlive && (
                  <div className="player-status">💀 Respawning...</div>
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Right Sidebar - Controls */}
      <div className="right-sidebar">
        <div className="panel controls-panel">
          <h3>Game Controls</h3>
          {isMyTurn ? (
            <div className="controls">
              <button
                onClick={onRollDice}
                className="roll-dice-btn"
                disabled={ctx.gameover}
              >
                🎲 Roll Dice
              </button>

              {canOpenChest && (
                <button
                  onClick={onOpenChest}
                  className="open-chest-btn"
                >
                  🏆 Open Treasure Chest
                </button>
              )}
            </div>
          ) : (
            <div className="waiting">
              <p>Waiting for {G.players[ctx.currentPlayer]?.name} to play...</p>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="action-buttons">
          <button
            className="action-btn"
            onClick={() => setShowRules(!showRules)}
          >
            📋 Rules
          </button>
          <button
            className="action-btn"
            onClick={() => setShowLog(!showLog)}
          >
            📜 Log
          </button>
        </div>
      </div>

      {/* Rules Modal */}
      {showRules && (
        <div className="modal-overlay" onClick={() => setShowRules(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>📋 Game Rules</h3>
              <button onClick={() => setShowRules(false)}>✕</button>
            </div>
            <div className="modal-body">
              <ul>
                <li>🎲 Roll dice to move around the board</li>
                <li>❤️ Healing tiles restore 20 health</li>
                <li>💀 Damage tiles deal 30 damage</li>
                <li>🗝️ Key tiles give you 10 keys</li>
                <li>🏆 Reach the treasure chest with 40+ keys to win!</li>
                <li>💀 If health reaches 0, you respawn at start</li>
              </ul>
            </div>
          </div>
        </div>
      )}

      {/* Game Log Modal */}
      {showLog && (
        <div className="modal-overlay" onClick={() => setShowLog(false)}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h3>📜 Game Log</h3>
              <button onClick={() => setShowLog(false)}>✕</button>
            </div>
            <div className="modal-body">
              <div className="log-entries">
                {G.gameLog.slice(-10).reverse().map((entry, index) => (
                  <div key={index} className="log-entry">
                    <span className="log-time">
                      {new Date(entry.timestamp).toLocaleTimeString()}
                    </span>
                    <span className="log-details">{entry.details}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
