.fullscreen-game-board {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

  .ui-toggle-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    width: 50px;
    height: 50px;
    border: none;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    font-size: 1.5em;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(0, 0, 0, 0.9);
      transform: scale(1.1);
    }
  }

  .game-over-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    backdrop-filter: blur(10px);

    .game-over-content {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 40px;
      border-radius: 20px;
      text-align: center;
      color: white;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);

      h1 {
        font-size: 3em;
        margin: 0 0 20px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }

      h2 {
        font-size: 1.5em;
        margin: 0 0 30px 0;
        opacity: 0.9;
      }

      button {
        padding: 15px 30px;
        font-size: 1.2em;
        border: none;
        border-radius: 10px;
        background: #f39c12;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #e67e22;
          transform: translateY(-2px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .fullscreen-game-board {
    .ui-toggle-btn {
      top: 10px;
      right: 10px;
      width: 40px;
      height: 40px;
      font-size: 1.2em;
    }

    .game-over-overlay {
      .game-over-content {
        margin: 20px;
        padding: 30px;

        h1 {
          font-size: 2em;
        }

        h2 {
          font-size: 1.2em;
        }

        button {
          padding: 12px 24px;
          font-size: 1em;
        }
      }
    }
  }
}
