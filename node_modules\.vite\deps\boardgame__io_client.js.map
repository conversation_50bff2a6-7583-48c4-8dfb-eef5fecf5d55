{"version": 3, "sources": ["../../boardgame.io/dist/esm/client.js"], "sourcesContent": ["import 'nanoid/non-secure';\nimport './Debug-8242c26e.js';\nimport 'redux';\nimport './turn-order-8cc4909b.js';\nimport 'immer';\nimport './plugin-random-087f861e.js';\nimport 'lodash.isplainobject';\nimport './reducer-24ea3e4c.js';\nimport 'rfc6902';\nimport './initialize-7316768f.js';\nimport './transport-ce07b771.js';\nexport { C as Client } from './client-f7f02b82.js';\nimport 'flatted';\nimport 'setimmediate';\nimport './ai-7998b00f.js';\nexport { L as LobbyClient, a as LobbyClientError } from './client-5f57c3f2.js';\n"], "mappings": ";;;;;;;;;;;;;;;AAMA,oBAAO;AAEP,qBAAO;AAKP,0BAAO;", "names": []}