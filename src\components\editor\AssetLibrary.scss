.asset-library {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;

  &__header {
    padding: 16px;
    background: #ffffff;
    border-bottom: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 12px 0;
      color: #2c3e50;
      font-size: 18px;
      font-weight: 600;
    }
  }

  &__controls {
    display: flex;
    gap: 12px;
    align-items: center;
    flex-wrap: wrap;

    .search-box {
      flex: 1;
      min-width: 200px;

      .search-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.2s ease;

        &:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }
      }
    }

    .category-filter {
      padding: 8px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px;
      background: white;
      font-size: 14px;
      cursor: pointer;
      transition: border-color 0.2s ease;

      &:focus {
        outline: none;
        border-color: #007bff;
      }
    }

    .view-mode-toggle {
      display: flex;
      border: 1px solid #ced4da;
      border-radius: 4px;
      overflow: hidden;

      button {
        padding: 8px 12px;
        border: none;
        background: white;
        cursor: pointer;
        font-size: 16px;
        transition: all 0.2s ease;

        &:hover {
          background: #f8f9fa;
        }

        &.active {
          background: #007bff;
          color: white;
        }

        &:not(:last-child) {
          border-right: 1px solid #ced4da;
        }
      }
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    &--grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 12px;
    }

    &--list {
      display: flex;
      flex-direction: column;
      gap: 8px;
    }
  }

  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6c757d;
    text-align: center;

    p {
      margin: 4px 0;
    }
  }
}

.asset-item {
  background: white;
  border: 2px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  &.selected {
    border-color: #28a745;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
  }

  // Grid view styles
  .asset-library__content--grid & {
    display: flex;
    flex-direction: column;
    aspect-ratio: 1;
  }

  // List view styles
  .asset-library__content--list & {
    display: flex;
    flex-direction: row;
    padding: 12px;
    align-items: center;
  }

  &__thumbnail {
    position: relative;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;

    // Grid view thumbnail
    .asset-library__content--grid & {
      flex: 1;
      width: 100%;
    }

    // List view thumbnail
    .asset-library__content--list & {
      width: 60px;
      height: 60px;
      border-radius: 6px;
      margin-right: 12px;
      flex-shrink: 0;
    }

    img {
      max-width: 100%;
      max-height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }

  &__category {
    position: absolute;
    top: 4px;
    right: 4px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  &__info {
    // Grid view info
    .asset-library__content--grid & {
      padding: 8px;
      text-align: center;
    }

    // List view info
    .asset-library__content--list & {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  }

  &__name {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    line-height: 1.2;

    // Grid view name
    .asset-library__content--grid & {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    // List view name
    .asset-library__content--list & {
      font-size: 16px;
    }
  }

  &__details {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  &__description {
    color: #6c757d;
    font-size: 13px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  &__tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;

    .tag {
      background: #e9ecef;
      color: #495057;
      padding: 2px 6px;
      border-radius: 3px;
      font-size: 11px;
      font-weight: 500;
    }
  }
}

// Scrollbar styling
.asset-library__content {
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .asset-library {
    &__controls {
      flex-direction: column;
      align-items: stretch;

      .search-box {
        min-width: auto;
      }
    }

    &__content--grid {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
      gap: 8px;
    }
  }

  .asset-item {
    &__name {
      font-size: 12px;
    }

    &__thumbnail {
      .asset-library__content--list & {
        width: 50px;
        height: 50px;
        margin-right: 8px;
      }
    }
  }
}
