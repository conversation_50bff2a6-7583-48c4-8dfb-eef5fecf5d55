import React, { useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { useNavigate } from 'react-router-dom';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { BrawlPartyScene } from './BrawlPartyScene';
import { GameUI } from './GameUI';
import { useAppSelector } from '../../store';
import { usePlayAgainMutation } from '../../api';
import './FullscreenGameBoard.scss';

interface FullscreenGameBoardProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

export const FullscreenGameBoard: React.FC<FullscreenGameBoardProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  const [uiVisible, setUiVisible] = useState(true);
  const [isPlayingAgain, setIsPlayingAgain] = useState(false);
  const navigate = useNavigate();
  const userRoomData = useAppSelector((s) => s.user.roomData);
  const [playAgain] = usePlayAgainMutation();

  const currentPlayer = playerID ? G.players[playerID] : null;
  const isMyTurn = isActive && ctx.currentPlayer === playerID;

  const handleRollDice = () => {
    if (isMyTurn && moves.rollDice) {
      moves.rollDice();
    }
  };

  const handleOpenChest = () => {
    if (isMyTurn && moves.openTreasureChest) {
      moves.openTreasureChest();
    }
  };

  const canOpenChest = currentPlayer &&
    currentPlayer.position === G.gameConfig.treasureChestPosition &&
    currentPlayer.keys >= G.gameConfig.keysToWin;

  const toggleUI = () => {
    setUiVisible(!uiVisible);
  };

  const handlePlayAgain = async () => {
    if (!userRoomData) {
      console.error('No user room data available for play again');
      return;
    }

    setIsPlayingAgain(true);

    try {
      console.log('🔄 Starting play again with:', userRoomData);

      const newMatchID = await playAgain({
        matchID: userRoomData.matchID,
        playerID: userRoomData.playerID,
        credentials: userRoomData.credentials,
      }).unwrap();

      console.log('✅ New match created:', newMatchID);

      // Navigate to the new match
      navigate(`/${newMatchID}`);
    } catch (error) {
      console.error('❌ Failed to create new match:', error);
      setIsPlayingAgain(false);

      // Fallback to page reload if API fails
      window.location.reload();
    }
  };

  return (
    <div className="fullscreen-game-board">
      {/* Fullscreen 3D Canvas */}
      <Canvas
        camera={{
          position: [0, 35, 35],
          fov: 50,
          near: 0.1,
          far: 1000
        }}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: 1
        }}
      >
        <BrawlPartyScene
          G={G}
          ctx={ctx}
          moves={moves}
          playerID={playerID}
          isActive={isActive}
        />
      </Canvas>

      {/* UI Toggle Button */}
      <button
        className="ui-toggle-btn"
        onClick={toggleUI}
        title={uiVisible ? "Hide UI" : "Show UI"}
      >
        {uiVisible ? '🙈' : '👁️'}
      </button>

      {/* Game UI Overlay */}
      {uiVisible && (
        <GameUI
          G={G}
          ctx={ctx}
          moves={moves}
          playerID={playerID}
          isActive={isActive}
          isMyTurn={isMyTurn}
          canOpenChest={canOpenChest}
          onRollDice={handleRollDice}
          onOpenChest={handleOpenChest}
        />
      )}

      {/* Game Over Overlay */}
      {ctx.gameover && (
        <div className="game-over-overlay">
          <div className="game-over-content">
            <h1>🎉 Game Over!</h1>
            <h2>
              Winner: {G.players[ctx.gameover.winner]?.name || `Player ${parseInt(ctx.gameover.winner) + 1}`}
            </h2>
            <button
              onClick={handlePlayAgain}
              disabled={isPlayingAgain}
              className={isPlayingAgain ? 'loading' : ''}
            >
              {isPlayingAgain ? '⏳ Creating New Game...' : '🔄 Play Again'}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
