import React, { useState } from 'react';
import { Canvas } from '@react-three/fiber';
import { GameState, Player, TileType } from '../../types/gameTypes';
import { Ctx } from 'boardgame.io';
import { BrawlPartyScene } from './BrawlPartyScene';
import { GameUI } from './GameUI';
import './FullscreenGameBoard.scss';

interface FullscreenGameBoardProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  playerID: string | null;
  isActive: boolean;
}

export const FullscreenGameBoard: React.FC<FullscreenGameBoardProps> = ({
  G,
  ctx,
  moves,
  playerID,
  isActive,
}) => {
  const [uiVisible, setUiVisible] = useState(true);
  const currentPlayer = playerID ? G.players[playerID] : null;
  const isMyTurn = isActive && ctx.currentPlayer === playerID;

  const handleRollDice = () => {
    if (isMyTurn && moves.rollDice) {
      moves.rollDice();
    }
  };

  const handleOpenChest = () => {
    if (isMyTurn && moves.openTreasureChest) {
      moves.openTreasureChest();
    }
  };

  const canOpenChest = currentPlayer && 
    currentPlayer.position === G.gameConfig.treasureChestPosition &&
    currentPlayer.keys >= G.gameConfig.keysToWin;

  const toggleUI = () => {
    setUiVisible(!uiVisible);
  };

  return (
    <div className="fullscreen-game-board">
      {/* Fullscreen 3D Canvas */}
      <Canvas
        camera={{ 
          position: [0, 35, 35], 
          fov: 50,
          near: 0.1,
          far: 1000
        }}
        style={{ 
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100vw',
          height: '100vh',
          zIndex: 1
        }}
      >
        <BrawlPartyScene
          G={G}
          ctx={ctx}
          moves={moves}
          playerID={playerID}
          isActive={isActive}
        />
      </Canvas>

      {/* UI Toggle Button */}
      <button 
        className="ui-toggle-btn"
        onClick={toggleUI}
        title={uiVisible ? "Hide UI" : "Show UI"}
      >
        {uiVisible ? '🙈' : '👁️'}
      </button>

      {/* Game UI Overlay */}
      {uiVisible && (
        <GameUI
          G={G}
          ctx={ctx}
          moves={moves}
          playerID={playerID}
          isActive={isActive}
          isMyTurn={isMyTurn}
          canOpenChest={canOpenChest}
          onRollDice={handleRollDice}
          onOpenChest={handleOpenChest}
        />
      )}

      {/* Game Over Overlay */}
      {ctx.gameover && (
        <div className="game-over-overlay">
          <div className="game-over-content">
            <h1>🎉 Game Over!</h1>
            <h2>Winner: {G.players[ctx.gameover.winner]?.name || 'Unknown'}</h2>
            <button onClick={() => window.location.reload()}>
              🔄 Play Again
            </button>
          </div>
        </div>
      )}
    </div>
  );
};
