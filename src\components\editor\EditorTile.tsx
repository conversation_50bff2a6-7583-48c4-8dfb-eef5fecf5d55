import React, { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Text } from '@react-three/drei';
import { Mesh } from 'three';
import { CustomTile } from '../../types/editorTypes';
import { TileType } from '../../types/gameTypes';

interface EditorTileProps {
  tile: CustomTile;
  isSelected: boolean;
  isHovered: boolean;
  editable: boolean;
  isEraseMode?: boolean;
  onSelect: () => void;
  onHover: (hovered: boolean) => void;
}

export const EditorTile: React.FC<EditorTileProps> = ({
  tile,
  isSelected,
  isHovered,
  editable,
  isEraseMode = false,
  onSelect,
  onHover,
}) => {
  const meshRef = useRef<Mesh>(null);
  const [localHovered, setLocalHovered] = useState(false);

  const getTileColor = (type: TileType): string => {
    switch (type) {
      case TileType.BASIC:
        return '#ecf0f1';
      case TileType.HEALING:
        return '#2ecc71';
      case TileType.DAMAGE:
        return '#e74c3c';
      case TileType.KEY:
        return '#f1c40f';
      case TileType.TREASURE_CHEST:
        return '#9b59b6';
      default:
        return '#ecf0f1';
    }
  };

  const getTileHeight = (type: TileType): number => {
    return type === TileType.TREASURE_CHEST ? 2 : 1;
  };

  const tileHeight = getTileHeight(tile.type);
  const heightOffset = tile.customProperties?.heightOffset || 0;
  const scale = tile.customProperties?.scale || [1, 1, 1];
  const rotation = tile.customProperties?.rotation || [0, 0, 0];

  // Animate special tiles
  useFrame((state, delta) => {
    if (meshRef.current && tile.type !== TileType.BASIC) {
      meshRef.current.rotation.y += delta * 0.5;
      const baseY = (tileHeight / 2) + heightOffset;
      const animationOffset = Math.sin(state.clock.elapsedTime * 2) * 0.1;
      meshRef.current.position.y = baseY + animationOffset;
    }
  });

  const handlePointerOver = () => {
    if (editable) {
      setLocalHovered(true);
      onHover(true);
    }
  };

  const handlePointerOut = () => {
    setLocalHovered(false);
    onHover(false);
  };

  const handleClick = (event: any) => {
    if (editable) {
      // Only respond to left mouse button (button 0)
      if (event.button !== 0) return;

      event.stopPropagation();

      // In erase mode, don't select tiles - let the scene handle deletion
      if (isEraseMode) {
        // Don't call onSelect() in erase mode - let EditorScene handle the deletion
        return;
      }

      onSelect();
    }
  };

  const getEmissiveColor = () => {
    if (isSelected) return '#00ff00';
    if ((isHovered || localHovered) && isEraseMode) return '#ff0000';
    if (isHovered || localHovered) return '#333333';
    return '#000000';
  };

  const getEmissiveIntensity = () => {
    if (isSelected) return 0.3;
    if ((isHovered || localHovered) && isEraseMode) return 0.4;
    if (isHovered || localHovered) return 0.2;
    return 0;
  };

  return (
    <group position={tile.position} scale={scale} rotation={rotation} userData={{ tileId: tile.id }}>
      {/* Tile Base */}
      <mesh
        ref={meshRef}
        position={[0, (tileHeight / 2) + heightOffset, 0]}
        onClick={handleClick}
        onPointerOver={handlePointerOver}
        onPointerOut={handlePointerOut}
        castShadow
        receiveShadow
        userData={{ tileId: tile.id }}
      >
        <cylinderGeometry args={[1.5, 1.5, tileHeight, 8]} />
        <meshStandardMaterial
          color={tile.customProperties?.material?.color || getTileColor(tile.type)}
          emissive={getEmissiveColor()}
          emissiveIntensity={getEmissiveIntensity()}
          roughness={tile.customProperties?.material?.roughness || 0.8}
          metalness={tile.customProperties?.material?.metalness || 0.0}
        />
      </mesh>

      {/* Tile Number */}
      <Text
        position={[0, tileHeight + heightOffset + 0.5, 0]}
        fontSize={0.7}
        color="#2c3e50"
        anchorX="center"
        anchorY="middle"
        outlineWidth={0.03}
        outlineColor="#ffffff"
      >
        {tile.id}
      </Text>

      {/* Special Effects for Treasure Chest */}
      {tile.type === TileType.TREASURE_CHEST && (
        <>
          {/* Glowing particles effect */}
          <pointLight
            position={[0, tileHeight + heightOffset + 1, 0]}
            intensity={0.5}
            color="#f1c40f"
            distance={5}
          />

          {/* Rotating ring */}
          <mesh
            position={[0, tileHeight + heightOffset + 1.5, 0]}
            rotation={[Math.PI / 2, 0, 0]}
          >
            <torusGeometry args={[2, 0.1, 8, 16]} />
            <meshStandardMaterial
              color="#f1c40f"
              emissive="#f1c40f"
              emissiveIntensity={0.3}
            />
          </mesh>
        </>
      )}

      {/* Selection Indicator */}
      {isSelected && (
        <mesh position={[0, 0.1, 0]} rotation={[-Math.PI / 2, 0, 0]}>
          <ringGeometry args={[2, 2.2, 16]} />
          <meshBasicMaterial color="#00ff00" transparent opacity={0.8} />
        </mesh>
      )}

      {/* Hover Indicator */}
      {(isHovered || localHovered) && !isSelected && (
        <mesh position={[0, 0.1, 0]} rotation={[-Math.PI / 2, 0, 0]}>
          <ringGeometry args={[2, 2.1, 16]} />
          <meshBasicMaterial color="#ffffff" transparent opacity={0.5} />
        </mesh>
      )}
    </group>
  );
};
