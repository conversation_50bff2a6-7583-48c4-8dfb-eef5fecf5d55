import { Local } from 'boardgame.io/multiplayer';
import { Client } from 'boardgame.io/client';
import {
  ClientOpts,
  _ClientImpl,
} from 'boardgame.io/dist/types/src/client/client';
import { initBoard, BrawlPartyGame } from '../game';
import { GameState } from '../types/gameTypes';

export const initializeTestPlayers = (
  numPlayers: number = 2,
): [_ClientImpl<GameState>, _ClientImpl<GameState>] => {
  const tiles = initBoard();

  const spec: ClientOpts<GameState> = {
    game: {
      ...BrawlPartyGame,
      setup: (context) => ({
        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
        ...BrawlPartyGame.setup!(context),
        tiles,
      }),
    },
    multiplayer: Local(),
  };

  const p0 = Client({ ...spec, playerID: '0' });
  const p1 = Client({ ...spec, playerID: '1' });

  p0.start();
  p1.start();

  return [p0, p1];
};
