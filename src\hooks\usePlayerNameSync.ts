import { useEffect, useRef } from 'react';
import { skipToken } from '@reduxjs/toolkit/query';
import { useGetMatchQuery } from '../api';
import { GameState } from '../types/gameTypes';
import { Ctx } from 'boardgame.io';

interface UsePlayerNameSyncProps {
  G: GameState;
  ctx: Ctx;
  moves: any;
  matchID?: string;
  playerID?: string | null;
}

/**
 * Hook to synchronize player names from match metadata to game state
 * This ensures that actual player nicknames are displayed instead of generic "Player 1", "Player 2" labels
 *
 * NEW APPROACH: Uses initializePlayerName move to sync ALL player names immediately when available,
 * then falls back to turn-based sync for any remaining players
 */
export const usePlayerNameSync = ({
  G,
  ctx,
  moves,
  matchID,
  playerID,
}: UsePlayerNameSyncProps) => {
  const syncedRef = useRef<Set<string>>(new Set());
  const initializationAttemptedRef = useRef<Set<string>>(new Set());

  // Get match metadata to access player names
  const { data: matchMetadata } = useGetMatchQuery(
    matchID ?? skipToken,
    {
      // Poll more frequently during initialization, less frequently after
      pollingInterval: syncedRef.current.size < ctx.numPlayers ? 1000 : 5000,
    }
  );

  useEffect(() => {
    // Only proceed if we have match metadata and moves
    if (!matchMetadata?.players || !moves) {
      return;
    }

    // PHASE 1: Immediate initialization for all available players
    // Try to initialize all player names immediately using the special initializePlayerName move
    if (moves.initializePlayerName) {
      matchMetadata.players.forEach((playerMetadata) => {
        const playerIDStr = playerMetadata.id.toString();
        const gamePlayer = G.players[playerIDStr];
        const attemptKey = `${playerIDStr}-${playerMetadata.name}`;

        // Check if this player needs initialization and we haven't attempted it yet
        if (
          playerMetadata.name && // Player has a name in metadata
          gamePlayer && // Player exists in game state
          gamePlayer.name !== playerMetadata.name && // Names don't match
          !syncedRef.current.has(playerIDStr) && // Haven't synced this player yet
          !initializationAttemptedRef.current.has(attemptKey) // Haven't attempted this specific name yet
        ) {
          try {
            console.log(`🚀 Initializing player ${playerIDStr} name: "${gamePlayer.name}" → "${playerMetadata.name}"`);

            // Use the special initialization move that works for any player
            moves.initializePlayerName(playerIDStr, playerMetadata.name);

            // Mark this attempt to avoid duplicate calls
            initializationAttemptedRef.current.add(attemptKey);
            syncedRef.current.add(playerIDStr);
          } catch (error) {
            console.error(`❌ Failed to initialize player ${playerIDStr} name:`, error);
          }
        }
      });
    }

    // PHASE 2: Fallback turn-based sync for any remaining players
    // This handles cases where initialization didn't work or for late-joining players
    if (moves.updatePlayerName) {
      const currentPlayerIDStr = ctx.currentPlayer;
      const currentPlayerMetadata = matchMetadata.players.find(p => p.id.toString() === currentPlayerIDStr);
      const currentGamePlayer = G.players[currentPlayerIDStr];

      // Only update if it's this client's player's turn and they still need a name update
      if (
        playerID === currentPlayerIDStr && // Only update when it's this client's player's turn
        currentPlayerMetadata?.name && // Player has a name in metadata
        currentGamePlayer && // Player exists in game state
        currentGamePlayer.name !== currentPlayerMetadata.name && // Names don't match
        !syncedRef.current.has(currentPlayerIDStr) // Haven't synced this player yet
      ) {
        try {
          console.log(`🔄 Turn-based sync for player ${currentPlayerIDStr}: "${currentGamePlayer.name}" → "${currentPlayerMetadata.name}"`);

          // Use the regular turn-based move as fallback
          moves.updatePlayerName(currentPlayerIDStr, currentPlayerMetadata.name);

          // Mark this player as synced
          syncedRef.current.add(currentPlayerIDStr);
        } catch (error) {
          console.error(`❌ Failed to sync player ${currentPlayerIDStr} name:`, error);
        }
      }
    }
  }, [matchMetadata, G.players, moves, ctx.numPlayers, ctx.currentPlayer, playerID]);

  // Reset sync tracking when game restarts or match changes
  useEffect(() => {
    syncedRef.current.clear();
    initializationAttemptedRef.current.clear();
  }, [matchID]);

  // Update sync status based on actual game state
  useEffect(() => {
    if (matchMetadata?.players) {
      const actualSyncedPlayers = new Set<string>();

      matchMetadata.players.forEach((playerMetadata) => {
        const playerIDStr = playerMetadata.id.toString();
        const gamePlayer = G.players[playerIDStr];

        // Consider a player synced if their game name matches their metadata name
        if (gamePlayer && playerMetadata.name && gamePlayer.name === playerMetadata.name) {
          actualSyncedPlayers.add(playerIDStr);
        }
      });

      syncedRef.current = actualSyncedPlayers;
    }
  }, [G.players, matchMetadata]);

  return {
    isNameSyncComplete: syncedRef.current.size >= ctx.numPlayers,
    syncedPlayerCount: syncedRef.current.size,
  };
};
