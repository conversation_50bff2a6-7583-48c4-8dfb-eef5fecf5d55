{"hash": "b2737139", "configHash": "7b3034bf", "lockfileHash": "2729bf8d", "browserHash": "eb4407c3", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d1fce9e2", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "321677ba", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ad210e71", "needsInterop": true}, "@react-three/drei": {"src": "../../@react-three/drei/index.js", "file": "@react-three_drei.js", "fileHash": "8ee79a7e", "needsInterop": false}, "@react-three/fiber": {"src": "../../@react-three/fiber/dist/react-three-fiber.esm.js", "file": "@react-three_fiber.js", "fileHash": "34964e42", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "89e9a703", "needsInterop": false}, "@reduxjs/toolkit/query": {"src": "../../@reduxjs/toolkit/dist/query/rtk-query.modern.mjs", "file": "@reduxjs_toolkit_query.js", "fileHash": "1b70cab7", "needsInterop": false}, "@reduxjs/toolkit/query/react": {"src": "../../@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs", "file": "@reduxjs_toolkit_query_react.js", "fileHash": "91d0546c", "needsInterop": false}, "@tanstack/react-table": {"src": "../../@tanstack/react-table/build/lib/index.mjs", "file": "@tanstack_react-table.js", "fileHash": "dc4065dd", "needsInterop": false}, "@tippyjs/react": {"src": "../../@tippyjs/react/dist/tippy-react.esm.js", "file": "@tippyjs_react.js", "fileHash": "aadf2bec", "needsInterop": false}, "boardgame.io/client": {"src": "../../boardgame.io/dist/esm/client.js", "file": "boardgame__io_client.js", "fileHash": "4ba020a4", "needsInterop": false}, "boardgame.io/multiplayer": {"src": "../../boardgame.io/dist/esm/multiplayer.js", "file": "boardgame__io_multiplayer.js", "fileHash": "cb6fff80", "needsInterop": false}, "boardgame.io/react": {"src": "../../boardgame.io/dist/esm/react.js", "file": "boardgame__io_react.js", "fileHash": "98fb6997", "needsInterop": false}, "classnames": {"src": "../../classnames/index.js", "file": "classnames.js", "fileHash": "54559fa3", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "df953749", "needsInterop": true}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "f1e63bb1", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "c505793e", "needsInterop": false}}, "chunks": {"vision_bundle-72AN3B4E": {"file": "vision_bundle-72AN3B4E.js"}, "chunk-6QUVGBWT": {"file": "chunk-6QUVGBWT.js"}, "chunk-J7D76I2G": {"file": "chunk-J7D76I2G.js"}, "chunk-CT2AWXLA": {"file": "chunk-CT2AWXLA.js"}, "chunk-MYOAH3BB": {"file": "chunk-MYOAH3BB.js"}, "chunk-DURPDMJC": {"file": "chunk-DURPDMJC.js"}, "chunk-W2KR2GQC": {"file": "chunk-W2KR2GQC.js"}, "chunk-FBBS33D2": {"file": "chunk-FBBS33D2.js"}, "chunk-4O5H2NC7": {"file": "chunk-4O5H2NC7.js"}, "chunk-UN5CEAJI": {"file": "chunk-UN5CEAJI.js"}, "chunk-ZCMNKZO7": {"file": "chunk-ZCMNKZO7.js"}, "chunk-Y5IW5QF7": {"file": "chunk-Y5IW5QF7.js"}, "chunk-UV5CTPV7": {"file": "chunk-UV5CTPV7.js"}}}