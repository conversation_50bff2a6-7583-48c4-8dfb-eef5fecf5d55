import React, { useRef, useState } from 'react';
import { useFrame } from '@react-three/fiber';
import { Mesh } from 'three';
import { PlacedObject } from '../../types/editorTypes';

interface EditorObjectProps {
  object: PlacedObject;
  isSelected: boolean;
  isHovered: boolean;
  editable: boolean;
  isEraseMode?: boolean;
  onSelect: () => void;
  onHover: (hovered: boolean) => void;
}

export const EditorObject: React.FC<EditorObjectProps> = ({
  object,
  isSelected,
  isHovered,
  editable,
  isEraseMode = false,
  onSelect,
  onHover,
}) => {
  const meshRef = useRef<Mesh>(null);
  const [localHovered, setLocalHovered] = useState(false);

  // Render different objects based on asset ID
  const renderObjectGeometry = () => {
    switch (object.assetId) {
      case 'rock_small':
      case 'rock_medium':
      case 'rock_large':
        const rockScale = object.assetId === 'rock_small' ? 1 :
                         object.assetId === 'rock_medium' ? 1.5 : 2;
        return (
          <sphereGeometry args={[rockScale, 8, 6]} />
        );

      case 'cactus_small':
      case 'cactus_large':
        const cactusHeight = object.assetId === 'cactus_small' ? 2 : 4;
        return (
          <group>
            <mesh position={[0, cactusHeight / 2, 0]}>
              <cylinderGeometry args={[0.3, 0.4, cactusHeight, 8]} />
              <meshStandardMaterial color="#228B22" />
            </mesh>
            {object.assetId === 'cactus_large' && (
              <mesh position={[0.6, cactusHeight * 0.7, 0]} rotation={[0, 0, Math.PI / 2]}>
                <cylinderGeometry args={[0.2, 0.25, 1.5, 6]} />
                <meshStandardMaterial color="#228B22" />
              </mesh>
            )}
          </group>
        );

      case 'palm_tree':
        return (
          <group>
            {/* Palm trunk */}
            <mesh position={[0, 4, 0]}>
              <cylinderGeometry args={[0.4, 0.6, 8, 8]} />
              <meshStandardMaterial color="#8B4513" />
            </mesh>
            {/* Palm fronds */}
            {Array.from({ length: 6 }, (_, i) => {
              const frondAngle = (i / 6) * Math.PI * 2;
              const frondX = Math.cos(frondAngle) * 2;
              const frondZ = Math.sin(frondAngle) * 2;
              return (
                <mesh
                  key={i}
                  position={[frondX, 8.5, frondZ]}
                  rotation={[Math.PI / 6, frondAngle, 0]}
                >
                  <boxGeometry args={[0.2, 3, 0.8]} />
                  <meshStandardMaterial color="#228B22" />
                </mesh>
              );
            })}
          </group>
        );

      case 'sand_dune':
        return (
          <sphereGeometry args={[3, 16, 8]} />
        );

      case 'oasis_water':
        return (
          <cylinderGeometry args={[4, 4, 0.2, 32]} />
        );

      case 'desert_grass':
        return (
          <group>
            {Array.from({ length: 5 }, (_, i) => (
              <mesh
                key={i}
                position={[
                  (Math.random() - 0.5) * 0.5,
                  0.4,
                  (Math.random() - 0.5) * 0.5
                ]}
                rotation={[0, Math.random() * Math.PI * 2, 0]}
              >
                <boxGeometry args={[0.05, 0.8, 0.05]} />
                <meshStandardMaterial color="#9ACD32" />
              </mesh>
            ))}
          </group>
        );

      case 'bone_pile':
        return (
          <group>
            {Array.from({ length: 3 }, (_, i) => (
              <mesh
                key={i}
                position={[
                  (Math.random() - 0.5) * 1,
                  0.2,
                  (Math.random() - 0.5) * 1
                ]}
                rotation={[
                  Math.random() * Math.PI,
                  Math.random() * Math.PI * 2,
                  Math.random() * Math.PI
                ]}
              >
                <cylinderGeometry args={[0.05, 0.05, 0.8, 6]} />
                <meshStandardMaterial color="#F5F5DC" />
              </mesh>
            ))}
          </group>
        );

      default:
        // Default cube for unknown objects
        return (
          <boxGeometry args={[1, 1, 1]} />
        );
    }
  };

  const getObjectMaterial = () => {
    switch (object.assetId) {
      case 'rock_small':
      case 'rock_medium':
      case 'rock_large':
        return (
          <meshStandardMaterial
            color="#8B7355"
            roughness={0.95}
            metalness={0.0}
            emissive={getEmissiveColor()}
            emissiveIntensity={getEmissiveIntensity()}
          />
        );

      case 'sand_dune':
        return (
          <meshStandardMaterial
            color="#DEB887"
            roughness={0.9}
            metalness={0.0}
            emissive={getEmissiveColor()}
            emissiveIntensity={getEmissiveIntensity()}
          />
        );

      case 'oasis_water':
        return (
          <meshStandardMaterial
            color="#4682B4"
            roughness={0.1}
            metalness={0.8}
            transparent
            opacity={0.8}
            emissive={getEmissiveColor()}
            emissiveIntensity={getEmissiveIntensity()}
          />
        );

      default:
        return (
          <meshStandardMaterial
            color="#cccccc"
            emissive={getEmissiveColor()}
            emissiveIntensity={getEmissiveIntensity()}
          />
        );
    }
  };

  const handlePointerOver = () => {
    if (editable) {
      setLocalHovered(true);
      onHover(true);
    }
  };

  const handlePointerOut = () => {
    setLocalHovered(false);
    onHover(false);
  };

  const handleClick = (event: any) => {
    if (editable) {
      // Only respond to left mouse button (button 0)
      if (event.button !== 0) return;

      event.stopPropagation();
      onSelect();
    }
  };

  const getEmissiveColor = () => {
    if (isSelected) return '#00ff00';
    if ((isHovered || localHovered) && isEraseMode) return '#ff0000';
    if (isHovered || localHovered) return '#333333';
    return '#000000';
  };

  const getEmissiveIntensity = () => {
    if (isSelected) return 0.3;
    if ((isHovered || localHovered) && isEraseMode) return 0.4;
    if (isHovered || localHovered) return 0.2;
    return 0;
  };

  return (
    <group
      position={object.position}
      rotation={object.rotation}
      scale={object.scale}
      userData={{ objectId: object.id }}
    >
      <mesh
        ref={meshRef}
        onClick={handleClick}
        onPointerOver={handlePointerOver}
        onPointerOut={handlePointerOut}
        castShadow
        receiveShadow
        userData={{ objectId: object.id }}
      >
        {renderObjectGeometry()}
        {getObjectMaterial()}
      </mesh>

      {/* Selection Indicator */}
      {isSelected && (
        <mesh position={[0, 0, 0]}>
          <sphereGeometry args={[2, 16, 8]} />
          <meshBasicMaterial
            color="#00ff00"
            transparent
            opacity={0.2}
            wireframe
          />
        </mesh>
      )}

      {/* Hover Indicator */}
      {(isHovered || localHovered) && !isSelected && (
        <mesh position={[0, 0, 0]}>
          <sphereGeometry args={[1.8, 16, 8]} />
          <meshBasicMaterial
            color="#ffffff"
            transparent
            opacity={0.1}
            wireframe
          />
        </mesh>
      )}
    </group>
  );
};
