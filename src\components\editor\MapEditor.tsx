import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Canvas } from '@react-three/fiber';
import { RootState } from '../../store';
import {
  createNewMap,
  setEditorMode,
  setEditorTool,
  toggleGrid,
  toggleSnapToGrid,
  togglePreviewMode,
  undo,
  redo,
  saveMap
} from '../../store/editor';
import { EditorMode, EditorTool } from '../../types/editorTypes';
import { EditorScene } from './EditorScene';
import { AssetLibrary } from './AssetLibrary';
import { EditorToolbar } from './EditorToolbar';
import { EditorSidebar } from './EditorSidebar';
import { TileEditor } from './TileEditor';
import { TerrainEditor } from './TerrainEditor';
import { TerrainToolsPanel } from './TerrainToolsPanel';
import { EnvironmentEditor } from './EnvironmentEditor';
import './MapEditor.scss';

interface MapEditorProps {
  onExit?: () => void;
}

export const MapEditor: React.FC<MapEditorProps> = ({ onExit }) => {
  const dispatch = useDispatch();
  const {
    currentMap,
    editorState,
    history,
    isDirty,
    previewMode,
    isLoading,
    error
  } = useSelector((state: RootState) => state.editor);

  const [showNewMapDialog, setShowNewMapDialog] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Initialize with a default map if none exists
  useEffect(() => {
    if (!currentMap) {
      setShowNewMapDialog(true);
    }
  }, [currentMap]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'z':
            e.preventDefault();
            if (e.shiftKey) {
              dispatch(redo());
            } else {
              dispatch(undo());
            }
            break;
          case 's':
            e.preventDefault();
            handleSave();
            break;
          case 'g':
            e.preventDefault();
            dispatch(toggleGrid());
            break;
          case 'p':
            e.preventDefault();
            dispatch(togglePreviewMode());
            break;
        }
      }

      // Tool shortcuts
      switch (e.key) {
        case 'v':
          dispatch(setEditorTool(EditorTool.SELECT));
          break;
        case 'b':
          dispatch(setEditorTool(EditorTool.PLACE));
          break;
        case 'e':
          dispatch(setEditorTool(EditorTool.ERASE));
          break;
        case 'r':
          dispatch(setEditorTool(EditorTool.ROTATE));
          break;
        case 's':
          if (!e.ctrlKey && !e.metaKey) {
            dispatch(setEditorTool(EditorTool.SCALE));
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [dispatch]);

  const handleNewMap = (name: string, boardSize: number) => {
    dispatch(createNewMap({ name, boardSize }));
    setShowNewMapDialog(false);
  };

  const handleSave = () => {
    if (currentMap) {
      dispatch(saveMap());
      // TODO: Implement actual save to backend/localStorage
      console.log('Saving map:', currentMap.name);
    }
  };

  const handleExport = () => {
    if (currentMap) {
      // TODO: Implement export functionality
      console.log('Exporting map:', currentMap.name);
    }
  };

  const handleModeChange = (mode: EditorMode) => {
    dispatch(setEditorMode(mode));
  };

  const renderModePanel = () => {
    switch (editorState.mode) {
      case EditorMode.TILE_PLACEMENT:
        return <TileEditor />;
      case EditorMode.TERRAIN_SCULPTING:
        return <TerrainToolsPanel />;
      case EditorMode.ENVIRONMENT_OBJECTS:
        return <AssetLibrary />;
      case EditorMode.LIGHTING:
        return <EnvironmentEditor />;
      default:
        return <AssetLibrary />;
    }
  };

  if (showNewMapDialog) {
    return <NewMapDialog onConfirm={handleNewMap} onCancel={() => setShowNewMapDialog(false)} />;
  }

  if (!currentMap) {
    return (
      <div className="map-editor__loading">
        <div className="loading-spinner">Loading...</div>
      </div>
    );
  }

  return (
    <div className="map-editor">
      {/* Header */}
      <div className="map-editor__header">
        <div className="map-editor__title">
          <h1>{currentMap.name}</h1>
          {isDirty && <span className="dirty-indicator">*</span>}
        </div>

        <div className="map-editor__actions">
          <button onClick={handleSave} disabled={!isDirty} className="btn btn--primary">
            Save {isDirty && '*'}
          </button>
          <button onClick={handleExport} className="btn btn--secondary">
            Export
          </button>
          <button onClick={() => dispatch(togglePreviewMode())} className="btn btn--secondary">
            {previewMode ? 'Edit' : 'Preview'}
          </button>
          {onExit && (
            <button onClick={onExit} className="btn btn--secondary">
              Exit
            </button>
          )}
        </div>
      </div>

      {/* Main Content */}
      <div className="map-editor__content">
        {/* Toolbar */}
        <EditorToolbar
          currentMode={editorState.mode}
          currentTool={editorState.selectedTool}
          onModeChange={handleModeChange}
          onToolChange={(tool) => dispatch(setEditorTool(tool))}
          canUndo={history.currentIndex >= 0}
          canRedo={history.currentIndex < history.actions.length - 1}
          onUndo={() => dispatch(undo())}
          onRedo={() => dispatch(redo())}
          gridVisible={editorState.gridVisible}
          snapToGrid={editorState.snapToGrid}
          onToggleGrid={() => dispatch(toggleGrid())}
          onToggleSnap={() => dispatch(toggleSnapToGrid())}
        />

        {/* Main Editor Area */}
        <div className="map-editor__main">
          {/* 3D Viewport */}
          <div className="map-editor__viewport">
            <Canvas
              camera={{
                position: editorState.cameraPosition,
                fov: 50,
                near: 0.1,
                far: 1000
              }}
              style={{
                width: '100%',
                height: '100%',
                background: 'linear-gradient(to bottom, #87CEEB 0%, #F4A460 100%)'
              }}
            >
              <EditorScene />
            </Canvas>

            {/* Viewport Overlay */}
            <div className="map-editor__viewport-overlay">
              {isLoading && (
                <div className="loading-overlay">
                  <div className="loading-spinner">Loading...</div>
                </div>
              )}

              {error && (
                <div className="error-overlay">
                  <div className="error-message">{error}</div>
                </div>
              )}

              {previewMode && (
                <div className="preview-overlay">
                  <div className="preview-badge">Preview Mode</div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className={`map-editor__sidebar ${sidebarCollapsed ? 'collapsed' : ''}`}>
            <div className="sidebar-header">
              <button
                className="sidebar-toggle"
                onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              >
                {sidebarCollapsed ? '→' : '←'}
              </button>
            </div>

            {!sidebarCollapsed && (
              <div className="sidebar-content">
                <div className="sidebar-section">
                  <h3>Mode: {editorState.mode.replace('_', ' ').toUpperCase()}</h3>
                  {renderModePanel()}
                </div>

                <div className="sidebar-section">
                  <EditorSidebar />
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Status Bar */}
      <div className="map-editor__status">
        <div className="status-left">
          <span>Tiles: {currentMap.tiles.length}</span>
          <span>Objects: {currentMap.placedObjects.length}</span>
          <span>Grid: {editorState.gridVisible ? 'ON' : 'OFF'}</span>
          <span>Snap: {editorState.snapToGrid ? 'ON' : 'OFF'}</span>
        </div>

        <div className="status-right">
          <span>Tool: {editorState.selectedTool}</span>
          {editorState.selectedAsset && (
            <span>Asset: {editorState.selectedAsset.name}</span>
          )}
          <span className="camera-controls-hint" title="Camera Controls: Middle Mouse = Rotate, Right Mouse = Pan, Scroll = Zoom">
            📹 MMB: Rotate | RMB: Pan | Scroll: Zoom
          </span>
        </div>
      </div>
    </div>
  );
};

// New Map Dialog Component
interface NewMapDialogProps {
  onConfirm: (name: string, boardSize: number) => void;
  onCancel: () => void;
}

const NewMapDialog: React.FC<NewMapDialogProps> = ({ onConfirm, onCancel }) => {
  const [name, setName] = useState('New Map');
  const [boardSize, setBoardSize] = useState(60);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (name.trim()) {
      onConfirm(name.trim(), boardSize);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="modal">
        <h2>Create New Map</h2>
        <form onSubmit={handleSubmit}>
          <div className="form-group">
            <label htmlFor="map-name">Map Name:</label>
            <input
              id="map-name"
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              required
              autoFocus
            />
          </div>

          <div className="form-group">
            <label htmlFor="board-size">Board Size (tiles):</label>
            <input
              id="board-size"
              type="number"
              min="20"
              max="120"
              value={boardSize}
              onChange={(e) => setBoardSize(parseInt(e.target.value))}
              required
            />
          </div>

          <div className="form-actions">
            <button type="button" onClick={onCancel} className="btn btn--secondary">
              Cancel
            </button>
            <button type="submit" className="btn btn--primary">
              Create
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
