.simple-game-board {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Arial', sans-serif;

  .game-header {
    text-align: center;
    margin-bottom: 30px;

    h1 {
      color: #333;
      margin-bottom: 10px;
    }

    .game-over {
      background: linear-gradient(135deg, #FFD700, #FFA500);
      padding: 20px;
      border-radius: 10px;
      color: #333;

      h2 {
        margin: 0 0 10px 0;
      }
    }

    .turn-info {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      border-left: 4px solid #007bff;

      .dice-result {
        font-size: 1.2em;
        font-weight: bold;
        color: #28a745;
      }
    }
  }

  .game-content {
    display: grid;
    grid-template-columns: 250px 1fr 250px;
    gap: 20px;
    margin-bottom: 30px;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 15px;
    }
  }

  .players-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #dee2e6;

    h3 {
      margin-top: 0;
      color: #495057;
    }

    .player-card {
      background: white;
      border: 2px solid #dee2e6;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      transition: all 0.3s ease;

      &.current-player {
        border-color: #007bff;
        box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
        transform: scale(1.02);
      }

      .player-name {
        font-weight: bold;
        font-size: 1.1em;
        margin-bottom: 10px;
        color: #333;
      }

      .player-stats {
        display: flex;
        flex-direction: column;
        gap: 5px;

        .stat {
          display: flex;
          align-items: center;
          gap: 8px;

          .stat-icon {
            font-size: 1.2em;
          }

          .stat-value {
            font-weight: bold;
            color: #495057;
          }
        }
      }

      .player-status {
        margin-top: 10px;
        padding: 5px 10px;
        background: #dc3545;
        color: white;
        border-radius: 4px;
        text-align: center;
        font-size: 0.9em;
      }
    }
  }

  .board-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #dee2e6;

    h3 {
      margin-top: 0;
      color: #495057;
    }

    .board-3d-container {
      width: 100%;
      height: 500px;
      border-radius: 12px;
      overflow: hidden;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
      border: 2px solid #dee2e6;
    }

    .board-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
      gap: 5px;
      max-height: 400px;
      overflow-y: auto;
      padding: 10px;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      background: white;

      .tile {
        position: relative;
        width: 60px;
        height: 60px;
        border: 2px solid #333;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: transform 0.2s ease;

        &:hover {
          transform: scale(1.1);
        }

        .tile-number {
          position: absolute;
          top: 2px;
          left: 4px;
          font-size: 0.7em;
          font-weight: bold;
          color: #333;
        }

        .tile-icon {
          font-size: 1.5em;
        }

        .players-on-tile {
          position: absolute;
          bottom: 2px;
          right: 2px;
          display: flex;
          gap: 2px;

          .player-marker {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.6em;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
          }
        }

        &.treasure_chest {
          border-color: #FF6347;
          box-shadow: 0 0 10px rgba(255, 99, 71, 0.5);
        }
      }
    }
  }

  .controls-panel {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #dee2e6;

    h3 {
      margin-top: 0;
      color: #495057;
    }

    .controls {
      margin-bottom: 20px;

      button {
        display: block;
        width: 100%;
        padding: 12px;
        margin-bottom: 10px;
        border: none;
        border-radius: 8px;
        font-size: 1.1em;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;

        &.roll-dice-btn {
          background: linear-gradient(135deg, #28a745, #20c997);
          color: white;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
          }
        }

        &.open-chest-btn {
          background: linear-gradient(135deg, #FFD700, #FFA500);
          color: #333;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
          }
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    .waiting {
      text-align: center;
      padding: 20px;
      background: #e9ecef;
      border-radius: 8px;
      margin-bottom: 20px;

      p {
        margin: 0;
        color: #6c757d;
        font-style: italic;
      }
    }

    .game-info {
      h4 {
        color: #495057;
        margin-bottom: 10px;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          padding: 5px 0;
          font-size: 0.9em;
          color: #6c757d;
        }
      }
    }
  }

  .game-log {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #dee2e6;

    h3 {
      margin-top: 0;
      color: #495057;
    }

    .log-entries {
      max-height: 200px;
      overflow-y: auto;
      background: white;
      border: 1px solid #dee2e6;
      border-radius: 8px;
      padding: 10px;

      .log-entry {
        display: flex;
        gap: 10px;
        padding: 5px 0;
        border-bottom: 1px solid #f1f3f4;

        &:last-child {
          border-bottom: none;
        }

        .log-time {
          color: #6c757d;
          font-size: 0.8em;
          min-width: 80px;
        }

        .log-details {
          color: #495057;
          font-size: 0.9em;
        }
      }
    }
  }
}
