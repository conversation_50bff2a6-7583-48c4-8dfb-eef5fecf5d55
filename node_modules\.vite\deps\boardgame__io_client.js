import {
  Client,
  LobbyClient,
  LobbyClientError,
  require_setImmediate
} from "./chunk-J7D76I2G.js";
import {
  require_lodash,
  require_rfc6902
} from "./chunk-CT2AWXLA.js";
import {
  __toESM
} from "./chunk-UV5CTPV7.js";

// node_modules/boardgame.io/dist/esm/client.js
var import_lodash = __toESM(require_lodash());
var import_rfc6902 = __toESM(require_rfc6902());
var import_setimmediate = __toESM(require_setImmediate());
export {
  Client,
  LobbyClient,
  LobbyClientError
};
//# sourceMappingURL=boardgame__io_client.js.map
