import { Ctx, DefaultPluginAPIs } from 'boardgame.io';

export enum GameType {
  Online,
  Local,
  AI,
}

export type GameStage =
  | 'rollDice'
  | 'movePlayer'
  | 'applyTileEffect'
  | 'checkWin'
  | 'end';

export type GameContext = DefaultPluginAPIs & {
  G: GameState;
  ctx: Ctx;
  playerID: string;
};

export type OverrideState = {
  G: GameState;
  ctx: Ctx;
};

export interface GameState {
  tiles: Tile[];
  players: Record<string, Player>;
  currentDiceRoll: number;
  gameLog: GameLogEntry[];
  gameConfig: GameConfig;
}

export interface GameConfig {
  boardSize: number; // 40-120 tiles
  treasureChestPosition: number; // Position of treasure chest (default: 15)
  keysToWin: number; // Keys needed to open chest (default: 40)
}

export interface Player {
  ID: string;
  name: string;
  health: number; // 0-100
  keys: number; // Number of keys collected
  position: number; // Current tile position (1-based)
  color: string; // Player color for 3D representation
  isAlive: boolean; // False if health reaches 0
}

export interface Tile {
  id: number; // 1-based tile ID
  type: TileType;
  position: { x: number; y: number; z: number }; // 3D world position
  occupiedBy?: string; // Player ID if occupied
}

export enum TileType {
  BASIC = 'basic',
  HEALING = 'healing',
  DAMAGE = 'damage',
  KEY = 'key',
  TREASURE_CHEST = 'treasure_chest'
}

export interface GameLogEntry {
  playerId: string;
  action: string;
  details: string;
  timestamp: number;
}

export interface TileEffect {
  type: TileType;
  name: string;
  description: string;
  apply: (player: Player) => Player;
}

export interface DiceRollResult {
  value: number;
  playerId: string;
}

export interface MoveResult {
  fromPosition: number;
  toPosition: number;
  playerId: string;
  tileEffect?: TileEffect;
}

export interface GameWinner {
  playerId: string;
  reason: 'treasure_chest' | 'last_standing';
}

// Legacy types for compatibility with old UI components
export interface TokenState {
  tokenName: string;
  playerID: string;
  obstructing: 'none' | 'all' | 'opponent';
  isSecret: boolean;
  isRemovable: boolean;
  color: string;
}

export interface OffBoardToken {
  playerID: string;
  direction: number;
}


