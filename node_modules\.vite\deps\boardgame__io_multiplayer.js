import {
  Local,
  SocketIO
} from "./chunk-6QUVGBWT.js";
import {
  require_lodash,
  require_rfc6902
} from "./chunk-CT2AWXLA.js";
import {
  __toESM
} from "./chunk-UV5CTPV7.js";

// node_modules/boardgame.io/dist/esm/multiplayer.js
var import_lodash = __toESM(require_lodash());
var import_rfc6902 = __toESM(require_rfc6902());
export {
  Local,
  SocketIO
};
//# sourceMappingURL=boardgame__io_multiplayer.js.map
