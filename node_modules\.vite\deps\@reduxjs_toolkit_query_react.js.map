{"version": 3, "sources": ["../../@reduxjs/toolkit/src/query/react/index.ts", "../../@reduxjs/toolkit/src/query/react/module.ts", "../../@reduxjs/toolkit/src/query/react/buildHooks.ts", "../../@reduxjs/toolkit/src/query/react/useSerializedStableValue.ts", "../../@reduxjs/toolkit/src/query/react/constants.ts", "../../@reduxjs/toolkit/src/query/react/useShallowStableValue.ts", "../../@reduxjs/toolkit/src/query/core/rtkImports.ts", "../../@reduxjs/toolkit/src/query/defaultSerializeQueryArgs.ts", "../../@reduxjs/toolkit/src/query/endpointDefinitions.ts", "../../@reduxjs/toolkit/src/query/utils/capitalize.ts", "../../@reduxjs/toolkit/src/query/tsHelpers.ts", "../../@reduxjs/toolkit/src/query/utils/countObjectKeys.ts", "../../@reduxjs/toolkit/src/query/react/ApiProvider.tsx"], "sourcesContent": ["// This must remain here so that the `mangleErrors.cjs` build script\n// does not have to import this into each source file it rewrites.\nimport { formatProdErrorMessage } from '@reduxjs/toolkit';\nimport { coreModule, buildCreateApi } from '@reduxjs/toolkit/query';\nimport { reactHooksModule, reactHooksModuleName } from './module';\nexport * from '@reduxjs/toolkit/query';\nexport { ApiProvider } from './ApiProvider';\nconst createApi = /* @__PURE__ */buildCreateApi(coreModule(), reactHooksModule());\nexport type { TypedUseQueryHookResult, TypedUseQueryStateResult, TypedUseQuerySubscriptionResult, TypedUseMutationResult } from './buildHooks';\nexport { createApi, reactHooksModule, reactHooksModuleName };", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { MutationHooks, QueryHooks } from './buildHooks';\nimport { buildHooks } from './buildHooks';\nimport { isQueryDefinition, isMutationDefinition } from '../endpointDefinitions';\nimport type { EndpointDefinitions, QueryDefinition, MutationDefinition, QueryArgFrom } from '@reduxjs/toolkit/query';\nimport type { Api, Module } from '../apiTypes';\nimport { capitalize } from '../utils';\nimport { safeAssign } from '../tsHelpers';\nimport type { BaseQueryFn } from '@reduxjs/toolkit/query';\nimport type { HooksWithUniqueNames } from './namedHooks';\nimport { useDispatch as rrUseDispatch, useSelector as rrUseSelector, useStore as rrUseStore, batch as rrBatch } from 'react-redux';\nimport type { QueryKeys } from '../core/apiState';\nimport type { PrefetchOptions } from '../core/module';\nimport { countObjectKeys } from '../utils/countObjectKeys';\nimport { createSelector as _createSelector } from 'reselect';\nexport const reactHooksModuleName = /* @__PURE__ */Symbol();\nexport type ReactHooksModule = typeof reactHooksModuleName;\ndeclare module '@reduxjs/toolkit/query' {\n  export interface ApiModules< // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  BaseQuery extends BaseQueryFn, Definitions extends EndpointDefinitions, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  ReducerPath extends string, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  TagTypes extends string> {\n    [reactHooksModuleName]: {\n      /**\n       *  Endpoints based on the input endpoints provided to `createApi`, containing `select`, `hooks` and `action matchers`.\n       */\n      endpoints: { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<any, any, any, any, any> ? QueryHooks<Definitions[K]> : Definitions[K] extends MutationDefinition<any, any, any, any, any> ? MutationHooks<Definitions[K]> : never };\n      /**\n       * A hook that accepts a string endpoint name, and provides a callback that when called, pre-fetches the data for that endpoint.\n       */\n\n      usePrefetch<EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, options?: PrefetchOptions): (arg: QueryArgFrom<Definitions[EndpointName]>, options?: PrefetchOptions) => void;\n    } & HooksWithUniqueNames<Definitions>;\n  }\n}\ntype RR = typeof import('react-redux');\nexport interface ReactHooksModuleOptions {\n  /**\n   * The hooks from React Redux to be used\n   */\n  hooks?: {\n    /**\n     * The version of the `useDispatch` hook to be used\n     */\n    useDispatch: RR['useDispatch'];\n    /**\n     * The version of the `useSelector` hook to be used\n     */\n\n    useSelector: RR['useSelector'];\n    /**\n     * The version of the `useStore` hook to be used\n     */\n\n    useStore: RR['useStore'];\n  };\n  /**\n   * The version of the `batchedUpdates` function to be used\n   */\n\n  batch?: RR['batch'];\n  /**\n   * Enables performing asynchronous tasks immediately within a render.\n   *\n   * @example\n   *\n   * ```ts\n   * import {\n   *   buildCreateApi,\n   *   coreModule,\n   *   reactHooksModule\n   * } from '@reduxjs/toolkit/query/react'\n   *\n   * const createApi = buildCreateApi(\n   *   coreModule(),\n   *   reactHooksModule({ unstable__sideEffectsInRender: true })\n   * )\n   * ```\n   */\n\n  unstable__sideEffectsInRender?: boolean;\n  /**\n   * A selector creator (usually from `reselect`, or matching the same signature)\n   */\n\n  createSelector?: typeof _createSelector;\n}\n/**\n * Creates a module that generates react hooks from endpoints, for use with `buildCreateApi`.\n *\n *  @example\n * ```ts\n * const MyContext = React.createContext<ReactReduxContextValue>(null as any);\n * const customCreateApi = buildCreateApi(\n *   coreModule(),\n *   reactHooksModule({\n *     hooks: {\n *       useDispatch: createDispatchHook(MyContext),\n *       useSelector: createSelectorHook(MyContext),\n *       useStore: createStoreHook(MyContext)\n *     }\n *   })\n * );\n * ```\n *\n * @returns A module for use with `buildCreateApi`\n */\n\nexport const reactHooksModule = ({\n  batch = rrBatch,\n  hooks = {\n    useDispatch: rrUseDispatch,\n    useSelector: rrUseSelector,\n    useStore: rrUseStore\n  },\n  createSelector = _createSelector,\n  unstable__sideEffectsInRender = false,\n  ...rest\n}: ReactHooksModuleOptions = {}): Module<ReactHooksModule> => {\n  if (process.env.NODE_ENV !== 'production') {\n    const hookNames = (['useDispatch', 'useSelector', 'useStore'] as const);\n    let warned = false;\n\n    for (const hookName of hookNames) {\n      // warn for old hook options\n      if (countObjectKeys(rest) > 0) {\n        if ((rest as Partial<typeof hooks>)[hookName]) {\n          if (!warned) {\n            console.warn('As of RTK 2.0, the hooks now need to be specified as one object, provided under a `hooks` key:' + '\\n`reactHooksModule({ hooks: { useDispatch, useSelector, useStore } })`');\n            warned = true;\n          }\n        } // migrate\n        // @ts-ignore\n\n\n        hooks[hookName] = rest[hookName];\n      } // then make sure we have them all\n\n\n      if (typeof hooks[hookName] !== 'function') {\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(36) : `When using custom hooks for context, all ${hookNames.length} hooks need to be provided: ${hookNames.join(', ')}.\\nHook ${hookName} was either not provided or not a function.`);\n      }\n    }\n  }\n\n  return {\n    name: reactHooksModuleName,\n\n    init(api, {\n      serializeQueryArgs\n    }, context) {\n      const anyApi = ((api as any) as Api<any, Record<string, any>, string, string, ReactHooksModule>);\n      const {\n        buildQueryHooks,\n        buildMutationHook,\n        usePrefetch\n      } = buildHooks({\n        api,\n        moduleOptions: {\n          batch,\n          hooks,\n          unstable__sideEffectsInRender,\n          createSelector\n        },\n        serializeQueryArgs,\n        context\n      });\n      safeAssign(anyApi, {\n        usePrefetch\n      });\n      safeAssign(context, {\n        batch\n      });\n      return {\n        injectEndpoint(endpointName, definition) {\n          if (isQueryDefinition(definition)) {\n            const {\n              useQuery,\n              useLazyQuery,\n              useLazyQuerySubscription,\n              useQueryState,\n              useQuerySubscription\n            } = buildQueryHooks(endpointName);\n            safeAssign(anyApi.endpoints[endpointName], {\n              useQuery,\n              useLazyQuery,\n              useLazyQuerySubscription,\n              useQueryState,\n              useQuerySubscription\n            });\n            (api as any)[`use${capitalize(endpointName)}Query`] = useQuery;\n            (api as any)[`useLazy${capitalize(endpointName)}Query`] = useLazyQuery;\n          } else if (isMutationDefinition(definition)) {\n            const useMutation = buildMutationHook(endpointName);\n            safeAssign(anyApi.endpoints[endpointName], {\n              useMutation\n            });\n            (api as any)[`use${capitalize(endpointName)}Mutation`] = useMutation;\n          }\n        }\n\n      };\n    }\n\n  };\n};", "import { formatProdErrorMessage as _formatProdErrorMessage2 } from \"@reduxjs/toolkit\";\nimport { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { UnknownAction, Selector, ThunkAction, ThunkDispatch } from '@reduxjs/toolkit';\nimport type { DependencyList } from 'react';\nimport { useCallback, useDebugValue, useEffect, useLayoutEffect, useMemo, useRef, useState } from 'react';\nimport { QueryStatus, skipToken } from '@reduxjs/toolkit/query';\nimport type { QuerySubState, SubscriptionOptions, QueryKeys, RootState } from '@reduxjs/toolkit/query';\nimport type { EndpointDefinitions, MutationDefinition, QueryDefinition, QueryArgFrom, ResultTypeFrom, QueryResultSelectorResult, MutationResultSelectorResult, SkipToken, QueryActionCreatorResult, MutationActionCreatorResult, SerializeQueryArgs, Api, ApiContext, TSHelpersId, TSHelpersNoInfer, TSHelpersOverride, ApiEndpointMutation, ApiEndpointQuery, CoreModule, PrefetchOptions } from '@reduxjs/toolkit/query';\nimport { shallowEqual } from 'react-redux';\nimport type { ReactHooksModuleOptions } from './module';\nimport { useStableQueryArgs } from './useSerializedStableValue';\nimport type { UninitializedValue } from './constants';\nimport { UNINITIALIZED_VALUE } from './constants';\nimport { useShallowStableValue } from './useShallowStableValue';\nimport type { BaseQueryFn } from '../baseQueryTypes';\nimport { defaultSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport type { SubscriptionSelectors } from '../core/buildMiddleware/types'; // Copy-pasted from React-Redux\n\nexport const useIsomorphicLayoutEffect = typeof window !== 'undefined' && !!window.document && !!window.document.createElement ? useLayoutEffect : useEffect;\nexport interface QueryHooks<Definition extends QueryDefinition<any, any, any, any, any>> {\n  useQuery: UseQuery<Definition>;\n  useLazyQuery: UseLazyQuery<Definition>;\n  useQuerySubscription: UseQuerySubscription<Definition>;\n  useLazyQuerySubscription: UseLazyQuerySubscription<Definition>;\n  useQueryState: UseQueryState<Definition>;\n}\nexport interface MutationHooks<Definition extends MutationDefinition<any, any, any, any, any>> {\n  useMutation: UseMutation<Definition>;\n}\n/**\n * A React hook that automatically triggers fetches of data from an endpoint, 'subscribes' the component to the cached data, and reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\n *\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already, and the hook will return the data for that query arg once it's available.\n *\n * This hook combines the functionality of both [`useQueryState`](#usequerystate) and [`useQuerySubscription`](#usequerysubscription) together, and is intended to be used in the majority of situations.\n *\n * #### Features\n *\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n */\n\nexport type UseQuery<D extends QueryDefinition<any, any, any, any>> = <R extends Record<string, any> = UseQueryStateDefaultResult<D>>(arg: QueryArgFrom<D> | SkipToken, options?: UseQuerySubscriptionOptions & UseQueryStateOptions<D, R>) => UseQueryHookResult<D, R>;\nexport type UseQueryHookResult<D extends QueryDefinition<any, any, any, any>, R = UseQueryStateDefaultResult<D>> = UseQueryStateResult<D, R> & UseQuerySubscriptionResult<D>;\n/**\n * Helper type to manually type the result\n * of the `useQuery` hook in userland code.\n */\n\nexport type TypedUseQueryHookResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn, R = UseQueryStateDefaultResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>> = TypedUseQueryStateResult<ResultType, QueryArg, BaseQuery, R> & TypedUseQuerySubscriptionResult<ResultType, QueryArg, BaseQuery>;\ninterface UseQuerySubscriptionOptions extends SubscriptionOptions {\n  /**\n   * Prevents a query from automatically running.\n   *\n   * @remarks\n   * When `skip` is true (or `skipToken` is passed in as `arg`):\n   *\n   * - **If the query has cached data:**\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\n   *   * The query will have a status of `uninitialized`\n   *   * If `skip: false` is set after the initial load, the cached result will be used\n   * - **If the query does not have cached data:**\n   *   * The query will have a status of `uninitialized`\n   *   * The query will not exist in the state when viewed with the dev tools\n   *   * The query will not automatically fetch on mount\n   *   * The query will not automatically run when additional components with the same query are added that do run\n   *\n   * @example\n   * ```tsx\n   * // codeblock-meta no-transpile title=\"Skip example\"\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\n   *     skip,\n   *   });\n   *\n   *   return (\n   *     <div>\n   *       {name} - {status}\n   *     </div>\n   *   );\n   * };\n   * ```\n   */\n  skip?: boolean;\n  /**\n   * Defaults to `false`. This setting allows you to control whether if a cached result is already available, RTK Query will only serve a cached result, or if it should `refetch` when set to `true` or if an adequate amount of time has passed since the last successful query result.\n   * - `false` - Will not cause a query to be performed _unless_ it does not exist yet.\n   * - `true` - Will always refetch when a new subscriber to a query is added. Behaves the same as calling the `refetch` callback or passing `forceRefetch: true` in the action creator.\n   * - `number` - **Value is in seconds**. If a number is provided and there is an existing query in the cache, it will compare the current time vs the last fulfilled timestamp, and only refetch if enough time has elapsed.\n   *\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\n   */\n\n  refetchOnMountOrArgChange?: boolean | number;\n}\n/**\n * A React hook that automatically triggers fetches of data from an endpoint, and 'subscribes' the component to the cached data.\n *\n * The query arg is used as a cache key. Changing the query arg will tell the hook to re-fetch the data if it does not exist in the cache already.\n *\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useQuery`](#usequery) or [`useQueryState`](#usequerystate).\n *\n * #### Features\n *\n * - Automatically triggers requests to retrieve data based on the hook argument and whether cached data exists by default\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met\n */\n\nexport type UseQuerySubscription<D extends QueryDefinition<any, any, any, any>> = (arg: QueryArgFrom<D> | SkipToken, options?: UseQuerySubscriptionOptions) => UseQuerySubscriptionResult<D>;\nexport type UseQuerySubscriptionResult<D extends QueryDefinition<any, any, any, any>> = Pick<QueryActionCreatorResult<D>, 'refetch'>;\n/**\n * Helper type to manually type the result\n * of the `useQuerySubscription` hook in userland code.\n */\n\nexport type TypedUseQuerySubscriptionResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn> = UseQuerySubscriptionResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>;\nexport type UseLazyQueryLastPromiseInfo<D extends QueryDefinition<any, any, any, any>> = {\n  lastArg: QueryArgFrom<D>;\n};\n/**\n * A React hook similar to [`useQuery`](#usequery), but with manual control over when the data fetching occurs.\n *\n * This hook includes the functionality of [`useLazyQuerySubscription`](#uselazyquerysubscription).\n *\n * #### Features\n *\n * - Manual control over firing a request to retrieve data\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met and the fetch has been manually called at least once\n *\n * #### Note\n *\n * When the trigger function returned from a LazyQuery is called, it always initiates a new request to the server even if there is cached data. Set `preferCacheValue`(the second argument to the function) as `true` if you want it to immediately return a cached value if one exists.\n */\n\nexport type UseLazyQuery<D extends QueryDefinition<any, any, any, any>> = <R extends Record<string, any> = UseQueryStateDefaultResult<D>>(options?: SubscriptionOptions & Omit<UseQueryStateOptions<D, R>, 'skip'>) => [LazyQueryTrigger<D>, UseQueryStateResult<D, R>, UseLazyQueryLastPromiseInfo<D>];\nexport type LazyQueryTrigger<D extends QueryDefinition<any, any, any, any>> = {\n  /**\n   * Triggers a lazy query.\n   *\n   * By default, this will start a new request even if there is already a value in the cache.\n   * If you want to use the cache value and only start a request if there is no cache value, set the second argument to `true`.\n   *\n   * @remarks\n   * If you need to access the error or success payload immediately after a lazy query, you can chain .unwrap().\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using .unwrap with async await\"\n   * try {\n   *   const payload = await getUserById(1).unwrap();\n   *   console.log('fulfilled', payload)\n   * } catch (error) {\n   *   console.error('rejected', error);\n   * }\n   * ```\n   */\n  (arg: QueryArgFrom<D>, preferCacheValue?: boolean): QueryActionCreatorResult<D>;\n};\n/**\n * A React hook similar to [`useQuerySubscription`](#usequerysubscription), but with manual control over when the data fetching occurs.\n *\n * Note that this hook does not return a request status or cached data. For that use-case, see [`useLazyQuery`](#uselazyquery).\n *\n * #### Features\n *\n * - Manual control over firing a request to retrieve data\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Accepts polling/re-fetching options to trigger automatic re-fetches when the corresponding criteria is met and the fetch has been manually called at least once\n */\n\nexport type UseLazyQuerySubscription<D extends QueryDefinition<any, any, any, any>> = (options?: SubscriptionOptions) => readonly [LazyQueryTrigger<D>, QueryArgFrom<D> | UninitializedValue];\nexport type QueryStateSelector<R extends Record<string, any>, D extends QueryDefinition<any, any, any, any>> = (state: UseQueryStateDefaultResult<D>) => R;\n/**\n * A React hook that reads the request status and cached data from the Redux store. The component will re-render as the loading status changes and the data becomes available.\n *\n * Note that this hook does not trigger fetching new data. For that use-case, see [`useQuery`](#usequery) or [`useQuerySubscription`](#usequerysubscription).\n *\n * #### Features\n *\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n */\n\nexport type UseQueryState<D extends QueryDefinition<any, any, any, any>> = <R extends Record<string, any> = UseQueryStateDefaultResult<D>>(arg: QueryArgFrom<D> | SkipToken, options?: UseQueryStateOptions<D, R>) => UseQueryStateResult<D, R>;\nexport type UseQueryStateOptions<D extends QueryDefinition<any, any, any, any>, R extends Record<string, any>> = {\n  /**\n   * Prevents a query from automatically running.\n   *\n   * @remarks\n   * When skip is true:\n   *\n   * - **If the query has cached data:**\n   *   * The cached data **will not be used** on the initial load, and will ignore updates from any identical query until the `skip` condition is removed\n   *   * The query will have a status of `uninitialized`\n   *   * If `skip: false` is set after skipping the initial load, the cached result will be used\n   * - **If the query does not have cached data:**\n   *   * The query will have a status of `uninitialized`\n   *   * The query will not exist in the state when viewed with the dev tools\n   *   * The query will not automatically fetch on mount\n   *   * The query will not automatically run when additional components with the same query are added that do run\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Skip example\"\n   * const Pokemon = ({ name, skip }: { name: string; skip: boolean }) => {\n   *   const { data, error, status } = useGetPokemonByNameQuery(name, {\n   *     skip,\n   *   });\n   *\n   *   return (\n   *     <div>\n   *       {name} - {status}\n   *     </div>\n   *   );\n   * };\n   * ```\n   */\n  skip?: boolean;\n  /**\n   * `selectFromResult` allows you to get a specific segment from a query result in a performant manner.\n   * When using this feature, the component will not rerender unless the underlying data of the selected item has changed.\n   * If the selected item is one element in a larger collection, it will disregard changes to elements in the same collection.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using selectFromResult to extract a single result\"\n   * function PostsList() {\n   *   const { data: posts } = api.useGetPostsQuery();\n   *\n   *   return (\n   *     <ul>\n   *       {posts?.data?.map((post) => (\n   *         <PostById key={post.id} id={post.id} />\n   *       ))}\n   *     </ul>\n   *   );\n   * }\n   *\n   * function PostById({ id }: { id: number }) {\n   *   // Will select the post with the given id, and will only rerender if the given posts data changes\n   *   const { post } = api.useGetPostsQuery(undefined, {\n   *     selectFromResult: ({ data }) => ({ post: data?.find((post) => post.id === id) }),\n   *   });\n   *\n   *   return <li>{post?.name}</li>;\n   * }\n   * ```\n   */\n\n  selectFromResult?: QueryStateSelector<R, D>;\n};\nexport type UseQueryStateResult<_ extends QueryDefinition<any, any, any, any>, R> = TSHelpersNoInfer<R>;\n/**\n * Helper type to manually type the result\n * of the `useQueryState` hook in userland code.\n */\n\nexport type TypedUseQueryStateResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn, R = UseQueryStateDefaultResult<QueryDefinition<QueryArg, BaseQuery, string, ResultType, string>>> = TSHelpersNoInfer<R>;\ntype UseQueryStateBaseResult<D extends QueryDefinition<any, any, any, any>> = QuerySubState<D> & {\n  /**\n   * Where `data` tries to hold data as much as possible, also re-using\n   * data from the last arguments passed into the hook, this property\n   * will always contain the received data from the query, for the current query arguments.\n   */\n  currentData?: ResultTypeFrom<D>;\n  /**\n   * Query has not started yet.\n   */\n\n  isUninitialized: false;\n  /**\n   * Query is currently loading for the first time. No data yet.\n   */\n\n  isLoading: false;\n  /**\n   * Query is currently fetching, but might have data from an earlier request.\n   */\n\n  isFetching: false;\n  /**\n   * Query has data from a successful load.\n   */\n\n  isSuccess: false;\n  /**\n   * Query is currently in \"error\" state.\n   */\n\n  isError: false;\n};\ntype UseQueryStateDefaultResult<D extends QueryDefinition<any, any, any, any>> = TSHelpersId<TSHelpersOverride<Extract<UseQueryStateBaseResult<D>, {\n  status: QueryStatus.uninitialized;\n}>, {\n  isUninitialized: true;\n}> | TSHelpersOverride<UseQueryStateBaseResult<D>, {\n  isLoading: true;\n  isFetching: boolean;\n  data: undefined;\n} | ({\n  isSuccess: true;\n  isFetching: true;\n  error: undefined;\n} & Required<Pick<UseQueryStateBaseResult<D>, 'data' | 'fulfilledTimeStamp'>>) | ({\n  isSuccess: true;\n  isFetching: false;\n  error: undefined;\n} & Required<Pick<UseQueryStateBaseResult<D>, 'data' | 'fulfilledTimeStamp' | 'currentData'>>) | ({\n  isError: true;\n} & Required<Pick<UseQueryStateBaseResult<D>, 'error'>>)>> & {\n  /**\n   * @deprecated Included for completeness, but discouraged.\n   * Please use the `isLoading`, `isFetching`, `isSuccess`, `isError`\n   * and `isUninitialized` flags instead\n   */\n  status: QueryStatus;\n};\nexport type MutationStateSelector<R extends Record<string, any>, D extends MutationDefinition<any, any, any, any>> = (state: MutationResultSelectorResult<D>) => R;\nexport type UseMutationStateOptions<D extends MutationDefinition<any, any, any, any>, R extends Record<string, any>> = {\n  selectFromResult?: MutationStateSelector<R, D>;\n  fixedCacheKey?: string;\n};\nexport type UseMutationStateResult<D extends MutationDefinition<any, any, any, any>, R> = TSHelpersNoInfer<R> & {\n  originalArgs?: QueryArgFrom<D>;\n  /**\n   * Resets the hook state to it's initial `uninitialized` state.\n   * This will also remove the last result from the cache.\n   */\n\n  reset: () => void;\n};\n/**\n * Helper type to manually type the result\n * of the `useMutation` hook in userland code.\n */\n\nexport type TypedUseMutationResult<ResultType, QueryArg, BaseQuery extends BaseQueryFn, R = MutationResultSelectorResult<MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>>> = UseMutationStateResult<MutationDefinition<QueryArg, BaseQuery, string, ResultType, string>, R>;\n/**\n * A React hook that lets you trigger an update request for a given endpoint, and subscribes the component to read the request status from the Redux store. The component will re-render as the loading status changes.\n *\n * #### Features\n *\n * - Manual control over firing a request to alter data on the server or possibly invalidate the cache\n * - 'Subscribes' the component to keep cached data in the store, and 'unsubscribes' when the component unmounts\n * - Returns the latest request status and cached data from the Redux store\n * - Re-renders as the request status changes and data becomes available\n */\n\nexport type UseMutation<D extends MutationDefinition<any, any, any, any>> = <R extends Record<string, any> = MutationResultSelectorResult<D>>(options?: UseMutationStateOptions<D, R>) => readonly [MutationTrigger<D>, UseMutationStateResult<D, R>];\nexport type MutationTrigger<D extends MutationDefinition<any, any, any, any>> = {\n  /**\n   * Triggers the mutation and returns a Promise.\n   * @remarks\n   * If you need to access the error or success payload immediately after a mutation, you can chain .unwrap().\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using .unwrap with async await\"\n   * try {\n   *   const payload = await addPost({ id: 1, name: 'Example' }).unwrap();\n   *   console.log('fulfilled', payload)\n   * } catch (error) {\n   *   console.error('rejected', error);\n   * }\n   * ```\n   */\n  (arg: QueryArgFrom<D>): MutationActionCreatorResult<D>;\n};\n/**\n * Wrapper around `defaultQueryStateSelector` to be used in `useQuery`.\n * We want the initial render to already come back with\n * `{ isUninitialized: false, isFetching: true, isLoading: true }`\n * to prevent that the library user has to do an additional check for `isUninitialized`/\n */\n\nconst noPendingQueryStateSelector: QueryStateSelector<any, any> = selected => {\n  if (selected.isUninitialized) {\n    return ({ ...selected,\n      isUninitialized: false,\n      isFetching: true,\n      isLoading: selected.data !== undefined ? false : true,\n      status: QueryStatus.pending\n    } as any);\n  }\n\n  return selected;\n};\n\ntype GenericPrefetchThunk = (endpointName: any, arg: any, options: PrefetchOptions) => ThunkAction<void, any, any, UnknownAction>;\n/**\n *\n * @param opts.api - An API with defined endpoints to create hooks for\n * @param opts.moduleOptions.batch - The version of the `batchedUpdates` function to be used\n * @param opts.moduleOptions.useDispatch - The version of the `useDispatch` hook to be used\n * @param opts.moduleOptions.useSelector - The version of the `useSelector` hook to be used\n * @returns An object containing functions to generate hooks based on an endpoint\n */\n\nexport function buildHooks<Definitions extends EndpointDefinitions>({\n  api,\n  moduleOptions: {\n    batch,\n    hooks: {\n      useDispatch,\n      useSelector,\n      useStore\n    },\n    unstable__sideEffectsInRender,\n    createSelector\n  },\n  serializeQueryArgs,\n  context\n}: {\n  api: Api<any, Definitions, any, any, CoreModule>;\n  moduleOptions: Required<ReactHooksModuleOptions>;\n  serializeQueryArgs: SerializeQueryArgs<any>;\n  context: ApiContext<Definitions>;\n}) {\n  const usePossiblyImmediateEffect: (effect: () => void | undefined, deps?: DependencyList) => void = unstable__sideEffectsInRender ? cb => cb() : useEffect;\n  return {\n    buildQueryHooks,\n    buildMutationHook,\n    usePrefetch\n  };\n\n  function queryStatePreSelector(currentState: QueryResultSelectorResult<any>, lastResult: UseQueryStateDefaultResult<any> | undefined, queryArgs: any): UseQueryStateDefaultResult<any> {\n    // if we had a last result and the current result is uninitialized,\n    // we might have called `api.util.resetApiState`\n    // in this case, reset the hook\n    if (lastResult?.endpointName && currentState.isUninitialized) {\n      const {\n        endpointName\n      } = lastResult;\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      if (serializeQueryArgs({\n        queryArgs: lastResult.originalArgs,\n        endpointDefinition,\n        endpointName\n      }) === serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      })) lastResult = undefined;\n    } // data is the last known good request result we have tracked - or if none has been tracked yet the last good result for the current args\n\n\n    let data = currentState.isSuccess ? currentState.data : lastResult?.data;\n    if (data === undefined) data = currentState.data;\n    const hasData = data !== undefined; // isFetching = true any time a request is in flight\n\n    const isFetching = currentState.isLoading; // isLoading = true only when loading while no data is present yet (initial load with no data in the cache)\n\n    const isLoading = !hasData && isFetching; // isSuccess = true when data is present\n\n    const isSuccess = currentState.isSuccess || isFetching && hasData;\n    return ({ ...currentState,\n      data,\n      currentData: currentState.data,\n      isFetching,\n      isLoading,\n      isSuccess\n    } as UseQueryStateDefaultResult<any>);\n  }\n\n  function usePrefetch<EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, defaultOptions?: PrefetchOptions) {\n    const dispatch = useDispatch<ThunkDispatch<any, any, UnknownAction>>();\n    const stableDefaultOptions = useShallowStableValue(defaultOptions);\n    return useCallback((arg: any, options?: PrefetchOptions) => dispatch((api.util.prefetch as GenericPrefetchThunk)(endpointName, arg, { ...stableDefaultOptions,\n      ...options\n    })), [endpointName, dispatch, stableDefaultOptions]);\n  }\n\n  function buildQueryHooks(name: string): QueryHooks<any> {\n    const useQuerySubscription: UseQuerySubscription<any> = (arg: any, {\n      refetchOnReconnect,\n      refetchOnFocus,\n      refetchOnMountOrArgChange,\n      skip = false,\n      pollingInterval = 0,\n      skipPollingIfUnfocused = false\n    } = {}) => {\n      const {\n        initiate\n      } = (api.endpoints[name] as ApiEndpointQuery<QueryDefinition<any, any, any, any, any>, Definitions>);\n      const dispatch = useDispatch<ThunkDispatch<any, any, UnknownAction>>();\n      const subscriptionSelectorsRef = useRef<SubscriptionSelectors>();\n\n      if (!subscriptionSelectorsRef.current) {\n        const returnedValue = dispatch(api.internalActions.internal_getRTKQSubscriptions());\n\n        if (process.env.NODE_ENV !== 'production') {\n          if (typeof returnedValue !== 'object' || typeof returnedValue?.type === 'string') {\n            throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(37) : `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\n    You must add the middleware for RTK-Query to function correctly!`);\n          }\n        }\n\n        subscriptionSelectorsRef.current = ((returnedValue as unknown) as SubscriptionSelectors);\n      }\n\n      const stableArg = useStableQueryArgs(skip ? skipToken : arg, // Even if the user provided a per-endpoint `serializeQueryArgs` with\n      // a consistent return value, _here_ we want to use the default behavior\n      // so we can tell if _anything_ actually changed. Otherwise, we can end up\n      // with a case where the query args did change but the serialization doesn't,\n      // and then we never try to initiate a refetch.\n      defaultSerializeQueryArgs, context.endpointDefinitions[name], name);\n      const stableSubscriptionOptions = useShallowStableValue({\n        refetchOnReconnect,\n        refetchOnFocus,\n        pollingInterval,\n        skipPollingIfUnfocused\n      });\n      const lastRenderHadSubscription = useRef(false);\n      const promiseRef = useRef<QueryActionCreatorResult<any>>();\n      let {\n        queryCacheKey,\n        requestId\n      } = promiseRef.current || {}; // HACK We've saved the middleware subscription lookup callbacks into a ref,\n      // so we can directly check here if the subscription exists for this query.\n\n      let currentRenderHasSubscription = false;\n\n      if (queryCacheKey && requestId) {\n        currentRenderHasSubscription = subscriptionSelectorsRef.current.isRequestSubscribed(queryCacheKey, requestId);\n      }\n\n      const subscriptionRemoved = !currentRenderHasSubscription && lastRenderHadSubscription.current;\n      usePossiblyImmediateEffect(() => {\n        lastRenderHadSubscription.current = currentRenderHasSubscription;\n      });\n      usePossiblyImmediateEffect((): void | undefined => {\n        if (subscriptionRemoved) {\n          promiseRef.current = undefined;\n        }\n      }, [subscriptionRemoved]);\n      usePossiblyImmediateEffect((): void | undefined => {\n        const lastPromise = promiseRef.current;\n\n        if (typeof process !== 'undefined' && process.env.NODE_ENV === 'removeMeOnCompilation') {\n          // this is only present to enforce the rule of hooks to keep `isSubscribed` in the dependency array\n          console.log(subscriptionRemoved);\n        }\n\n        if (stableArg === skipToken) {\n          lastPromise?.unsubscribe();\n          promiseRef.current = undefined;\n          return;\n        }\n\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions;\n\n        if (!lastPromise || lastPromise.arg !== stableArg) {\n          lastPromise?.unsubscribe();\n          const promise = dispatch(initiate(stableArg, {\n            subscriptionOptions: stableSubscriptionOptions,\n            forceRefetch: refetchOnMountOrArgChange\n          }));\n          promiseRef.current = promise;\n        } else if (stableSubscriptionOptions !== lastSubscriptionOptions) {\n          lastPromise.updateSubscriptionOptions(stableSubscriptionOptions);\n        }\n      }, [dispatch, initiate, refetchOnMountOrArgChange, stableArg, stableSubscriptionOptions, subscriptionRemoved]);\n      useEffect(() => {\n        return () => {\n          promiseRef.current?.unsubscribe();\n          promiseRef.current = undefined;\n        };\n      }, []);\n      return useMemo(() => ({\n        /**\n         * A method to manually refetch data for the query\n         */\n        refetch: () => {\n          if (!promiseRef.current) throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage2(38) : 'Cannot refetch a query that has not been started yet.');\n          return promiseRef.current?.refetch();\n        }\n      }), []);\n    };\n\n    const useLazyQuerySubscription: UseLazyQuerySubscription<any> = ({\n      refetchOnReconnect,\n      refetchOnFocus,\n      pollingInterval = 0,\n      skipPollingIfUnfocused = false\n    } = {}) => {\n      const {\n        initiate\n      } = (api.endpoints[name] as ApiEndpointQuery<QueryDefinition<any, any, any, any, any>, Definitions>);\n      const dispatch = useDispatch<ThunkDispatch<any, any, UnknownAction>>();\n      const [arg, setArg] = useState<any>(UNINITIALIZED_VALUE);\n      const promiseRef = useRef<QueryActionCreatorResult<any> | undefined>();\n      const stableSubscriptionOptions = useShallowStableValue({\n        refetchOnReconnect,\n        refetchOnFocus,\n        pollingInterval,\n        skipPollingIfUnfocused\n      });\n      usePossiblyImmediateEffect(() => {\n        const lastSubscriptionOptions = promiseRef.current?.subscriptionOptions;\n\n        if (stableSubscriptionOptions !== lastSubscriptionOptions) {\n          promiseRef.current?.updateSubscriptionOptions(stableSubscriptionOptions);\n        }\n      }, [stableSubscriptionOptions]);\n      const subscriptionOptionsRef = useRef(stableSubscriptionOptions);\n      usePossiblyImmediateEffect(() => {\n        subscriptionOptionsRef.current = stableSubscriptionOptions;\n      }, [stableSubscriptionOptions]);\n      const trigger = useCallback(function (arg: any, preferCacheValue = false) {\n        let promise: QueryActionCreatorResult<any>;\n        batch(() => {\n          promiseRef.current?.unsubscribe();\n          promiseRef.current = promise = dispatch(initiate(arg, {\n            subscriptionOptions: subscriptionOptionsRef.current,\n            forceRefetch: !preferCacheValue\n          }));\n          setArg(arg);\n        });\n        return promise!;\n      }, [dispatch, initiate]);\n      /* cleanup on unmount */\n\n      useEffect(() => {\n        return () => {\n          promiseRef?.current?.unsubscribe();\n        };\n      }, []);\n      /* if \"cleanup on unmount\" was triggered from a fast refresh, we want to reinstate the query */\n\n      useEffect(() => {\n        if (arg !== UNINITIALIZED_VALUE && !promiseRef.current) {\n          trigger(arg, true);\n        }\n      }, [arg, trigger]);\n      return useMemo(() => ([trigger, arg] as const), [trigger, arg]);\n    };\n\n    const useQueryState: UseQueryState<any> = (arg: any, {\n      skip = false,\n      selectFromResult\n    } = {}) => {\n      const {\n        select\n      } = (api.endpoints[name] as ApiEndpointQuery<QueryDefinition<any, any, any, any, any>, Definitions>);\n      const stableArg = useStableQueryArgs(skip ? skipToken : arg, serializeQueryArgs, context.endpointDefinitions[name], name);\n      type ApiRootState = Parameters<ReturnType<typeof select>>[0];\n      const lastValue = useRef<any>();\n      const selectDefaultResult: Selector<ApiRootState, any, [any]> = useMemo(() => createSelector([select(stableArg), (_: ApiRootState, lastResult: any) => lastResult, (_: ApiRootState) => stableArg], queryStatePreSelector, {\n        memoizeOptions: {\n          resultEqualityCheck: shallowEqual\n        }\n      }), [select, stableArg]);\n      const querySelector: Selector<ApiRootState, any, [any]> = useMemo(() => selectFromResult ? createSelector([selectDefaultResult], selectFromResult, {\n        devModeChecks: {\n          identityFunctionCheck: 'never'\n        }\n      }) : selectDefaultResult, [selectDefaultResult, selectFromResult]);\n      const currentState = useSelector((state: RootState<Definitions, any, any>) => querySelector(state, lastValue.current), shallowEqual);\n      const store = useStore<RootState<Definitions, any, any>>();\n      const newLastValue = selectDefaultResult(store.getState(), lastValue.current);\n      useIsomorphicLayoutEffect(() => {\n        lastValue.current = newLastValue;\n      }, [newLastValue]);\n      return currentState;\n    };\n\n    return {\n      useQueryState,\n      useQuerySubscription,\n      useLazyQuerySubscription,\n\n      useLazyQuery(options) {\n        const [trigger, arg] = useLazyQuerySubscription(options);\n        const queryStateResults = useQueryState(arg, { ...options,\n          skip: arg === UNINITIALIZED_VALUE\n        });\n        const info = useMemo(() => ({\n          lastArg: arg\n        }), [arg]);\n        return useMemo(() => [trigger, queryStateResults, info], [trigger, queryStateResults, info]);\n      },\n\n      useQuery(arg, options) {\n        const querySubscriptionResults = useQuerySubscription(arg, options);\n        const queryStateResults = useQueryState(arg, {\n          selectFromResult: arg === skipToken || options?.skip ? undefined : noPendingQueryStateSelector,\n          ...options\n        });\n        const {\n          data,\n          status,\n          isLoading,\n          isSuccess,\n          isError,\n          error\n        } = queryStateResults;\n        useDebugValue({\n          data,\n          status,\n          isLoading,\n          isSuccess,\n          isError,\n          error\n        });\n        return useMemo(() => ({ ...queryStateResults,\n          ...querySubscriptionResults\n        }), [queryStateResults, querySubscriptionResults]);\n      }\n\n    };\n  }\n\n  function buildMutationHook(name: string): UseMutation<any> {\n    return ({\n      selectFromResult,\n      fixedCacheKey\n    } = {}) => {\n      const {\n        select,\n        initiate\n      } = (api.endpoints[name] as ApiEndpointMutation<MutationDefinition<any, any, any, any, any>, Definitions>);\n      const dispatch = useDispatch<ThunkDispatch<any, any, UnknownAction>>();\n      const [promise, setPromise] = useState<MutationActionCreatorResult<any>>();\n      useEffect(() => () => {\n        if (!promise?.arg.fixedCacheKey) {\n          promise?.reset();\n        }\n      }, [promise]);\n      const triggerMutation = useCallback(function (arg: Parameters<typeof initiate>['0']) {\n        const promise = dispatch(initiate(arg, {\n          fixedCacheKey\n        }));\n        setPromise(promise);\n        return promise;\n      }, [dispatch, initiate, fixedCacheKey]);\n      const {\n        requestId\n      } = promise || {};\n      const selectDefaultResult = useMemo(() => select({\n        fixedCacheKey,\n        requestId: promise?.requestId\n      }), [fixedCacheKey, promise, select]);\n      const mutationSelector = useMemo(() => selectFromResult ? createSelector([selectDefaultResult], selectFromResult) : selectDefaultResult, [selectFromResult, selectDefaultResult]);\n      const currentState = useSelector(mutationSelector, shallowEqual);\n      const originalArgs = fixedCacheKey == null ? promise?.arg.originalArgs : undefined;\n      const reset = useCallback(() => {\n        batch(() => {\n          if (promise) {\n            setPromise(undefined);\n          }\n\n          if (fixedCacheKey) {\n            dispatch(api.internalActions.removeMutationResult({\n              requestId,\n              fixedCacheKey\n            }));\n          }\n        });\n      }, [dispatch, fixedCacheKey, promise, requestId]);\n      const {\n        endpointName,\n        data,\n        status,\n        isLoading,\n        isSuccess,\n        isError,\n        error\n      } = currentState;\n      useDebugValue({\n        endpointName,\n        data,\n        status,\n        isLoading,\n        isSuccess,\n        isError,\n        error\n      });\n      const finalState = useMemo(() => ({ ...currentState,\n        originalArgs,\n        reset\n      }), [currentState, originalArgs, reset]);\n      return useMemo(() => ([triggerMutation, finalState] as const), [triggerMutation, finalState]);\n    };\n  }\n}", "import { useEffect, useRef, useMemo } from 'react';\nimport type { SerializeQueryArgs } from '@reduxjs/toolkit/query';\nimport type { EndpointDefinition } from '@reduxjs/toolkit/query';\nexport function useStableQueryArgs<T>(queryArgs: T, serialize: SerializeQueryArgs<any>, endpointDefinition: EndpointDefinition<any, any, any, any>, endpointName: string) {\n  const incoming = useMemo(() => ({\n    queryArgs,\n    serialized: typeof queryArgs == 'object' ? serialize({\n      queryArgs,\n      endpointDefinition,\n      endpointName\n    }) : queryArgs\n  }), [queryArgs, serialize, endpointDefinition, endpointName]);\n  const cache = useRef(incoming);\n  useEffect(() => {\n    if (cache.current.serialized !== incoming.serialized) {\n      cache.current = incoming;\n    }\n  }, [incoming]);\n  return cache.current.serialized === incoming.serialized ? cache.current.queryArgs : queryArgs;\n}", "export const UNINITIALIZED_VALUE = Symbol();\nexport type UninitializedValue = typeof UNINITIALIZED_VALUE;", "import { useEffect, useRef } from 'react';\nimport { shallowEqual } from 'react-redux';\nexport function useShallowStableValue<T>(value: T) {\n  const cache = useRef(value);\n  useEffect(() => {\n    if (!shallowEqual(cache.current, value)) {\n      cache.current = value;\n    }\n  }, [value]);\n  return shallowEqual(cache.current, value) ? cache.current : value;\n}", "// This file exists to consolidate all of the imports from the `@reduxjs/toolkit` package.\n// ESBuild does not de-duplicate imports, so this file is used to ensure that each method\n// imported is only listed once, and there's only one mention of the `@reduxjs/toolkit` package.\nexport { createAction, createSlice, createSelector, createAsyncThunk, combineReducers, createNextState, isAnyOf, isAllOf, isAction, isPending, isRejected, isFulfilled, isRejectedWithValue, isAsyncThunkAction, prepareAutoBatched, SHOULD_AUTOBATCH, isPlainObject, nanoid } from '@reduxjs/toolkit';", "import type { QueryCacheKey } from './core/apiState';\nimport type { EndpointDefinition } from './endpointDefinitions';\nimport { isPlainObject } from './core/rtkImports';\nconst cache: WeakMap<any, string> | undefined = WeakMap ? new WeakMap() : undefined;\nexport const defaultSerializeQueryArgs: SerializeQueryArgs<any> = ({\n  endpointName,\n  queryArgs\n}) => {\n  let serialized = '';\n  const cached = cache?.get(queryArgs);\n\n  if (typeof cached === 'string') {\n    serialized = cached;\n  } else {\n    const stringified = JSON.stringify(queryArgs, (key, value) => isPlainObject(value) ? Object.keys(value).sort().reduce<any>((acc, key) => {\n      acc[key] = (value as any)[key];\n      return acc;\n    }, {}) : value);\n\n    if (isPlainObject(queryArgs)) {\n      cache?.set(queryArgs, stringified);\n    }\n\n    serialized = stringified;\n  } // Sort the object keys before stringifying, to prevent useQuery({ a: 1, b: 2 }) having a different cache key than useQuery({ b: 2, a: 1 })\n\n\n  return `${endpointName}(${serialized})`;\n};\nexport type SerializeQueryArgs<QueryArgs, ReturnType = string> = (_: {\n  queryArgs: QueryArgs;\n  endpointDefinition: EndpointDefinition<any, any, any, any>;\n  endpointName: string;\n}) => ReturnType;\nexport type InternalSerializeQueryArgs = (_: {\n  queryArgs: any;\n  endpointDefinition: EndpointDefinition<any, any, any, any>;\n  endpointName: string;\n}) => QueryCacheKey;", "import type { SerializeQueryArgs } from './defaultSerializeQueryArgs';\nimport type { QuerySubState, RootState } from './core/apiState';\nimport type { BaseQueryExtraOptions, BaseQueryFn, BaseQueryResult, BaseQueryArg, BaseQueryApi, QueryReturnValue, BaseQueryError, BaseQueryMeta } from './baseQueryTypes';\nimport type { HasRequiredProps, MaybePromise, OmitFromUnion, CastAny, NonUndefined, UnwrapPromise } from './tsHelpers';\nimport type { NEVER } from './fakeBaseQuery';\nimport type { Api } from '@reduxjs/toolkit/query';\nconst resultType = /* @__PURE__ */Symbol();\nconst baseQuery = /* @__PURE__ */Symbol();\ninterface EndpointDefinitionWithQuery<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  /**\n   * `query` can be a function that returns either a `string` or an `object` which is passed to your `baseQuery`. If you are using [fetchBaseQuery](./fetchBaseQuery), this can return either a `string` or an `object` of properties in `FetchArgs`. If you use your own custom [`baseQuery`](../../rtk-query/usage/customizing-queries), you can customize this behavior to your liking.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"query example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Post'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       // highlight-start\n   *       query: () => 'posts',\n   *       // highlight-end\n   *     }),\n   *     addPost: build.mutation<Post, Partial<Post>>({\n   *      // highlight-start\n   *      query: (body) => ({\n   *        url: `posts`,\n   *        method: 'POST',\n   *        body,\n   *      }),\n   *      // highlight-end\n   *      invalidatesTags: [{ type: 'Post', id: 'LIST' }],\n   *    }),\n   *   })\n   * })\n   * ```\n   */\n  query(arg: QueryArg): BaseQueryArg<BaseQuery>;\n  queryFn?: never;\n  /**\n   * A function to manipulate the data returned by a query or mutation.\n   */\n\n  transformResponse?(baseQueryReturnValue: BaseQueryResult<BaseQuery>, meta: BaseQueryMeta<BaseQuery>, arg: QueryArg): ResultType | Promise<ResultType>;\n  /**\n   * A function to manipulate the data returned by a failed query or mutation.\n   */\n\n  transformErrorResponse?(baseQueryReturnValue: BaseQueryError<BaseQuery>, meta: BaseQueryMeta<BaseQuery>, arg: QueryArg): unknown;\n  /**\n   * Defaults to `true`.\n   *\n   * Most apps should leave this setting on. The only time it can be a performance issue\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\n   * you're unable to paginate it.\n   *\n   * For details of how this works, please see the below. When it is set to `false`,\n   * every request will cause subscribed components to rerender, even when the data has not changed.\n   *\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\n   */\n\n  structuralSharing?: boolean;\n}\ninterface EndpointDefinitionWithQueryFn<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  /**\n   * Can be used in place of `query` as an inline function that bypasses `baseQuery` completely for the endpoint.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Basic queryFn example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *     }),\n   *     flipCoin: build.query<'heads' | 'tails', void>({\n   *       // highlight-start\n   *       queryFn(arg, queryApi, extraOptions, baseQuery) {\n   *         const randomVal = Math.random()\n   *         if (randomVal < 0.45) {\n   *           return { data: 'heads' }\n   *         }\n   *         if (randomVal < 0.9) {\n   *           return { data: 'tails' }\n   *         }\n   *         return { error: { status: 500, statusText: 'Internal Server Error', data: \"Coin landed on it's edge!\" } }\n   *       }\n   *       // highlight-end\n   *     })\n   *   })\n   * })\n   * ```\n   */\n  queryFn(arg: QueryArg, api: BaseQueryApi, extraOptions: BaseQueryExtraOptions<BaseQuery>, baseQuery: (arg: Parameters<BaseQuery>[0]) => ReturnType<BaseQuery>): MaybePromise<QueryReturnValue<ResultType, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>>;\n  query?: never;\n  transformResponse?: never;\n  transformErrorResponse?: never;\n  /**\n   * Defaults to `true`.\n   *\n   * Most apps should leave this setting on. The only time it can be a performance issue\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\n   * you're unable to paginate it.\n   *\n   * For details of how this works, please see the below. When it is set to `false`,\n   * every request will cause subscribed components to rerender, even when the data has not changed.\n   *\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\n   */\n\n  structuralSharing?: boolean;\n}\nexport interface BaseEndpointTypes<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  QueryArg: QueryArg;\n  BaseQuery: BaseQuery;\n  ResultType: ResultType;\n}\nexport type BaseEndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, ResultType> = (([CastAny<BaseQueryResult<BaseQuery>, {}>] extends [NEVER] ? never : EndpointDefinitionWithQuery<QueryArg, BaseQuery, ResultType>) | EndpointDefinitionWithQueryFn<QueryArg, BaseQuery, ResultType>) & {\n  /* phantom type */\n  [resultType]?: ResultType;\n  /* phantom type */\n\n  [baseQuery]?: BaseQuery;\n} & HasRequiredProps<BaseQueryExtraOptions<BaseQuery>, {\n  extraOptions: BaseQueryExtraOptions<BaseQuery>;\n}, {\n  extraOptions?: BaseQueryExtraOptions<BaseQuery>;\n}>;\nexport enum DefinitionType {\n  query = 'query',\n  mutation = 'mutation',\n}\nexport type GetResultDescriptionFn<TagTypes extends string, ResultType, QueryArg, ErrorType, MetaType> = (result: ResultType | undefined, error: ErrorType | undefined, arg: QueryArg, meta: MetaType) => ReadonlyArray<TagDescription<TagTypes>>;\nexport type FullTagDescription<TagType> = {\n  type: TagType;\n  id?: number | string;\n};\nexport type TagDescription<TagType> = TagType | FullTagDescription<TagType>;\nexport type ResultDescription<TagTypes extends string, ResultType, QueryArg, ErrorType, MetaType> = ReadonlyArray<TagDescription<TagTypes>> | GetResultDescriptionFn<TagTypes, ResultType, QueryArg, ErrorType, MetaType>;\nexport interface QueryTypes<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\n  /**\n   * The endpoint definition type. To be used with some internal generic types.\n   * @example\n   * ```ts\n   * const useMyWrappedHook: UseQuery<typeof api.endpoints.query.Types.QueryDefinition> = ...\n   * ```\n   */\n  QueryDefinition: QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n}\nexport interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n  type: DefinitionType.query;\n  /**\n   * Used by `query` endpoints. Determines which 'tag' is attached to the cached data returned by the query.\n   * Expects an array of tag type strings, an array of objects of tag types with ids, or a function that returns such an array.\n   * 1.  `['Post']` - equivalent to `2`\n   * 2.  `[{ type: 'Post' }]` - equivalent to `1`\n   * 3.  `[{ type: 'Post', id: 1 }]`\n   * 4.  `(result, error, arg) => ['Post']` - equivalent to `5`\n   * 5.  `(result, error, arg) => [{ type: 'Post' }]` - equivalent to `4`\n   * 6.  `(result, error, arg) => [{ type: 'Post', id: 1 }]`\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"providesTags example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Posts'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *       // highlight-start\n   *       providesTags: (result) =>\n   *         result\n   *           ? [\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\n   *               { type: 'Posts', id: 'LIST' },\n   *             ]\n   *           : [{ type: 'Posts', id: 'LIST' }],\n   *       // highlight-end\n   *     })\n   *   })\n   * })\n   * ```\n   */\n\n  providesTags?: ResultDescription<TagTypes, ResultType, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\n   * Not to be used. A query should not invalidate tags in the cache.\n   */\n\n  invalidatesTags?: never;\n  /**\n   * Can be provided to return a custom cache key value based on the query arguments.\n   *\n   * This is primarily intended for cases where a non-serializable value is passed as part of the query arg object and should be excluded from the cache key.  It may also be used for cases where an endpoint should only have a single cache entry, such as an infinite loading / pagination implementation.\n   *\n   * Unlike the `createApi` version which can _only_ return a string, this per-endpoint option can also return an an object, number, or boolean.  If it returns a string, that value will be used as the cache key directly.  If it returns an object / number / boolean, that value will be passed to the built-in `defaultSerializeQueryArgs`.  This simplifies the use case of stripping out args you don't want included in the cache key.\n   *\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"serializeQueryArgs : exclude value\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * interface MyApiClient {\n   *   fetchPost: (id: string) => Promise<Post>\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    // Example: an endpoint with an API client passed in as an argument,\n   *    // but only the item ID should be used as the cache key\n   *    getPost: build.query<Post, { id: string; client: MyApiClient }>({\n   *      queryFn: async ({ id, client }) => {\n   *        const post = await client.fetchPost(id)\n   *        return { data: post }\n   *      },\n   *      // highlight-start\n   *      serializeQueryArgs: ({ queryArgs, endpointDefinition, endpointName }) => {\n   *        const { id } = queryArgs\n   *        // This can return a string, an object, a number, or a boolean.\n   *        // If it returns an object, number or boolean, that value\n   *        // will be serialized automatically via `defaultSerializeQueryArgs`\n   *        return { id } // omit `client` from the cache key\n   *\n   *        // Alternately, you can use `defaultSerializeQueryArgs` yourself:\n   *        // return defaultSerializeQueryArgs({\n   *        //   endpointName,\n   *        //   queryArgs: { id },\n   *        //   endpointDefinition\n   *        // })\n   *        // Or  create and return a string yourself:\n   *        // return `getPost(${id})`\n   *      },\n   *      // highlight-end\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n\n  serializeQueryArgs?: SerializeQueryArgs<QueryArg, string | number | boolean | Record<any, any>>;\n  /**\n   * Can be provided to merge an incoming response value into the current cache data.\n   * If supplied, no automatic structural sharing will be applied - it's up to\n   * you to update the cache appropriately.\n   *\n   * Since RTKQ normally replaces cache entries with the new response, you will usually\n   * need to use this with the `serializeQueryArgs` or `forceRefetch` options to keep\n   * an existing cache entry so that it can be updated.\n   *\n   * Since this is wrapped with Immer, you may either mutate the `currentCacheValue` directly,\n   * or return a new value, but _not_ both at once.\n   *\n   * Will only be called if the existing `currentCacheData` is _not_ `undefined` - on first response,\n   * the cache entry will just save the response data directly.\n   *\n   * Useful if you don't want a new request to completely override the current cache value,\n   * maybe because you have manually updated it from another source and don't want those\n   * updates to get lost.\n   *\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"merge: pagination\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    listItems: build.query<string[], number>({\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\n   *     // Only have one cache entry because the arg always maps to one string\n   *     serializeQueryArgs: ({ endpointName }) => {\n   *       return endpointName\n   *      },\n   *      // Always merge incoming data to the cache entry\n   *      merge: (currentCache, newItems) => {\n   *        currentCache.push(...newItems)\n   *      },\n   *      // Refetch when the page arg changes\n   *      forceRefetch({ currentArg, previousArg }) {\n   *        return currentArg !== previousArg\n   *      },\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n\n  merge?(currentCacheData: ResultType, responseData: ResultType, otherArgs: {\n    arg: QueryArg;\n    baseQueryMeta: BaseQueryMeta<BaseQuery>;\n    requestId: string;\n    fulfilledTimeStamp: number;\n  }): ResultType | void;\n  /**\n   * Check to see if the endpoint should force a refetch in cases where it normally wouldn't.\n   * This is primarily useful for \"infinite scroll\" / pagination use cases where\n   * RTKQ is keeping a single cache entry that is added to over time, in combination\n   * with `serializeQueryArgs` returning a fixed cache key and a `merge` callback\n   * set to add incoming data to the cache entry each time.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"forceRefresh: pagination\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    listItems: build.query<string[], number>({\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\n   *     // Only have one cache entry because the arg always maps to one string\n   *     serializeQueryArgs: ({ endpointName }) => {\n   *       return endpointName\n   *      },\n   *      // Always merge incoming data to the cache entry\n   *      merge: (currentCache, newItems) => {\n   *        currentCache.push(...newItems)\n   *      },\n   *      // Refetch when the page arg changes\n   *      forceRefetch({ currentArg, previousArg }) {\n   *        return currentArg !== previousArg\n   *      },\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n\n  forceRefetch?(params: {\n    currentArg: QueryArg | undefined;\n    previousArg: QueryArg | undefined;\n    state: RootState<any, any, string>;\n    endpointState?: QuerySubState<any>;\n  }): boolean;\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n\n  Types?: QueryTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type QueryDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> & QueryExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>;\nexport interface MutationTypes<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\n  /**\n   * The endpoint definition type. To be used with some internal generic types.\n   * @example\n   * ```ts\n   * const useMyWrappedHook: UseMutation<typeof api.endpoints.query.Types.MutationDefinition> = ...\n   * ```\n   */\n  MutationDefinition: MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n}\nexport interface MutationExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n  type: DefinitionType.mutation;\n  /**\n   * Used by `mutation` endpoints. Determines which cached data should be either re-fetched or removed from the cache.\n   * Expects the same shapes as `providesTags`.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"invalidatesTags example\"\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Posts'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *       providesTags: (result) =>\n   *         result\n   *           ? [\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\n   *               { type: 'Posts', id: 'LIST' },\n   *             ]\n   *           : [{ type: 'Posts', id: 'LIST' }],\n   *     }),\n   *     addPost: build.mutation<Post, Partial<Post>>({\n   *       query(body) {\n   *         return {\n   *           url: `posts`,\n   *           method: 'POST',\n   *           body,\n   *         }\n   *       },\n   *       // highlight-start\n   *       invalidatesTags: [{ type: 'Posts', id: 'LIST' }],\n   *       // highlight-end\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n\n  invalidatesTags?: ResultDescription<TagTypes, ResultType, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\n   * Not to be used. A mutation should not provide tags to the cache.\n   */\n\n  providesTags?: never;\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n\n  Types?: MutationTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type MutationDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> & MutationExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>;\nexport type EndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath> | MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\nexport type EndpointDefinitions = Record<string, EndpointDefinition<any, any, any, any>>;\nexport function isQueryDefinition(e: EndpointDefinition<any, any, any, any>): e is QueryDefinition<any, any, any, any> {\n  return e.type === DefinitionType.query;\n}\nexport function isMutationDefinition(e: EndpointDefinition<any, any, any, any>): e is MutationDefinition<any, any, any, any> {\n  return e.type === DefinitionType.mutation;\n}\nexport type EndpointBuilder<BaseQuery extends BaseQueryFn, TagTypes extends string, ReducerPath extends string> = {\n  /**\n   * An endpoint definition that retrieves data, and may provide tags to the cache.\n   *\n   * @example\n   * ```js\n   * // codeblock-meta title=\"Example of all query endpoint options\"\n   * const api = createApi({\n   *  baseQuery,\n   *  endpoints: (build) => ({\n   *    getPost: build.query({\n   *      query: (id) => ({ url: `post/${id}` }),\n   *      // Pick out data and prevent nested properties in a hook or selector\n   *      transformResponse: (response) => response.data,\n   *      // Pick out error and prevent nested properties in a hook or selector\n   *      transformErrorResponse: (response) => response.error,\n   *      // `result` is the server response\n   *      providesTags: (result, error, id) => [{ type: 'Post', id }],\n   *      // trigger side effects or optimistic updates\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry, updateCachedData }) {},\n   *      // handle subscriptions etc\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry, updateCachedData }) {},\n   *    }),\n   *  }),\n   *});\n   *```\n   */\n  query<ResultType, QueryArg>(definition: OmitFromUnion<QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>, 'type'>): QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  /**\n   * An endpoint definition that alters data on the server or will possibly invalidate the cache.\n   *\n   * @example\n   * ```js\n   * // codeblock-meta title=\"Example of all mutation endpoint options\"\n   * const api = createApi({\n   *   baseQuery,\n   *   endpoints: (build) => ({\n   *     updatePost: build.mutation({\n   *       query: ({ id, ...patch }) => ({ url: `post/${id}`, method: 'PATCH', body: patch }),\n   *       // Pick out data and prevent nested properties in a hook or selector\n   *       transformResponse: (response) => response.data,\n   *       // Pick out error and prevent nested properties in a hook or selector\n   *       transformErrorResponse: (response) => response.error,\n   *       // `result` is the server response\n   *       invalidatesTags: (result, error, id) => [{ type: 'Post', id }],\n   *      // trigger side effects or optimistic updates\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry }) {},\n   *      // handle subscriptions etc\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry }) {},\n   *     }),\n   *   }),\n   * });\n   * ```\n   */\n\n  mutation<ResultType, QueryArg>(definition: OmitFromUnion<MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>, 'type'>): MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n};\nexport type AssertTagTypes = <T extends FullTagDescription<string>>(t: T) => T;\nexport function calculateProvidedBy<ResultType, QueryArg, ErrorType, MetaType>(description: ResultDescription<string, ResultType, QueryArg, ErrorType, MetaType> | undefined, result: ResultType | undefined, error: ErrorType | undefined, queryArg: QueryArg, meta: MetaType | undefined, assertTagTypes: AssertTagTypes): readonly FullTagDescription<string>[] {\n  if (isFunction(description)) {\n    return description((result as ResultType), (error as undefined), queryArg, (meta as MetaType)).map(expandTagDescription).map(assertTagTypes);\n  }\n\n  if (Array.isArray(description)) {\n    return description.map(expandTagDescription).map(assertTagTypes);\n  }\n\n  return [];\n}\n\nfunction isFunction<T>(t: T): t is Extract<T, Function> {\n  return typeof t === 'function';\n}\n\nexport function expandTagDescription(description: TagDescription<string>): FullTagDescription<string> {\n  return typeof description === 'string' ? {\n    type: description\n  } : description;\n}\nexport type QueryArgFrom<D extends BaseEndpointDefinition<any, any, any>> = D extends BaseEndpointDefinition<infer QA, any, any> ? QA : unknown;\nexport type ResultTypeFrom<D extends BaseEndpointDefinition<any, any, any>> = D extends BaseEndpointDefinition<any, any, infer RT> ? RT : unknown;\nexport type ReducerPathFrom<D extends EndpointDefinition<any, any, any, any, any>> = D extends EndpointDefinition<any, any, any, any, infer RP> ? RP : unknown;\nexport type TagTypesFrom<D extends EndpointDefinition<any, any, any, any>> = D extends EndpointDefinition<any, any, infer RP, any> ? RP : unknown;\nexport type TagTypesFromApi<T> = T extends Api<any, any, any, infer TagTypes> ? TagTypes : never;\nexport type DefinitionsFromApi<T> = T extends Api<any, infer Definitions, any, any> ? Definitions : never;\nexport type TransformedResponse<NewDefinitions extends EndpointDefinitions, K, ResultType> = K extends keyof NewDefinitions ? NewDefinitions[K]['transformResponse'] extends undefined ? ResultType : UnwrapPromise<ReturnType<NonUndefined<NewDefinitions[K]['transformResponse']>>> : ResultType;\nexport type OverrideResultType<Definition, NewResultType> = Definition extends QueryDefinition<infer QueryArg, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? QueryDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath> : Definition extends MutationDefinition<infer QueryArg, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? MutationDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath> : never;\nexport type UpdateDefinitions<Definitions extends EndpointDefinitions, NewTagTypes extends string, NewDefinitions extends EndpointDefinitions> = { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<infer QueryArg, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? QueryDefinition<QueryArg, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : Definitions[K] extends MutationDefinition<infer QueryArg, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? MutationDefinition<QueryArg, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : never };", "export function capitalize(str: string) {\n  return str.replace(str[0], str[0].toUpperCase());\n}", "export type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type Override<T1, T2> = T2 extends any ? Omit<T1, keyof T2> & T2 : never;\nexport function assertCast<T>(v: any): asserts v is T {}\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>): T {\n  return Object.assign(target, ...args);\n}\n/**\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\n */\n\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;\nexport type NonOptionalKeys<T> = { [K in keyof T]-?: undefined extends T[K] ? never : K }[keyof T];\nexport type HasRequiredProps<T, True, False> = NonOptionalKeys<T> extends never ? False : True;\nexport type OptionalIfAllPropsOptional<T> = HasRequiredProps<T, T, T | never>;\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type UnwrapPromise<T> = T extends PromiseLike<infer V> ? V : T;\nexport type MaybePromise<T> = T | PromiseLike<T>;\nexport type OmitFromUnion<T, K extends keyof T> = T extends any ? Omit<T, K> : never;\nexport type IsAny<T, True, False = never> = true | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;", "// Fast method for counting an object's keys\n// without resorting to `Object.keys(obj).length\n// Will this make a big difference in perf? Probably not\n// But we can save a few allocations.\nexport function countObjectKeys(obj: Record<any, any>) {\n  let count = 0;\n\n  for (const _key in obj) {\n    count++;\n  }\n\n  return count;\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport { configureStore } from '@reduxjs/toolkit';\nimport type { Context } from 'react';\nimport { useContext } from 'react';\nimport { useEffect } from 'react';\nimport React from 'react';\nimport type { ReactReduxContextValue } from 'react-redux';\nimport { Provider, ReactReduxContext } from 'react-redux';\nimport { setupListeners } from '@reduxjs/toolkit/query';\nimport type { Api } from '@reduxjs/toolkit/query';\n/**\n * Can be used as a `Provider` if you **do not already have a Redux store**.\n *\n * @example\n * ```tsx\n * // codeblock-meta no-transpile title=\"Basic usage - wrap your App with ApiProvider\"\n * import * as React from 'react';\n * import { ApiProvider } from '@reduxjs/toolkit/query/react';\n * import { Pokemon } from './features/Pokemon';\n *\n * function App() {\n *   return (\n *     <ApiProvider api={api}>\n *       <Pokemon />\n *     </ApiProvider>\n *   );\n * }\n * ```\n *\n * @remarks\n * Using this together with an existing redux store, both will\n * conflict with each other - please use the traditional redux setup\n * in that case.\n */\n\nexport function ApiProvider<A extends Api<any, {}, any, any>>(props: {\n  children: any;\n  api: A;\n  setupListeners?: Parameters<typeof setupListeners>[1] | false;\n  context?: Context<ReactReduxContextValue>;\n}) {\n  const context = props.context || ReactReduxContext;\n  const existingContext = useContext(context);\n\n  if (existingContext) {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(35) : 'Existing Redux context detected. If you already have a store set up, please use the traditional Redux setup.');\n  }\n\n  const [store] = React.useState(() => configureStore({\n    reducer: {\n      [props.api.reducerPath]: props.api.reducer\n    },\n    middleware: gDM => gDM().concat(props.api.middleware)\n  })); // Adds the event listeners for online/offline/focus/etc\n\n  useEffect((): undefined | (() => void) => props.setupListeners === false ? undefined : setupListeners(store.dispatch, props.setupListeners), [props.setupListeners, store.dispatch]);\n  return <Provider store={store} context={context}>\n      {props.children}\n    </Provider>;\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AEIA,mBAAkG;ACJlG,IAAAA,gBAA2C;AEA3C,IAAAA,gBAAkC;AOGlC,IAAAC,gBAA2B;AAC3B,IAAAA,gBAA0B;AAC1B,IAAAA,gBAAkB;ATFX,SAAS,mBAAsB,WAAc,WAAoC,oBAA4D,cAAsB;AACxK,QAAM,eAAW,uBAAQ,OAAO;IAC9B;IACA,YAAY,OAAO,aAAa,WAAW,UAAU;MACnD;MACA;MACA;IACF,CAAC,IAAI;EACP,IAAI,CAAC,WAAW,WAAW,oBAAoB,YAAY,CAAC;AAC5D,QAAMC,aAAQ,sBAAO,QAAQ;AAC7B,+BAAU,MAAM;AACd,QAAIA,OAAM,QAAQ,eAAe,SAAS,YAAY;AACpDA,aAAM,UAAU;IAClB;EACF,GAAG,CAAC,QAAQ,CAAC;AACb,SAAOA,OAAM,QAAQ,eAAe,SAAS,aAAaA,OAAM,QAAQ,YAAY;AACtF;ACnBO,IAAM,sBAAsB,OAAO;ACEnC,SAAS,sBAAyB,OAAU;AACjD,QAAMA,aAAQC,cAAAA,QAAO,KAAK;AAC1BC,oBAAAA,WAAU,MAAM;AACd,QAAI,CAAC,aAAaF,OAAM,SAAS,KAAK,GAAG;AACvCA,aAAM,UAAU;IAClB;EACF,GAAG,CAAC,KAAK,CAAC;AACV,SAAO,aAAaA,OAAM,SAAS,KAAK,IAAIA,OAAM,UAAU;AAC9D;AEPA,IAAM,QAA0C,UAAU,oBAAI,QAAQ,IAAI;AACnE,IAAMG,6BAAqD,CAAC;EACjE;EACA;AACF,MAAM;AACJ,MAAI,aAAa;AACjB,QAAM,SAAS,+BAAO,IAAI;AAE1B,MAAI,OAAO,WAAW,UAAU;AAC9B,iBAAa;EACf,OAAO;AACL,UAAM,cAAc,KAAK,UAAU,WAAW,CAAC,KAAK,UAAU,cAAc,KAAK,IAAI,OAAO,KAAK,KAAK,EAAE,KAAK,EAAE,OAAY,CAAC,KAAKC,SAAQ;AACvI,UAAIA,IAAG,IAAK,MAAcA,IAAG;AAC7B,aAAO;IACT,GAAG,CAAC,CAAC,IAAI,KAAK;AAEd,QAAI,cAAc,SAAS,GAAG;AAC5B,qCAAO,IAAI,WAAW;IACxB;AAEA,iBAAa;EACf;AAGA,SAAO,GAAG,YAAY,IAAI,UAAU;AACtC;ALVO,IAAM,4BAA4B,OAAO,WAAW,eAAe,CAAC,CAAC,OAAO,YAAY,CAAC,CAAC,OAAO,SAAS,gBAAgB,+BAAkBF,aAAAA;AA4WnJ,IAAM,8BAA4D,CAAA,aAAY;AAC5E,MAAI,SAAS,iBAAiB;AAC5B,WAAQ;MAAE,GAAG;MACX,iBAAiB;MACjB,YAAY;MACZ,WAAW,SAAS,SAAS,SAAY,QAAQ;MACjD,QAAQ,YAAY;IACtB;EACF;AAEA,SAAO;AACT;AAYO,SAAS,WAAoD;EAClE;EACA,eAAe;IACb,OAAAG;IACA,OAAO;MACL,aAAAC;MACA,aAAAC;MACA,UAAAC;IACF;IACA;IACA,gBAAAC;EACF;EACA;EACA;AACF,GAKG;AACD,QAAM,6BAA8F,gCAAgC,CAAA,OAAM,GAAG,IAAIP,aAAAA;AACjJ,SAAO;IACL;IACA;IACA;EACF;AAEA,WAAS,sBAAsB,cAA8C,YAAyD,WAAiD;AAIrL,SAAI,yCAAY,iBAAgB,aAAa,iBAAiB;AAC5D,YAAM;QACJ;MACF,IAAI;AACJ,YAAM,qBAAqB,QAAQ,oBAAoB,YAAY;AACnE,UAAI,mBAAmB;QACrB,WAAW,WAAW;QACtB;QACA;MACF,CAAC,MAAM,mBAAmB;QACxB;QACA;QACA;MACF,CAAC;AAAG,qBAAa;IACnB;AAGA,QAAI,OAAO,aAAa,YAAY,aAAa,OAAO,yCAAY;AACpE,QAAI,SAAS;AAAW,aAAO,aAAa;AAC5C,UAAM,UAAU,SAAS;AAEzB,UAAM,aAAa,aAAa;AAEhC,UAAM,YAAY,CAAC,WAAW;AAE9B,UAAM,YAAY,aAAa,aAAa,cAAc;AAC1D,WAAQ;MAAE,GAAG;MACX;MACA,aAAa,aAAa;MAC1B;MACA;MACA;IACF;EACF;AAEA,WAAS,YAAyD,cAA4B,gBAAkC;AAC9H,UAAM,WAAWI,aAAoD;AACrE,UAAM,uBAAuB,sBAAsB,cAAc;AACjE,eAAO,0BAAY,CAAC,KAAU,YAA8B,SAAU,IAAI,KAAK,SAAkC,cAAc,KAAK;MAAE,GAAG;MACvI,GAAG;IACL,CAAC,CAAC,GAAG,CAAC,cAAc,UAAU,oBAAoB,CAAC;EACrD;AAEA,WAAS,gBAAgB,MAA+B;AACtD,UAAM,uBAAkD,CAAC,KAAU;MACjE;MACA;MACA;MACA,OAAO;MACP,kBAAkB;MAClB,yBAAyB;IAC3B,IAAI,CAAC,MAAM;AACT,YAAM;QACJ;MACF,IAAK,IAAI,UAAU,IAAI;AACvB,YAAM,WAAWA,aAAoD;AACrE,YAAM,+BAA2BL,aAAAA,QAA8B;AAE/D,UAAI,CAAC,yBAAyB,SAAS;AACrC,cAAM,gBAAgB,SAAS,IAAI,gBAAgB,8BAA8B,CAAC;AAElF,YAAI,MAAuC;AACzC,cAAI,OAAO,kBAAkB,YAAY,QAAO,+CAAe,UAAS,UAAU;AAChF,kBAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,yDAAyD,IAAI,WAAW;qEACrG;UAC3D;QACF;AAEA,iCAAyB,UAAY;MACvC;AAEA,YAAM,YAAY;QAAmB,OAAO,YAAY;;;;;;QAKxDE;QAA2B,QAAQ,oBAAoB,IAAI;QAAG;MAAI;AAClE,YAAM,4BAA4B,sBAAsB;QACtD;QACA;QACA;QACA;MACF,CAAC;AACD,YAAM,gCAA4BF,aAAAA,QAAO,KAAK;AAC9C,YAAM,iBAAaA,aAAAA,QAAsC;AACzD,UAAI;QACF;QACA;MACF,IAAI,WAAW,WAAW,CAAC;AAG3B,UAAI,+BAA+B;AAEnC,UAAI,iBAAiB,WAAW;AAC9B,uCAA+B,yBAAyB,QAAQ,oBAAoB,eAAe,SAAS;MAC9G;AAEA,YAAM,sBAAsB,CAAC,gCAAgC,0BAA0B;AACvF,iCAA2B,MAAM;AAC/B,kCAA0B,UAAU;MACtC,CAAC;AACD,iCAA2B,MAAwB;AACjD,YAAI,qBAAqB;AACvB,qBAAW,UAAU;QACvB;MACF,GAAG,CAAC,mBAAmB,CAAC;AACxB,iCAA2B,MAAwB;;AACjD,cAAM,cAAc,WAAW;AAE/B,YAAI,OAAO,YAAY,eAAe,OAAkD;AAEtF,kBAAQ,IAAI,mBAAmB;QACjC;AAEA,YAAI,cAAc,WAAW;AAC3B,qDAAa;AACb,qBAAW,UAAU;AACrB;QACF;AAEA,cAAM,2BAA0B,gBAAW,YAAX,mBAAoB;AAEpD,YAAI,CAAC,eAAe,YAAY,QAAQ,WAAW;AACjD,qDAAa;AACb,gBAAM,UAAU,SAAS,SAAS,WAAW;YAC3C,qBAAqB;YACrB,cAAc;UAChB,CAAC,CAAC;AACF,qBAAW,UAAU;QACvB,WAAW,8BAA8B,yBAAyB;AAChE,sBAAY,0BAA0B,yBAAyB;QACjE;MACF,GAAG,CAAC,UAAU,UAAU,2BAA2B,WAAW,2BAA2B,mBAAmB,CAAC;AAC7GC,uBAAAA,WAAU,MAAM;AACd,eAAO,MAAM;;AACX,2BAAW,YAAX,mBAAoB;AACpB,qBAAW,UAAU;QACvB;MACF,GAAG,CAAC,CAAC;AACL,iBAAOQ,aAAAA,SAAQ,OAAO;;;;QAIpB,SAAS,MAAM;;AACb,cAAI,CAAC,WAAW;AAAS,kBAAM,IAAI,MAAM,QAAwC,uBAAyB,EAAE,IAAI,uDAAuD;AACvK,kBAAO,gBAAW,YAAX,mBAAoB;QAC7B;MACF,IAAI,CAAC,CAAC;IACR;AAEA,UAAM,2BAA0D,CAAC;MAC/D;MACA;MACA,kBAAkB;MAClB,yBAAyB;IAC3B,IAAI,CAAC,MAAM;AACT,YAAM;QACJ;MACF,IAAK,IAAI,UAAU,IAAI;AACvB,YAAM,WAAWJ,aAAoD;AACrE,YAAM,CAAC,KAAK,MAAM,QAAI,uBAAc,mBAAmB;AACvD,YAAM,iBAAaL,aAAAA,QAAkD;AACrE,YAAM,4BAA4B,sBAAsB;QACtD;QACA;QACA;QACA;MACF,CAAC;AACD,iCAA2B,MAAM;;AAC/B,cAAM,2BAA0B,gBAAW,YAAX,mBAAoB;AAEpD,YAAI,8BAA8B,yBAAyB;AACzD,2BAAW,YAAX,mBAAoB,0BAA0B;QAChD;MACF,GAAG,CAAC,yBAAyB,CAAC;AAC9B,YAAM,6BAAyBA,aAAAA,QAAO,yBAAyB;AAC/D,iCAA2B,MAAM;AAC/B,+BAAuB,UAAU;MACnC,GAAG,CAAC,yBAAyB,CAAC;AAC9B,YAAM,cAAU,0BAAY,SAAUU,MAAU,mBAAmB,OAAO;AACxE,YAAI;AACJ,QAAAN,OAAM,MAAM;;AACV,2BAAW,YAAX,mBAAoB;AACpB,qBAAW,UAAU,UAAU,SAAS,SAASM,MAAK;YACpD,qBAAqB,uBAAuB;YAC5C,cAAc,CAAC;UACjB,CAAC,CAAC;AACF,iBAAOA,IAAG;QACZ,CAAC;AACD,eAAO;MACT,GAAG,CAAC,UAAU,QAAQ,CAAC;AAGvBT,uBAAAA,WAAU,MAAM;AACd,eAAO,MAAM;;AACX,yDAAY,YAAZ,mBAAqB;QACvB;MACF,GAAG,CAAC,CAAC;AAGLA,uBAAAA,WAAU,MAAM;AACd,YAAI,QAAQ,uBAAuB,CAAC,WAAW,SAAS;AACtD,kBAAQ,KAAK,IAAI;QACnB;MACF,GAAG,CAAC,KAAK,OAAO,CAAC;AACjB,iBAAOQ,aAAAA,SAAQ,MAAO,CAAC,SAAS,GAAG,GAAa,CAAC,SAAS,GAAG,CAAC;IAChE;AAEA,UAAM,gBAAoC,CAAC,KAAU;MACnD,OAAO;MACP;IACF,IAAI,CAAC,MAAM;AACT,YAAM;QACJ;MACF,IAAK,IAAI,UAAU,IAAI;AACvB,YAAM,YAAY,mBAAmB,OAAO,YAAY,KAAK,oBAAoB,QAAQ,oBAAoB,IAAI,GAAG,IAAI;AAExH,YAAM,gBAAYT,aAAAA,QAAY;AAC9B,YAAM,0BAA0DS,aAAAA,SAAQ,MAAMD,gBAAe,CAAC,OAAO,SAAS,GAAG,CAAC,GAAiB,eAAoB,YAAY,CAAC,MAAoB,SAAS,GAAG,uBAAuB;QACzN,gBAAgB;UACd,qBAAqBG;QACvB;MACF,CAAC,GAAG,CAAC,QAAQ,SAAS,CAAC;AACvB,YAAM,oBAAoDF,aAAAA,SAAQ,MAAM,mBAAmBD,gBAAe,CAAC,mBAAmB,GAAG,kBAAkB;QACjJ,eAAe;UACb,uBAAuB;QACzB;MACF,CAAC,IAAI,qBAAqB,CAAC,qBAAqB,gBAAgB,CAAC;AACjE,YAAM,eAAeF,aAAY,CAAC,UAA4C,cAAc,OAAO,UAAU,OAAO,GAAGK,YAAY;AACnI,YAAM,QAAQJ,UAA2C;AACzD,YAAM,eAAe,oBAAoB,MAAM,SAAS,GAAG,UAAU,OAAO;AAC5E,gCAA0B,MAAM;AAC9B,kBAAU,UAAU;MACtB,GAAG,CAAC,YAAY,CAAC;AACjB,aAAO;IACT;AAEA,WAAO;MACL;MACA;MACA;MAEA,aAAa,SAAS;AACpB,cAAM,CAAC,SAAS,GAAG,IAAI,yBAAyB,OAAO;AACvD,cAAM,oBAAoB,cAAc,KAAK;UAAE,GAAG;UAChD,MAAM,QAAQ;QAChB,CAAC;AACD,cAAM,WAAOE,aAAAA,SAAQ,OAAO;UAC1B,SAAS;QACX,IAAI,CAAC,GAAG,CAAC;AACT,mBAAOA,aAAAA,SAAQ,MAAM,CAAC,SAAS,mBAAmB,IAAI,GAAG,CAAC,SAAS,mBAAmB,IAAI,CAAC;MAC7F;MAEA,SAAS,KAAK,SAAS;AACrB,cAAM,2BAA2B,qBAAqB,KAAK,OAAO;AAClE,cAAM,oBAAoB,cAAc,KAAK;UAC3C,kBAAkB,QAAQ,cAAa,mCAAS,QAAO,SAAY;UACnE,GAAG;QACL,CAAC;AACD,cAAM;UACJ;UACA;UACA;UACA;UACA;UACA;QACF,IAAI;AACJ,wCAAc;UACZ;UACA;UACA;UACA;UACA;UACA;QACF,CAAC;AACD,mBAAOA,aAAAA,SAAQ,OAAO;UAAE,GAAG;UACzB,GAAG;QACL,IAAI,CAAC,mBAAmB,wBAAwB,CAAC;MACnD;IAEF;EACF;AAEA,WAAS,kBAAkB,MAAgC;AACzD,WAAO,CAAC;MACN;MACA;IACF,IAAI,CAAC,MAAM;AACT,YAAM;QACJ;QACA;MACF,IAAK,IAAI,UAAU,IAAI;AACvB,YAAM,WAAWJ,aAAoD;AACrE,YAAM,CAAC,SAAS,UAAU,QAAI,uBAA2C;AACzEJ,uBAAAA,WAAU,MAAM,MAAM;AACpB,YAAI,EAAC,mCAAS,IAAI,gBAAe;AAC/B,6CAAS;QACX;MACF,GAAG,CAAC,OAAO,CAAC;AACZ,YAAM,sBAAkB,0BAAY,SAAU,KAAuC;AACnF,cAAMW,WAAU,SAAS,SAAS,KAAK;UACrC;QACF,CAAC,CAAC;AACF,mBAAWA,QAAO;AAClB,eAAOA;MACT,GAAG,CAAC,UAAU,UAAU,aAAa,CAAC;AACtC,YAAM;QACJ;MACF,IAAI,WAAW,CAAC;AAChB,YAAM,0BAAsBH,aAAAA,SAAQ,MAAM,OAAO;QAC/C;QACA,WAAW,mCAAS;MACtB,CAAC,GAAG,CAAC,eAAe,SAAS,MAAM,CAAC;AACpC,YAAM,uBAAmBA,aAAAA,SAAQ,MAAM,mBAAmBD,gBAAe,CAAC,mBAAmB,GAAG,gBAAgB,IAAI,qBAAqB,CAAC,kBAAkB,mBAAmB,CAAC;AAChL,YAAM,eAAeF,aAAY,kBAAkBK,YAAY;AAC/D,YAAM,eAAe,iBAAiB,OAAO,mCAAS,IAAI,eAAe;AACzE,YAAM,YAAQ,0BAAY,MAAM;AAC9B,QAAAP,OAAM,MAAM;AACV,cAAI,SAAS;AACX,uBAAW,MAAS;UACtB;AAEA,cAAI,eAAe;AACjB,qBAAS,IAAI,gBAAgB,qBAAqB;cAChD;cACA;YACF,CAAC,CAAC;UACJ;QACF,CAAC;MACH,GAAG,CAAC,UAAU,eAAe,SAAS,SAAS,CAAC;AAChD,YAAM;QACJ;QACA;QACA;QACA;QACA;QACA;QACA;MACF,IAAI;AACJ,sCAAc;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;MACF,CAAC;AACD,YAAM,iBAAaK,aAAAA,SAAQ,OAAO;QAAE,GAAG;QACrC;QACA;MACF,IAAI,CAAC,cAAc,cAAc,KAAK,CAAC;AACvC,iBAAOA,aAAAA,SAAQ,MAAO,CAAC,iBAAiB,UAAU,GAAa,CAAC,iBAAiB,UAAU,CAAC;IAC9F;EACF;AACF;AMlUO,SAAS,kBAAkB,GAAqF;AACrH,SAAO,EAAE,SAAS;AACpB;AACO,SAAS,qBAAqB,GAAwF;AAC3H,SAAO,EAAE,SAAS;AACpB;AC1dO,SAAS,WAAW,KAAa;AACtC,SAAO,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,YAAY,CAAC;AACjD;ACEO,SAAS,WAA6B,WAAc,MAAqC;AAC9F,SAAO,OAAO,OAAO,QAAQ,GAAG,IAAI;AACtC;ACFO,SAAS,gBAAgB,KAAuB;AACrD,MAAI,QAAQ;AAEZ,aAAW,QAAQ,KAAK;AACtB;EACF;AAEA,SAAO;AACT;AVGO,IAAM,uBAAsC,OAAO;AA6FnD,IAAM,mBAAmB,CAAC;EAC/B,OAAAL,SAAQ;EACR,QAAQ;IACN;IACA;IACA;EACF;EACA,gBAAAI,kBAAiB;EACjB,gCAAgC;EAChC,GAAG;AACL,IAA6B,CAAC,MAAgC;AAC5D,MAAI,MAAuC;AACzC,UAAM,YAAa,CAAC,eAAe,eAAe,UAAU;AAC5D,QAAI,SAAS;AAEb,eAAW,YAAY,WAAW;AAEhC,UAAI,gBAAgB,IAAI,IAAI,GAAG;AAC7B,YAAK,KAA+B,QAAQ,GAAG;AAC7C,cAAI,CAAC,QAAQ;AACX,oBAAQ,KAAK,uKAA4K;AACzL,qBAAS;UACX;QACF;AAIA,cAAM,QAAQ,IAAI,KAAK,QAAQ;MACjC;AAGA,UAAI,OAAO,MAAM,QAAQ,MAAM,YAAY;AACzC,cAAM,IAAI,MAAM,QAAwCK,uBAAwB,EAAE,IAAI,4CAA4C,UAAU,MAAM,+BAA+B,UAAU,KAAK,IAAI,CAAC;OAAW,QAAQ,6CAA6C;MACvQ;IACF;EACF;AAEA,SAAO;IACL,MAAM;IAEN,KAAK,KAAK;MACR;IACF,GAAG,SAAS;AACV,YAAM,SAAW;AACjB,YAAM;QACJ;QACA;QACA;MACF,IAAI,WAAW;QACb;QACA,eAAe;UACb,OAAAT;UACA;UACA;UACA,gBAAAI;QACF;QACA;QACA;MACF,CAAC;AACD,iBAAW,QAAQ;QACjB;MACF,CAAC;AACD,iBAAW,SAAS;QAClB,OAAAJ;MACF,CAAC;AACD,aAAO;QACL,eAAe,cAAc,YAAY;AACvC,cAAI,kBAAkB,UAAU,GAAG;AACjC,kBAAM;cACJ;cACA;cACA;cACA;cACA;YACF,IAAI,gBAAgB,YAAY;AAChC,uBAAW,OAAO,UAAU,YAAY,GAAG;cACzC;cACA;cACA;cACA;cACA;YACF,CAAC;AACA,gBAAY,MAAM,WAAW,YAAY,CAAC,OAAO,IAAI;AACrD,gBAAY,UAAU,WAAW,YAAY,CAAC,OAAO,IAAI;UAC5D,WAAW,qBAAqB,UAAU,GAAG;AAC3C,kBAAM,cAAc,kBAAkB,YAAY;AAClD,uBAAW,OAAO,UAAU,YAAY,GAAG;cACzC;YACF,CAAC;AACA,gBAAY,MAAM,WAAW,YAAY,CAAC,UAAU,IAAI;UAC3D;QACF;MAEF;IACF;EAEF;AACF;AW1KO,SAAS,YAA8C,OAK3D;AACD,QAAM,UAAU,MAAM,WAAW;AACjC,QAAM,sBAAkB,0BAAW,OAAO;AAE1C,MAAI,iBAAiB;AACnB,UAAM,IAAI,MAAM,QAAwCS,uBAAwB,EAAE,IAAI,8GAA8G;EACtM;AAEA,QAAM,CAAC,KAAK,IAAI,cAAAC,QAAM,SAAS,MAAM,eAAe;IAClD,SAAS;MACP,CAAC,MAAM,IAAI,WAAW,GAAG,MAAM,IAAI;IACrC;IACA,YAAY,CAAA,QAAO,IAAI,EAAE,OAAO,MAAM,IAAI,UAAU;EACtD,CAAC,CAAC;AAEFb,oBAAAA,WAAU,MAAgC,MAAM,mBAAmB,QAAQ,SAAY,eAAe,MAAM,UAAU,MAAM,cAAc,GAAG,CAAC,MAAM,gBAAgB,MAAM,QAAQ,CAAC;AACnL,SAAO,cAAAa,QAAA,cAAC,kBAAA,EAAS,OAAc,QAAA,GAC1B,MAAM,QACT;AACJ;AZpDA,IAAM,YAA2B,eAAe,WAAW,GAAG,iBAAiB,CAAC;", "names": ["import_react", "import_react", "cache", "useRef", "useEffect", "defaultSerializeQueryArgs", "key", "batch", "useDispatch", "useSelector", "useStore", "createSelector", "useMemo", "arg", "shallowEqual", "promise", "_formatProdErrorMessage", "React"]}