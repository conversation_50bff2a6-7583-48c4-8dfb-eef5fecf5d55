import React from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '../../store';

export const TerrainEditor: React.FC = () => {
  const { currentMap } = useSelector((state: RootState) => state.editor);

  if (!currentMap) return null;

  return (
    <div className="terrain-editor">
      <div className="terrain-editor__header">
        <h3>Terrain Sculpting</h3>
        <p>Modify the terrain height and shape to create natural landscapes.</p>
      </div>

      <div className="terrain-tools">
        <h4>Terrain Tools</h4>
        <div className="tool-grid">
          <button className="terrain-tool-btn">
            <span className="tool-icon">⬆️</span>
            <span className="tool-name">Raise</span>
          </button>
          <button className="terrain-tool-btn">
            <span className="tool-icon">⬇️</span>
            <span className="tool-name">Lower</span>
          </button>
          <button className="terrain-tool-btn">
            <span className="tool-icon">〰️</span>
            <span className="tool-name">Smooth</span>
          </button>
          <button className="terrain-tool-btn">
            <span className="tool-icon">▬</span>
            <span className="tool-name">Flatten</span>
          </button>
        </div>
      </div>

      <div className="terrain-settings">
        <h4>Brush Settings</h4>
        <div className="setting-group">
          <label>Brush Size:</label>
          <input type="range" min="1" max="10" defaultValue="3" />
        </div>
        <div className="setting-group">
          <label>Strength:</label>
          <input type="range" min="0.1" max="2" step="0.1" defaultValue="1" />
        </div>
        <div className="setting-group">
          <label>Falloff:</label>
          <select defaultValue="smooth">
            <option value="linear">Linear</option>
            <option value="smooth">Smooth</option>
            <option value="sharp">Sharp</option>
          </select>
        </div>
      </div>

      <div className="terrain-presets">
        <h4>Terrain Presets</h4>
        <div className="preset-grid">
          <button className="preset-btn">🏜️ Flat Desert</button>
          <button className="preset-btn">🏔️ Rolling Hills</button>
          <button className="preset-btn">🌋 Crater</button>
          <button className="preset-btn">🏞️ Valley</button>
        </div>
      </div>
    </div>
  );
};
