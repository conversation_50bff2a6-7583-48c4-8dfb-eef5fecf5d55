.game-ui-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  pointer-events: none;
  z-index: 100;

  * {
    pointer-events: auto;
  }

  .top-bar {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    gap: 30px;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px 30px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    color: white;

    .game-title h1 {
      margin: 0;
      font-size: 1.5em;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    }

    .turn-info {
      display: flex;
      align-items: center;
      gap: 15px;
      font-size: 1.1em;

      .current-turn {
        color: #f39c12;
      }

      .dice-result {
        background: #e74c3c;
        padding: 5px 10px;
        border-radius: 8px;
        font-weight: bold;
      }
    }
  }

  .left-sidebar {
    position: absolute;
    top: 120px;
    left: 20px;
    width: 280px;
    max-height: calc(100vh - 140px);
    overflow-y: auto;

    .panel {
      background: rgba(0, 0, 0, 0.8);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      backdrop-filter: blur(10px);
      color: white;

      h3 {
        margin: 0 0 15px 0;
        color: #f39c12;
        font-size: 1.2em;
      }
    }

    .players-list {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .player-card {
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid transparent;
        border-radius: 10px;
        padding: 15px;
        transition: all 0.3s ease;

        &.current-player {
          border-color: #f39c12;
          box-shadow: 0 0 15px rgba(243, 156, 18, 0.3);
        }

        .player-name {
          font-weight: bold;
          font-size: 1.1em;
          margin-bottom: 10px;
          color: #fff;
        }

        .player-stats {
          display: flex;
          flex-direction: column;
          gap: 5px;

          .stat {
            display: flex;
            align-items: center;
            gap: 8px;

            .stat-icon {
              font-size: 1.2em;
            }

            .stat-value {
              font-weight: bold;
              color: #ecf0f1;
            }
          }
        }

        .player-status {
          margin-top: 10px;
          padding: 5px 10px;
          background: #e74c3c;
          color: white;
          border-radius: 5px;
          text-align: center;
          font-size: 0.9em;
        }
      }
    }
  }

  .right-sidebar {
    position: absolute;
    top: 120px;
    right: 20px;
    width: 280px;

    .panel {
      background: rgba(0, 0, 0, 0.8);
      border-radius: 15px;
      padding: 20px;
      margin-bottom: 20px;
      backdrop-filter: blur(10px);
      color: white;

      h3 {
        margin: 0 0 15px 0;
        color: #f39c12;
        font-size: 1.2em;
      }
    }

    .controls {
      display: flex;
      flex-direction: column;
      gap: 10px;

      button {
        padding: 12px;
        border: none;
        border-radius: 8px;
        font-size: 1.1em;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;

        &.roll-dice-btn {
          background: linear-gradient(135deg, #28a745, #20c997);
          color: white;

          &:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
          }
        }

        &.open-chest-btn {
          background: linear-gradient(135deg, #FFD700, #FFA500);
          color: #333;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
          }
        }

        &:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }
      }
    }

    .waiting {
      text-align: center;
      padding: 20px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 8px;
      color: #ecf0f1;
      font-style: italic;
    }

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 10px;

      .action-btn {
        padding: 10px;
        border: none;
        border-radius: 8px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-1px);
        }
      }
    }
  }

  // Modal styles
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);

    .modal-content {
      background: rgba(0, 0, 0, 0.9);
      border-radius: 15px;
      max-width: 500px;
      max-height: 70vh;
      overflow-y: auto;
      color: white;
      backdrop-filter: blur(10px);

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);

        h3 {
          margin: 0;
          color: #f39c12;
        }

        button {
          background: none;
          border: none;
          color: white;
          font-size: 1.5em;
          cursor: pointer;
          padding: 5px;
          border-radius: 5px;
          transition: background 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }
        }
      }

      .modal-body {
        padding: 20px;

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            padding: 8px 0;
            font-size: 1em;
            color: #ecf0f1;
          }
        }

        .log-entries {
          max-height: 300px;
          overflow-y: auto;

          .log-entry {
            display: flex;
            gap: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);

            &:last-child {
              border-bottom: none;
            }

            .log-time {
              color: #95a5a6;
              font-size: 0.9em;
              min-width: 80px;
            }

            .log-details {
              color: #ecf0f1;
              font-size: 0.95em;
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .game-ui-overlay {
    .top-bar {
      flex-direction: column;
      gap: 10px;
      padding: 10px 20px;

      .game-title h1 {
        font-size: 1.2em;
      }

      .turn-info {
        font-size: 1em;
      }
    }

    .left-sidebar,
    .right-sidebar {
      width: calc(100vw - 40px);
      position: relative;
      top: auto;
      left: 20px;
      right: 20px;
    }

    .left-sidebar {
      top: 140px;
    }

    .right-sidebar {
      top: auto;
    }
  }
}
