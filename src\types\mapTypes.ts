import { TileType } from './gameTypes';
import { MapConfiguration, CustomTile, TerrainConfiguration, EnvironmentConfiguration, LightingConfiguration } from './editorTypes';

export interface MapTemplate {
  id: string;
  name: string;
  description: string;
  thumbnail: string;
  difficulty: 'easy' | 'medium' | 'hard';
  playerCount: [number, number]; // [min, max]
  estimatedPlayTime: number; // in minutes
  tags: string[];
  configuration: MapConfiguration;
}

export interface MapPreset {
  id: string;
  name: string;
  description: string;
  category: 'desert' | 'forest' | 'mountain' | 'ocean' | 'space' | 'custom';
  terrain: Partial<TerrainConfiguration>;
  environment: Partial<EnvironmentConfiguration>;
  lighting: Partial<LightingConfiguration>;
  defaultAssets: string[]; // Asset IDs that come with this preset
}

export interface MapValidationRules {
  minTiles: number;
  maxTiles: number;
  requiredTileTypes: TileType[];
  maxConsecutiveTileType: Record<TileType, number>;
  treasureChestRequired: boolean;
  minDistanceBetweenSpecialTiles: number;
  maxTerrainHeight: number;
  minTerrainHeight: number;
  maxObjectsPerTile: number;
  performanceLimits: {
    maxTriangles: number;
    maxTextures: number;
    maxLights: number;
    maxParticles: number;
  };
}

export interface MapExportFormat {
  boardgameIo: {
    gameState: any;
    moves: any;
    setup: any;
  };
  json: MapConfiguration;
  gltf: {
    scene: any;
    metadata: any;
  };
}

export interface MapImportResult {
  success: boolean;
  configuration?: MapConfiguration;
  errors: string[];
  warnings: string[];
  missingAssets: string[];
}

export interface TileLayoutPattern {
  id: string;
  name: string;
  description: string;
  pattern: 'sequential' | 'spiral' | 'grid' | 'random' | 'custom';
  parameters: {
    baseRadius?: number;
    radiusVariation?: number;
    spacing?: number;
    customPositions?: Array<[number, number, number]>;
  };
}

export interface EnvironmentPreset {
  id: string;
  name: string;
  description: string;
  theme: 'desert' | 'oasis' | 'canyon' | 'dunes' | 'rocky' | 'lush';
  objects: {
    rocks: {
      density: number;
      sizeVariation: [number, number];
      types: string[];
    };
    vegetation: {
      density: number;
      types: string[];
      distribution: 'random' | 'clustered' | 'sparse';
    };
    structures: {
      density: number;
      types: string[];
      placement: 'random' | 'strategic' | 'decorative';
    };
  };
  materials: {
    ground: {
      color: string;
      texture?: string;
      normalMap?: string;
      roughness: number;
    };
    accent: {
      color: string;
      texture?: string;
    };
  };
}

export interface GameplayFeature {
  id: string;
  name: string;
  description: string;
  type: 'tile_effect' | 'environmental_hazard' | 'bonus_area' | 'shortcut' | 'teleporter';
  configuration: Record<string, any>;
  requiredAssets: string[];
  gameLogicHooks: {
    onPlayerEnter?: string;
    onPlayerExit?: string;
    onTurnStart?: string;
    onTurnEnd?: string;
  };
}

export interface MapStatistics {
  totalTiles: number;
  tileTypeDistribution: Record<TileType, number>;
  averageTileSpacing: number;
  pathLength: number;
  estimatedPlayTime: number;
  difficultyScore: number;
  performanceMetrics: {
    triangleCount: number;
    textureMemory: number;
    lightCount: number;
    particleCount: number;
    estimatedFPS: number;
  };
}

export interface MapThumbnail {
  id: string;
  mapId: string;
  size: 'small' | 'medium' | 'large';
  url: string;
  generated: number;
  cameraPosition: [number, number, number];
  cameraTarget: [number, number, number];
}

export interface MapCollection {
  id: string;
  name: string;
  description: string;
  author: string;
  maps: string[]; // Map IDs
  tags: string[];
  created: number;
  modified: number;
  isPublic: boolean;
}

export interface MapSearchFilters {
  category?: string[];
  difficulty?: string[];
  playerCount?: [number, number];
  playTime?: [number, number];
  tags?: string[];
  author?: string;
  dateRange?: [number, number];
  sortBy?: 'name' | 'created' | 'modified' | 'popularity' | 'rating';
  sortOrder?: 'asc' | 'desc';
}

export interface MapRating {
  mapId: string;
  userId: string;
  rating: number; // 1-5 stars
  review?: string;
  created: number;
}

export interface MapUsageStatistics {
  mapId: string;
  timesPlayed: number;
  averageGameDuration: number;
  playerFeedback: {
    averageRating: number;
    totalRatings: number;
    commonComplaints: string[];
    commonPraises: string[];
  };
  performanceData: {
    averageFPS: number;
    loadTime: number;
    memoryUsage: number;
  };
}
