{"version": 3, "sources": ["../../@reduxjs/toolkit/src/query/core/apiState.ts", "../../@reduxjs/toolkit/src/query/utils/isAbsoluteUrl.ts", "../../@reduxjs/toolkit/src/query/utils/joinUrls.ts", "../../@reduxjs/toolkit/src/query/utils/flatten.ts", "../../@reduxjs/toolkit/src/query/utils/isOnline.ts", "../../@reduxjs/toolkit/src/query/utils/isDocumentVisible.ts", "../../@reduxjs/toolkit/src/query/core/rtkImports.ts", "../../@reduxjs/toolkit/src/query/utils/copyWithStructuralSharing.ts", "../../@reduxjs/toolkit/src/query/fetchBaseQuery.ts", "../../@reduxjs/toolkit/src/query/HandledError.ts", "../../@reduxjs/toolkit/src/query/retry.ts", "../../@reduxjs/toolkit/src/query/core/setupListeners.ts", "../../@reduxjs/toolkit/src/query/endpointDefinitions.ts", "../../@reduxjs/toolkit/src/query/core/buildInitiate.ts", "../../@reduxjs/toolkit/src/query/utils/isNotNullish.ts", "../../@reduxjs/toolkit/src/query/utils/countObjectKeys.ts", "../../@reduxjs/toolkit/src/tsHelpers.ts", "../../@reduxjs/toolkit/src/query/core/buildThunks.ts", "../../@reduxjs/toolkit/src/query/core/buildSlice.ts", "../../@reduxjs/toolkit/src/query/core/buildSelectors.ts", "../../@reduxjs/toolkit/src/query/defaultSerializeQueryArgs.ts", "../../@reduxjs/toolkit/src/query/createApi.ts", "../../@reduxjs/toolkit/src/query/fakeBaseQuery.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/cacheCollection.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/invalidationByTags.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/polling.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/windowEventHandling.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/cacheLifecycle.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/queryLifecycle.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/devMiddleware.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/batchActions.ts", "../../@reduxjs/toolkit/src/query/core/buildMiddleware/index.ts", "../../@reduxjs/toolkit/src/query/tsHelpers.ts", "../../@reduxjs/toolkit/src/query/core/module.ts", "../../@reduxjs/toolkit/src/query/core/index.ts"], "sourcesContent": ["import type { SerializedError } from '@reduxjs/toolkit';\nimport type { BaseQueryError } from '../baseQueryTypes';\nimport type { QueryDefinition, MutationDefinition, EndpointDefinitions, BaseEndpointDefinition, ResultTypeFrom, QueryArgFrom } from '../endpointDefinitions';\nimport type { Id, WithRequiredProp } from '../tsHelpers';\nexport type QueryCacheKey = string & {\n  _type: 'queryCacheKey';\n};\nexport type QuerySubstateIdentifier = {\n  queryCacheKey: QueryCacheKey;\n};\nexport type MutationSubstateIdentifier = {\n  requestId: string;\n  fixedCacheKey?: string;\n} | {\n  requestId?: string;\n  fixedCacheKey: string;\n};\nexport type RefetchConfigOptions = {\n  refetchOnMountOrArgChange: boolean | number;\n  refetchOnReconnect: boolean;\n  refetchOnFocus: boolean;\n};\n/**\n * Strings describing the query state at any given time.\n */\n\nexport enum QueryStatus {\n  uninitialized = 'uninitialized',\n  pending = 'pending',\n  fulfilled = 'fulfilled',\n  rejected = 'rejected',\n}\nexport type RequestStatusFlags = {\n  status: QueryStatus.uninitialized;\n  isUninitialized: true;\n  isLoading: false;\n  isSuccess: false;\n  isError: false;\n} | {\n  status: QueryStatus.pending;\n  isUninitialized: false;\n  isLoading: true;\n  isSuccess: false;\n  isError: false;\n} | {\n  status: QueryStatus.fulfilled;\n  isUninitialized: false;\n  isLoading: false;\n  isSuccess: true;\n  isError: false;\n} | {\n  status: QueryStatus.rejected;\n  isUninitialized: false;\n  isLoading: false;\n  isSuccess: false;\n  isError: true;\n};\nexport function getRequestStatusFlags(status: QueryStatus): RequestStatusFlags {\n  return ({\n    status,\n    isUninitialized: status === QueryStatus.uninitialized,\n    isLoading: status === QueryStatus.pending,\n    isSuccess: status === QueryStatus.fulfilled,\n    isError: status === QueryStatus.rejected\n  } as any);\n}\nexport type SubscriptionOptions = {\n  /**\n   * How frequently to automatically re-fetch data (in milliseconds). Defaults to `0` (off).\n   */\n  pollingInterval?: number;\n  /**\n   *  Defaults to 'false'. This setting allows you to control whether RTK Query will continue polling if the window is not focused.\n   *\n   *  If pollingInterval is not set or set to 0, this **will not be evaluated** until pollingInterval is greater than 0.\n   *\n   *  Note: requires [`setupListeners`](./setupListeners) to have been called.\n   */\n\n  skipPollingIfUnfocused?: boolean;\n  /**\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after regaining a network connection.\n   *\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\n   *\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\n   */\n\n  refetchOnReconnect?: boolean;\n  /**\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after the application window regains focus.\n   *\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\n   *\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\n   */\n\n  refetchOnFocus?: boolean;\n};\nexport type Subscribers = {\n  [requestId: string]: SubscriptionOptions;\n};\nexport type QueryKeys<Definitions extends EndpointDefinitions> = { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<any, any, any, any> ? K : never }[keyof Definitions];\nexport type MutationKeys<Definitions extends EndpointDefinitions> = { [K in keyof Definitions]: Definitions[K] extends MutationDefinition<any, any, any, any> ? K : never }[keyof Definitions];\ntype BaseQuerySubState<D extends BaseEndpointDefinition<any, any, any>> = {\n  /**\n   * The argument originally passed into the hook or `initiate` action call\n   */\n  originalArgs: QueryArgFrom<D>;\n  /**\n   * A unique ID associated with the request\n   */\n\n  requestId: string;\n  /**\n   * The received data from the query\n   */\n\n  data?: ResultTypeFrom<D>;\n  /**\n   * The received error if applicable\n   */\n\n  error?: SerializedError | (D extends QueryDefinition<any, infer BaseQuery, any, any> ? BaseQueryError<BaseQuery> : never);\n  /**\n   * The name of the endpoint associated with the query\n   */\n\n  endpointName: string;\n  /**\n   * Time that the latest query started\n   */\n\n  startedTimeStamp: number;\n  /**\n   * Time that the latest query was fulfilled\n   */\n\n  fulfilledTimeStamp?: number;\n};\nexport type QuerySubState<D extends BaseEndpointDefinition<any, any, any>> = Id<({\n  status: QueryStatus.fulfilled;\n} & WithRequiredProp<BaseQuerySubState<D>, 'data' | 'fulfilledTimeStamp'> & {\n  error: undefined;\n}) | ({\n  status: QueryStatus.pending;\n} & BaseQuerySubState<D>) | ({\n  status: QueryStatus.rejected;\n} & WithRequiredProp<BaseQuerySubState<D>, 'error'>) | {\n  status: QueryStatus.uninitialized;\n  originalArgs?: undefined;\n  data?: undefined;\n  error?: undefined;\n  requestId?: undefined;\n  endpointName?: string;\n  startedTimeStamp?: undefined;\n  fulfilledTimeStamp?: undefined;\n}>;\ntype BaseMutationSubState<D extends BaseEndpointDefinition<any, any, any>> = {\n  requestId: string;\n  data?: ResultTypeFrom<D>;\n  error?: SerializedError | (D extends MutationDefinition<any, infer BaseQuery, any, any> ? BaseQueryError<BaseQuery> : never);\n  endpointName: string;\n  startedTimeStamp: number;\n  fulfilledTimeStamp?: number;\n};\nexport type MutationSubState<D extends BaseEndpointDefinition<any, any, any>> = (({\n  status: QueryStatus.fulfilled;\n} & WithRequiredProp<BaseMutationSubState<D>, 'data' | 'fulfilledTimeStamp'>) & {\n  error: undefined;\n}) | (({\n  status: QueryStatus.pending;\n} & BaseMutationSubState<D>) & {\n  data?: undefined;\n}) | ({\n  status: QueryStatus.rejected;\n} & WithRequiredProp<BaseMutationSubState<D>, 'error'>) | {\n  requestId?: undefined;\n  status: QueryStatus.uninitialized;\n  data?: undefined;\n  error?: undefined;\n  endpointName?: string;\n  startedTimeStamp?: undefined;\n  fulfilledTimeStamp?: undefined;\n};\nexport type CombinedState<D extends EndpointDefinitions, E extends string, ReducerPath extends string> = {\n  queries: QueryState<D>;\n  mutations: MutationState<D>;\n  provided: InvalidationState<E>;\n  subscriptions: SubscriptionState;\n  config: ConfigState<ReducerPath>;\n};\nexport type InvalidationState<TagTypes extends string> = { [_ in TagTypes]: {\n  [id: string]: Array<QueryCacheKey>;\n  [id: number]: Array<QueryCacheKey>;\n} };\nexport type QueryState<D extends EndpointDefinitions> = {\n  [queryCacheKey: string]: QuerySubState<D[string]> | undefined;\n};\nexport type SubscriptionState = {\n  [queryCacheKey: string]: Subscribers | undefined;\n};\nexport type ConfigState<ReducerPath> = RefetchConfigOptions & {\n  reducerPath: ReducerPath;\n  online: boolean;\n  focused: boolean;\n  middlewareRegistered: boolean | 'conflict';\n} & ModifiableConfigState;\nexport type ModifiableConfigState = {\n  keepUnusedDataFor: number;\n  invalidationBehavior: 'delayed' | 'immediately';\n} & RefetchConfigOptions;\nexport type MutationState<D extends EndpointDefinitions> = {\n  [requestId: string]: MutationSubState<D[string]> | undefined;\n};\nexport type RootState<Definitions extends EndpointDefinitions, TagTypes extends string, ReducerPath extends string> = { [P in ReducerPath]: CombinedState<Definitions, TagTypes, P> };", "/**\n * If either :// or // is present consider it to be an absolute url\n *\n * @param url string\n */\nexport function isAbsoluteUrl(url: string) {\n  return new RegExp(`(^|:)//`).test(url);\n}", "import { isAbsoluteUrl } from './isAbsoluteUrl';\n\nconst withoutTrailingSlash = (url: string) => url.replace(/\\/$/, '');\n\nconst withoutLeadingSlash = (url: string) => url.replace(/^\\//, '');\n\nexport function joinUrls(base: string | undefined, url: string | undefined): string {\n  if (!base) {\n    return url!;\n  }\n\n  if (!url) {\n    return base;\n  }\n\n  if (isAbsoluteUrl(url)) {\n    return url;\n  }\n\n  const delimiter = base.endsWith('/') || !url.startsWith('?') ? '/' : '';\n  base = withoutTrailingSlash(base);\n  url = withoutLeadingSlash(url);\n  return `${base}${delimiter}${url}`;\n}", "/**\n * Alternative to `Array.flat(1)`\n * @param arr An array like [1,2,3,[1,2]]\n * @link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/flat\n */\nexport const flatten = (arr: readonly any[]) => [].concat(...arr);", "/**\n * Assumes a browser is online if `undefined`, otherwise makes a best effort\n * @link https://developer.mozilla.org/en-US/docs/Web/API/NavigatorOnLine/onLine\n */\nexport function isOnline() {\n  // We set the default config value in the store, so we'd need to check for this in a SSR env\n  return typeof navigator === 'undefined' ? true : navigator.onLine === undefined ? true : navigator.onLine;\n}", "/**\n * Assumes true for a non-browser env, otherwise makes a best effort\n * @link https://developer.mozilla.org/en-US/docs/Web/API/Document/visibilityState\n */\nexport function isDocumentVisible(): boolean {\n  // `document` may not exist in non-browser envs (like RN)\n  if (typeof document === 'undefined') {\n    return true;\n  } // Match true for visible, prerender, undefined\n\n\n  return document.visibilityState !== 'hidden';\n}", "// This file exists to consolidate all of the imports from the `@reduxjs/toolkit` package.\n// ESBuild does not de-duplicate imports, so this file is used to ensure that each method\n// imported is only listed once, and there's only one mention of the `@reduxjs/toolkit` package.\nexport { createAction, createSlice, createSelector, createAsyncThunk, combineReducers, createNextState, isAnyOf, isAllOf, isAction, isPending, isRejected, isFulfilled, isRejectedWithValue, isAsyncThunkAction, prepareAutoBatched, SHOULD_AUTOBATCH, isPlainObject, nanoid } from '@reduxjs/toolkit';", "import { isPlainObject as _iPO } from '../core/rtkImports'; // remove type guard\n\nconst isPlainObject: (_: any) => boolean = _iPO;\nexport function copyWithStructuralSharing<T>(oldObj: any, newObj: T): T;\nexport function copyWithStructuralSharing(oldObj: any, newObj: any): any {\n  if (oldObj === newObj || !(isPlainObject(oldObj) && isPlainObject(newObj) || Array.isArray(oldObj) && Array.isArray(newObj))) {\n    return newObj;\n  }\n\n  const newKeys = Object.keys(newObj);\n  const oldKeys = Object.keys(oldObj);\n  let isSameObject = newKeys.length === oldKeys.length;\n  const mergeObj: any = Array.isArray(newObj) ? [] : {};\n\n  for (const key of newKeys) {\n    mergeObj[key] = copyWithStructuralSharing(oldObj[key], newObj[key]);\n    if (isSameObject) isSameObject = oldObj[key] === mergeObj[key];\n  }\n\n  return isSameObject ? oldObj : mergeObj;\n}", "import { joinUrls } from './utils';\nimport { isPlainObject } from './core/rtkImports';\nimport type { BaseQueryApi, BaseQueryFn } from './baseQueryTypes';\nimport type { MaybePromise, Override } from './tsHelpers';\nexport type ResponseHandler = 'content-type' | 'json' | 'text' | ((response: Response) => Promise<any>);\ntype CustomRequestInit = Override<RequestInit, {\n  headers?: Headers | string[][] | Record<string, string | undefined> | undefined;\n}>;\nexport interface FetchArgs extends CustomRequestInit {\n  url: string;\n  params?: Record<string, any>;\n  body?: any;\n  responseHandler?: ResponseHandler;\n  validateStatus?: (response: Response, body: any) => boolean;\n  /**\n   * A number in milliseconds that represents that maximum time a request can take before timing out.\n   */\n\n  timeout?: number;\n}\n/**\n * A mini-wrapper that passes arguments straight through to\n * {@link [fetch](https://developer.mozilla.org/en-US/docs/Web/API/Fetch_API)}.\n * Avoids storing `fetch` in a closure, in order to permit mocking/monkey-patching.\n */\n\nconst defaultFetchFn: typeof fetch = (...args) => fetch(...args);\n\nconst defaultValidateStatus = (response: Response) => response.status >= 200 && response.status <= 299;\n\nconst defaultIsJsonContentType = (headers: Headers) =>\n/*applicat*/\n/ion\\/(vnd\\.api\\+)?json/.test(headers.get('content-type') || '');\n\nexport type FetchBaseQueryError = {\n  /**\n   * * `number`:\n   *   HTTP status code\n   */\n  status: number;\n  data: unknown;\n} | {\n  /**\n   * * `\"FETCH_ERROR\"`:\n   *   An error that occurred during execution of `fetch` or the `fetchFn` callback option\n   **/\n  status: 'FETCH_ERROR';\n  data?: undefined;\n  error: string;\n} | {\n  /**\n   * * `\"PARSING_ERROR\"`:\n   *   An error happened during parsing.\n   *   Most likely a non-JSON-response was returned with the default `responseHandler` \"JSON\",\n   *   or an error occurred while executing a custom `responseHandler`.\n   **/\n  status: 'PARSING_ERROR';\n  originalStatus: number;\n  data: string;\n  error: string;\n} | {\n  /**\n   * * `\"TIMEOUT_ERROR\"`:\n   *   Request timed out\n   **/\n  status: 'TIMEOUT_ERROR';\n  data?: undefined;\n  error: string;\n} | {\n  /**\n   * * `\"CUSTOM_ERROR\"`:\n   *   A custom error type that you can return from your `queryFn` where another error might not make sense.\n   **/\n  status: 'CUSTOM_ERROR';\n  data?: unknown;\n  error: string;\n};\n\nfunction stripUndefined(obj: any) {\n  if (!isPlainObject(obj)) {\n    return obj;\n  }\n\n  const copy: Record<string, any> = { ...obj\n  };\n\n  for (const [k, v] of Object.entries(copy)) {\n    if (v === undefined) delete copy[k];\n  }\n\n  return copy;\n}\n\nexport type FetchBaseQueryArgs = {\n  baseUrl?: string;\n  prepareHeaders?: (headers: Headers, api: Pick<BaseQueryApi, 'getState' | 'extra' | 'endpoint' | 'type' | 'forced'>) => MaybePromise<Headers | void>;\n  fetchFn?: (input: RequestInfo, init?: RequestInit | undefined) => Promise<Response>;\n  paramsSerializer?: (params: Record<string, any>) => string;\n  /**\n   * By default, we only check for 'application/json' and 'application/vnd.api+json' as the content-types for json. If you need to support another format, you can pass\n   * in a predicate function for your given api to get the same automatic stringifying behavior\n   * @example\n   * ```ts\n   * const isJsonContentType = (headers: Headers) => [\"application/vnd.api+json\", \"application/json\", \"application/vnd.hal+json\"].includes(headers.get(\"content-type\")?.trim());\n   * ```\n   */\n\n  isJsonContentType?: (headers: Headers) => boolean;\n  /**\n   * Defaults to `application/json`;\n   */\n\n  jsonContentType?: string;\n  /**\n   * Custom replacer function used when calling `JSON.stringify()`;\n   */\n\n  jsonReplacer?: (this: any, key: string, value: any) => any;\n} & RequestInit & Pick<FetchArgs, 'responseHandler' | 'validateStatus' | 'timeout'>;\nexport type FetchBaseQueryMeta = {\n  request: Request;\n  response?: Response;\n};\n/**\n * This is a very small wrapper around fetch that aims to simplify requests.\n *\n * @example\n * ```ts\n * const baseQuery = fetchBaseQuery({\n *   baseUrl: 'https://api.your-really-great-app.com/v1/',\n *   prepareHeaders: (headers, { getState }) => {\n *     const token = (getState() as RootState).auth.token;\n *     // If we have a token set in state, let's assume that we should be passing it.\n *     if (token) {\n *       headers.set('authorization', `Bearer ${token}`);\n *     }\n *     return headers;\n *   },\n * })\n * ```\n *\n * @param {string} baseUrl\n * The base URL for an API service.\n * Typically in the format of https://example.com/\n *\n * @param {(headers: Headers, api: { getState: () => unknown; extra: unknown; endpoint: string; type: 'query' | 'mutation'; forced: boolean; }) => Headers} prepareHeaders\n * An optional function that can be used to inject headers on requests.\n * Provides a Headers object, as well as most of the `BaseQueryApi` (`dispatch` is not available).\n * Useful for setting authentication or headers that need to be set conditionally.\n *\n * @link https://developer.mozilla.org/en-US/docs/Web/API/Headers\n *\n * @param {(input: RequestInfo, init?: RequestInit | undefined) => Promise<Response>} fetchFn\n * Accepts a custom `fetch` function if you do not want to use the default on the window.\n * Useful in SSR environments if you need to use a library such as `isomorphic-fetch` or `cross-fetch`\n *\n * @param {(params: Record<string, unknown>) => string} paramsSerializer\n * An optional function that can be used to stringify querystring parameters.\n *\n * @param {(headers: Headers) => boolean} isJsonContentType\n * An optional predicate function to determine if `JSON.stringify()` should be called on the `body` arg of `FetchArgs`\n *\n * @param {string} jsonContentType Used when automatically setting the content-type header for a request with a jsonifiable body that does not have an explicit content-type header. Defaults to `application/json`.\n *\n * @param {(this: any, key: string, value: any) => any} jsonReplacer Custom replacer function used when calling `JSON.stringify()`.\n *\n * @param {number} timeout\n * A number in milliseconds that represents the maximum time a request can take before timing out.\n */\n\nexport function fetchBaseQuery({\n  baseUrl,\n  prepareHeaders = x => x,\n  fetchFn = defaultFetchFn,\n  paramsSerializer,\n  isJsonContentType = defaultIsJsonContentType,\n  jsonContentType = 'application/json',\n  jsonReplacer,\n  timeout: defaultTimeout,\n  responseHandler: globalResponseHandler,\n  validateStatus: globalValidateStatus,\n  ...baseFetchOptions\n}: FetchBaseQueryArgs = {}): BaseQueryFn<string | FetchArgs, unknown, FetchBaseQueryError, {}, FetchBaseQueryMeta> {\n  if (typeof fetch === 'undefined' && fetchFn === defaultFetchFn) {\n    console.warn('Warning: `fetch` is not available. Please supply a custom `fetchFn` property to use `fetchBaseQuery` on SSR environments.');\n  }\n\n  return async (arg, api) => {\n    const {\n      signal,\n      getState,\n      extra,\n      endpoint,\n      forced,\n      type\n    } = api;\n    let meta: FetchBaseQueryMeta | undefined;\n    let {\n      url,\n      headers = new Headers(baseFetchOptions.headers),\n      params = undefined,\n      responseHandler = globalResponseHandler ?? ('json' as const),\n      validateStatus = globalValidateStatus ?? defaultValidateStatus,\n      timeout = defaultTimeout,\n      ...rest\n    } = typeof arg == 'string' ? {\n      url: arg\n    } : arg;\n    let config: RequestInit = { ...baseFetchOptions,\n      signal,\n      ...rest\n    };\n    headers = new Headers(stripUndefined(headers));\n    config.headers = (await prepareHeaders(headers, {\n      getState,\n      extra,\n      endpoint,\n      forced,\n      type\n    })) || headers; // Only set the content-type to json if appropriate. Will not be true for FormData, ArrayBuffer, Blob, etc.\n\n    const isJsonifiable = (body: any) => typeof body === 'object' && (isPlainObject(body) || Array.isArray(body) || typeof body.toJSON === 'function');\n\n    if (!config.headers.has('content-type') && isJsonifiable(config.body)) {\n      config.headers.set('content-type', jsonContentType);\n    }\n\n    if (isJsonifiable(config.body) && isJsonContentType(config.headers)) {\n      config.body = JSON.stringify(config.body, jsonReplacer);\n    }\n\n    if (params) {\n      const divider = ~url.indexOf('?') ? '&' : '?';\n      const query = paramsSerializer ? paramsSerializer(params) : new URLSearchParams(stripUndefined(params));\n      url += divider + query;\n    }\n\n    url = joinUrls(baseUrl, url);\n    const request = new Request(url, config);\n    const requestClone = new Request(url, config);\n    meta = {\n      request: requestClone\n    };\n    let response,\n        timedOut = false,\n        timeoutId = timeout && setTimeout(() => {\n      timedOut = true;\n      api.abort();\n    }, timeout);\n\n    try {\n      response = await fetchFn(request);\n    } catch (e) {\n      return {\n        error: {\n          status: timedOut ? 'TIMEOUT_ERROR' : 'FETCH_ERROR',\n          error: String(e)\n        },\n        meta\n      };\n    } finally {\n      if (timeoutId) clearTimeout(timeoutId);\n    }\n\n    const responseClone = response.clone();\n    meta.response = responseClone;\n    let resultData: any;\n    let responseText: string = '';\n\n    try {\n      let handleResponseError;\n      await Promise.all([handleResponse(response, responseHandler).then(r => resultData = r, e => handleResponseError = e), // see https://github.com/node-fetch/node-fetch/issues/665#issuecomment-538995182\n      // we *have* to \"use up\" both streams at the same time or they will stop running in node-fetch scenarios\n      responseClone.text().then(r => responseText = r, () => {})]);\n      if (handleResponseError) throw handleResponseError;\n    } catch (e) {\n      return {\n        error: {\n          status: 'PARSING_ERROR',\n          originalStatus: response.status,\n          data: responseText,\n          error: String(e)\n        },\n        meta\n      };\n    }\n\n    return validateStatus(response, resultData) ? {\n      data: resultData,\n      meta\n    } : {\n      error: {\n        status: response.status,\n        data: resultData\n      },\n      meta\n    };\n  };\n\n  async function handleResponse(response: Response, responseHandler: ResponseHandler) {\n    if (typeof responseHandler === 'function') {\n      return responseHandler(response);\n    }\n\n    if (responseHandler === 'content-type') {\n      responseHandler = isJsonContentType(response.headers) ? 'json' : 'text';\n    }\n\n    if (responseHandler === 'json') {\n      const text = await response.text();\n      return text.length ? JSON.parse(text) : null;\n    }\n\n    return response.text();\n  }\n}", "export class HandledError {\n  constructor(public readonly value: any, public readonly meta: any = undefined) {}\n\n}", "import type { BaseQueryApi, BaseQueryArg, BaseQueryEnhancer, BaseQueryExtraOptions, BaseQueryFn } from './baseQueryTypes';\nimport type { FetchBaseQueryError } from './fetchBaseQuery';\nimport { HandledError } from './HandledError';\n/**\n * Exponential backoff based on the attempt number.\n *\n * @remarks\n * 1. 600ms * random(0.4, 1.4)\n * 2. 1200ms * random(0.4, 1.4)\n * 3. 2400ms * random(0.4, 1.4)\n * 4. 4800ms * random(0.4, 1.4)\n * 5. 9600ms * random(0.4, 1.4)\n *\n * @param attempt - Current attempt\n * @param maxRetries - Maximum number of retries\n */\n\nasync function defaultBackoff(attempt: number = 0, maxRetries: number = 5) {\n  const attempts = Math.min(attempt, maxRetries);\n  const timeout = ~~((Math.random() + 0.4) * (300 << attempts)); // Force a positive int in the case we make this an option\n\n  await new Promise(resolve => setTimeout((res: any) => resolve(res), timeout));\n}\n\ntype RetryConditionFunction = (error: FetchBaseQueryError, args: BaseQueryArg<BaseQueryFn>, extraArgs: {\n  attempt: number;\n  baseQueryApi: BaseQueryApi;\n  extraOptions: BaseQueryExtraOptions<BaseQueryFn> & RetryOptions;\n}) => boolean;\nexport type RetryOptions = {\n  /**\n   * Function used to determine delay between retries\n   */\n  backoff?: (attempt: number, maxRetries: number) => Promise<void>;\n} & ({\n  /**\n   * How many times the query will be retried (default: 5)\n   */\n  maxRetries?: number;\n  retryCondition?: undefined;\n} | {\n  /**\n   * Callback to determine if a retry should be attempted.\n   * Return `true` for another retry and `false` to quit trying prematurely.\n   */\n  retryCondition?: RetryConditionFunction;\n  maxRetries?: undefined;\n});\n\nfunction fail(e: any): never {\n  throw Object.assign(new HandledError({\n    error: e\n  }), {\n    throwImmediately: true\n  });\n}\n\nconst EMPTY_OPTIONS = {};\n\nconst retryWithBackoff: BaseQueryEnhancer<unknown, RetryOptions, RetryOptions | void> = (baseQuery, defaultOptions) => async (args, api, extraOptions) => {\n  // We need to figure out `maxRetries` before we define `defaultRetryCondition.\n  // This is probably goofy, but ought to work.\n  // Put our defaults in one array, filter out undefineds, grab the last value.\n  const possibleMaxRetries: number[] = [5, ((defaultOptions as any) || EMPTY_OPTIONS).maxRetries, ((extraOptions as any) || EMPTY_OPTIONS).maxRetries].filter(x => x !== undefined);\n  const [maxRetries] = possibleMaxRetries.slice(-1);\n\n  const defaultRetryCondition: RetryConditionFunction = (_, __, {\n    attempt\n  }) => attempt <= maxRetries;\n\n  const options: {\n    maxRetries: number;\n    backoff: typeof defaultBackoff;\n    retryCondition: typeof defaultRetryCondition;\n  } = {\n    maxRetries,\n    backoff: defaultBackoff,\n    retryCondition: defaultRetryCondition,\n    ...defaultOptions,\n    ...extraOptions\n  };\n  let retry = 0;\n\n  while (true) {\n    try {\n      const result = await baseQuery(args, api, extraOptions); // baseQueries _should_ return an error property, so we should check for that and throw it to continue retrying\n\n      if (result.error) {\n        throw new HandledError(result);\n      }\n\n      return result;\n    } catch (e: any) {\n      retry++;\n\n      if (e.throwImmediately) {\n        if (e instanceof HandledError) {\n          return e.value;\n        } // We don't know what this is, so we have to rethrow it\n\n\n        throw e;\n      }\n\n      if (e instanceof HandledError && !options.retryCondition((e.value.error as FetchBaseQueryError), args, {\n        attempt: retry,\n        baseQueryApi: api,\n        extraOptions\n      })) {\n        return e.value;\n      }\n\n      await options.backoff(retry, options.maxRetries);\n    }\n  }\n};\n/**\n * A utility that can wrap `baseQuery` in the API definition to provide retries with a basic exponential backoff.\n *\n * @example\n *\n * ```ts\n * // codeblock-meta title=\"Retry every request 5 times by default\"\n * import { createApi, fetchBaseQuery, retry } from '@reduxjs/toolkit/query/react'\n * interface Post {\n *   id: number\n *   name: string\n * }\n * type PostsResponse = Post[]\n *\n * // maxRetries: 5 is the default, and can be omitted. Shown for documentation purposes.\n * const staggeredBaseQuery = retry(fetchBaseQuery({ baseUrl: '/' }), { maxRetries: 5 });\n * export const api = createApi({\n *   baseQuery: staggeredBaseQuery,\n *   endpoints: (build) => ({\n *     getPosts: build.query<PostsResponse, void>({\n *       query: () => ({ url: 'posts' }),\n *     }),\n *     getPost: build.query<PostsResponse, string>({\n *       query: (id) => ({ url: `post/${id}` }),\n *       extraOptions: { maxRetries: 8 }, // You can override the retry behavior on each endpoint\n *     }),\n *   }),\n * });\n *\n * export const { useGetPostsQuery, useGetPostQuery } = api;\n * ```\n */\n\n\nexport const retry = /* @__PURE__ */Object.assign(retryWithBackoff, {\n  fail\n});", "import type { ThunkDispatch, ActionCreatorWithoutPayload // Workaround for API-Extractor\n} from '@reduxjs/toolkit';\nimport { createAction } from './rtkImports';\nexport const onFocus = /* @__PURE__ */createAction('__rtkq/focused');\nexport const onFocusLost = /* @__PURE__ */createAction('__rtkq/unfocused');\nexport const onOnline = /* @__PURE__ */createAction('__rtkq/online');\nexport const onOffline = /* @__PURE__ */createAction('__rtkq/offline');\nlet initialized = false;\n/**\n * A utility used to enable `refetchOnMount` and `refetchOnReconnect` behaviors.\n * It requires the dispatch method from your store.\n * Calling `setupListeners(store.dispatch)` will configure listeners with the recommended defaults,\n * but you have the option of providing a callback for more granular control.\n *\n * @example\n * ```ts\n * setupListeners(store.dispatch)\n * ```\n *\n * @param dispatch - The dispatch method from your store\n * @param customHandler - An optional callback for more granular control over listener behavior\n * @returns Return value of the handler.\n * The default handler returns an `unsubscribe` method that can be called to remove the listeners.\n */\n\nexport function setupListeners(dispatch: ThunkDispatch<any, any, any>, customHandler?: (dispatch: ThunkDispatch<any, any, any>, actions: {\n  onFocus: typeof onFocus;\n  onFocusLost: typeof onFocusLost;\n  onOnline: typeof onOnline;\n  onOffline: typeof onOffline;\n}) => () => void) {\n  function defaultHandler() {\n    const handleFocus = () => dispatch(onFocus());\n\n    const handleFocusLost = () => dispatch(onFocusLost());\n\n    const handleOnline = () => dispatch(onOnline());\n\n    const handleOffline = () => dispatch(onOffline());\n\n    const handleVisibilityChange = () => {\n      if (window.document.visibilityState === 'visible') {\n        handleFocus();\n      } else {\n        handleFocusLost();\n      }\n    };\n\n    if (!initialized) {\n      if (typeof window !== 'undefined' && window.addEventListener) {\n        // Handle focus events\n        window.addEventListener('visibilitychange', handleVisibilityChange, false);\n        window.addEventListener('focus', handleFocus, false); // Handle connection events\n\n        window.addEventListener('online', handleOnline, false);\n        window.addEventListener('offline', handleOffline, false);\n        initialized = true;\n      }\n    }\n\n    const unsubscribe = () => {\n      window.removeEventListener('focus', handleFocus);\n      window.removeEventListener('visibilitychange', handleVisibilityChange);\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n      initialized = false;\n    };\n\n    return unsubscribe;\n  }\n\n  return customHandler ? customHandler(dispatch, {\n    onFocus,\n    onFocusLost,\n    onOffline,\n    onOnline\n  }) : defaultHandler();\n}", "import type { SerializeQueryArgs } from './defaultSerializeQueryArgs';\nimport type { QuerySubState, RootState } from './core/apiState';\nimport type { BaseQueryExtraOptions, BaseQueryFn, BaseQueryResult, BaseQueryArg, BaseQueryApi, QueryReturnValue, BaseQueryError, BaseQueryMeta } from './baseQueryTypes';\nimport type { HasRequiredProps, MaybePromise, OmitFromUnion, CastAny, NonUndefined, UnwrapPromise } from './tsHelpers';\nimport type { NEVER } from './fakeBaseQuery';\nimport type { Api } from '@reduxjs/toolkit/query';\nconst resultType = /* @__PURE__ */Symbol();\nconst baseQuery = /* @__PURE__ */Symbol();\ninterface EndpointDefinitionWithQuery<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  /**\n   * `query` can be a function that returns either a `string` or an `object` which is passed to your `baseQuery`. If you are using [fetchBaseQuery](./fetchBaseQuery), this can return either a `string` or an `object` of properties in `FetchArgs`. If you use your own custom [`baseQuery`](../../rtk-query/usage/customizing-queries), you can customize this behavior to your liking.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"query example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Post'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       // highlight-start\n   *       query: () => 'posts',\n   *       // highlight-end\n   *     }),\n   *     addPost: build.mutation<Post, Partial<Post>>({\n   *      // highlight-start\n   *      query: (body) => ({\n   *        url: `posts`,\n   *        method: 'POST',\n   *        body,\n   *      }),\n   *      // highlight-end\n   *      invalidatesTags: [{ type: 'Post', id: 'LIST' }],\n   *    }),\n   *   })\n   * })\n   * ```\n   */\n  query(arg: QueryArg): BaseQueryArg<BaseQuery>;\n  queryFn?: never;\n  /**\n   * A function to manipulate the data returned by a query or mutation.\n   */\n\n  transformResponse?(baseQueryReturnValue: BaseQueryResult<BaseQuery>, meta: BaseQueryMeta<BaseQuery>, arg: QueryArg): ResultType | Promise<ResultType>;\n  /**\n   * A function to manipulate the data returned by a failed query or mutation.\n   */\n\n  transformErrorResponse?(baseQueryReturnValue: BaseQueryError<BaseQuery>, meta: BaseQueryMeta<BaseQuery>, arg: QueryArg): unknown;\n  /**\n   * Defaults to `true`.\n   *\n   * Most apps should leave this setting on. The only time it can be a performance issue\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\n   * you're unable to paginate it.\n   *\n   * For details of how this works, please see the below. When it is set to `false`,\n   * every request will cause subscribed components to rerender, even when the data has not changed.\n   *\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\n   */\n\n  structuralSharing?: boolean;\n}\ninterface EndpointDefinitionWithQueryFn<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  /**\n   * Can be used in place of `query` as an inline function that bypasses `baseQuery` completely for the endpoint.\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Basic queryFn example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *     }),\n   *     flipCoin: build.query<'heads' | 'tails', void>({\n   *       // highlight-start\n   *       queryFn(arg, queryApi, extraOptions, baseQuery) {\n   *         const randomVal = Math.random()\n   *         if (randomVal < 0.45) {\n   *           return { data: 'heads' }\n   *         }\n   *         if (randomVal < 0.9) {\n   *           return { data: 'tails' }\n   *         }\n   *         return { error: { status: 500, statusText: 'Internal Server Error', data: \"Coin landed on it's edge!\" } }\n   *       }\n   *       // highlight-end\n   *     })\n   *   })\n   * })\n   * ```\n   */\n  queryFn(arg: QueryArg, api: BaseQueryApi, extraOptions: BaseQueryExtraOptions<BaseQuery>, baseQuery: (arg: Parameters<BaseQuery>[0]) => ReturnType<BaseQuery>): MaybePromise<QueryReturnValue<ResultType, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>>;\n  query?: never;\n  transformResponse?: never;\n  transformErrorResponse?: never;\n  /**\n   * Defaults to `true`.\n   *\n   * Most apps should leave this setting on. The only time it can be a performance issue\n   * is if an API returns extremely large amounts of data (e.g. 10,000 rows per request) and\n   * you're unable to paginate it.\n   *\n   * For details of how this works, please see the below. When it is set to `false`,\n   * every request will cause subscribed components to rerender, even when the data has not changed.\n   *\n   * @see https://redux-toolkit.js.org/api/other-exports#copywithstructuralsharing\n   */\n\n  structuralSharing?: boolean;\n}\nexport interface BaseEndpointTypes<QueryArg, BaseQuery extends BaseQueryFn, ResultType> {\n  QueryArg: QueryArg;\n  BaseQuery: BaseQuery;\n  ResultType: ResultType;\n}\nexport type BaseEndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, ResultType> = (([CastAny<BaseQueryResult<BaseQuery>, {}>] extends [NEVER] ? never : EndpointDefinitionWithQuery<QueryArg, BaseQuery, ResultType>) | EndpointDefinitionWithQueryFn<QueryArg, BaseQuery, ResultType>) & {\n  /* phantom type */\n  [resultType]?: ResultType;\n  /* phantom type */\n\n  [baseQuery]?: BaseQuery;\n} & HasRequiredProps<BaseQueryExtraOptions<BaseQuery>, {\n  extraOptions: BaseQueryExtraOptions<BaseQuery>;\n}, {\n  extraOptions?: BaseQueryExtraOptions<BaseQuery>;\n}>;\nexport enum DefinitionType {\n  query = 'query',\n  mutation = 'mutation',\n}\nexport type GetResultDescriptionFn<TagTypes extends string, ResultType, QueryArg, ErrorType, MetaType> = (result: ResultType | undefined, error: ErrorType | undefined, arg: QueryArg, meta: MetaType) => ReadonlyArray<TagDescription<TagTypes>>;\nexport type FullTagDescription<TagType> = {\n  type: TagType;\n  id?: number | string;\n};\nexport type TagDescription<TagType> = TagType | FullTagDescription<TagType>;\nexport type ResultDescription<TagTypes extends string, ResultType, QueryArg, ErrorType, MetaType> = ReadonlyArray<TagDescription<TagTypes>> | GetResultDescriptionFn<TagTypes, ResultType, QueryArg, ErrorType, MetaType>;\nexport interface QueryTypes<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\n  /**\n   * The endpoint definition type. To be used with some internal generic types.\n   * @example\n   * ```ts\n   * const useMyWrappedHook: UseQuery<typeof api.endpoints.query.Types.QueryDefinition> = ...\n   * ```\n   */\n  QueryDefinition: QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n}\nexport interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n  type: DefinitionType.query;\n  /**\n   * Used by `query` endpoints. Determines which 'tag' is attached to the cached data returned by the query.\n   * Expects an array of tag type strings, an array of objects of tag types with ids, or a function that returns such an array.\n   * 1.  `['Post']` - equivalent to `2`\n   * 2.  `[{ type: 'Post' }]` - equivalent to `1`\n   * 3.  `[{ type: 'Post', id: 1 }]`\n   * 4.  `(result, error, arg) => ['Post']` - equivalent to `5`\n   * 5.  `(result, error, arg) => [{ type: 'Post' }]` - equivalent to `4`\n   * 6.  `(result, error, arg) => [{ type: 'Post', id: 1 }]`\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"providesTags example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Posts'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *       // highlight-start\n   *       providesTags: (result) =>\n   *         result\n   *           ? [\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\n   *               { type: 'Posts', id: 'LIST' },\n   *             ]\n   *           : [{ type: 'Posts', id: 'LIST' }],\n   *       // highlight-end\n   *     })\n   *   })\n   * })\n   * ```\n   */\n\n  providesTags?: ResultDescription<TagTypes, ResultType, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\n   * Not to be used. A query should not invalidate tags in the cache.\n   */\n\n  invalidatesTags?: never;\n  /**\n   * Can be provided to return a custom cache key value based on the query arguments.\n   *\n   * This is primarily intended for cases where a non-serializable value is passed as part of the query arg object and should be excluded from the cache key.  It may also be used for cases where an endpoint should only have a single cache entry, such as an infinite loading / pagination implementation.\n   *\n   * Unlike the `createApi` version which can _only_ return a string, this per-endpoint option can also return an an object, number, or boolean.  If it returns a string, that value will be used as the cache key directly.  If it returns an object / number / boolean, that value will be passed to the built-in `defaultSerializeQueryArgs`.  This simplifies the use case of stripping out args you don't want included in the cache key.\n   *\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"serializeQueryArgs : exclude value\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * interface MyApiClient {\n   *   fetchPost: (id: string) => Promise<Post>\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    // Example: an endpoint with an API client passed in as an argument,\n   *    // but only the item ID should be used as the cache key\n   *    getPost: build.query<Post, { id: string; client: MyApiClient }>({\n   *      queryFn: async ({ id, client }) => {\n   *        const post = await client.fetchPost(id)\n   *        return { data: post }\n   *      },\n   *      // highlight-start\n   *      serializeQueryArgs: ({ queryArgs, endpointDefinition, endpointName }) => {\n   *        const { id } = queryArgs\n   *        // This can return a string, an object, a number, or a boolean.\n   *        // If it returns an object, number or boolean, that value\n   *        // will be serialized automatically via `defaultSerializeQueryArgs`\n   *        return { id } // omit `client` from the cache key\n   *\n   *        // Alternately, you can use `defaultSerializeQueryArgs` yourself:\n   *        // return defaultSerializeQueryArgs({\n   *        //   endpointName,\n   *        //   queryArgs: { id },\n   *        //   endpointDefinition\n   *        // })\n   *        // Or  create and return a string yourself:\n   *        // return `getPost(${id})`\n   *      },\n   *      // highlight-end\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n\n  serializeQueryArgs?: SerializeQueryArgs<QueryArg, string | number | boolean | Record<any, any>>;\n  /**\n   * Can be provided to merge an incoming response value into the current cache data.\n   * If supplied, no automatic structural sharing will be applied - it's up to\n   * you to update the cache appropriately.\n   *\n   * Since RTKQ normally replaces cache entries with the new response, you will usually\n   * need to use this with the `serializeQueryArgs` or `forceRefetch` options to keep\n   * an existing cache entry so that it can be updated.\n   *\n   * Since this is wrapped with Immer, you may either mutate the `currentCacheValue` directly,\n   * or return a new value, but _not_ both at once.\n   *\n   * Will only be called if the existing `currentCacheData` is _not_ `undefined` - on first response,\n   * the cache entry will just save the response data directly.\n   *\n   * Useful if you don't want a new request to completely override the current cache value,\n   * maybe because you have manually updated it from another source and don't want those\n   * updates to get lost.\n   *\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"merge: pagination\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    listItems: build.query<string[], number>({\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\n   *     // Only have one cache entry because the arg always maps to one string\n   *     serializeQueryArgs: ({ endpointName }) => {\n   *       return endpointName\n   *      },\n   *      // Always merge incoming data to the cache entry\n   *      merge: (currentCache, newItems) => {\n   *        currentCache.push(...newItems)\n   *      },\n   *      // Refetch when the page arg changes\n   *      forceRefetch({ currentArg, previousArg }) {\n   *        return currentArg !== previousArg\n   *      },\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n\n  merge?(currentCacheData: ResultType, responseData: ResultType, otherArgs: {\n    arg: QueryArg;\n    baseQueryMeta: BaseQueryMeta<BaseQuery>;\n    requestId: string;\n    fulfilledTimeStamp: number;\n  }): ResultType | void;\n  /**\n   * Check to see if the endpoint should force a refetch in cases where it normally wouldn't.\n   * This is primarily useful for \"infinite scroll\" / pagination use cases where\n   * RTKQ is keeping a single cache entry that is added to over time, in combination\n   * with `serializeQueryArgs` returning a fixed cache key and a `merge` callback\n   * set to add incoming data to the cache entry each time.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"forceRefresh: pagination\"\n   *\n   * import { createApi, fetchBaseQuery, defaultSerializeQueryArgs } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   *\n   * createApi({\n   *  baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *  endpoints: (build) => ({\n   *    listItems: build.query<string[], number>({\n   *      query: (pageNumber) => `/listItems?page=${pageNumber}`,\n   *     // Only have one cache entry because the arg always maps to one string\n   *     serializeQueryArgs: ({ endpointName }) => {\n   *       return endpointName\n   *      },\n   *      // Always merge incoming data to the cache entry\n   *      merge: (currentCache, newItems) => {\n   *        currentCache.push(...newItems)\n   *      },\n   *      // Refetch when the page arg changes\n   *      forceRefetch({ currentArg, previousArg }) {\n   *        return currentArg !== previousArg\n   *      },\n   *    }),\n   *  }),\n   *})\n   * ```\n   */\n\n  forceRefetch?(params: {\n    currentArg: QueryArg | undefined;\n    previousArg: QueryArg | undefined;\n    state: RootState<any, any, string>;\n    endpointState?: QuerySubState<any>;\n  }): boolean;\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n\n  Types?: QueryTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type QueryDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> & QueryExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>;\nexport interface MutationTypes<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> extends BaseEndpointTypes<QueryArg, BaseQuery, ResultType> {\n  /**\n   * The endpoint definition type. To be used with some internal generic types.\n   * @example\n   * ```ts\n   * const useMyWrappedHook: UseMutation<typeof api.endpoints.query.Types.MutationDefinition> = ...\n   * ```\n   */\n  MutationDefinition: MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  TagTypes: TagTypes;\n  ReducerPath: ReducerPath;\n}\nexport interface MutationExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n  type: DefinitionType.mutation;\n  /**\n   * Used by `mutation` endpoints. Determines which cached data should be either re-fetched or removed from the cache.\n   * Expects the same shapes as `providesTags`.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"invalidatesTags example\"\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   tagTypes: ['Posts'],\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *       providesTags: (result) =>\n   *         result\n   *           ? [\n   *               ...result.map(({ id }) => ({ type: 'Posts' as const, id })),\n   *               { type: 'Posts', id: 'LIST' },\n   *             ]\n   *           : [{ type: 'Posts', id: 'LIST' }],\n   *     }),\n   *     addPost: build.mutation<Post, Partial<Post>>({\n   *       query(body) {\n   *         return {\n   *           url: `posts`,\n   *           method: 'POST',\n   *           body,\n   *         }\n   *       },\n   *       // highlight-start\n   *       invalidatesTags: [{ type: 'Posts', id: 'LIST' }],\n   *       // highlight-end\n   *     }),\n   *   })\n   * })\n   * ```\n   */\n\n  invalidatesTags?: ResultDescription<TagTypes, ResultType, QueryArg, BaseQueryError<BaseQuery>, BaseQueryMeta<BaseQuery>>;\n  /**\n   * Not to be used. A mutation should not provide tags to the cache.\n   */\n\n  providesTags?: never;\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n\n  Types?: MutationTypes<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n}\nexport type MutationDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = BaseEndpointDefinition<QueryArg, BaseQuery, ResultType> & MutationExtraOptions<TagTypes, ResultType, QueryArg, BaseQuery, ReducerPath>;\nexport type EndpointDefinition<QueryArg, BaseQuery extends BaseQueryFn, TagTypes extends string, ResultType, ReducerPath extends string = string> = QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath> | MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\nexport type EndpointDefinitions = Record<string, EndpointDefinition<any, any, any, any>>;\nexport function isQueryDefinition(e: EndpointDefinition<any, any, any, any>): e is QueryDefinition<any, any, any, any> {\n  return e.type === DefinitionType.query;\n}\nexport function isMutationDefinition(e: EndpointDefinition<any, any, any, any>): e is MutationDefinition<any, any, any, any> {\n  return e.type === DefinitionType.mutation;\n}\nexport type EndpointBuilder<BaseQuery extends BaseQueryFn, TagTypes extends string, ReducerPath extends string> = {\n  /**\n   * An endpoint definition that retrieves data, and may provide tags to the cache.\n   *\n   * @example\n   * ```js\n   * // codeblock-meta title=\"Example of all query endpoint options\"\n   * const api = createApi({\n   *  baseQuery,\n   *  endpoints: (build) => ({\n   *    getPost: build.query({\n   *      query: (id) => ({ url: `post/${id}` }),\n   *      // Pick out data and prevent nested properties in a hook or selector\n   *      transformResponse: (response) => response.data,\n   *      // Pick out error and prevent nested properties in a hook or selector\n   *      transformErrorResponse: (response) => response.error,\n   *      // `result` is the server response\n   *      providesTags: (result, error, id) => [{ type: 'Post', id }],\n   *      // trigger side effects or optimistic updates\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry, updateCachedData }) {},\n   *      // handle subscriptions etc\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry, updateCachedData }) {},\n   *    }),\n   *  }),\n   *});\n   *```\n   */\n  query<ResultType, QueryArg>(definition: OmitFromUnion<QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>, 'type'>): QueryDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n  /**\n   * An endpoint definition that alters data on the server or will possibly invalidate the cache.\n   *\n   * @example\n   * ```js\n   * // codeblock-meta title=\"Example of all mutation endpoint options\"\n   * const api = createApi({\n   *   baseQuery,\n   *   endpoints: (build) => ({\n   *     updatePost: build.mutation({\n   *       query: ({ id, ...patch }) => ({ url: `post/${id}`, method: 'PATCH', body: patch }),\n   *       // Pick out data and prevent nested properties in a hook or selector\n   *       transformResponse: (response) => response.data,\n   *       // Pick out error and prevent nested properties in a hook or selector\n   *       transformErrorResponse: (response) => response.error,\n   *       // `result` is the server response\n   *       invalidatesTags: (result, error, id) => [{ type: 'Post', id }],\n   *      // trigger side effects or optimistic updates\n   *      onQueryStarted(id, { dispatch, getState, extra, requestId, queryFulfilled, getCacheEntry }) {},\n   *      // handle subscriptions etc\n   *      onCacheEntryAdded(id, { dispatch, getState, extra, requestId, cacheEntryRemoved, cacheDataLoaded, getCacheEntry }) {},\n   *     }),\n   *   }),\n   * });\n   * ```\n   */\n\n  mutation<ResultType, QueryArg>(definition: OmitFromUnion<MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>, 'type'>): MutationDefinition<QueryArg, BaseQuery, TagTypes, ResultType, ReducerPath>;\n};\nexport type AssertTagTypes = <T extends FullTagDescription<string>>(t: T) => T;\nexport function calculateProvidedBy<ResultType, QueryArg, ErrorType, MetaType>(description: ResultDescription<string, ResultType, QueryArg, ErrorType, MetaType> | undefined, result: ResultType | undefined, error: ErrorType | undefined, queryArg: QueryArg, meta: MetaType | undefined, assertTagTypes: AssertTagTypes): readonly FullTagDescription<string>[] {\n  if (isFunction(description)) {\n    return description((result as ResultType), (error as undefined), queryArg, (meta as MetaType)).map(expandTagDescription).map(assertTagTypes);\n  }\n\n  if (Array.isArray(description)) {\n    return description.map(expandTagDescription).map(assertTagTypes);\n  }\n\n  return [];\n}\n\nfunction isFunction<T>(t: T): t is Extract<T, Function> {\n  return typeof t === 'function';\n}\n\nexport function expandTagDescription(description: TagDescription<string>): FullTagDescription<string> {\n  return typeof description === 'string' ? {\n    type: description\n  } : description;\n}\nexport type QueryArgFrom<D extends BaseEndpointDefinition<any, any, any>> = D extends BaseEndpointDefinition<infer QA, any, any> ? QA : unknown;\nexport type ResultTypeFrom<D extends BaseEndpointDefinition<any, any, any>> = D extends BaseEndpointDefinition<any, any, infer RT> ? RT : unknown;\nexport type ReducerPathFrom<D extends EndpointDefinition<any, any, any, any, any>> = D extends EndpointDefinition<any, any, any, any, infer RP> ? RP : unknown;\nexport type TagTypesFrom<D extends EndpointDefinition<any, any, any, any>> = D extends EndpointDefinition<any, any, infer RP, any> ? RP : unknown;\nexport type TagTypesFromApi<T> = T extends Api<any, any, any, infer TagTypes> ? TagTypes : never;\nexport type DefinitionsFromApi<T> = T extends Api<any, infer Definitions, any, any> ? Definitions : never;\nexport type TransformedResponse<NewDefinitions extends EndpointDefinitions, K, ResultType> = K extends keyof NewDefinitions ? NewDefinitions[K]['transformResponse'] extends undefined ? ResultType : UnwrapPromise<ReturnType<NonUndefined<NewDefinitions[K]['transformResponse']>>> : ResultType;\nexport type OverrideResultType<Definition, NewResultType> = Definition extends QueryDefinition<infer QueryArg, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? QueryDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath> : Definition extends MutationDefinition<infer QueryArg, infer BaseQuery, infer TagTypes, any, infer ReducerPath> ? MutationDefinition<QueryArg, BaseQuery, TagTypes, NewResultType, ReducerPath> : never;\nexport type UpdateDefinitions<Definitions extends EndpointDefinitions, NewTagTypes extends string, NewDefinitions extends EndpointDefinitions> = { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<infer QueryArg, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? QueryDefinition<QueryArg, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : Definitions[K] extends MutationDefinition<infer QueryArg, infer BaseQuery, any, infer ResultType, infer ReducerPath> ? MutationDefinition<QueryArg, BaseQuery, NewTagTypes, TransformedResponse<NewDefinitions, K, ResultType>, ReducerPath> : never };", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { EndpointDefinitions, QueryDefinition, MutationDefinition, QueryArgFrom, ResultTypeFrom } from '../endpointDefinitions';\nimport { DefinitionType, isQueryDefinition } from '../endpointDefinitions';\nimport type { QueryThunk, MutationThunk, QueryThunkArg } from './buildThunks';\nimport type { UnknownAction, ThunkAction, SerializedError } from '@reduxjs/toolkit';\nimport type { SubscriptionOptions, RootState } from './apiState';\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport type { Api, ApiContext } from '../apiTypes';\nimport type { ApiEndpointQuery } from './module';\nimport type { BaseQueryError, QueryReturnValue } from '../baseQueryTypes';\nimport type { QueryResultSelectorResult } from './buildSelectors';\nimport type { Dispatch } from 'redux';\nimport { isNotNullish } from '../utils/isNotNullish';\nimport { countObjectKeys } from '../utils/countObjectKeys';\nimport type { SafePromise } from '../../tsHelpers';\nimport { asSafePromise } from '../../tsHelpers';\ndeclare module './module' {\n  export interface ApiEndpointQuery<Definition extends QueryDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  Definitions extends EndpointDefinitions> {\n    initiate: StartQueryActionCreator<Definition>;\n  }\n  export interface ApiEndpointMutation<Definition extends MutationDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  Definitions extends EndpointDefinitions> {\n    initiate: StartMutationActionCreator<Definition>;\n  }\n}\nexport const forceQueryFnSymbol = Symbol('forceQueryFn');\nexport const isUpsertQuery = (arg: QueryThunkArg) => typeof arg[forceQueryFnSymbol] === 'function';\nexport interface StartQueryActionCreatorOptions {\n  subscribe?: boolean;\n  forceRefetch?: boolean | number;\n  subscriptionOptions?: SubscriptionOptions;\n  [forceQueryFnSymbol]?: () => QueryReturnValue;\n}\ntype StartQueryActionCreator<D extends QueryDefinition<any, any, any, any, any>> = (arg: QueryArgFrom<D>, options?: StartQueryActionCreatorOptions) => ThunkAction<QueryActionCreatorResult<D>, any, any, UnknownAction>;\nexport type QueryActionCreatorResult<D extends QueryDefinition<any, any, any, any>> = SafePromise<QueryResultSelectorResult<D>> & {\n  arg: QueryArgFrom<D>;\n  requestId: string;\n  subscriptionOptions: SubscriptionOptions | undefined;\n  abort(): void;\n  unwrap(): Promise<ResultTypeFrom<D>>;\n  unsubscribe(): void;\n  refetch(): QueryActionCreatorResult<D>;\n  updateSubscriptionOptions(options: SubscriptionOptions): void;\n  queryCacheKey: string;\n};\ntype StartMutationActionCreator<D extends MutationDefinition<any, any, any, any>> = (arg: QueryArgFrom<D>, options?: {\n  /**\n   * If this mutation should be tracked in the store.\n   * If you just want to manually trigger this mutation using `dispatch` and don't care about the\n   * result, state & potential errors being held in store, you can set this to false.\n   * (defaults to `true`)\n   */\n  track?: boolean;\n  fixedCacheKey?: string;\n}) => ThunkAction<MutationActionCreatorResult<D>, any, any, UnknownAction>;\nexport type MutationActionCreatorResult<D extends MutationDefinition<any, any, any, any>> = SafePromise<{\n  data: ResultTypeFrom<D>;\n} | {\n  error: Exclude<BaseQueryError<D extends MutationDefinition<any, infer BaseQuery, any, any> ? BaseQuery : never>, undefined> | SerializedError;\n}> & {\n  /** @internal */\n  arg: {\n    /**\n     * The name of the given endpoint for the mutation\n     */\n    endpointName: string;\n    /**\n     * The original arguments supplied to the mutation call\n     */\n\n    originalArgs: QueryArgFrom<D>;\n    /**\n     * Whether the mutation is being tracked in the store.\n     */\n\n    track?: boolean;\n    fixedCacheKey?: string;\n  };\n  /**\n   * A unique string generated for the request sequence\n   */\n\n  requestId: string;\n  /**\n   * A method to cancel the mutation promise. Note that this is not intended to prevent the mutation\n   * that was fired off from reaching the server, but only to assist in handling the response.\n   *\n   * Calling `abort()` prior to the promise resolving will force it to reach the error state with\n   * the serialized error:\n   * `{ name: 'AbortError', message: 'Aborted' }`\n   *\n   * @example\n   * ```ts\n   * const [updateUser] = useUpdateUserMutation();\n   *\n   * useEffect(() => {\n   *   const promise = updateUser(id);\n   *   promise\n   *     .unwrap()\n   *     .catch((err) => {\n   *       if (err.name === 'AbortError') return;\n   *       // else handle the unexpected error\n   *     })\n   *\n   *   return () => {\n   *     promise.abort();\n   *   }\n   * }, [id, updateUser])\n   * ```\n   */\n\n  abort(): void;\n  /**\n   * Unwraps a mutation call to provide the raw response/error.\n   *\n   * @remarks\n   * If you need to access the error or success payload immediately after a mutation, you can chain .unwrap().\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using .unwrap\"\n   * addPost({ id: 1, name: 'Example' })\n   *   .unwrap()\n   *   .then((payload) => console.log('fulfilled', payload))\n   *   .catch((error) => console.error('rejected', error));\n   * ```\n   *\n   * @example\n   * ```ts\n   * // codeblock-meta title=\"Using .unwrap with async await\"\n   * try {\n   *   const payload = await addPost({ id: 1, name: 'Example' }).unwrap();\n   *   console.log('fulfilled', payload)\n   * } catch (error) {\n   *   console.error('rejected', error);\n   * }\n   * ```\n   */\n\n  unwrap(): Promise<ResultTypeFrom<D>>;\n  /**\n   * A method to manually unsubscribe from the mutation call, meaning it will be removed from cache after the usual caching grace period.\n   The value returned by the hook will reset to `isUninitialized` afterwards.\n   */\n\n  reset(): void;\n};\nexport function buildInitiate({\n  serializeQueryArgs,\n  queryThunk,\n  mutationThunk,\n  api,\n  context\n}: {\n  serializeQueryArgs: InternalSerializeQueryArgs;\n  queryThunk: QueryThunk;\n  mutationThunk: MutationThunk;\n  api: Api<any, EndpointDefinitions, any, any>;\n  context: ApiContext<EndpointDefinitions>;\n}) {\n  const runningQueries: Map<Dispatch, Record<string, QueryActionCreatorResult<any> | undefined>> = new Map();\n  const runningMutations: Map<Dispatch, Record<string, MutationActionCreatorResult<any> | undefined>> = new Map();\n  const {\n    unsubscribeQueryResult,\n    removeMutationResult,\n    updateSubscriptionOptions\n  } = api.internalActions;\n  return {\n    buildInitiateQuery,\n    buildInitiateMutation,\n    getRunningQueryThunk,\n    getRunningMutationThunk,\n    getRunningQueriesThunk,\n    getRunningMutationsThunk\n  };\n\n  function getRunningQueryThunk(endpointName: string, queryArgs: any) {\n    return (dispatch: Dispatch) => {\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      const queryCacheKey = serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      });\n      return (runningQueries.get(dispatch)?.[queryCacheKey] as QueryActionCreatorResult<never> | undefined);\n    };\n  }\n\n  function getRunningMutationThunk(\n  /**\n   * this is only here to allow TS to infer the result type by input value\n   * we could use it to validate the result, but it's probably not necessary\n   */\n  _endpointName: string, fixedCacheKeyOrRequestId: string) {\n    return (dispatch: Dispatch) => {\n      return (runningMutations.get(dispatch)?.[fixedCacheKeyOrRequestId] as MutationActionCreatorResult<never> | undefined);\n    };\n  }\n\n  function getRunningQueriesThunk() {\n    return (dispatch: Dispatch) => Object.values(runningQueries.get(dispatch) || {}).filter(isNotNullish);\n  }\n\n  function getRunningMutationsThunk() {\n    return (dispatch: Dispatch) => Object.values(runningMutations.get(dispatch) || {}).filter(isNotNullish);\n  }\n\n  function middlewareWarning(dispatch: Dispatch) {\n    if (process.env.NODE_ENV !== 'production') {\n      if ((middlewareWarning as any).triggered) return;\n      const returnedValue = dispatch(api.internalActions.internal_getRTKQSubscriptions());\n      (middlewareWarning as any).triggered = true; // The RTKQ middleware should return the internal state object,\n      // but it should _not_ be the action object.\n\n      if (typeof returnedValue !== 'object' || typeof returnedValue?.type === 'string') {\n        // Otherwise, must not have been added\n        throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(34) : `Warning: Middleware for RTK-Query API at reducerPath \"${api.reducerPath}\" has not been added to the store.\nYou must add the middleware for RTK-Query to function correctly!`);\n      }\n    }\n  }\n\n  function buildInitiateQuery(endpointName: string, endpointDefinition: QueryDefinition<any, any, any, any>) {\n    const queryAction: StartQueryActionCreator<any> = (arg, {\n      subscribe = true,\n      forceRefetch,\n      subscriptionOptions,\n      [forceQueryFnSymbol]: forceQueryFn\n    } = {}) => (dispatch, getState) => {\n      const queryCacheKey = serializeQueryArgs({\n        queryArgs: arg,\n        endpointDefinition,\n        endpointName\n      });\n      const thunk = queryThunk({\n        type: 'query',\n        subscribe,\n        forceRefetch: forceRefetch,\n        subscriptionOptions,\n        endpointName,\n        originalArgs: arg,\n        queryCacheKey,\n        [forceQueryFnSymbol]: forceQueryFn\n      });\n      const selector = (api.endpoints[endpointName] as ApiEndpointQuery<any, any>).select(arg);\n      const thunkResult = dispatch(thunk);\n      const stateAfter = selector(getState());\n      middlewareWarning(dispatch);\n      const {\n        requestId,\n        abort\n      } = thunkResult;\n      const skippedSynchronously = stateAfter.requestId !== requestId;\n      const runningQuery = runningQueries.get(dispatch)?.[queryCacheKey];\n\n      const selectFromState = () => selector(getState());\n\n      const statePromise: QueryActionCreatorResult<any> = Object.assign(((forceQueryFn ? // a query has been forced (upsertQueryData)\n      // -> we want to resolve it once data has been written with the data that will be written\n      thunkResult.then(selectFromState) : skippedSynchronously && !runningQuery ? // a query has been skipped due to a condition and we do not have any currently running query\n      // -> we want to resolve it immediately with the current data\n      Promise.resolve(stateAfter) : // query just started or one is already in flight\n      // -> wait for the running query, then resolve with data from after that\n      Promise.all([runningQuery, thunkResult]).then(selectFromState)) as SafePromise<any>), {\n        arg,\n        requestId,\n        subscriptionOptions,\n        queryCacheKey,\n        abort,\n\n        async unwrap() {\n          const result = await statePromise;\n\n          if (result.isError) {\n            throw result.error;\n          }\n\n          return result.data;\n        },\n\n        refetch: () => dispatch(queryAction(arg, {\n          subscribe: false,\n          forceRefetch: true\n        })),\n\n        unsubscribe() {\n          if (subscribe) dispatch(unsubscribeQueryResult({\n            queryCacheKey,\n            requestId\n          }));\n        },\n\n        updateSubscriptionOptions(options: SubscriptionOptions) {\n          statePromise.subscriptionOptions = options;\n          dispatch(updateSubscriptionOptions({\n            endpointName,\n            requestId,\n            queryCacheKey,\n            options\n          }));\n        }\n\n      });\n\n      if (!runningQuery && !skippedSynchronously && !forceQueryFn) {\n        const running = runningQueries.get(dispatch) || {};\n        running[queryCacheKey] = statePromise;\n        runningQueries.set(dispatch, running);\n        statePromise.then(() => {\n          delete running[queryCacheKey];\n\n          if (!countObjectKeys(running)) {\n            runningQueries.delete(dispatch);\n          }\n        });\n      }\n\n      return statePromise;\n    };\n\n    return queryAction;\n  }\n\n  function buildInitiateMutation(endpointName: string): StartMutationActionCreator<any> {\n    return (arg, {\n      track = true,\n      fixedCacheKey\n    } = {}) => (dispatch, getState) => {\n      const thunk = mutationThunk({\n        type: 'mutation',\n        endpointName,\n        originalArgs: arg,\n        track,\n        fixedCacheKey\n      });\n      const thunkResult = dispatch(thunk);\n      middlewareWarning(dispatch);\n      const {\n        requestId,\n        abort,\n        unwrap\n      } = thunkResult;\n      const returnValuePromise = asSafePromise(thunkResult.unwrap().then(data => ({\n        data\n      })), error => ({\n        error\n      }));\n\n      const reset = () => {\n        dispatch(removeMutationResult({\n          requestId,\n          fixedCacheKey\n        }));\n      };\n\n      const ret = Object.assign(returnValuePromise, {\n        arg: thunkResult.arg,\n        requestId,\n        abort,\n        unwrap,\n        reset\n      });\n      const running = runningMutations.get(dispatch) || {};\n      runningMutations.set(dispatch, running);\n      running[requestId] = ret;\n      ret.then(() => {\n        delete running[requestId];\n\n        if (!countObjectKeys(running)) {\n          runningMutations.delete(dispatch);\n        }\n      });\n\n      if (fixedCacheKey) {\n        running[fixedCacheKey] = ret;\n        ret.then(() => {\n          if (running[fixedCacheKey] === ret) {\n            delete running[fixedCacheKey];\n\n            if (!countObjectKeys(running)) {\n              runningMutations.delete(dispatch);\n            }\n          }\n        });\n      }\n\n      return ret;\n    };\n  }\n}", "export function isNotNullish<T>(v: T | null | undefined): v is T {\n  return v != null;\n}", "// Fast method for counting an object's keys\n// without resorting to `Object.keys(obj).length\n// Will this make a big difference in perf? Probably not\n// But we can save a few allocations.\nexport function countObjectKeys(obj: Record<any, any>) {\n  let count = 0;\n\n  for (const _key in obj) {\n    count++;\n  }\n\n  return count;\n}", "import type { Middleware, StoreEnhancer } from 'redux';\nimport type { Tuple } from './utils';\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>) {\n  Object.assign(target, ...args);\n}\n/**\n * return True if T is `any`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\n\nexport type IsAny<T, True, False = never> = // test if we are going the left AND right path in the condition\ntrue | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;\n/**\n * return True if T is `unknown`, otherwise return False\n * taken from https://github.com/joonhocho/tsdef\n *\n * @internal\n */\n\nexport type IsUnknown<T, True, False = never> = unknown extends T ? IsAny<T, False, True> : False;\nexport type FallbackIfUnknown<T, Fallback> = IsUnknown<T, Fallback, T>;\n/**\n * @internal\n */\n\nexport type IfMaybeUndefined<P, True, False> = [undefined] extends [P] ? True : False;\n/**\n * @internal\n */\n\nexport type IfVoid<P, True, False> = [void] extends [P] ? True : False;\n/**\n * @internal\n */\n\nexport type IsEmptyObj<T, True, False = never> = T extends any ? keyof T extends never ? IsUnknown<T, False, IfMaybeUndefined<T, False, IfVoid<T, False, True>>> : False : never;\n/**\n * returns True if TS version is above 3.5, False if below.\n * uses feature detection to detect TS version >= 3.5\n * * versions below 3.5 will return `{}` for unresolvable interference\n * * versions above will return `unknown`\n *\n * @internal\n */\n\nexport type AtLeastTS35<True, False> = [True, False][IsUnknown<ReturnType<<T>() => T>, 0, 1>];\n/**\n * @internal\n */\n\nexport type IsUnknownOrNonInferrable<T, True, False> = AtLeastTS35<IsUnknown<T, True, False>, IsEmptyObj<T, True, IsUnknown<T, True, False>>>;\n/**\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\n */\n\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never; // Appears to have a convenient side effect of ignoring `never` even if that's not what you specified\n\nexport type ExcludeFromTuple<T, E, Acc extends unknown[] = []> = T extends [infer Head, ...infer Tail] ? ExcludeFromTuple<Tail, E, [...Acc, ...([Head] extends [E] ? [] : [Head])]> : Acc;\ntype ExtractDispatchFromMiddlewareTuple<MiddlewareTuple extends readonly any[], Acc extends {}> = MiddlewareTuple extends [infer Head, ...infer Tail] ? ExtractDispatchFromMiddlewareTuple<Tail, Acc & (Head extends Middleware<infer D> ? IsAny<D, {}, D> : {})> : Acc;\nexport type ExtractDispatchExtensions<M> = M extends Tuple<infer MiddlewareTuple> ? ExtractDispatchFromMiddlewareTuple<MiddlewareTuple, {}> : M extends ReadonlyArray<Middleware> ? ExtractDispatchFromMiddlewareTuple<[...M], {}> : never;\ntype ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStoreExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<infer Ext> ? IsAny<Ext, {}, Ext> : {})> : Acc;\nexport type ExtractStoreExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStoreExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<infer Ext> ? Ext extends {} ? IsAny<Ext, {}, Ext> : {} : {}> : never;\ntype ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple extends readonly any[], Acc extends {}> = EnhancerTuple extends [infer Head, ...infer Tail] ? ExtractStateExtensionsFromEnhancerTuple<Tail, Acc & (Head extends StoreEnhancer<any, infer StateExt> ? IsAny<StateExt, {}, StateExt> : {})> : Acc;\nexport type ExtractStateExtensions<E> = E extends Tuple<infer EnhancerTuple> ? ExtractStateExtensionsFromEnhancerTuple<EnhancerTuple, {}> : E extends ReadonlyArray<StoreEnhancer> ? UnionToIntersection<E[number] extends StoreEnhancer<any, infer StateExt> ? StateExt extends {} ? IsAny<StateExt, {}, StateExt> : {} : {}> : never;\n/**\n * Helper type. Passes T out again, but boxes it in a way that it cannot\n * \"widen\" the type by accident if it is a generic that should be inferred\n * from elsewhere.\n *\n * @internal\n */\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type Omit<T, K extends keyof any> = Pick<T, Exclude<keyof T, K>>;\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type WithOptionalProp<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\nexport interface TypeGuard<T> {\n  (value: any): value is T;\n}\nexport interface HasMatchFunction<T> {\n  match: TypeGuard<T>;\n}\nexport const hasMatchFunction = <T,>(v: Matcher<T>): v is HasMatchFunction<T> => {\n  return v && typeof (v as HasMatchFunction<T>).match === 'function';\n};\n/** @public */\n\nexport type Matcher<T> = HasMatchFunction<T> | TypeGuard<T>;\n/** @public */\n\nexport type ActionFromMatcher<M extends Matcher<any>> = M extends Matcher<infer T> ? T : never;\nexport type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type Tail<T extends any[]> = T extends [any, ...infer Tail] ? Tail : never;\nexport type UnknownIfNonSpecific<T> = {} extends T ? unknown : T;\n/**\n * A Promise that will never reject.\n * @see https://github.com/reduxjs/redux-toolkit/issues/4101\n */\n\nexport type SafePromise<T> = Promise<T> & {\n  __linterBrands: 'SafePromise';\n};\n/**\n * Properly wraps a Promise as a {@link SafePromise} with .catch(fallback).\n */\n\nexport function asSafePromise<Resolved, Rejected>(promise: Promise<Resolved>, fallback: (error: unknown) => Rejected) {\n  return (promise.catch(fallback) as SafePromise<Resolved | Rejected>);\n}", "import type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport type { Api, ApiContext } from '../apiTypes';\nimport type { BaseQueryFn, BaseQueryError, QueryReturnValue } from '../baseQueryTypes';\nimport type { RootState, QueryKeys, QuerySubstateIdentifier } from './apiState';\nimport { QueryStatus } from './apiState';\nimport type { StartQueryActionCreatorOptions, QueryActionCreatorResult } from './buildInitiate';\nimport { forceQueryFnSymbol, isUpsertQuery } from './buildInitiate';\nimport type { AssertTagTypes, EndpointDefinition, EndpointDefinitions, MutationDefinition, QueryArgFrom, QueryDefinition, ResultTypeFrom, FullTagDescription } from '../endpointDefinitions';\nimport { isQueryDefinition } from '../endpointDefinitions';\nimport { calculateProvidedBy } from '../endpointDefinitions';\nimport type { AsyncThunkPayloadCreator, Draft, UnknownAction } from '@reduxjs/toolkit';\nimport { isAllOf, isFulfilled, isPending, isRejected, isRejectedWithValue, createAsyncThunk, SHOULD_AUTOBATCH } from './rtkImports';\nimport type { Patch } from 'immer';\nimport { isDraftable, produceWithPatches } from 'immer';\nimport type { ThunkAction, ThunkDispatch, AsyncThunk } from '@reduxjs/toolkit';\nimport { HandledError } from '../HandledError';\nimport type { ApiEndpointQuery, PrefetchOptions } from './module';\nimport type { UnwrapPromise } from '../tsHelpers';\ndeclare module './module' {\n  export interface ApiEndpointQuery<Definition extends QueryDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  Definitions extends EndpointDefinitions> extends Matchers<QueryThunk, Definition> {}\n  export interface ApiEndpointMutation<Definition extends MutationDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  Definitions extends EndpointDefinitions> extends Matchers<MutationThunk, Definition> {}\n}\ntype EndpointThunk<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> = Definition extends EndpointDefinition<infer QueryArg, infer BaseQueryFn, any, infer ResultType> ? Thunk extends AsyncThunk<unknown, infer ATArg, infer ATConfig> ? AsyncThunk<ResultType, ATArg & {\n  originalArgs: QueryArg;\n}, ATConfig & {\n  rejectValue: BaseQueryError<BaseQueryFn>;\n}> : never : never;\nexport type PendingAction<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> = ReturnType<EndpointThunk<Thunk, Definition>['pending']>;\nexport type FulfilledAction<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> = ReturnType<EndpointThunk<Thunk, Definition>['fulfilled']>;\nexport type RejectedAction<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> = ReturnType<EndpointThunk<Thunk, Definition>['rejected']>;\nexport type Matcher<M> = (value: any) => value is M;\nexport interface Matchers<Thunk extends QueryThunk | MutationThunk, Definition extends EndpointDefinition<any, any, any, any>> {\n  matchPending: Matcher<PendingAction<Thunk, Definition>>;\n  matchFulfilled: Matcher<FulfilledAction<Thunk, Definition>>;\n  matchRejected: Matcher<RejectedAction<Thunk, Definition>>;\n}\nexport interface QueryThunkArg extends QuerySubstateIdentifier, StartQueryActionCreatorOptions {\n  type: 'query';\n  originalArgs: unknown;\n  endpointName: string;\n}\nexport interface MutationThunkArg {\n  type: 'mutation';\n  originalArgs: unknown;\n  endpointName: string;\n  track?: boolean;\n  fixedCacheKey?: string;\n}\nexport type ThunkResult = unknown;\nexport type ThunkApiMetaConfig = {\n  pendingMeta: {\n    startedTimeStamp: number;\n    [SHOULD_AUTOBATCH]: true;\n  };\n  fulfilledMeta: {\n    fulfilledTimeStamp: number;\n    baseQueryMeta: unknown;\n    [SHOULD_AUTOBATCH]: true;\n  };\n  rejectedMeta: {\n    baseQueryMeta: unknown;\n    [SHOULD_AUTOBATCH]: true;\n  };\n};\nexport type QueryThunk = AsyncThunk<ThunkResult, QueryThunkArg, ThunkApiMetaConfig>;\nexport type MutationThunk = AsyncThunk<ThunkResult, MutationThunkArg, ThunkApiMetaConfig>;\n\nfunction defaultTransformResponse(baseQueryReturnValue: unknown) {\n  return baseQueryReturnValue;\n}\n\nexport type MaybeDrafted<T> = T | Draft<T>;\nexport type Recipe<T> = (data: MaybeDrafted<T>) => void | MaybeDrafted<T>;\nexport type UpsertRecipe<T> = (data: MaybeDrafted<T> | undefined) => void | MaybeDrafted<T>;\nexport type PatchQueryDataThunk<Definitions extends EndpointDefinitions, PartialState> = <EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, args: QueryArgFrom<Definitions[EndpointName]>, patches: readonly Patch[], updateProvided?: boolean) => ThunkAction<void, PartialState, any, UnknownAction>;\nexport type UpdateQueryDataThunk<Definitions extends EndpointDefinitions, PartialState> = <EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, args: QueryArgFrom<Definitions[EndpointName]>, updateRecipe: Recipe<ResultTypeFrom<Definitions[EndpointName]>>, updateProvided?: boolean) => ThunkAction<PatchCollection, PartialState, any, UnknownAction>;\nexport type UpsertQueryDataThunk<Definitions extends EndpointDefinitions, PartialState> = <EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, args: QueryArgFrom<Definitions[EndpointName]>, value: ResultTypeFrom<Definitions[EndpointName]>) => ThunkAction<QueryActionCreatorResult<Definitions[EndpointName] extends QueryDefinition<any, any, any, any> ? Definitions[EndpointName] : never>, PartialState, any, UnknownAction>;\n/**\n * An object returned from dispatching a `api.util.updateQueryData` call.\n */\n\nexport type PatchCollection = {\n  /**\n   * An `immer` Patch describing the cache update.\n   */\n  patches: Patch[];\n  /**\n   * An `immer` Patch to revert the cache update.\n   */\n\n  inversePatches: Patch[];\n  /**\n   * A function that will undo the cache update.\n   */\n\n  undo: () => void;\n};\nexport function buildThunks<BaseQuery extends BaseQueryFn, ReducerPath extends string, Definitions extends EndpointDefinitions>({\n  reducerPath,\n  baseQuery,\n  context: {\n    endpointDefinitions\n  },\n  serializeQueryArgs,\n  api,\n  assertTagType\n}: {\n  baseQuery: BaseQuery;\n  reducerPath: ReducerPath;\n  context: ApiContext<Definitions>;\n  serializeQueryArgs: InternalSerializeQueryArgs;\n  api: Api<BaseQuery, Definitions, ReducerPath, any>;\n  assertTagType: AssertTagTypes;\n}) {\n  type State = RootState<any, string, ReducerPath>;\n\n  const patchQueryData: PatchQueryDataThunk<EndpointDefinitions, State> = (endpointName, args, patches, updateProvided) => (dispatch, getState) => {\n    const endpointDefinition = endpointDefinitions[endpointName];\n    const queryCacheKey = serializeQueryArgs({\n      queryArgs: args,\n      endpointDefinition,\n      endpointName\n    });\n    dispatch(api.internalActions.queryResultPatched({\n      queryCacheKey,\n      patches\n    }));\n\n    if (!updateProvided) {\n      return;\n    }\n\n    const newValue = api.endpoints[endpointName].select(args)(( // Work around TS 4.1 mismatch\n    getState() as RootState<any, any, any>));\n    const providedTags = calculateProvidedBy(endpointDefinition.providesTags, newValue.data, undefined, args, {}, assertTagType);\n    dispatch(api.internalActions.updateProvidedBy({\n      queryCacheKey,\n      providedTags\n    }));\n  };\n\n  const updateQueryData: UpdateQueryDataThunk<EndpointDefinitions, State> = (endpointName, args, updateRecipe, updateProvided = true) => (dispatch, getState) => {\n    const endpointDefinition = api.endpoints[endpointName];\n    const currentState = endpointDefinition.select(args)(( // Work around TS 4.1 mismatch\n    getState() as RootState<any, any, any>));\n    let ret: PatchCollection = {\n      patches: [],\n      inversePatches: [],\n      undo: () => dispatch(api.util.patchQueryData(endpointName, args, ret.inversePatches, updateProvided))\n    };\n\n    if (currentState.status === QueryStatus.uninitialized) {\n      return ret;\n    }\n\n    let newValue;\n\n    if ('data' in currentState) {\n      if (isDraftable(currentState.data)) {\n        const [value, patches, inversePatches] = produceWithPatches(currentState.data, updateRecipe);\n        ret.patches.push(...patches);\n        ret.inversePatches.push(...inversePatches);\n        newValue = value;\n      } else {\n        newValue = updateRecipe(currentState.data);\n        ret.patches.push({\n          op: 'replace',\n          path: [],\n          value: newValue\n        });\n        ret.inversePatches.push({\n          op: 'replace',\n          path: [],\n          value: currentState.data\n        });\n      }\n    }\n\n    dispatch(api.util.patchQueryData(endpointName, args, ret.patches, updateProvided));\n    return ret;\n  };\n\n  const upsertQueryData: UpsertQueryDataThunk<Definitions, State> = (endpointName, args, value) => dispatch => {\n    return dispatch((api.endpoints[endpointName] as ApiEndpointQuery<QueryDefinition<any, any, any, any, any>, Definitions>).initiate(args, {\n      subscribe: false,\n      forceRefetch: true,\n      [forceQueryFnSymbol]: () => ({\n        data: value\n      })\n    }));\n  };\n\n  const executeEndpoint: AsyncThunkPayloadCreator<ThunkResult, QueryThunkArg | MutationThunkArg, ThunkApiMetaConfig & {\n    state: RootState<any, string, ReducerPath>;\n  }> = async (arg, {\n    signal,\n    abort,\n    rejectWithValue,\n    fulfillWithValue,\n    dispatch,\n    getState,\n    extra\n  }) => {\n    const endpointDefinition = endpointDefinitions[arg.endpointName];\n\n    try {\n      let transformResponse: (baseQueryReturnValue: any, meta: any, arg: any) => any = defaultTransformResponse;\n      let result: QueryReturnValue;\n      const baseQueryApi = {\n        signal,\n        abort,\n        dispatch,\n        getState,\n        extra,\n        endpoint: arg.endpointName,\n        type: arg.type,\n        forced: arg.type === 'query' ? isForcedQuery(arg, getState()) : undefined\n      };\n      const forceQueryFn = arg.type === 'query' ? arg[forceQueryFnSymbol] : undefined;\n\n      if (forceQueryFn) {\n        result = forceQueryFn();\n      } else if (endpointDefinition.query) {\n        result = await baseQuery(endpointDefinition.query(arg.originalArgs), baseQueryApi, (endpointDefinition.extraOptions as any));\n\n        if (endpointDefinition.transformResponse) {\n          transformResponse = endpointDefinition.transformResponse;\n        }\n      } else {\n        result = await endpointDefinition.queryFn(arg.originalArgs, baseQueryApi, (endpointDefinition.extraOptions as any), arg => baseQuery(arg, baseQueryApi, (endpointDefinition.extraOptions as any)));\n      }\n\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n        const what = endpointDefinition.query ? '`baseQuery`' : '`queryFn`';\n        let err: undefined | string;\n\n        if (!result) {\n          err = `${what} did not return anything.`;\n        } else if (typeof result !== 'object') {\n          err = `${what} did not return an object.`;\n        } else if (result.error && result.data) {\n          err = `${what} returned an object containing both \\`error\\` and \\`result\\`.`;\n        } else if (result.error === undefined && result.data === undefined) {\n          err = `${what} returned an object containing neither a valid \\`error\\` and \\`result\\`. At least one of them should not be \\`undefined\\``;\n        } else {\n          for (const key of Object.keys(result)) {\n            if (key !== 'error' && key !== 'data' && key !== 'meta') {\n              err = `The object returned by ${what} has the unknown property ${key}.`;\n              break;\n            }\n          }\n        }\n\n        if (err) {\n          console.error(`Error encountered handling the endpoint ${arg.endpointName}.\n              ${err}\n              It needs to return an object with either the shape \\`{ data: <value> }\\` or \\`{ error: <value> }\\` that may contain an optional \\`meta\\` property.\n              Object returned was:`, result);\n        }\n      }\n\n      if (result.error) throw new HandledError(result.error, result.meta);\n      return fulfillWithValue(await transformResponse(result.data, result.meta, arg.originalArgs), {\n        fulfilledTimeStamp: Date.now(),\n        baseQueryMeta: result.meta,\n        [SHOULD_AUTOBATCH]: true\n      });\n    } catch (error) {\n      let catchedError = error;\n\n      if (catchedError instanceof HandledError) {\n        let transformErrorResponse: (baseQueryReturnValue: any, meta: any, arg: any) => any = defaultTransformResponse;\n\n        if (endpointDefinition.query && endpointDefinition.transformErrorResponse) {\n          transformErrorResponse = endpointDefinition.transformErrorResponse;\n        }\n\n        try {\n          return rejectWithValue(await transformErrorResponse(catchedError.value, catchedError.meta, arg.originalArgs), {\n            baseQueryMeta: catchedError.meta,\n            [SHOULD_AUTOBATCH]: true\n          });\n        } catch (e) {\n          catchedError = e;\n        }\n      }\n\n      if (typeof process !== 'undefined' && process.env.NODE_ENV !== 'production') {\n        console.error(`An unhandled error occurred processing a request for the endpoint \"${arg.endpointName}\".\nIn the case of an unhandled error, no tags will be \"provided\" or \"invalidated\".`, catchedError);\n      } else {\n        console.error(catchedError);\n      }\n\n      throw catchedError;\n    }\n  };\n\n  function isForcedQuery(arg: QueryThunkArg, state: RootState<any, string, ReducerPath>) {\n    const requestState = state[reducerPath]?.queries?.[arg.queryCacheKey];\n    const baseFetchOnMountOrArgChange = state[reducerPath]?.config.refetchOnMountOrArgChange;\n    const fulfilledVal = requestState?.fulfilledTimeStamp;\n    const refetchVal = arg.forceRefetch ?? (arg.subscribe && baseFetchOnMountOrArgChange);\n\n    if (refetchVal) {\n      // Return if its true or compare the dates because it must be a number\n      return refetchVal === true || (Number(new Date()) - Number(fulfilledVal)) / 1000 >= refetchVal;\n    }\n\n    return false;\n  }\n\n  const queryThunk = createAsyncThunk<ThunkResult, QueryThunkArg, ThunkApiMetaConfig & {\n    state: RootState<any, string, ReducerPath>;\n  }>(`${reducerPath}/executeQuery`, executeEndpoint, {\n    getPendingMeta() {\n      return {\n        startedTimeStamp: Date.now(),\n        [SHOULD_AUTOBATCH]: true\n      };\n    },\n\n    condition(queryThunkArgs, {\n      getState\n    }) {\n      const state = getState();\n      const requestState = state[reducerPath]?.queries?.[queryThunkArgs.queryCacheKey];\n      const fulfilledVal = requestState?.fulfilledTimeStamp;\n      const currentArg = queryThunkArgs.originalArgs;\n      const previousArg = requestState?.originalArgs;\n      const endpointDefinition = endpointDefinitions[queryThunkArgs.endpointName]; // Order of these checks matters.\n      // In order for `upsertQueryData` to successfully run while an existing request is in flight,\n      /// we have to check for that first, otherwise `queryThunk` will bail out and not run at all.\n\n      if (isUpsertQuery(queryThunkArgs)) {\n        return true;\n      } // Don't retry a request that's currently in-flight\n\n\n      if (requestState?.status === 'pending') {\n        return false;\n      } // if this is forced, continue\n\n\n      if (isForcedQuery(queryThunkArgs, state)) {\n        return true;\n      }\n\n      if (isQueryDefinition(endpointDefinition) && endpointDefinition?.forceRefetch?.({\n        currentArg,\n        previousArg,\n        endpointState: requestState,\n        state\n      })) {\n        return true;\n      } // Pull from the cache unless we explicitly force refetch or qualify based on time\n\n\n      if (fulfilledVal) {\n        // Value is cached and we didn't specify to refresh, skip it.\n        return false;\n      }\n\n      return true;\n    },\n\n    dispatchConditionRejection: true\n  });\n  const mutationThunk = createAsyncThunk<ThunkResult, MutationThunkArg, ThunkApiMetaConfig & {\n    state: RootState<any, string, ReducerPath>;\n  }>(`${reducerPath}/executeMutation`, executeEndpoint, {\n    getPendingMeta() {\n      return {\n        startedTimeStamp: Date.now(),\n        [SHOULD_AUTOBATCH]: true\n      };\n    }\n\n  });\n\n  const hasTheForce = (options: any): options is {\n    force: boolean;\n  } => 'force' in options;\n\n  const hasMaxAge = (options: any): options is {\n    ifOlderThan: false | number;\n  } => 'ifOlderThan' in options;\n\n  const prefetch = <EndpointName extends QueryKeys<Definitions>,>(endpointName: EndpointName, arg: any, options: PrefetchOptions): ThunkAction<void, any, any, UnknownAction> => (dispatch: ThunkDispatch<any, any, any>, getState: () => any) => {\n    const force = hasTheForce(options) && options.force;\n    const maxAge = hasMaxAge(options) && options.ifOlderThan;\n\n    const queryAction = (force: boolean = true) => (api.endpoints[endpointName] as ApiEndpointQuery<any, any>).initiate(arg, {\n      forceRefetch: force\n    });\n\n    const latestStateValue = (api.endpoints[endpointName] as ApiEndpointQuery<any, any>).select(arg)(getState());\n\n    if (force) {\n      dispatch(queryAction());\n    } else if (maxAge) {\n      const lastFulfilledTs = latestStateValue?.fulfilledTimeStamp;\n\n      if (!lastFulfilledTs) {\n        dispatch(queryAction());\n        return;\n      }\n\n      const shouldRetrigger = (Number(new Date()) - Number(new Date(lastFulfilledTs))) / 1000 >= maxAge;\n\n      if (shouldRetrigger) {\n        dispatch(queryAction());\n      }\n    } else {\n      // If prefetching with no options, just let it try\n      dispatch(queryAction(false));\n    }\n  };\n\n  function matchesEndpoint(endpointName: string) {\n    return (action: any): action is UnknownAction => action?.meta?.arg?.endpointName === endpointName;\n  }\n\n  function buildMatchThunkActions<Thunk extends AsyncThunk<any, QueryThunkArg, ThunkApiMetaConfig> | AsyncThunk<any, MutationThunkArg, ThunkApiMetaConfig>>(thunk: Thunk, endpointName: string) {\n    return ({\n      matchPending: isAllOf(isPending(thunk), matchesEndpoint(endpointName)),\n      matchFulfilled: isAllOf(isFulfilled(thunk), matchesEndpoint(endpointName)),\n      matchRejected: isAllOf(isRejected(thunk), matchesEndpoint(endpointName))\n    } as Matchers<Thunk, any>);\n  }\n\n  return {\n    queryThunk,\n    mutationThunk,\n    prefetch,\n    updateQueryData,\n    upsertQueryData,\n    patchQueryData,\n    buildMatchThunkActions\n  };\n}\nexport function calculateProvidedByThunk(action: UnwrapPromise<ReturnType<ReturnType<QueryThunk>> | ReturnType<ReturnType<MutationThunk>>>, type: 'providesTags' | 'invalidatesTags', endpointDefinitions: EndpointDefinitions, assertTagType: AssertTagTypes) {\n  return calculateProvidedBy(endpointDefinitions[action.meta.arg.endpointName][type], isFulfilled(action) ? action.payload : undefined, isRejectedWithValue(action) ? action.payload : undefined, action.meta.arg.originalArgs, 'baseQueryMeta' in action.meta ? action.meta.baseQueryMeta : undefined, assertTagType);\n}", "import type { Action, PayloadAction, UnknownAction } from '@reduxjs/toolkit';\nimport { combineReducers, createAction, createSlice, isAnyOf, isFulfilled, isRejectedWithValue, createNextState, prepareAutoBatched } from './rtkImports';\nimport type { QuerySubstateIdentifier, QuerySubState, MutationSubstateIdentifier, MutationSubState, MutationState, QueryState, InvalidationState, Subscribers, QueryCacheKey, SubscriptionState, ConfigState } from './apiState';\nimport { QueryStatus } from './apiState';\nimport type { MutationThunk, QueryThunk, RejectedAction } from './buildThunks';\nimport { calculateProvidedByThunk } from './buildThunks';\nimport type { AssertTagTypes, EndpointDefinitions, FullTagDescription, QueryDefinition } from '../endpointDefinitions';\nimport type { Patch } from 'immer';\nimport { isDraft } from 'immer';\nimport { applyPatches, original } from 'immer';\nimport { onFocus, onFocusLost, onOffline, onOnline } from './setupListeners';\nimport { isDocumentVisible, isOnline, copyWithStructuralSharing } from '../utils';\nimport type { ApiContext } from '../apiTypes';\nimport { isUpsertQuery } from './buildInitiate';\n\nfunction updateQuerySubstateIfExists(state: QueryState<any>, queryCacheKey: QueryCacheKey, update: (substate: QuerySubState<any>) => void) {\n  const substate = state[queryCacheKey];\n\n  if (substate) {\n    update(substate);\n  }\n}\n\nexport function getMutationCacheKey(id: MutationSubstateIdentifier | {\n  requestId: string;\n  arg: {\n    fixedCacheKey?: string | undefined;\n  };\n}): string;\nexport function getMutationCacheKey(id: {\n  fixedCacheKey?: string;\n  requestId?: string;\n}): string | undefined;\nexport function getMutationCacheKey(id: {\n  fixedCacheKey?: string;\n  requestId?: string;\n} | MutationSubstateIdentifier | {\n  requestId: string;\n  arg: {\n    fixedCacheKey?: string | undefined;\n  };\n}): string | undefined {\n  return ('arg' in id ? id.arg.fixedCacheKey : id.fixedCacheKey) ?? id.requestId;\n}\n\nfunction updateMutationSubstateIfExists(state: MutationState<any>, id: MutationSubstateIdentifier | {\n  requestId: string;\n  arg: {\n    fixedCacheKey?: string | undefined;\n  };\n}, update: (substate: MutationSubState<any>) => void) {\n  const substate = state[getMutationCacheKey(id)];\n\n  if (substate) {\n    update(substate);\n  }\n}\n\nconst initialState = ({} as any);\nexport function buildSlice({\n  reducerPath,\n  queryThunk,\n  mutationThunk,\n  context: {\n    endpointDefinitions: definitions,\n    apiUid,\n    extractRehydrationInfo,\n    hasRehydrationInfo\n  },\n  assertTagType,\n  config\n}: {\n  reducerPath: string;\n  queryThunk: QueryThunk;\n  mutationThunk: MutationThunk;\n  context: ApiContext<EndpointDefinitions>;\n  assertTagType: AssertTagTypes;\n  config: Omit<ConfigState<string>, 'online' | 'focused' | 'middlewareRegistered'>;\n}) {\n  const resetApiState = createAction(`${reducerPath}/resetApiState`);\n  const querySlice = createSlice({\n    name: `${reducerPath}/queries`,\n    initialState: (initialState as QueryState<any>),\n    reducers: {\n      removeQueryResult: {\n        reducer(draft, {\n          payload: {\n            queryCacheKey\n          }\n        }: PayloadAction<QuerySubstateIdentifier>) {\n          delete draft[queryCacheKey];\n        },\n\n        prepare: prepareAutoBatched<QuerySubstateIdentifier>()\n      },\n      queryResultPatched: {\n        reducer(draft, {\n          payload: {\n            queryCacheKey,\n            patches\n          }\n        }: PayloadAction<QuerySubstateIdentifier & {\n          patches: readonly Patch[];\n        }>) {\n          updateQuerySubstateIfExists(draft, queryCacheKey, substate => {\n            substate.data = applyPatches((substate.data as any), patches.concat());\n          });\n        },\n\n        prepare: prepareAutoBatched<QuerySubstateIdentifier & {\n          patches: readonly Patch[];\n        }>()\n      }\n    },\n\n    extraReducers(builder) {\n      builder.addCase(queryThunk.pending, (draft, {\n        meta,\n        meta: {\n          arg\n        }\n      }) => {\n        const upserting = isUpsertQuery(arg);\n        draft[arg.queryCacheKey] ??= {\n          status: QueryStatus.uninitialized,\n          endpointName: arg.endpointName\n        };\n        updateQuerySubstateIfExists(draft, arg.queryCacheKey, substate => {\n          substate.status = QueryStatus.pending;\n          substate.requestId = upserting && substate.requestId ? // for `upsertQuery` **updates**, keep the current `requestId`\n          substate.requestId : // for normal queries or `upsertQuery` **inserts** always update the `requestId`\n          meta.requestId;\n\n          if (arg.originalArgs !== undefined) {\n            substate.originalArgs = arg.originalArgs;\n          }\n\n          substate.startedTimeStamp = meta.startedTimeStamp;\n        });\n      }).addCase(queryThunk.fulfilled, (draft, {\n        meta,\n        payload\n      }) => {\n        updateQuerySubstateIfExists(draft, meta.arg.queryCacheKey, substate => {\n          if (substate.requestId !== meta.requestId && !isUpsertQuery(meta.arg)) return;\n          const {\n            merge\n          } = (definitions[meta.arg.endpointName] as QueryDefinition<any, any, any, any>);\n          substate.status = QueryStatus.fulfilled;\n\n          if (merge) {\n            if (substate.data !== undefined) {\n              const {\n                fulfilledTimeStamp,\n                arg,\n                baseQueryMeta,\n                requestId\n              } = meta; // There's existing cache data. Let the user merge it in themselves.\n              // We're already inside an Immer-powered reducer, and the user could just mutate `substate.data`\n              // themselves inside of `merge()`. But, they might also want to return a new value.\n              // Try to let Immer figure that part out, save the result, and assign it to `substate.data`.\n\n              let newData = createNextState(substate.data, draftSubstateData => {\n                // As usual with Immer, you can mutate _or_ return inside here, but not both\n                return merge(draftSubstateData, payload, {\n                  arg: arg.originalArgs,\n                  baseQueryMeta,\n                  fulfilledTimeStamp,\n                  requestId\n                });\n              });\n              substate.data = newData;\n            } else {\n              // Presumably a fresh request. Just cache the response data.\n              substate.data = payload;\n            }\n          } else {\n            // Assign or safely update the cache data.\n            substate.data = definitions[meta.arg.endpointName].structuralSharing ?? true ? copyWithStructuralSharing(isDraft(substate.data) ? original(substate.data) : substate.data, payload) : payload;\n          }\n\n          delete substate.error;\n          substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\n        });\n      }).addCase(queryThunk.rejected, (draft, {\n        meta: {\n          condition,\n          arg,\n          requestId\n        },\n        error,\n        payload\n      }) => {\n        updateQuerySubstateIfExists(draft, arg.queryCacheKey, substate => {\n          if (condition) {// request was aborted due to condition (another query already running)\n          } else {\n            // request failed\n            if (substate.requestId !== requestId) return;\n            substate.status = QueryStatus.rejected;\n            substate.error = (payload ?? error as any);\n          }\n        });\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          queries\n        } = extractRehydrationInfo(action)!;\n\n        for (const [key, entry] of Object.entries(queries)) {\n          if ( // do not rehydrate entries that were currently in flight.\n          entry?.status === QueryStatus.fulfilled || entry?.status === QueryStatus.rejected) {\n            draft[key] = entry;\n          }\n        }\n      });\n    }\n\n  });\n  const mutationSlice = createSlice({\n    name: `${reducerPath}/mutations`,\n    initialState: (initialState as MutationState<any>),\n    reducers: {\n      removeMutationResult: {\n        reducer(draft, {\n          payload\n        }: PayloadAction<MutationSubstateIdentifier>) {\n          const cacheKey = getMutationCacheKey(payload);\n\n          if (cacheKey in draft) {\n            delete draft[cacheKey];\n          }\n        },\n\n        prepare: prepareAutoBatched<MutationSubstateIdentifier>()\n      }\n    },\n\n    extraReducers(builder) {\n      builder.addCase(mutationThunk.pending, (draft, {\n        meta,\n        meta: {\n          requestId,\n          arg,\n          startedTimeStamp\n        }\n      }) => {\n        if (!arg.track) return;\n        draft[getMutationCacheKey(meta)] = {\n          requestId,\n          status: QueryStatus.pending,\n          endpointName: arg.endpointName,\n          startedTimeStamp\n        };\n      }).addCase(mutationThunk.fulfilled, (draft, {\n        payload,\n        meta\n      }) => {\n        if (!meta.arg.track) return;\n        updateMutationSubstateIfExists(draft, meta, substate => {\n          if (substate.requestId !== meta.requestId) return;\n          substate.status = QueryStatus.fulfilled;\n          substate.data = payload;\n          substate.fulfilledTimeStamp = meta.fulfilledTimeStamp;\n        });\n      }).addCase(mutationThunk.rejected, (draft, {\n        payload,\n        error,\n        meta\n      }) => {\n        if (!meta.arg.track) return;\n        updateMutationSubstateIfExists(draft, meta, substate => {\n          if (substate.requestId !== meta.requestId) return;\n          substate.status = QueryStatus.rejected;\n          substate.error = (payload ?? error as any);\n        });\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          mutations\n        } = extractRehydrationInfo(action)!;\n\n        for (const [key, entry] of Object.entries(mutations)) {\n          if ( // do not rehydrate entries that were currently in flight.\n          (entry?.status === QueryStatus.fulfilled || entry?.status === QueryStatus.rejected) && // only rehydrate endpoints that were persisted using a `fixedCacheKey`\n          key !== entry?.requestId) {\n            draft[key] = entry;\n          }\n        }\n      });\n    }\n\n  });\n  const invalidationSlice = createSlice({\n    name: `${reducerPath}/invalidation`,\n    initialState: (initialState as InvalidationState<string>),\n    reducers: {\n      updateProvidedBy: {\n        reducer(draft, action: PayloadAction<{\n          queryCacheKey: QueryCacheKey;\n          providedTags: readonly FullTagDescription<string>[];\n        }>) {\n          const {\n            queryCacheKey,\n            providedTags\n          } = action.payload;\n\n          for (const tagTypeSubscriptions of Object.values(draft)) {\n            for (const idSubscriptions of Object.values(tagTypeSubscriptions)) {\n              const foundAt = idSubscriptions.indexOf(queryCacheKey);\n\n              if (foundAt !== -1) {\n                idSubscriptions.splice(foundAt, 1);\n              }\n            }\n          }\n\n          for (const {\n            type,\n            id\n          } of providedTags) {\n            const subscribedQueries = (draft[type] ??= {})[id || '__internal_without_id'] ??= [];\n            const alreadySubscribed = subscribedQueries.includes(queryCacheKey);\n\n            if (!alreadySubscribed) {\n              subscribedQueries.push(queryCacheKey);\n            }\n          }\n        },\n\n        prepare: prepareAutoBatched<{\n          queryCacheKey: QueryCacheKey;\n          providedTags: readonly FullTagDescription<string>[];\n        }>()\n      }\n    },\n\n    extraReducers(builder) {\n      builder.addCase(querySlice.actions.removeQueryResult, (draft, {\n        payload: {\n          queryCacheKey\n        }\n      }) => {\n        for (const tagTypeSubscriptions of Object.values(draft)) {\n          for (const idSubscriptions of Object.values(tagTypeSubscriptions)) {\n            const foundAt = idSubscriptions.indexOf(queryCacheKey);\n\n            if (foundAt !== -1) {\n              idSubscriptions.splice(foundAt, 1);\n            }\n          }\n        }\n      }).addMatcher(hasRehydrationInfo, (draft, action) => {\n        const {\n          provided\n        } = extractRehydrationInfo(action)!;\n\n        for (const [type, incomingTags] of Object.entries(provided)) {\n          for (const [id, cacheKeys] of Object.entries(incomingTags)) {\n            const subscribedQueries = (draft[type] ??= {})[id || '__internal_without_id'] ??= [];\n\n            for (const queryCacheKey of cacheKeys) {\n              const alreadySubscribed = subscribedQueries.includes(queryCacheKey);\n\n              if (!alreadySubscribed) {\n                subscribedQueries.push(queryCacheKey);\n              }\n            }\n          }\n        }\n      }).addMatcher(isAnyOf(isFulfilled(queryThunk), isRejectedWithValue(queryThunk)), (draft, action) => {\n        const providedTags = calculateProvidedByThunk(action, 'providesTags', definitions, assertTagType);\n        const {\n          queryCacheKey\n        } = action.meta.arg;\n        invalidationSlice.caseReducers.updateProvidedBy(draft, invalidationSlice.actions.updateProvidedBy({\n          queryCacheKey,\n          providedTags\n        }));\n      });\n    }\n\n  }); // Dummy slice to generate actions\n\n  const subscriptionSlice = createSlice({\n    name: `${reducerPath}/subscriptions`,\n    initialState: (initialState as SubscriptionState),\n    reducers: {\n      updateSubscriptionOptions(d, a: PayloadAction<{\n        endpointName: string;\n        requestId: string;\n        options: Subscribers[number];\n      } & QuerySubstateIdentifier>) {// Dummy\n      },\n\n      unsubscribeQueryResult(d, a: PayloadAction<{\n        requestId: string;\n      } & QuerySubstateIdentifier>) {// Dummy\n      },\n\n      internal_getRTKQSubscriptions() {}\n\n    }\n  });\n  const internalSubscriptionsSlice = createSlice({\n    name: `${reducerPath}/internalSubscriptions`,\n    initialState: (initialState as SubscriptionState),\n    reducers: {\n      subscriptionsUpdated: {\n        reducer(state, action: PayloadAction<Patch[]>) {\n          return applyPatches(state, action.payload);\n        },\n\n        prepare: prepareAutoBatched<Patch[]>()\n      }\n    }\n  });\n  const configSlice = createSlice({\n    name: `${reducerPath}/config`,\n    initialState: ({\n      online: isOnline(),\n      focused: isDocumentVisible(),\n      middlewareRegistered: false,\n      ...config\n    } as ConfigState<string>),\n    reducers: {\n      middlewareRegistered(state, {\n        payload\n      }: PayloadAction<string>) {\n        state.middlewareRegistered = state.middlewareRegistered === 'conflict' || apiUid !== payload ? 'conflict' : true;\n      }\n\n    },\n    extraReducers: builder => {\n      builder.addCase(onOnline, state => {\n        state.online = true;\n      }).addCase(onOffline, state => {\n        state.online = false;\n      }).addCase(onFocus, state => {\n        state.focused = true;\n      }).addCase(onFocusLost, state => {\n        state.focused = false;\n      }) // update the state to be a new object to be picked up as a \"state change\"\n      // by redux-persist's `autoMergeLevel2`\n      .addMatcher(hasRehydrationInfo, draft => ({ ...draft\n      }));\n    }\n  });\n  const combinedReducer = combineReducers({\n    queries: querySlice.reducer,\n    mutations: mutationSlice.reducer,\n    provided: invalidationSlice.reducer,\n    subscriptions: internalSubscriptionsSlice.reducer,\n    config: configSlice.reducer\n  });\n\n  const reducer: typeof combinedReducer = (state, action) => combinedReducer(resetApiState.match(action) ? undefined : state, action);\n\n  const actions = { ...configSlice.actions,\n    ...querySlice.actions,\n    ...subscriptionSlice.actions,\n    ...internalSubscriptionsSlice.actions,\n    ...mutationSlice.actions,\n    ...invalidationSlice.actions,\n    resetApiState\n  };\n  return {\n    reducer,\n    actions\n  };\n}\nexport type SliceActions = ReturnType<typeof buildSlice>['actions'];", "import type { createSelector as _createSelector } from './rtkImports';\nimport { createNextState } from './rtkImports';\nimport type { MutationSubState, QuerySubState, RootState as _RootState, RequestStatusFlags, QueryCacheKey, QueryKeys, QueryState } from './apiState';\nimport { QueryStatus, getRequestStatusFlags } from './apiState';\nimport type { EndpointDefinitions, QueryDefinition, MutationDefinition, QueryArgFrom, TagTypesFrom, ReducerPathFrom, TagDescription } from '../endpointDefinitions';\nimport { expandTagDescription } from '../endpointDefinitions';\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport { getMutationCacheKey } from './buildSlice';\nimport { flatten } from '../utils';\nexport type SkipToken = typeof skipToken;\n/**\n * Can be passed into `useQuery`, `useQueryState` or `useQuerySubscription`\n * instead of the query argument to get the same effect as if setting\n * `skip: true` in the query options.\n *\n * Useful for scenarios where a query should be skipped when `arg` is `undefined`\n * and TypeScript complains about it because `arg` is not allowed to be passed\n * in as `undefined`, such as\n *\n * ```ts\n * // codeblock-meta title=\"will error if the query argument is not allowed to be undefined\" no-transpile\n * useSomeQuery(arg, { skip: !!arg })\n * ```\n *\n * ```ts\n * // codeblock-meta title=\"using skipToken instead\" no-transpile\n * useSomeQuery(arg ?? skipToken)\n * ```\n *\n * If passed directly into a query or mutation selector, that selector will always\n * return an uninitialized state.\n */\n\nexport const skipToken = /* @__PURE__ */Symbol.for('RTKQ/skipToken');\ndeclare module './module' {\n  export interface ApiEndpointQuery<Definition extends QueryDefinition<any, any, any, any, any>, Definitions extends EndpointDefinitions> {\n    select: QueryResultSelectorFactory<Definition, _RootState<Definitions, TagTypesFrom<Definition>, ReducerPathFrom<Definition>>>;\n  }\n  export interface ApiEndpointMutation<Definition extends MutationDefinition<any, any, any, any, any>, Definitions extends EndpointDefinitions> {\n    select: MutationResultSelectorFactory<Definition, _RootState<Definitions, TagTypesFrom<Definition>, ReducerPathFrom<Definition>>>;\n  }\n}\ntype QueryResultSelectorFactory<Definition extends QueryDefinition<any, any, any, any>, RootState> = (queryArg: QueryArgFrom<Definition> | SkipToken) => (state: RootState) => QueryResultSelectorResult<Definition>;\nexport type QueryResultSelectorResult<Definition extends QueryDefinition<any, any, any, any>> = QuerySubState<Definition> & RequestStatusFlags;\ntype MutationResultSelectorFactory<Definition extends MutationDefinition<any, any, any, any>, RootState> = (requestId: string | {\n  requestId: string | undefined;\n  fixedCacheKey: string | undefined;\n} | SkipToken) => (state: RootState) => MutationResultSelectorResult<Definition>;\nexport type MutationResultSelectorResult<Definition extends MutationDefinition<any, any, any, any>> = MutationSubState<Definition> & RequestStatusFlags;\nconst initialSubState: QuerySubState<any> = {\n  status: (QueryStatus.uninitialized as const)\n}; // abuse immer to freeze default states\n\nconst defaultQuerySubState = /* @__PURE__ */createNextState(initialSubState, () => {});\nconst defaultMutationSubState = /* @__PURE__ */createNextState((initialSubState as MutationSubState<any>), () => {});\nexport function buildSelectors<Definitions extends EndpointDefinitions, ReducerPath extends string>({\n  serializeQueryArgs,\n  reducerPath,\n  createSelector\n}: {\n  serializeQueryArgs: InternalSerializeQueryArgs;\n  reducerPath: ReducerPath;\n  createSelector: typeof _createSelector;\n}) {\n  type RootState = _RootState<Definitions, string, string>;\n\n  const selectSkippedQuery = (state: RootState) => defaultQuerySubState;\n\n  const selectSkippedMutation = (state: RootState) => defaultMutationSubState;\n\n  return {\n    buildQuerySelector,\n    buildMutationSelector,\n    selectInvalidatedBy,\n    selectCachedArgsForQuery\n  };\n\n  function withRequestFlags<T extends {\n    status: QueryStatus;\n  }>(substate: T): T & RequestStatusFlags {\n    return { ...substate,\n      ...getRequestStatusFlags(substate.status)\n    };\n  }\n\n  function selectInternalState(rootState: RootState) {\n    const state = rootState[reducerPath];\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!state) {\n        if ((selectInternalState as any).triggered) return state;\n        (selectInternalState as any).triggered = true;\n        console.error(`Error: No data found at \\`state.${reducerPath}\\`. Did you forget to add the reducer to the store?`);\n      }\n    }\n\n    return state;\n  }\n\n  function buildQuerySelector(endpointName: string, endpointDefinition: QueryDefinition<any, any, any, any>) {\n    return (((queryArgs: any) => {\n      const serializedArgs = serializeQueryArgs({\n        queryArgs,\n        endpointDefinition,\n        endpointName\n      });\n\n      const selectQuerySubstate = (state: RootState) => selectInternalState(state)?.queries?.[serializedArgs] ?? defaultQuerySubState;\n\n      const finalSelectQuerySubState = queryArgs === skipToken ? selectSkippedQuery : selectQuerySubstate;\n      return createSelector(finalSelectQuerySubState, withRequestFlags);\n    }) as QueryResultSelectorFactory<any, RootState>);\n  }\n\n  function buildMutationSelector() {\n    return ((id => {\n      let mutationId: string | typeof skipToken;\n\n      if (typeof id === 'object') {\n        mutationId = getMutationCacheKey(id) ?? skipToken;\n      } else {\n        mutationId = id;\n      }\n\n      const selectMutationSubstate = (state: RootState) => selectInternalState(state)?.mutations?.[(mutationId as string)] ?? defaultMutationSubState;\n\n      const finalSelectMutationSubstate = mutationId === skipToken ? selectSkippedMutation : selectMutationSubstate;\n      return createSelector(finalSelectMutationSubstate, withRequestFlags);\n    }) as MutationResultSelectorFactory<any, RootState>);\n  }\n\n  function selectInvalidatedBy(state: RootState, tags: ReadonlyArray<TagDescription<string>>): Array<{\n    endpointName: string;\n    originalArgs: any;\n    queryCacheKey: QueryCacheKey;\n  }> {\n    const apiState = state[reducerPath];\n    const toInvalidate = new Set<QueryCacheKey>();\n\n    for (const tag of tags.map(expandTagDescription)) {\n      const provided = apiState.provided[tag.type];\n\n      if (!provided) {\n        continue;\n      }\n\n      let invalidateSubscriptions = (tag.id !== undefined ? // id given: invalidate all queries that provide this type & id\n      provided[tag.id] : // no id: invalidate all queries that provide this type\n      flatten(Object.values(provided))) ?? [];\n\n      for (const invalidate of invalidateSubscriptions) {\n        toInvalidate.add(invalidate);\n      }\n    }\n\n    return flatten(Array.from(toInvalidate.values()).map(queryCacheKey => {\n      const querySubState = apiState.queries[queryCacheKey];\n      return querySubState ? [{\n        queryCacheKey,\n        endpointName: querySubState.endpointName!,\n        originalArgs: querySubState.originalArgs\n      }] : [];\n    }));\n  }\n\n  function selectCachedArgsForQuery<QueryName extends QueryKeys<Definitions>>(state: RootState, queryName: QueryName): Array<QueryArgFrom<Definitions[QueryName]>> {\n    return Object.values((state[reducerPath].queries as QueryState<any>)).filter((entry): entry is Exclude<QuerySubState<Definitions[QueryName]>, {\n      status: QueryStatus.uninitialized;\n    }> => entry?.endpointName === queryName && entry.status !== QueryStatus.uninitialized).map(entry => entry.originalArgs);\n  }\n}", "import type { QueryCacheKey } from './core/apiState';\nimport type { EndpointDefinition } from './endpointDefinitions';\nimport { isPlainObject } from './core/rtkImports';\nconst cache: WeakMap<any, string> | undefined = WeakMap ? new WeakMap() : undefined;\nexport const defaultSerializeQueryArgs: SerializeQueryArgs<any> = ({\n  endpointName,\n  queryArgs\n}) => {\n  let serialized = '';\n  const cached = cache?.get(queryArgs);\n\n  if (typeof cached === 'string') {\n    serialized = cached;\n  } else {\n    const stringified = JSON.stringify(queryArgs, (key, value) => isPlainObject(value) ? Object.keys(value).sort().reduce<any>((acc, key) => {\n      acc[key] = (value as any)[key];\n      return acc;\n    }, {}) : value);\n\n    if (isPlainObject(queryArgs)) {\n      cache?.set(queryArgs, stringified);\n    }\n\n    serialized = stringified;\n  } // Sort the object keys before stringifying, to prevent useQuery({ a: 1, b: 2 }) having a different cache key than useQuery({ b: 2, a: 1 })\n\n\n  return `${endpointName}(${serialized})`;\n};\nexport type SerializeQueryArgs<QueryArgs, ReturnType = string> = (_: {\n  queryArgs: QueryArgs;\n  endpointDefinition: EndpointDefinition<any, any, any, any>;\n  endpointName: string;\n}) => ReturnType;\nexport type InternalSerializeQueryArgs = (_: {\n  queryArgs: any;\n  endpointDefinition: EndpointDefinition<any, any, any, any>;\n  endpointName: string;\n}) => QueryCacheKey;", "import type { <PERSON><PERSON>, <PERSON>pi<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>leName } from './apiTypes';\nimport type { CombinedState } from './core/apiState';\nimport type { BaseQueryArg, BaseQueryFn } from './baseQueryTypes';\nimport type { SerializeQueryArgs } from './defaultSerializeQueryArgs';\nimport { defaultSerializeQueryArgs } from './defaultSerializeQueryArgs';\nimport type { EndpointBuilder, EndpointDefinitions } from './endpointDefinitions';\nimport { DefinitionType, isQueryDefinition } from './endpointDefinitions';\nimport { nanoid } from './core/rtkImports';\nimport type { UnknownAction } from '@reduxjs/toolkit';\nimport type { NoInfer } from './tsHelpers';\nimport { weakMapMemoize } from 'reselect';\nexport interface CreateApiOptions<BaseQuery extends BaseQueryFn, Definitions extends EndpointDefinitions, ReducerPath extends string = 'api', TagTypes extends string = never> {\n  /**\n   * The base query used by each endpoint if no `queryFn` option is specified. RTK Query exports a utility called [fetchBaseQuery](./fetchBaseQuery) as a lightweight wrapper around `fetch` for common use-cases. See [Customizing Queries](../../rtk-query/usage/customizing-queries) if `fetchBaseQuery` does not handle your requirements.\n   *\n   * @example\n   *\n   * ```ts\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\n   *\n   * const api = createApi({\n   *   // highlight-start\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   // highlight-end\n   *   endpoints: (build) => ({\n   *     // ...endpoints\n   *   }),\n   * })\n   * ```\n   */\n  baseQuery: BaseQuery;\n  /**\n   * An array of string tag type names. Specifying tag types is optional, but you should define them so that they can be used for caching and invalidation. When defining a tag type, you will be able to [provide](../../rtk-query/usage/automated-refetching#providing-tags) them with `providesTags` and [invalidate](../../rtk-query/usage/automated-refetching#invalidating-tags) them with `invalidatesTags` when configuring [endpoints](#endpoints).\n   *\n   * @example\n   *\n   * ```ts\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   // highlight-start\n   *   tagTypes: ['Post', 'User'],\n   *   // highlight-end\n   *   endpoints: (build) => ({\n   *     // ...endpoints\n   *   }),\n   * })\n   * ```\n   */\n\n  tagTypes?: readonly TagTypes[];\n  /**\n   * The `reducerPath` is a _unique_ key that your service will be mounted to in your store. If you call `createApi` more than once in your application, you will need to provide a unique value each time. Defaults to `'api'`.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"apis.js\"\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query';\n   *\n   * const apiOne = createApi({\n   *   // highlight-start\n   *   reducerPath: 'apiOne',\n   *   // highlight-end\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (builder) => ({\n   *     // ...endpoints\n   *   }),\n   * });\n   *\n   * const apiTwo = createApi({\n   *   // highlight-start\n   *   reducerPath: 'apiTwo',\n   *   // highlight-end\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (builder) => ({\n   *     // ...endpoints\n   *   }),\n   * });\n   * ```\n   */\n\n  reducerPath?: ReducerPath;\n  /**\n   * Accepts a custom function if you have a need to change the creation of cache keys for any reason.\n   */\n\n  serializeQueryArgs?: SerializeQueryArgs<BaseQueryArg<BaseQuery>>;\n  /**\n   * Endpoints are just a set of operations that you want to perform against your server. You define them as an object using the builder syntax. There are two basic endpoint types: [`query`](../../rtk-query/usage/queries) and [`mutation`](../../rtk-query/usage/mutations).\n   */\n\n  endpoints(build: EndpointBuilder<BaseQuery, TagTypes, ReducerPath>): Definitions;\n  /**\n   * Defaults to `60` _(this value is in seconds)_. This is how long RTK Query will keep your data cached for **after** the last component unsubscribes. For example, if you query an endpoint, then unmount the component, then mount another component that makes the same request within the given time frame, the most recent value will be served from the cache.\n   *\n   * ```ts\n   * // codeblock-meta title=\"keepUnusedDataFor example\"\n   *\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * interface Post {\n   *   id: number\n   *   name: string\n   * }\n   * type PostsResponse = Post[]\n   *\n   * const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   endpoints: (build) => ({\n   *     getPosts: build.query<PostsResponse, void>({\n   *       query: () => 'posts',\n   *       // highlight-start\n   *       keepUnusedDataFor: 5\n   *       // highlight-end\n   *     })\n   *   })\n   * })\n   * ```\n   */\n\n  keepUnusedDataFor?: number;\n  /**\n   * Defaults to `false`. This setting allows you to control whether if a cached result is already available RTK Query will only serve a cached result, or if it should `refetch` when set to `true` or if an adequate amount of time has passed since the last successful query result.\n   * - `false` - Will not cause a query to be performed _unless_ it does not exist yet.\n   * - `true` - Will always refetch when a new subscriber to a query is added. Behaves the same as calling the `refetch` callback or passing `forceRefetch: true` in the action creator.\n   * - `number` - **Value is in seconds**. If a number is provided and there is an existing query in the cache, it will compare the current time vs the last fulfilled timestamp, and only refetch if enough time has elapsed.\n   *\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\n   */\n\n  refetchOnMountOrArgChange?: boolean | number;\n  /**\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after the application window regains focus.\n   *\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\n   *\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\n   */\n\n  refetchOnFocus?: boolean;\n  /**\n   * Defaults to `false`. This setting allows you to control whether RTK Query will try to refetch all subscribed queries after regaining a network connection.\n   *\n   * If you specify this option alongside `skip: true`, this **will not be evaluated** until `skip` is false.\n   *\n   * Note: requires [`setupListeners`](./setupListeners) to have been called.\n   */\n\n  refetchOnReconnect?: boolean;\n  /**\n   * Defaults to `'immediately'`. This setting allows you to control when tags are invalidated after a mutation.\n   *\n   * - `'immediately'`: Queries are invalidated instantly after the mutation finished, even if they are running.\n   *   If the query provides tags that were invalidated while it ran, it won't be re-fetched.\n   * - `'delayed'`: Invalidation only happens after all queries and mutations are settled.\n   *   This ensures that queries are always invalidated correctly and automatically \"batches\" invalidations of concurrent mutations.\n   *   Note that if you constantly have some queries (or mutations) running, this can delay tag invalidations indefinitely.\n   */\n\n  invalidationBehavior?: 'delayed' | 'immediately';\n  /**\n   * A function that is passed every dispatched action. If this returns something other than `undefined`,\n   * that return value will be used to rehydrate fulfilled & errored queries.\n   *\n   * @example\n   *\n   * ```ts\n   * // codeblock-meta title=\"next-redux-wrapper rehydration example\"\n   * import type { Action, PayloadAction } from '@reduxjs/toolkit'\n   * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'\n   * import { HYDRATE } from 'next-redux-wrapper'\n   *\n   * type RootState = any; // normally inferred from state\n   *\n   * function isHydrateAction(action: Action): action is PayloadAction<RootState> {\n   *   return action.type === HYDRATE\n   * }\n   *\n   * export const api = createApi({\n   *   baseQuery: fetchBaseQuery({ baseUrl: '/' }),\n   *   // highlight-start\n   *   extractRehydrationInfo(action, { reducerPath }): any {\n   *     if (isHydrateAction(action)) {\n   *       return action.payload[reducerPath]\n   *     }\n   *   },\n   *   // highlight-end\n   *   endpoints: (build) => ({\n   *     // omitted\n   *   }),\n   * })\n   * ```\n   */\n\n  extractRehydrationInfo?: (action: UnknownAction, {\n    reducerPath\n  }: {\n    reducerPath: ReducerPath;\n  }) => undefined | CombinedState<NoInfer<Definitions>, NoInfer<TagTypes>, NoInfer<ReducerPath>>;\n}\nexport type CreateApi<Modules extends ModuleName> = {\n  /**\n   * Creates a service to use in your application. Contains only the basic redux logic (the core module).\n   *\n   * @link https://rtk-query-docs.netlify.app/api/createApi\n   */\n  <BaseQuery extends BaseQueryFn, Definitions extends EndpointDefinitions, ReducerPath extends string = 'api', TagTypes extends string = never>(options: CreateApiOptions<BaseQuery, Definitions, ReducerPath, TagTypes>): Api<BaseQuery, Definitions, ReducerPath, TagTypes, Modules>;\n};\n/**\n * Builds a `createApi` method based on the provided `modules`.\n *\n * @link https://rtk-query-docs.netlify.app/concepts/customizing-create-api\n *\n * @example\n * ```ts\n * const MyContext = React.createContext<ReactReduxContextValue>(null as any);\n * const customCreateApi = buildCreateApi(\n *   coreModule(),\n *   reactHooksModule({\n *     hooks: {\n *       useDispatch: createDispatchHook(MyContext),\n *       useSelector: createSelectorHook(MyContext),\n *       useStore: createStoreHook(MyContext)\n *     }\n *   })\n * );\n * ```\n *\n * @param modules - A variable number of modules that customize how the `createApi` method handles endpoints\n * @returns A `createApi` method using the provided `modules`.\n */\n\nexport function buildCreateApi<Modules extends [Module<any>, ...Module<any>[]]>(...modules: Modules): CreateApi<Modules[number]['name']> {\n  return function baseCreateApi(options) {\n    const extractRehydrationInfo = weakMapMemoize((action: UnknownAction) => options.extractRehydrationInfo?.(action, {\n      reducerPath: (options.reducerPath ?? 'api' as any)\n    }));\n    const optionsWithDefaults: CreateApiOptions<any, any, any, any> = {\n      reducerPath: 'api',\n      keepUnusedDataFor: 60,\n      refetchOnMountOrArgChange: false,\n      refetchOnFocus: false,\n      refetchOnReconnect: false,\n      invalidationBehavior: 'delayed',\n      ...options,\n      extractRehydrationInfo,\n\n      serializeQueryArgs(queryArgsApi) {\n        let finalSerializeQueryArgs = defaultSerializeQueryArgs;\n\n        if ('serializeQueryArgs' in queryArgsApi.endpointDefinition) {\n          const endpointSQA = queryArgsApi.endpointDefinition.serializeQueryArgs!;\n\n          finalSerializeQueryArgs = queryArgsApi => {\n            const initialResult = endpointSQA(queryArgsApi);\n\n            if (typeof initialResult === 'string') {\n              // If the user function returned a string, use it as-is\n              return initialResult;\n            } else {\n              // Assume they returned an object (such as a subset of the original\n              // query args) or a primitive, and serialize it ourselves\n              return defaultSerializeQueryArgs({ ...queryArgsApi,\n                queryArgs: initialResult\n              });\n            }\n          };\n        } else if (options.serializeQueryArgs) {\n          finalSerializeQueryArgs = options.serializeQueryArgs;\n        }\n\n        return finalSerializeQueryArgs(queryArgsApi);\n      },\n\n      tagTypes: [...(options.tagTypes || [])]\n    };\n    const context: ApiContext<EndpointDefinitions> = {\n      endpointDefinitions: {},\n\n      batch(fn) {\n        // placeholder \"batch\" method to be overridden by plugins, for example with React.unstable_batchedUpdate\n        fn();\n      },\n\n      apiUid: nanoid(),\n      extractRehydrationInfo,\n      hasRehydrationInfo: weakMapMemoize(action => extractRehydrationInfo(action) != null)\n    };\n    const api = ({\n      injectEndpoints,\n\n      enhanceEndpoints({\n        addTagTypes,\n        endpoints\n      }) {\n        if (addTagTypes) {\n          for (const eT of addTagTypes) {\n            if (!optionsWithDefaults.tagTypes!.includes((eT as any))) {\n              ;\n              (optionsWithDefaults.tagTypes as any[]).push(eT);\n            }\n          }\n        }\n\n        if (endpoints) {\n          for (const [endpointName, partialDefinition] of Object.entries(endpoints)) {\n            if (typeof partialDefinition === 'function') {\n              partialDefinition(context.endpointDefinitions[endpointName]);\n            } else {\n              Object.assign(context.endpointDefinitions[endpointName] || {}, partialDefinition);\n            }\n          }\n        }\n\n        return api;\n      }\n\n    } as Api<BaseQueryFn, {}, string, string, Modules[number]['name']>);\n    const initializedModules = modules.map(m => m.init((api as any), (optionsWithDefaults as any), context));\n\n    function injectEndpoints(inject: Parameters<typeof api.injectEndpoints>[0]) {\n      const evaluatedEndpoints = inject.endpoints({\n        query: x => ({ ...x,\n          type: DefinitionType.query\n        } as any),\n        mutation: x => ({ ...x,\n          type: DefinitionType.mutation\n        } as any)\n      });\n\n      for (const [endpointName, definition] of Object.entries(evaluatedEndpoints)) {\n        if (!inject.overrideExisting && endpointName in context.endpointDefinitions) {\n          if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n            console.error(`called \\`injectEndpoints\\` to override already-existing endpointName ${endpointName} without specifying \\`overrideExisting: true\\``);\n          }\n\n          continue;\n        }\n\n        context.endpointDefinitions[endpointName] = definition;\n\n        for (const m of initializedModules) {\n          m.injectEndpoint(endpointName, definition);\n        }\n      }\n\n      return (api as any);\n    }\n\n    return api.injectEndpoints({\n      endpoints: (options.endpoints as any)\n    });\n  };\n}", "import { formatProdErrorMessage as _formatProdErrorMessage } from \"@reduxjs/toolkit\";\nimport type { BaseQueryFn } from './baseQueryTypes';\n\nconst _NEVER = /* @__PURE__ */Symbol();\n\nexport type NEVER = typeof _NEVER;\n/**\n * Creates a \"fake\" baseQuery to be used if your api *only* uses the `queryFn` definition syntax.\n * This also allows you to specify a specific error type to be shared by all your `queryFn` definitions.\n */\n\nexport function fakeBaseQuery<ErrorType>(): BaseQueryFn<void, NEVER, ErrorType, {}> {\n  return function () {\n    throw new Error(process.env.NODE_ENV === \"production\" ? _formatProdErrorMessage(33) : 'When using `fakeBaseQuery`, all queries & mutations must use the `queryFn` definition syntax.');\n  };\n}", "import type { BaseQueryFn } from '../../baseQueryTypes';\nimport type { QueryDefinition } from '../../endpointDefinitions';\nimport type { ConfigState, QueryCacheKey } from '../apiState';\nimport type { QueryStateMeta, SubMiddlewareApi, TimeoutId, InternalHandlerBuilder, ApiMiddlewareInternalHandler, InternalMiddlewareState } from './types';\nexport type ReferenceCacheCollection = never;\n\nfunction isObjectEmpty(obj: Record<any, any>) {\n  // Apparently a for..in loop is faster than `Object.keys()` here:\n  // https://stackoverflow.com/a/59787784/62937\n  for (let k in obj) {\n    // If there is at least one key, it's not empty\n    return false;\n  }\n\n  return true;\n}\n\ndeclare module '../../endpointDefinitions' {\n  interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    /**\n     * Overrides the api-wide definition of `keepUnusedDataFor` for this endpoint only. _(This value is in seconds.)_\n     *\n     * This is how long RTK Query will keep your data cached for **after** the last component unsubscribes. For example, if you query an endpoint, then unmount the component, then mount another component that makes the same request within the given time frame, the most recent value will be served from the cache.\n     */\n    keepUnusedDataFor?: number;\n  }\n} // Per https://developer.mozilla.org/en-US/docs/Web/API/setTimeout#maximum_delay_value , browsers store\n// `setTimeout()` timer values in a 32-bit int. If we pass a value in that's larger than that,\n// it wraps and ends up executing immediately.\n// Our `keepUnusedDataFor` values are in seconds, so adjust the numbers here accordingly.\n\nexport const THIRTY_TWO_BIT_MAX_INT = 2_147_483_647;\nexport const THIRTY_TWO_BIT_MAX_TIMER_SECONDS = 2_147_483_647 / 1_000 - 1;\nexport const buildCacheCollectionHandler: InternalHandlerBuilder = ({\n  reducerPath,\n  api,\n  context,\n  internalState\n}) => {\n  const {\n    removeQueryResult,\n    unsubscribeQueryResult\n  } = api.internalActions;\n\n  function anySubscriptionsRemainingForKey(queryCacheKey: string) {\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    return !!subscriptions && !isObjectEmpty(subscriptions);\n  }\n\n  const currentRemovalTimeouts: QueryStateMeta<TimeoutId> = {};\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi, internalState) => {\n    if (unsubscribeQueryResult.match(action)) {\n      const state = mwApi.getState()[reducerPath];\n      const {\n        queryCacheKey\n      } = action.payload;\n      handleUnsubscribe(queryCacheKey, state.queries[queryCacheKey]?.endpointName, mwApi, state.config);\n    }\n\n    if (api.util.resetApiState.match(action)) {\n      for (const [key, timeout] of Object.entries(currentRemovalTimeouts)) {\n        if (timeout) clearTimeout(timeout);\n        delete currentRemovalTimeouts[key];\n      }\n    }\n\n    if (context.hasRehydrationInfo(action)) {\n      const state = mwApi.getState()[reducerPath];\n      const {\n        queries\n      } = context.extractRehydrationInfo(action)!;\n\n      for (const [queryCacheKey, queryState] of Object.entries(queries)) {\n        // Gotcha:\n        // If rehydrating before the endpoint has been injected,the global `keepUnusedDataFor`\n        // will be used instead of the endpoint-specific one.\n        handleUnsubscribe((queryCacheKey as QueryCacheKey), queryState?.endpointName, mwApi, state.config);\n      }\n    }\n  };\n\n  function handleUnsubscribe(queryCacheKey: QueryCacheKey, endpointName: string | undefined, api: SubMiddlewareApi, config: ConfigState<string>) {\n    const endpointDefinition = (context.endpointDefinitions[endpointName!] as QueryDefinition<any, any, any, any>);\n    const keepUnusedDataFor = endpointDefinition?.keepUnusedDataFor ?? config.keepUnusedDataFor;\n\n    if (keepUnusedDataFor === Infinity) {\n      // Hey, user said keep this forever!\n      return;\n    } // Prevent `setTimeout` timers from overflowing a 32-bit internal int, by\n    // clamping the max value to be at most 1000ms less than the 32-bit max.\n    // Look, a 24.8-day keepalive ought to be enough for anybody, right? :)\n    // Also avoid negative values too.\n\n\n    const finalKeepUnusedDataFor = Math.max(0, Math.min(keepUnusedDataFor, THIRTY_TWO_BIT_MAX_TIMER_SECONDS));\n\n    if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\n      const currentTimeout = currentRemovalTimeouts[queryCacheKey];\n\n      if (currentTimeout) {\n        clearTimeout(currentTimeout);\n      }\n\n      currentRemovalTimeouts[queryCacheKey] = setTimeout(() => {\n        if (!anySubscriptionsRemainingForKey(queryCacheKey)) {\n          api.dispatch(removeQueryResult({\n            queryCacheKey\n          }));\n        }\n\n        delete currentRemovalTimeouts![queryCacheKey];\n      }, finalKeepUnusedDataFor * 1000);\n    }\n  }\n\n  return handler;\n};", "import { isAnyOf, isFulfilled, isRejected, isRejectedWithValue } from '../rtkImports';\nimport type { EndpointDefinitions, FullTagDescription } from '../../endpointDefinitions';\nimport { calculateProvidedBy } from '../../endpointDefinitions';\nimport type { CombinedState, QueryCacheKey } from '../apiState';\nimport { QueryStatus } from '../apiState';\nimport { calculateProvidedByThunk } from '../buildThunks';\nimport type { SubMiddlewareApi, InternalHandlerBuilder, ApiMiddlewareInternalHandler, InternalMiddlewareState } from './types';\nimport { countObjectKeys } from '../../utils/countObjectKeys';\nexport const buildInvalidationByTagsHandler: InternalHandlerBuilder = ({\n  reducerPath,\n  context,\n  context: {\n    endpointDefinitions\n  },\n  mutationThunk,\n  queryThunk,\n  api,\n  assertTagType,\n  refetchQuery,\n  internalState\n}) => {\n  const {\n    removeQueryResult\n  } = api.internalActions;\n  const isThunkActionWithTags = isAnyOf(isFulfilled(mutationThunk), isRejectedWithValue(mutationThunk));\n  const isQueryEnd = isAnyOf(isFulfilled(mutationThunk, queryThunk), isRejected(mutationThunk, queryThunk));\n  let pendingTagInvalidations: FullTagDescription<string>[] = [];\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\n    if (isThunkActionWithTags(action)) {\n      invalidateTags(calculateProvidedByThunk(action, 'invalidatesTags', endpointDefinitions, assertTagType), mwApi);\n    } else if (isQueryEnd(action)) {\n      invalidateTags([], mwApi);\n    } else if (api.util.invalidateTags.match(action)) {\n      invalidateTags(calculateProvidedBy(action.payload, undefined, undefined, undefined, undefined, assertTagType), mwApi);\n    }\n  };\n\n  function hasPendingRequests(state: CombinedState<EndpointDefinitions, string, string>) {\n    for (const key in state.queries) {\n      if (state.queries[key]?.status === QueryStatus.pending) return true;\n    }\n\n    for (const key in state.mutations) {\n      if (state.mutations[key]?.status === QueryStatus.pending) return true;\n    }\n\n    return false;\n  }\n\n  function invalidateTags(newTags: readonly FullTagDescription<string>[], mwApi: SubMiddlewareApi) {\n    const rootState = mwApi.getState();\n    const state = rootState[reducerPath];\n    pendingTagInvalidations.push(...newTags);\n\n    if (state.config.invalidationBehavior === 'delayed' && hasPendingRequests(state)) {\n      return;\n    }\n\n    const tags = pendingTagInvalidations;\n    pendingTagInvalidations = [];\n    if (tags.length === 0) return;\n    const toInvalidate = api.util.selectInvalidatedBy(rootState, tags);\n    context.batch(() => {\n      const valuesArray = Array.from(toInvalidate.values());\n\n      for (const {\n        queryCacheKey\n      } of valuesArray) {\n        const querySubState = state.queries[queryCacheKey];\n        const subscriptionSubState = internalState.currentSubscriptions[queryCacheKey] ?? {};\n\n        if (querySubState) {\n          if (countObjectKeys(subscriptionSubState) === 0) {\n            mwApi.dispatch(removeQueryResult({\n              queryCacheKey: (queryCacheKey as QueryCacheKey)\n            }));\n          } else if (querySubState.status !== QueryStatus.uninitialized) {\n            mwApi.dispatch(refetchQuery(querySubState, queryCacheKey));\n          }\n        }\n      }\n    });\n  }\n\n  return handler;\n};", "import type { QuerySubstateIdentifier, Subscribers } from '../apiState';\nimport { QueryStatus } from '../apiState';\nimport type { QueryStateMeta, SubMiddlewareApi, TimeoutId, InternalHandlerBuilder, ApiMiddlewareInternalHandler, InternalMiddlewareState } from './types';\nexport const buildPollingHandler: InternalHandlerBuilder = ({\n  reducerPath,\n  queryThunk,\n  api,\n  refetchQuery,\n  internalState\n}) => {\n  const currentPolls: QueryStateMeta<{\n    nextPollTimestamp: number;\n    timeout?: TimeoutId;\n    pollingInterval: number;\n  }> = {};\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\n    if (api.internalActions.updateSubscriptionOptions.match(action) || api.internalActions.unsubscribeQueryResult.match(action)) {\n      updatePollingInterval(action.payload, mwApi);\n    }\n\n    if (queryThunk.pending.match(action) || queryThunk.rejected.match(action) && action.meta.condition) {\n      updatePollingInterval(action.meta.arg, mwApi);\n    }\n\n    if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action) && !action.meta.condition) {\n      startNextPoll(action.meta.arg, mwApi);\n    }\n\n    if (api.util.resetApiState.match(action)) {\n      clearPolls();\n    }\n  };\n\n  function startNextPoll({\n    queryCacheKey\n  }: QuerySubstateIdentifier, api: SubMiddlewareApi) {\n    const state = api.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n    if (!querySubState || querySubState.status === QueryStatus.uninitialized) return;\n    const {\n      lowestPollingInterval,\n      skipPollingIfUnfocused\n    } = findLowestPollingInterval(subscriptions);\n    if (!Number.isFinite(lowestPollingInterval)) return;\n    const currentPoll = currentPolls[queryCacheKey];\n\n    if (currentPoll?.timeout) {\n      clearTimeout(currentPoll.timeout);\n      currentPoll.timeout = undefined;\n    }\n\n    const nextPollTimestamp = Date.now() + lowestPollingInterval;\n    currentPolls[queryCacheKey] = {\n      nextPollTimestamp,\n      pollingInterval: lowestPollingInterval,\n      timeout: setTimeout(() => {\n        if (state.config.focused || !skipPollingIfUnfocused) {\n          api.dispatch(refetchQuery(querySubState, queryCacheKey));\n        }\n\n        startNextPoll({\n          queryCacheKey\n        }, api);\n      }, lowestPollingInterval)\n    };\n  }\n\n  function updatePollingInterval({\n    queryCacheKey\n  }: QuerySubstateIdentifier, api: SubMiddlewareApi) {\n    const state = api.getState()[reducerPath];\n    const querySubState = state.queries[queryCacheKey];\n    const subscriptions = internalState.currentSubscriptions[queryCacheKey];\n\n    if (!querySubState || querySubState.status === QueryStatus.uninitialized) {\n      return;\n    }\n\n    const {\n      lowestPollingInterval\n    } = findLowestPollingInterval(subscriptions);\n\n    if (!Number.isFinite(lowestPollingInterval)) {\n      cleanupPollForKey(queryCacheKey);\n      return;\n    }\n\n    const currentPoll = currentPolls[queryCacheKey];\n    const nextPollTimestamp = Date.now() + lowestPollingInterval;\n\n    if (!currentPoll || nextPollTimestamp < currentPoll.nextPollTimestamp) {\n      startNextPoll({\n        queryCacheKey\n      }, api);\n    }\n  }\n\n  function cleanupPollForKey(key: string) {\n    const existingPoll = currentPolls[key];\n\n    if (existingPoll?.timeout) {\n      clearTimeout(existingPoll.timeout);\n    }\n\n    delete currentPolls[key];\n  }\n\n  function clearPolls() {\n    for (const key of Object.keys(currentPolls)) {\n      cleanupPollForKey(key);\n    }\n  }\n\n  function findLowestPollingInterval(subscribers: Subscribers = {}) {\n    let skipPollingIfUnfocused: boolean | undefined = false;\n    let lowestPollingInterval = Number.POSITIVE_INFINITY;\n\n    for (let key in subscribers) {\n      if (!!subscribers[key].pollingInterval) {\n        lowestPollingInterval = Math.min(subscribers[key].pollingInterval!, lowestPollingInterval);\n        skipPollingIfUnfocused = subscribers[key].skipPollingIfUnfocused || skipPollingIfUnfocused;\n      }\n    }\n\n    return {\n      lowestPollingInterval,\n      skipPollingIfUnfocused\n    };\n  }\n\n  return handler;\n};", "import { QueryStatus } from '../apiState';\nimport type { QueryCacheKey } from '../apiState';\nimport { onFocus, onOnline } from '../setupListeners';\nimport type { ApiMiddlewareInternalHandler, InternalHandlerBuilder, SubMiddlewareApi } from './types';\nimport { countObjectKeys } from '../../utils/countObjectKeys';\nexport const buildWindowEventHandler: InternalHandlerBuilder = ({\n  reducerPath,\n  context,\n  api,\n  refetchQuery,\n  internalState\n}) => {\n  const {\n    removeQueryResult\n  } = api.internalActions;\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\n    if (onFocus.match(action)) {\n      refetchValidQueries(mwApi, 'refetchOnFocus');\n    }\n\n    if (onOnline.match(action)) {\n      refetchValidQueries(mwApi, 'refetchOnReconnect');\n    }\n  };\n\n  function refetchValidQueries(api: SubMiddlewareApi, type: 'refetchOnFocus' | 'refetchOnReconnect') {\n    const state = api.getState()[reducerPath];\n    const queries = state.queries;\n    const subscriptions = internalState.currentSubscriptions;\n    context.batch(() => {\n      for (const queryCacheKey of Object.keys(subscriptions)) {\n        const querySubState = queries[queryCacheKey];\n        const subscriptionSubState = subscriptions[queryCacheKey];\n        if (!subscriptionSubState || !querySubState) continue;\n        const shouldRefetch = Object.values(subscriptionSubState).some(sub => sub[type] === true) || Object.values(subscriptionSubState).every(sub => sub[type] === undefined) && state.config[type];\n\n        if (shouldRefetch) {\n          if (countObjectKeys(subscriptionSubState) === 0) {\n            api.dispatch(removeQueryResult({\n              queryCacheKey: (queryCacheKey as QueryCacheKey)\n            }));\n          } else if (querySubState.status !== QueryStatus.uninitialized) {\n            api.dispatch(refetchQuery(querySubState, queryCacheKey));\n          }\n        }\n      }\n    });\n  }\n\n  return handler;\n};", "import { isAsyncThunkAction, isFulfilled } from '../rtkImports';\nimport type { UnknownAction } from 'redux';\nimport type { ThunkDispatch } from 'redux-thunk';\nimport type { BaseQueryFn, BaseQueryMeta } from '../../baseQueryTypes';\nimport { DefinitionType } from '../../endpointDefinitions';\nimport type { RootState } from '../apiState';\nimport type { MutationResultSelectorResult, QueryResultSelectorResult } from '../buildSelectors';\nimport { getMutationCacheKey } from '../buildSlice';\nimport type { PatchCollection, Recipe } from '../buildThunks';\nimport type { <PERSON>piMiddlewareInternalHandler, InternalHandlerBuilder, PromiseWithKnownReason, SubMiddlewareApi } from './types';\nexport type ReferenceCacheLifecycle = never;\ndeclare module '../../endpointDefinitions' {\n  export interface QueryBaseLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends LifecycleApi<ReducerPath> {\n    /**\n     * Gets the current value of this cache entry.\n     */\n    getCacheEntry(): QueryResultSelectorResult<{\n      type: DefinitionType.query;\n    } & BaseEndpointDefinition<QueryArg, BaseQuery, ResultType>>;\n    /**\n     * Updates the current cache entry value.\n     * For documentation see `api.util.updateQueryData`.\n     */\n\n    updateCachedData(updateRecipe: Recipe<ResultType>): PatchCollection;\n  }\n  export interface MutationBaseLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends LifecycleApi<ReducerPath> {\n    /**\n     * Gets the current value of this cache entry.\n     */\n    getCacheEntry(): MutationResultSelectorResult<{\n      type: DefinitionType.mutation;\n    } & BaseEndpointDefinition<QueryArg, BaseQuery, ResultType>>;\n  }\n  export interface LifecycleApi<ReducerPath extends string = string> {\n    /**\n     * The dispatch method for the store\n     */\n    dispatch: ThunkDispatch<any, any, UnknownAction>;\n    /**\n     * A method to get the current state\n     */\n\n    getState(): RootState<any, any, ReducerPath>;\n    /**\n     * `extra` as provided as `thunk.extraArgument` to the `configureStore` `getDefaultMiddleware` option.\n     */\n\n    extra: unknown;\n    /**\n     * A unique ID generated for the mutation\n     */\n\n    requestId: string;\n  }\n  export interface CacheLifecyclePromises<ResultType = unknown, MetaType = unknown> {\n    /**\n     * Promise that will resolve with the first value for this cache key.\n     * This allows you to `await` until an actual value is in cache.\n     *\n     * If the cache entry is removed from the cache before any value has ever\n     * been resolved, this Promise will reject with\n     * `new Error('Promise never resolved before cacheEntryRemoved.')`\n     * to prevent memory leaks.\n     * You can just re-throw that error (or not handle it at all) -\n     * it will be caught outside of `cacheEntryAdded`.\n     *\n     * If you don't interact with this promise, it will not throw.\n     */\n    cacheDataLoaded: PromiseWithKnownReason<{\n      /**\n       * The (transformed) query result.\n       */\n      data: ResultType;\n      /**\n       * The `meta` returned by the `baseQuery`\n       */\n\n      meta: MetaType;\n    }, typeof neverResolvedError>;\n    /**\n     * Promise that allows you to wait for the point in time when the cache entry\n     * has been removed from the cache, by not being used/subscribed to any more\n     * in the application for too long or by dispatching `api.util.resetApiState`.\n     */\n\n    cacheEntryRemoved: Promise<void>;\n  }\n  export interface QueryCacheLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends QueryBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>, CacheLifecyclePromises<ResultType, BaseQueryMeta<BaseQuery>> {}\n  export interface MutationCacheLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends MutationBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>, CacheLifecyclePromises<ResultType, BaseQueryMeta<BaseQuery>> {}\n  interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    onCacheEntryAdded?(arg: QueryArg, api: QueryCacheLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>): Promise<void> | void;\n  }\n  interface MutationExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    onCacheEntryAdded?(arg: QueryArg, api: MutationCacheLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>): Promise<void> | void;\n  }\n}\nconst neverResolvedError = (new Error('Promise never resolved before cacheEntryRemoved.') as Error & {\n  message: 'Promise never resolved before cacheEntryRemoved.';\n});\nexport const buildCacheLifecycleHandler: InternalHandlerBuilder = ({\n  api,\n  reducerPath,\n  context,\n  queryThunk,\n  mutationThunk,\n  internalState\n}) => {\n  const isQueryThunk = isAsyncThunkAction(queryThunk);\n  const isMutationThunk = isAsyncThunkAction(mutationThunk);\n  const isFulfilledThunk = isFulfilled(queryThunk, mutationThunk);\n  type CacheLifecycle = {\n    valueResolved?(value: {\n      data: unknown;\n      meta: unknown;\n    }): unknown;\n    cacheEntryRemoved(): void;\n  };\n  const lifecycleMap: Record<string, CacheLifecycle> = {};\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi, stateBefore) => {\n    const cacheKey = getCacheKey(action);\n\n    if (queryThunk.pending.match(action)) {\n      const oldState = stateBefore[reducerPath].queries[cacheKey];\n      const state = mwApi.getState()[reducerPath].queries[cacheKey];\n\n      if (!oldState && state) {\n        handleNewKey(action.meta.arg.endpointName, action.meta.arg.originalArgs, cacheKey, mwApi, action.meta.requestId);\n      }\n    } else if (mutationThunk.pending.match(action)) {\n      const state = mwApi.getState()[reducerPath].mutations[cacheKey];\n\n      if (state) {\n        handleNewKey(action.meta.arg.endpointName, action.meta.arg.originalArgs, cacheKey, mwApi, action.meta.requestId);\n      }\n    } else if (isFulfilledThunk(action)) {\n      const lifecycle = lifecycleMap[cacheKey];\n\n      if (lifecycle?.valueResolved) {\n        lifecycle.valueResolved({\n          data: action.payload,\n          meta: action.meta.baseQueryMeta\n        });\n        delete lifecycle.valueResolved;\n      }\n    } else if (api.internalActions.removeQueryResult.match(action) || api.internalActions.removeMutationResult.match(action)) {\n      const lifecycle = lifecycleMap[cacheKey];\n\n      if (lifecycle) {\n        delete lifecycleMap[cacheKey];\n        lifecycle.cacheEntryRemoved();\n      }\n    } else if (api.util.resetApiState.match(action)) {\n      for (const [cacheKey, lifecycle] of Object.entries(lifecycleMap)) {\n        delete lifecycleMap[cacheKey];\n        lifecycle.cacheEntryRemoved();\n      }\n    }\n  };\n\n  function getCacheKey(action: any) {\n    if (isQueryThunk(action)) return action.meta.arg.queryCacheKey;\n\n    if (isMutationThunk(action)) {\n      return action.meta.arg.fixedCacheKey ?? action.meta.requestId;\n    }\n\n    if (api.internalActions.removeQueryResult.match(action)) return action.payload.queryCacheKey;\n    if (api.internalActions.removeMutationResult.match(action)) return getMutationCacheKey(action.payload);\n    return '';\n  }\n\n  function handleNewKey(endpointName: string, originalArgs: any, queryCacheKey: string, mwApi: SubMiddlewareApi, requestId: string) {\n    const endpointDefinition = context.endpointDefinitions[endpointName];\n    const onCacheEntryAdded = endpointDefinition?.onCacheEntryAdded;\n    if (!onCacheEntryAdded) return;\n    let lifecycle = ({} as CacheLifecycle);\n    const cacheEntryRemoved = new Promise<void>(resolve => {\n      lifecycle.cacheEntryRemoved = resolve;\n    });\n    const cacheDataLoaded: PromiseWithKnownReason<{\n      data: unknown;\n      meta: unknown;\n    }, typeof neverResolvedError> = Promise.race([new Promise<{\n      data: unknown;\n      meta: unknown;\n    }>(resolve => {\n      lifecycle.valueResolved = resolve;\n    }), cacheEntryRemoved.then(() => {\n      throw neverResolvedError;\n    })]); // prevent uncaught promise rejections from happening.\n    // if the original promise is used in any way, that will create a new promise that will throw again\n\n    cacheDataLoaded.catch(() => {});\n    lifecycleMap[queryCacheKey] = lifecycle;\n    const selector = (api.endpoints[endpointName] as any).select(endpointDefinition.type === DefinitionType.query ? originalArgs : queryCacheKey);\n    const extra = mwApi.dispatch((_, __, extra) => extra);\n    const lifecycleApi = { ...mwApi,\n      getCacheEntry: () => selector(mwApi.getState()),\n      requestId,\n      extra,\n      updateCachedData: ((endpointDefinition.type === DefinitionType.query ? (updateRecipe: Recipe<any>) => mwApi.dispatch(api.util.updateQueryData((endpointName as never), originalArgs, updateRecipe)) : undefined) as any),\n      cacheDataLoaded,\n      cacheEntryRemoved\n    };\n    const runningHandler = onCacheEntryAdded(originalArgs, lifecycleApi); // if a `neverResolvedError` was thrown, but not handled in the running handler, do not let it leak out further\n\n    Promise.resolve(runningHandler).catch(e => {\n      if (e === neverResolvedError) return;\n      throw e;\n    });\n  }\n\n  return handler;\n};", "import { isPending, isRejected, isFulfilled } from '../rtkImports';\nimport type { BaseQueryError, BaseQueryFn, BaseQueryMeta } from '../../baseQueryTypes';\nimport { DefinitionType } from '../../endpointDefinitions';\nimport type { QueryFulfilledRejectionReason } from '../../endpointDefinitions';\nimport type { Recipe } from '../buildThunks';\nimport type { PromiseWithKnownReason, PromiseConstructorWithKnownReason, InternalHandlerBuilder, ApiMiddlewareInternalHandler } from './types';\nexport type ReferenceQueryLifecycle = never;\ndeclare module '../../endpointDefinitions' {\n  export interface QueryLifecyclePromises<ResultType, BaseQuery extends BaseQueryFn> {\n    /**\n     * Promise that will resolve with the (transformed) query result.\n     *\n     * If the query fails, this promise will reject with the error.\n     *\n     * This allows you to `await` for the query to finish.\n     *\n     * If you don't interact with this promise, it will not throw.\n     */\n    queryFulfilled: PromiseWithKnownReason<{\n      /**\n       * The (transformed) query result.\n       */\n      data: ResultType;\n      /**\n       * The `meta` returned by the `baseQuery`\n       */\n\n      meta: BaseQueryMeta<BaseQuery>;\n    }, QueryFulfilledRejectionReason<BaseQuery>>;\n  }\n  type QueryFulfilledRejectionReason<BaseQuery extends BaseQueryFn> = {\n    error: BaseQueryError<BaseQuery>;\n    /**\n     * If this is `false`, that means this error was returned from the `baseQuery` or `queryFn` in a controlled manner.\n     */\n\n    isUnhandledError: false;\n    /**\n     * The `meta` returned by the `baseQuery`\n     */\n\n    meta: BaseQueryMeta<BaseQuery>;\n  } | {\n    error: unknown;\n    meta?: undefined;\n    /**\n     * If this is `true`, that means that this error is the result of `baseQueryFn`, `queryFn`, `transformResponse` or `transformErrorResponse` throwing an error instead of handling it properly.\n     * There can not be made any assumption about the shape of `error`.\n     */\n\n    isUnhandledError: true;\n  };\n  interface QueryExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    /**\n     * A function that is called when the individual query is started. The function is called with a lifecycle api object containing properties such as `queryFulfilled`, allowing code to be run when a query is started, when it succeeds, and when it fails (i.e. throughout the lifecycle of an individual query/mutation call).\n     *\n     * Can be used to perform side-effects throughout the lifecycle of the query.\n     *\n     * @example\n     * ```ts\n     * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\n     * import { messageCreated } from './notificationsSlice\n     * export interface Post {\n     *   id: number\n     *   name: string\n     * }\n     *\n     * const api = createApi({\n     *   baseQuery: fetchBaseQuery({\n     *     baseUrl: '/',\n     *   }),\n     *   endpoints: (build) => ({\n     *     getPost: build.query<Post, number>({\n     *       query: (id) => `post/${id}`,\n     *       async onQueryStarted(id, { dispatch, queryFulfilled }) {\n     *         // `onStart` side-effect\n     *         dispatch(messageCreated('Fetching posts...'))\n     *         try {\n     *           const { data } = await queryFulfilled\n     *           // `onSuccess` side-effect\n     *           dispatch(messageCreated('Posts received!'))\n     *         } catch (err) {\n     *           // `onError` side-effect\n     *           dispatch(messageCreated('Error fetching posts!'))\n     *         }\n     *       }\n     *     }),\n     *   }),\n     * })\n     * ```\n     */\n    onQueryStarted?(arg: QueryArg, api: QueryLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>): Promise<void> | void;\n  }\n  interface MutationExtraOptions<TagTypes extends string, ResultType, QueryArg, BaseQuery extends BaseQueryFn, ReducerPath extends string = string> {\n    /**\n     * A function that is called when the individual mutation is started. The function is called with a lifecycle api object containing properties such as `queryFulfilled`, allowing code to be run when a query is started, when it succeeds, and when it fails (i.e. throughout the lifecycle of an individual query/mutation call).\n     *\n     * Can be used for `optimistic updates`.\n     *\n     * @example\n     *\n     * ```ts\n     * import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query'\n     * export interface Post {\n     *   id: number\n     *   name: string\n     * }\n     *\n     * const api = createApi({\n     *   baseQuery: fetchBaseQuery({\n     *     baseUrl: '/',\n     *   }),\n     *   tagTypes: ['Post'],\n     *   endpoints: (build) => ({\n     *     getPost: build.query<Post, number>({\n     *       query: (id) => `post/${id}`,\n     *       providesTags: ['Post'],\n     *     }),\n     *     updatePost: build.mutation<void, Pick<Post, 'id'> & Partial<Post>>({\n     *       query: ({ id, ...patch }) => ({\n     *         url: `post/${id}`,\n     *         method: 'PATCH',\n     *         body: patch,\n     *       }),\n     *       invalidatesTags: ['Post'],\n     *       async onQueryStarted({ id, ...patch }, { dispatch, queryFulfilled }) {\n     *         const patchResult = dispatch(\n     *           api.util.updateQueryData('getPost', id, (draft) => {\n     *             Object.assign(draft, patch)\n     *           })\n     *         )\n     *         try {\n     *           await queryFulfilled\n     *         } catch {\n     *           patchResult.undo()\n     *         }\n     *       },\n     *     }),\n     *   }),\n     * })\n     * ```\n     */\n    onQueryStarted?(arg: QueryArg, api: MutationLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>): Promise<void> | void;\n  }\n  export interface QueryLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends QueryBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>, QueryLifecyclePromises<ResultType, BaseQuery> {}\n  export interface MutationLifecycleApi<QueryArg, BaseQuery extends BaseQueryFn, ResultType, ReducerPath extends string = string> extends MutationBaseLifecycleApi<QueryArg, BaseQuery, ResultType, ReducerPath>, QueryLifecyclePromises<ResultType, BaseQuery> {}\n}\nexport const buildQueryLifecycleHandler: InternalHandlerBuilder = ({\n  api,\n  context,\n  queryThunk,\n  mutationThunk\n}) => {\n  const isPendingThunk = isPending(queryThunk, mutationThunk);\n  const isRejectedThunk = isRejected(queryThunk, mutationThunk);\n  const isFullfilledThunk = isFulfilled(queryThunk, mutationThunk);\n  type CacheLifecycle = {\n    resolve(value: {\n      data: unknown;\n      meta: unknown;\n    }): unknown;\n    reject(value: QueryFulfilledRejectionReason<any>): unknown;\n  };\n  const lifecycleMap: Record<string, CacheLifecycle> = {};\n\n  const handler: ApiMiddlewareInternalHandler = (action, mwApi) => {\n    if (isPendingThunk(action)) {\n      const {\n        requestId,\n        arg: {\n          endpointName,\n          originalArgs\n        }\n      } = action.meta;\n      const endpointDefinition = context.endpointDefinitions[endpointName];\n      const onQueryStarted = endpointDefinition?.onQueryStarted;\n\n      if (onQueryStarted) {\n        const lifecycle = ({} as CacheLifecycle);\n        const queryFulfilled = new (Promise as PromiseConstructorWithKnownReason)<{\n          data: unknown;\n          meta: unknown;\n        }, QueryFulfilledRejectionReason<any>>((resolve, reject) => {\n          lifecycle.resolve = resolve;\n          lifecycle.reject = reject;\n        }); // prevent uncaught promise rejections from happening.\n        // if the original promise is used in any way, that will create a new promise that will throw again\n\n        queryFulfilled.catch(() => {});\n        lifecycleMap[requestId] = lifecycle;\n        const selector = (api.endpoints[endpointName] as any).select(endpointDefinition.type === DefinitionType.query ? originalArgs : requestId);\n        const extra = mwApi.dispatch((_, __, extra) => extra);\n        const lifecycleApi = { ...mwApi,\n          getCacheEntry: () => selector(mwApi.getState()),\n          requestId,\n          extra,\n          updateCachedData: ((endpointDefinition.type === DefinitionType.query ? (updateRecipe: Recipe<any>) => mwApi.dispatch(api.util.updateQueryData((endpointName as never), originalArgs, updateRecipe)) : undefined) as any),\n          queryFulfilled\n        };\n        onQueryStarted(originalArgs, lifecycleApi);\n      }\n    } else if (isFullfilledThunk(action)) {\n      const {\n        requestId,\n        baseQueryMeta\n      } = action.meta;\n      lifecycleMap[requestId]?.resolve({\n        data: action.payload,\n        meta: baseQueryMeta\n      });\n      delete lifecycleMap[requestId];\n    } else if (isRejectedThunk(action)) {\n      const {\n        requestId,\n        rejectedWithValue,\n        baseQueryMeta\n      } = action.meta;\n      lifecycleMap[requestId]?.reject({\n        error: action.payload ?? action.error,\n        isUnhandledError: !rejectedWithValue,\n        meta: (baseQueryMeta as any)\n      });\n      delete lifecycleMap[requestId];\n    }\n  };\n\n  return handler;\n};", "import type { InternalHandlerBuilder } from './types';\nexport const buildDevCheckHandler: InternalHandlerBuilder = ({\n  api,\n  context: {\n    apiUid\n  },\n  reducerPath\n}) => {\n  return (action, mwApi) => {\n    if (api.util.resetApiState.match(action)) {\n      // dispatch after api reset\n      mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\n    }\n\n    if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n      if (api.internalActions.middlewareRegistered.match(action) && action.payload === apiUid && mwApi.getState()[reducerPath]?.config?.middlewareRegistered === 'conflict') {\n        console.warn(`There is a mismatch between slice and middleware for the reducerPath \"${reducerPath}\".\nYou can only have one api per reducer path, this will lead to crashes in various situations!${reducerPath === 'api' ? `\nIf you have multiple apis, you *have* to specify the reducerPath option when using createApi!` : ''}`);\n      }\n    }\n  };\n};", "import type { InternalHandlerBuilder, SubscriptionSelectors } from './types';\nimport type { SubscriptionState } from '../apiState';\nimport { produceWithPatches } from 'immer';\nimport type { Action } from '@reduxjs/toolkit';\nimport { countObjectKeys } from '../../utils/countObjectKeys';\nexport const buildBatchedActionsHandler: InternalHandlerBuilder<[actionShouldContinue: boolean, returnValue: SubscriptionSelectors | boolean]> = ({\n  api,\n  queryThunk,\n  internalState\n}) => {\n  const subscriptionsPrefix = `${api.reducerPath}/subscriptions`;\n  let previousSubscriptions: SubscriptionState = ((null as unknown) as SubscriptionState);\n  let updateSyncTimer: ReturnType<typeof window.setTimeout> | null = null;\n  const {\n    updateSubscriptionOptions,\n    unsubscribeQueryResult\n  } = api.internalActions; // Actually intentionally mutate the subscriptions state used in the middleware\n  // This is done to speed up perf when loading many components\n\n  const actuallyMutateSubscriptions = (mutableState: SubscriptionState, action: Action) => {\n    if (updateSubscriptionOptions.match(action)) {\n      const {\n        queryCacheKey,\n        requestId,\n        options\n      } = action.payload;\n\n      if (mutableState?.[queryCacheKey]?.[requestId]) {\n        mutableState[queryCacheKey]![requestId] = options;\n      }\n\n      return true;\n    }\n\n    if (unsubscribeQueryResult.match(action)) {\n      const {\n        queryCacheKey,\n        requestId\n      } = action.payload;\n\n      if (mutableState[queryCacheKey]) {\n        delete mutableState[queryCacheKey]![requestId];\n      }\n\n      return true;\n    }\n\n    if (api.internalActions.removeQueryResult.match(action)) {\n      delete mutableState[action.payload.queryCacheKey];\n      return true;\n    }\n\n    if (queryThunk.pending.match(action)) {\n      const {\n        meta: {\n          arg,\n          requestId\n        }\n      } = action;\n      const substate = mutableState[arg.queryCacheKey] ??= {};\n      substate[`${requestId}_running`] = {};\n\n      if (arg.subscribe) {\n        substate[requestId] = arg.subscriptionOptions ?? substate[requestId] ?? {};\n      }\n\n      return true;\n    }\n\n    let mutated = false;\n\n    if (queryThunk.fulfilled.match(action) || queryThunk.rejected.match(action)) {\n      const state = mutableState[action.meta.arg.queryCacheKey] || {};\n      const key = `${action.meta.requestId}_running`;\n      mutated ||= !!state[key];\n      delete state[key];\n    }\n\n    if (queryThunk.rejected.match(action)) {\n      const {\n        meta: {\n          condition,\n          arg,\n          requestId\n        }\n      } = action;\n\n      if (condition && arg.subscribe) {\n        const substate = mutableState[arg.queryCacheKey] ??= {};\n        substate[requestId] = arg.subscriptionOptions ?? substate[requestId] ?? {};\n        mutated = true;\n      }\n    }\n\n    return mutated;\n  };\n\n  const getSubscriptions = () => internalState.currentSubscriptions;\n\n  const getSubscriptionCount = (queryCacheKey: string) => {\n    const subscriptions = getSubscriptions();\n    const subscriptionsForQueryArg = subscriptions[queryCacheKey] ?? {};\n    return countObjectKeys(subscriptionsForQueryArg);\n  };\n\n  const isRequestSubscribed = (queryCacheKey: string, requestId: string) => {\n    const subscriptions = getSubscriptions();\n    return !!subscriptions?.[queryCacheKey]?.[requestId];\n  };\n\n  const subscriptionSelectors: SubscriptionSelectors = {\n    getSubscriptions,\n    getSubscriptionCount,\n    isRequestSubscribed\n  };\n  return (action, mwApi): [actionShouldContinue: boolean, result: SubscriptionSelectors | boolean] => {\n    if (!previousSubscriptions) {\n      // Initialize it the first time this handler runs\n      previousSubscriptions = JSON.parse(JSON.stringify(internalState.currentSubscriptions));\n    }\n\n    if (api.util.resetApiState.match(action)) {\n      previousSubscriptions = internalState.currentSubscriptions = {};\n      updateSyncTimer = null;\n      return [true, false];\n    } // Intercept requests by hooks to see if they're subscribed\n    // We return the internal state reference so that hooks\n    // can do their own checks to see if they're still active.\n    // It's stupid and hacky, but it does cut down on some dispatch calls.\n\n\n    if (api.internalActions.internal_getRTKQSubscriptions.match(action)) {\n      return [false, subscriptionSelectors];\n    } // Update subscription data based on this action\n\n\n    const didMutate = actuallyMutateSubscriptions(internalState.currentSubscriptions, action);\n    let actionShouldContinue = true;\n\n    if (didMutate) {\n      if (!updateSyncTimer) {\n        // We only use the subscription state for the Redux DevTools at this point,\n        // as the real data is kept here in the middleware.\n        // Given that, we can throttle synchronizing this state significantly to\n        // save on overall perf.\n        // In 1.9, it was updated in a microtask, but now we do it at most every 500ms.\n        updateSyncTimer = setTimeout(() => {\n          // Deep clone the current subscription data\n          const newSubscriptions: SubscriptionState = JSON.parse(JSON.stringify(internalState.currentSubscriptions)); // Figure out a smaller diff between original and current\n\n          const [, patches] = produceWithPatches(previousSubscriptions, () => newSubscriptions); // Sync the store state for visibility\n\n          mwApi.next(api.internalActions.subscriptionsUpdated(patches)); // Save the cloned state for later reference\n\n          previousSubscriptions = newSubscriptions;\n          updateSyncTimer = null;\n        }, 500);\n      }\n\n      const isSubscriptionSliceAction = typeof action.type == 'string' && !!action.type.startsWith(subscriptionsPrefix);\n      const isAdditionalSubscriptionAction = queryThunk.rejected.match(action) && action.meta.condition && !!action.meta.arg.subscribe;\n      actionShouldContinue = !isSubscriptionSliceAction && !isAdditionalSubscriptionAction;\n    }\n\n    return [actionShouldContinue, false];\n  };\n};", "import type { Action, Middleware, ThunkDispatch, UnknownAction } from '@reduxjs/toolkit';\nimport { isAction, createAction } from '../rtkImports';\nimport type { EndpointDefinitions, FullTagDescription } from '../../endpointDefinitions';\nimport type { QueryStatus, QuerySubState, RootState } from '../apiState';\nimport type { QueryThunkArg } from '../buildThunks';\nimport { buildCacheCollectionHandler } from './cacheCollection';\nimport { buildInvalidationByTagsHandler } from './invalidationByTags';\nimport { buildPollingHandler } from './polling';\nimport type { BuildMiddlewareInput, InternalHandlerBuilder, InternalMiddlewareState } from './types';\nimport { buildWindowEventHandler } from './windowEventHandling';\nimport { buildCacheLifecycleHandler } from './cacheLifecycle';\nimport { buildQueryLifecycleHandler } from './queryLifecycle';\nimport { buildDevCheckHandler } from './devMiddleware';\nimport { buildBatchedActionsHandler } from './batchActions';\nexport function buildMiddleware<Definitions extends EndpointDefinitions, ReducerPath extends string, TagTypes extends string>(input: BuildMiddlewareInput<Definitions, ReducerPath, TagTypes>) {\n  const {\n    reducerPath,\n    queryThunk,\n    api,\n    context\n  } = input;\n  const {\n    apiUid\n  } = context;\n  const actions = {\n    invalidateTags: createAction<Array<TagTypes | FullTagDescription<TagTypes>>>(`${reducerPath}/invalidateTags`)\n  };\n\n  const isThisApiSliceAction = (action: Action) => action.type.startsWith(`${reducerPath}/`);\n\n  const handlerBuilders: InternalHandlerBuilder[] = [buildDevCheckHandler, buildCacheCollectionHandler, buildInvalidationByTagsHandler, buildPollingHandler, buildCacheLifecycleHandler, buildQueryLifecycleHandler];\n\n  const middleware: Middleware<{}, RootState<Definitions, string, ReducerPath>, ThunkDispatch<any, any, UnknownAction>> = mwApi => {\n    let initialized = false;\n    let internalState: InternalMiddlewareState = {\n      currentSubscriptions: {}\n    };\n    const builderArgs = { ...((input as any) as BuildMiddlewareInput<EndpointDefinitions, string, string>),\n      internalState,\n      refetchQuery,\n      isThisApiSliceAction\n    };\n    const handlers = handlerBuilders.map(build => build(builderArgs));\n    const batchedActionsHandler = buildBatchedActionsHandler(builderArgs);\n    const windowEventsHandler = buildWindowEventHandler(builderArgs);\n    return next => {\n      return action => {\n        if (!isAction(action)) {\n          return next(action);\n        }\n\n        if (!initialized) {\n          initialized = true; // dispatch before any other action\n\n          mwApi.dispatch(api.internalActions.middlewareRegistered(apiUid));\n        }\n\n        const mwApiWithNext = { ...mwApi,\n          next\n        };\n        const stateBefore = mwApi.getState();\n        const [actionShouldContinue, internalProbeResult] = batchedActionsHandler(action, mwApiWithNext, stateBefore);\n        let res: any;\n\n        if (actionShouldContinue) {\n          res = next(action);\n        } else {\n          res = internalProbeResult;\n        }\n\n        if (!!mwApi.getState()[reducerPath]) {\n          // Only run these checks if the middleware is registered okay\n          // This looks for actions that aren't specific to the API slice\n          windowEventsHandler(action, mwApiWithNext, stateBefore);\n\n          if (isThisApiSliceAction(action) || context.hasRehydrationInfo(action)) {\n            // Only run these additional checks if the actions are part of the API slice,\n            // or the action has hydration-related data\n            for (let handler of handlers) {\n              handler(action, mwApiWithNext, stateBefore);\n            }\n          }\n        }\n\n        return res;\n      };\n    };\n  };\n\n  return {\n    middleware,\n    actions\n  };\n\n  function refetchQuery(querySubState: Exclude<QuerySubState<any>, {\n    status: QueryStatus.uninitialized;\n  }>, queryCacheKey: string, override: Partial<QueryThunkArg> = {}) {\n    return queryThunk({\n      type: 'query',\n      endpointName: querySubState.endpointName,\n      originalArgs: querySubState.originalArgs,\n      subscribe: false,\n      forceRefetch: true,\n      queryCacheKey: (queryCacheKey as any),\n      ...override\n    });\n  }\n}", "export type Id<T> = { [K in keyof T]: T[K] } & {};\nexport type WithRequiredProp<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>;\nexport type Override<T1, T2> = T2 extends any ? Omit<T1, keyof T2> & T2 : never;\nexport function assertCast<T>(v: any): asserts v is T {}\nexport function safeAssign<T extends object>(target: T, ...args: Array<Partial<NoInfer<T>>>): T {\n  return Object.assign(target, ...args);\n}\n/**\n * Convert a Union type `(A|B)` to an intersection type `(A&B)`\n */\n\nexport type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (k: infer I) => void ? I : never;\nexport type NonOptionalKeys<T> = { [K in keyof T]-?: undefined extends T[K] ? never : K }[keyof T];\nexport type HasRequiredProps<T, True, False> = NonOptionalKeys<T> extends never ? False : True;\nexport type OptionalIfAllPropsOptional<T> = HasRequiredProps<T, T, T | never>;\nexport type NoInfer<T> = [T][T extends any ? 0 : never];\nexport type NonUndefined<T> = T extends undefined ? never : T;\nexport type UnwrapPromise<T> = T extends PromiseLike<infer V> ? V : T;\nexport type MaybePromise<T> = T | PromiseLike<T>;\nexport type OmitFromUnion<T, K extends keyof T> = T extends any ? Omit<T, K> : never;\nexport type IsAny<T, True, False = never> = true | false extends (T extends never ? true : false) ? True : False;\nexport type CastAny<T, CastTo> = IsAny<T, CastTo, T>;", "/**\n * Note: this file should import all other files for type discovery and declaration merging\n */\nimport type { PatchQueryDataThunk, UpdateQueryDataThunk, UpsertQueryDataThunk } from './buildThunks';\nimport { buildThunks } from './buildThunks';\nimport type { ActionCreatorWithPayload, Middleware, Reducer, ThunkAction, ThunkDispatch, UnknownAction } from '@reduxjs/toolkit';\nimport type { EndpointDefinitions, QueryArgFrom, QueryDefinition, MutationDefinition, AssertTagTypes, TagDescription } from '../endpointDefinitions';\nimport { isQueryDefinition, isMutationDefinition } from '../endpointDefinitions';\nimport type { CombinedState, QueryKeys, MutationKeys, RootState } from './apiState';\nimport type { Api, Module } from '../apiTypes';\nimport { onFocus, onFocusLost, onOnline, onOffline } from './setupListeners';\nimport { buildSlice } from './buildSlice';\nimport { buildMiddleware } from './buildMiddleware';\nimport { buildSelectors } from './buildSelectors';\nimport type { MutationActionCreatorResult, QueryActionCreatorResult } from './buildInitiate';\nimport { buildInitiate } from './buildInitiate';\nimport { assertCast, safeAssign } from '../tsHelpers';\nimport type { InternalSerializeQueryArgs } from '../defaultSerializeQueryArgs';\nimport type { SliceActions } from './buildSlice';\nimport type { BaseQueryFn } from '../baseQueryTypes';\nimport type { ReferenceCacheLifecycle } from './buildMiddleware/cacheLifecycle';\nimport type { ReferenceQueryLifecycle } from './buildMiddleware/queryLifecycle';\nimport type { ReferenceCacheCollection } from './buildMiddleware/cacheCollection';\nimport { enablePatches } from 'immer';\nimport { createSelector as _createSelector } from './rtkImports';\n/**\n * `ifOlderThan` - (default: `false` | `number`) - _number is value in seconds_\n * - If specified, it will only run the query if the difference between `new Date()` and the last `fulfilledTimeStamp` is greater than the given value\n *\n * @overloadSummary\n * `force`\n * - If `force: true`, it will ignore the `ifOlderThan` value if it is set and the query will be run even if it exists in the cache.\n */\n\nexport type PrefetchOptions = {\n  ifOlderThan?: false | number;\n} | {\n  force?: boolean;\n};\nexport const coreModuleName = /* @__PURE__ */Symbol();\nexport type CoreModule = typeof coreModuleName | ReferenceCacheLifecycle | ReferenceQueryLifecycle | ReferenceCacheCollection;\nexport interface ThunkWithReturnValue<T> extends ThunkAction<T, any, any, UnknownAction> {}\ndeclare module '../apiTypes' {\n  export interface ApiModules< // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  BaseQuery extends BaseQueryFn, Definitions extends EndpointDefinitions, ReducerPath extends string, TagTypes extends string> {\n    [coreModuleName]: {\n      /**\n       * This api's reducer should be mounted at `store[api.reducerPath]`.\n       *\n       * @example\n       * ```ts\n       * configureStore({\n       *   reducer: {\n       *     [api.reducerPath]: api.reducer,\n       *   },\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\n       * })\n       * ```\n       */\n      reducerPath: ReducerPath;\n      /**\n       * Internal actions not part of the public API. Note: These are subject to change at any given time.\n       */\n\n      internalActions: InternalActions;\n      /**\n       *  A standard redux reducer that enables core functionality. Make sure it's included in your store.\n       *\n       * @example\n       * ```ts\n       * configureStore({\n       *   reducer: {\n       *     [api.reducerPath]: api.reducer,\n       *   },\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\n       * })\n       * ```\n       */\n\n      reducer: Reducer<CombinedState<Definitions, TagTypes, ReducerPath>, UnknownAction>;\n      /**\n       * This is a standard redux middleware and is responsible for things like polling, garbage collection and a handful of other things. Make sure it's included in your store.\n       *\n       * @example\n       * ```ts\n       * configureStore({\n       *   reducer: {\n       *     [api.reducerPath]: api.reducer,\n       *   },\n       *   middleware: (getDefaultMiddleware) => getDefaultMiddleware().concat(api.middleware),\n       * })\n       * ```\n       */\n\n      middleware: Middleware<{}, RootState<Definitions, string, ReducerPath>, ThunkDispatch<any, any, UnknownAction>>;\n      /**\n       * A collection of utility thunks for various situations.\n       */\n\n      util: {\n        /**\n         * A thunk that (if dispatched) will return a specific running query, identified\n         * by `endpointName` and `args`.\n         * If that query is not running, dispatching the thunk will result in `undefined`.\n         *\n         * Can be used to await a specific query triggered in any way,\n         * including via hook calls or manually dispatching `initiate` actions.\n         *\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\n         */\n        getRunningQueryThunk<EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, args: QueryArgFrom<Definitions[EndpointName]>): ThunkWithReturnValue<QueryActionCreatorResult<Definitions[EndpointName] & {\n          type: 'query';\n        }> | undefined>;\n        /**\n         * A thunk that (if dispatched) will return a specific running mutation, identified\n         * by `endpointName` and `fixedCacheKey` or `requestId`.\n         * If that mutation is not running, dispatching the thunk will result in `undefined`.\n         *\n         * Can be used to await a specific mutation triggered in any way,\n         * including via hook trigger functions or manually dispatching `initiate` actions.\n         *\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\n         */\n\n        getRunningMutationThunk<EndpointName extends MutationKeys<Definitions>>(endpointName: EndpointName, fixedCacheKeyOrRequestId: string): ThunkWithReturnValue<MutationActionCreatorResult<Definitions[EndpointName] & {\n          type: 'mutation';\n        }> | undefined>;\n        /**\n         * A thunk that (if dispatched) will return all running queries.\n         *\n         * Useful for SSR scenarios to await all running queries triggered in any way,\n         * including via hook calls or manually dispatching `initiate` actions.\n         *\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\n         */\n\n        getRunningQueriesThunk(): ThunkWithReturnValue<Array<QueryActionCreatorResult<any>>>;\n        /**\n         * A thunk that (if dispatched) will return all running mutations.\n         *\n         * Useful for SSR scenarios to await all running mutations triggered in any way,\n         * including via hook calls or manually dispatching `initiate` actions.\n         *\n         * See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for details.\n         */\n\n        getRunningMutationsThunk(): ThunkWithReturnValue<Array<MutationActionCreatorResult<any>>>;\n        /**\n         * A Redux thunk that can be used to manually trigger pre-fetching of data.\n         *\n         * The thunk accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and a set of options used to determine if the data actually should be re-fetched based on cache staleness.\n         *\n         * React Hooks users will most likely never need to use this directly, as the `usePrefetch` hook will dispatch this thunk internally as needed when you call the prefetching function supplied by the hook.\n         *\n         * @example\n         *\n         * ```ts no-transpile\n         * dispatch(api.util.prefetch('getPosts', undefined, { force: true }))\n         * ```\n         */\n\n        prefetch<EndpointName extends QueryKeys<Definitions>>(endpointName: EndpointName, arg: QueryArgFrom<Definitions[EndpointName]>, options: PrefetchOptions): ThunkAction<void, any, any, UnknownAction>;\n        /**\n         * A Redux thunk action creator that, when dispatched, creates and applies a set of JSON diff/patch objects to the current state. This immediately updates the Redux state with those changes.\n         *\n         * The thunk action creator accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and an `updateRecipe` callback function. The callback receives an Immer-wrapped `draft` of the current state, and may modify the draft to match the expected results after the mutation completes successfully.\n         *\n         * The thunk executes _synchronously_, and returns an object containing `{patches: Patch[], inversePatches: Patch[], undo: () => void}`. The `patches` and `inversePatches` are generated using Immer's [`produceWithPatches` method](https://immerjs.github.io/immer/patches).\n         *\n         * This is typically used as the first step in implementing optimistic updates. The generated `inversePatches` can be used to revert the updates by calling `dispatch(patchQueryData(endpointName, args, inversePatches))`. Alternatively, the `undo` method can be called directly to achieve the same effect.\n         *\n         * Note that the first two arguments (`endpointName` and `args`) are used to determine which existing cache entry to update. If no existing cache entry is found, the `updateRecipe` callback will not run.\n         *\n         * @example\n         *\n         * ```ts\n         * const patchCollection = dispatch(\n         *   api.util.updateQueryData('getPosts', undefined, (draftPosts) => {\n         *     draftPosts.push({ id: 1, name: 'Teddy' })\n         *   })\n         * )\n         * ```\n         */\n\n        updateQueryData: UpdateQueryDataThunk<Definitions, RootState<Definitions, string, ReducerPath>>;\n        /**\n         * A Redux thunk action creator that, when dispatched, acts as an artificial API request to upsert a value into the cache.\n         *\n         * The thunk action creator accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and the data to upsert.\n         *\n         * If no cache entry for that cache key exists, a cache entry will be created and the data added. If a cache entry already exists, this will _overwrite_ the existing cache entry data.\n         *\n         * The thunk executes _asynchronously_, and returns a promise that resolves when the store has been updated.\n         *\n         * If dispatched while an actual request is in progress, both the upsert and request will be handled as soon as they resolve, resulting in a \"last result wins\" update behavior.\n         *\n         * @example\n         *\n         * ```ts\n         * await dispatch(\n         *   api.util.upsertQueryData('getPost', {id: 1}, {id: 1, text: \"Hello!\"})\n         * )\n         * ```\n         */\n\n        upsertQueryData: UpsertQueryDataThunk<Definitions, RootState<Definitions, string, ReducerPath>>;\n        /**\n         * A Redux thunk that applies a JSON diff/patch array to the cached data for a given query result. This immediately updates the Redux state with those changes.\n         *\n         * The thunk accepts three arguments: the name of the endpoint we are updating (such as `'getPost'`), the appropriate query arg values to construct the desired cache key, and a JSON diff/patch array as produced by Immer's `produceWithPatches`.\n         *\n         * This is typically used as the second step in implementing optimistic updates. If a request fails, the optimistically-applied changes can be reverted by dispatching `patchQueryData` with the `inversePatches` that were generated by `updateQueryData` earlier.\n         *\n         * In cases where it is desired to simply revert the previous changes, it may be preferable to call the `undo` method returned from dispatching `updateQueryData` instead.\n         *\n         * @example\n         * ```ts\n         * const patchCollection = dispatch(\n         *   api.util.updateQueryData('getPosts', undefined, (draftPosts) => {\n         *     draftPosts.push({ id: 1, name: 'Teddy' })\n         *   })\n         * )\n         *\n         * // later\n         * dispatch(\n         *   api.util.patchQueryData('getPosts', undefined, patchCollection.inversePatches)\n         * )\n         *\n         * // or\n         * patchCollection.undo()\n         * ```\n         */\n\n        patchQueryData: PatchQueryDataThunk<Definitions, RootState<Definitions, string, ReducerPath>>;\n        /**\n         * A Redux action creator that can be dispatched to manually reset the api state completely. This will immediately remove all existing cache entries, and all queries will be considered 'uninitialized'.\n         *\n         * @example\n         *\n         * ```ts\n         * dispatch(api.util.resetApiState())\n         * ```\n         */\n\n        resetApiState: SliceActions['resetApiState'];\n        /**\n         * A Redux action creator that can be used to manually invalidate cache tags for [automated re-fetching](../../usage/automated-refetching.mdx).\n         *\n         * The action creator accepts one argument: the cache tags to be invalidated. It returns an action with those tags as a payload, and the corresponding `invalidateTags` action type for the api.\n         *\n         * Dispatching the result of this action creator will [invalidate](../../usage/automated-refetching.mdx#invalidating-cache-data) the given tags, causing queries to automatically re-fetch if they are subscribed to cache data that [provides](../../usage/automated-refetching.mdx#providing-cache-data) the corresponding tags.\n         *\n         * The array of tags provided to the action creator should be in one of the following formats, where `TagType` is equal to a string provided to the [`tagTypes`](../createApi.mdx#tagtypes) property of the api:\n         *\n         * - `[TagType]`\n         * - `[{ type: TagType }]`\n         * - `[{ type: TagType, id: number | string }]`\n         *\n         * @example\n         *\n         * ```ts\n         * dispatch(api.util.invalidateTags(['Post']))\n         * dispatch(api.util.invalidateTags([{ type: 'Post', id: 1 }]))\n         * dispatch(\n         *   api.util.invalidateTags([\n         *     { type: 'Post', id: 1 },\n         *     { type: 'Post', id: 'LIST' },\n         *   ])\n         * )\n         * ```\n         */\n\n        invalidateTags: ActionCreatorWithPayload<Array<TagDescription<TagTypes>>, string>;\n        /**\n         * A function to select all `{ endpointName, originalArgs, queryCacheKey }` combinations that would be invalidated by a specific set of tags.\n         *\n         * Can be used for mutations that want to do optimistic updates instead of invalidating a set of tags, but don't know exactly what they need to update.\n         */\n\n        selectInvalidatedBy: (state: RootState<Definitions, string, ReducerPath>, tags: ReadonlyArray<TagDescription<TagTypes>>) => Array<{\n          endpointName: string;\n          originalArgs: any;\n          queryCacheKey: string;\n        }>;\n        /**\n         * A function to select all arguments currently cached for a given endpoint.\n         *\n         * Can be used for mutations that want to do optimistic updates instead of invalidating a set of tags, but don't know exactly what they need to update.\n         */\n\n        selectCachedArgsForQuery: <QueryName extends QueryKeys<Definitions>>(state: RootState<Definitions, string, ReducerPath>, queryName: QueryName) => Array<QueryArgFrom<Definitions[QueryName]>>;\n      };\n      /**\n       * Endpoints based on the input endpoints provided to `createApi`, containing `select` and `action matchers`.\n       */\n\n      endpoints: { [K in keyof Definitions]: Definitions[K] extends QueryDefinition<any, any, any, any, any> ? ApiEndpointQuery<Definitions[K], Definitions> : Definitions[K] extends MutationDefinition<any, any, any, any, any> ? ApiEndpointMutation<Definitions[K], Definitions> : never };\n    };\n  }\n}\nexport interface ApiEndpointQuery< // eslint-disable-next-line @typescript-eslint/no-unused-vars\nDefinition extends QueryDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\nDefinitions extends EndpointDefinitions> {\n  name: string;\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n\n  Types: NonNullable<Definition['Types']>;\n} // eslint-disable-next-line @typescript-eslint/no-unused-vars\n\nexport interface ApiEndpointMutation< // eslint-disable-next-line @typescript-eslint/no-unused-vars\nDefinition extends MutationDefinition<any, any, any, any, any>, // eslint-disable-next-line @typescript-eslint/no-unused-vars\nDefinitions extends EndpointDefinitions> {\n  name: string;\n  /**\n   * All of these are `undefined` at runtime, purely to be used in TypeScript declarations!\n   */\n\n  Types: NonNullable<Definition['Types']>;\n}\nexport type ListenerActions = {\n  /**\n   * Will cause the RTK Query middleware to trigger any refetchOnReconnect-related behavior\n   * @link https://rtk-query-docs.netlify.app/api/setupListeners\n   */\n  onOnline: typeof onOnline;\n  onOffline: typeof onOffline;\n  /**\n   * Will cause the RTK Query middleware to trigger any refetchOnFocus-related behavior\n   * @link https://rtk-query-docs.netlify.app/api/setupListeners\n   */\n\n  onFocus: typeof onFocus;\n  onFocusLost: typeof onFocusLost;\n};\nexport type InternalActions = SliceActions & ListenerActions;\nexport interface CoreModuleOptions {\n  /**\n   * A selector creator (usually from `reselect`, or matching the same signature)\n   */\n  createSelector?: typeof _createSelector;\n}\n/**\n * Creates a module containing the basic redux logic for use with `buildCreateApi`.\n *\n * @example\n * ```ts\n * const createBaseApi = buildCreateApi(coreModule());\n * ```\n */\n\nexport const coreModule = ({\n  createSelector = _createSelector\n}: CoreModuleOptions = {}): Module<CoreModule> => ({\n  name: coreModuleName,\n\n  init(api, {\n    baseQuery,\n    tagTypes,\n    reducerPath,\n    serializeQueryArgs,\n    keepUnusedDataFor,\n    refetchOnMountOrArgChange,\n    refetchOnFocus,\n    refetchOnReconnect,\n    invalidationBehavior\n  }, context) {\n    enablePatches();\n    assertCast<InternalSerializeQueryArgs>(serializeQueryArgs);\n\n    const assertTagType: AssertTagTypes = tag => {\n      if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {\n        if (!tagTypes.includes((tag.type as any))) {\n          console.error(`Tag type '${tag.type}' was used, but not specified in \\`tagTypes\\`!`);\n        }\n      }\n\n      return tag;\n    };\n\n    Object.assign(api, {\n      reducerPath,\n      endpoints: {},\n      internalActions: {\n        onOnline,\n        onOffline,\n        onFocus,\n        onFocusLost\n      },\n      util: {}\n    });\n    const {\n      queryThunk,\n      mutationThunk,\n      patchQueryData,\n      updateQueryData,\n      upsertQueryData,\n      prefetch,\n      buildMatchThunkActions\n    } = buildThunks({\n      baseQuery,\n      reducerPath,\n      context,\n      api,\n      serializeQueryArgs,\n      assertTagType\n    });\n    const {\n      reducer,\n      actions: sliceActions\n    } = buildSlice({\n      context,\n      queryThunk,\n      mutationThunk,\n      reducerPath,\n      assertTagType,\n      config: {\n        refetchOnFocus,\n        refetchOnReconnect,\n        refetchOnMountOrArgChange,\n        keepUnusedDataFor,\n        reducerPath,\n        invalidationBehavior\n      }\n    });\n    safeAssign(api.util, {\n      patchQueryData,\n      updateQueryData,\n      upsertQueryData,\n      prefetch,\n      resetApiState: sliceActions.resetApiState\n    });\n    safeAssign(api.internalActions, sliceActions);\n    const {\n      middleware,\n      actions: middlewareActions\n    } = buildMiddleware({\n      reducerPath,\n      context,\n      queryThunk,\n      mutationThunk,\n      api,\n      assertTagType\n    });\n    safeAssign(api.util, middlewareActions);\n    safeAssign(api, {\n      reducer: (reducer as any),\n      middleware\n    });\n    const {\n      buildQuerySelector,\n      buildMutationSelector,\n      selectInvalidatedBy,\n      selectCachedArgsForQuery\n    } = buildSelectors({\n      serializeQueryArgs: (serializeQueryArgs as any),\n      reducerPath,\n      createSelector\n    });\n    safeAssign(api.util, {\n      selectInvalidatedBy,\n      selectCachedArgsForQuery\n    });\n    const {\n      buildInitiateQuery,\n      buildInitiateMutation,\n      getRunningMutationThunk,\n      getRunningMutationsThunk,\n      getRunningQueriesThunk,\n      getRunningQueryThunk\n    } = buildInitiate({\n      queryThunk,\n      mutationThunk,\n      api,\n      serializeQueryArgs: (serializeQueryArgs as any),\n      context\n    });\n    safeAssign(api.util, {\n      getRunningMutationThunk,\n      getRunningMutationsThunk,\n      getRunningQueryThunk,\n      getRunningQueriesThunk\n    });\n    return {\n      name: coreModuleName,\n\n      injectEndpoint(endpointName, definition) {\n        const anyApi = ((api as any) as Api<any, Record<string, any>, string, string, CoreModule>);\n        anyApi.endpoints[endpointName] ??= ({} as any);\n\n        if (isQueryDefinition(definition)) {\n          safeAssign(anyApi.endpoints[endpointName], {\n            name: endpointName,\n            select: buildQuerySelector(endpointName, definition),\n            initiate: buildInitiateQuery(endpointName, definition)\n          }, buildMatchThunkActions(queryThunk, endpointName));\n        } else if (isMutationDefinition(definition)) {\n          safeAssign(anyApi.endpoints[endpointName], {\n            name: endpointName,\n            select: buildMutationSelector(),\n            initiate: buildInitiateMutation(endpointName)\n          }, buildMatchThunkActions(mutationThunk, endpointName));\n        }\n      }\n\n    };\n  }\n\n});", "import { buildCreate<PERSON><PERSON>, Create<PERSON>pi } from '../createApi';\nimport { coreModule, coreModuleName } from './module';\nconst createApi = /* @__PURE__ */buildCreateApi(coreModule());\nexport { createApi, coreModule, coreModuleName };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BO,IAAK,eAAL,CAAKA,iBAAL;AACLA,eAAA,eAAA,IAAgB;AAChBA,eAAA,SAAA,IAAU;AACVA,eAAA,WAAA,IAAY;AACZA,eAAA,UAAA,IAAW;AAJD,SAAAA;AAAA,GAAA,eAAA,CAAA,CAAA;AA+BL,SAAS,sBAAsB,QAAyC;AAC7E,SAAQ;IACN;IACA,iBAAiB,WAAW;IAC5B,WAAW,WAAW;IACtB,WAAW,WAAW;IACtB,SAAS,WAAW;;EACtB;AACF;AC5DO,SAAS,cAAc,KAAa;AACzC,SAAO,IAAI,OAAO,SAAS,EAAE,KAAK,GAAG;AACvC;ACLA,IAAM,uBAAuB,CAAC,QAAgB,IAAI,QAAQ,OAAO,EAAE;AAEnE,IAAM,sBAAsB,CAAC,QAAgB,IAAI,QAAQ,OAAO,EAAE;AAE3D,SAAS,SAAS,MAA0B,KAAiC;AAClF,MAAI,CAAC,MAAM;AACT,WAAO;EACT;AAEA,MAAI,CAAC,KAAK;AACR,WAAO;EACT;AAEA,MAAI,cAAc,GAAG,GAAG;AACtB,WAAO;EACT;AAEA,QAAM,YAAY,KAAK,SAAS,GAAG,KAAK,CAAC,IAAI,WAAW,GAAG,IAAI,MAAM;AACrE,SAAO,qBAAqB,IAAI;AAChC,QAAM,oBAAoB,GAAG;AAC7B,SAAO,GAAG,IAAI,GAAG,SAAS,GAAG,GAAG;AAClC;AClBO,IAAM,UAAU,CAAC,QAAwB,CAAC,EAAE,OAAO,GAAG,GAAG;ACDzD,SAAS,WAAW;AAEzB,SAAO,OAAO,cAAc,cAAc,OAAO,UAAU,WAAW,SAAY,OAAO,UAAU;AACrG;ACHO,SAAS,oBAA6B;AAE3C,MAAI,OAAO,aAAa,aAAa;AACnC,WAAO;EACT;AAGA,SAAO,SAAS,oBAAoB;AACtC;AEVA,IAAMC,iBAAqC;AAEpC,SAAS,0BAA0B,QAAa,QAAkB;AACvE,MAAI,WAAW,UAAU,EAAEA,eAAc,MAAM,KAAKA,eAAc,MAAM,KAAK,MAAM,QAAQ,MAAM,KAAK,MAAM,QAAQ,MAAM,IAAI;AAC5H,WAAO;EACT;AAEA,QAAM,UAAU,OAAO,KAAK,MAAM;AAClC,QAAM,UAAU,OAAO,KAAK,MAAM;AAClC,MAAI,eAAe,QAAQ,WAAW,QAAQ;AAC9C,QAAM,WAAgB,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC;AAEpD,aAAW,OAAO,SAAS;AACzB,aAAS,GAAG,IAAI,0BAA0B,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAClE,QAAI;AAAc,qBAAe,OAAO,GAAG,MAAM,SAAS,GAAG;EAC/D;AAEA,SAAO,eAAe,SAAS;AACjC;ACMA,IAAM,iBAA+B,IAAI,SAAS,MAAM,GAAG,IAAI;AAE/D,IAAM,wBAAwB,CAAC,aAAuB,SAAS,UAAU,OAAO,SAAS,UAAU;AAEnG,IAAM,2BAA2B,CAAC;;EAElC,yBAAyB,KAAK,QAAQ,IAAI,cAAc,KAAK,EAAE;;AA8C/D,SAAS,eAAe,KAAU;AAChC,MAAI,CAAC,cAAc,GAAG,GAAG;AACvB,WAAO;EACT;AAEA,QAAM,OAA4B;IAAE,GAAG;EACvC;AAEA,aAAW,CAAC,GAAG,CAAC,KAAK,OAAO,QAAQ,IAAI,GAAG;AACzC,QAAI,MAAM;AAAW,aAAO,KAAK,CAAC;EACpC;AAEA,SAAO;AACT;AA+EO,SAAS,eAAe;EAC7B;EACA,iBAAiB,CAAA,MAAK;EACtB,UAAU;EACV;EACA,oBAAoB;EACpB,kBAAkB;EAClB;EACA,SAAS;EACT,iBAAiB;EACjB,gBAAgB;EAChB,GAAG;AACL,IAAwB,CAAC,GAA0F;AACjH,MAAI,OAAO,UAAU,eAAe,YAAY,gBAAgB;AAC9D,YAAQ,KAAK,2HAA2H;EAC1I;AAEA,SAAO,OAAO,KAAK,QAAQ;AACzB,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;IACF,IAAI;AACJ,QAAI;AACJ,QAAI;MACF;MACA,UAAU,IAAI,QAAQ,iBAAiB,OAAO;MAC9C,SAAS;MACT,kBAAkB,yBAA0B;MAC5C,iBAAiB,wBAAwB;MACzC,UAAU;MACV,GAAG;IACL,IAAI,OAAO,OAAO,WAAW;MAC3B,KAAK;IACP,IAAI;AACJ,QAAI,SAAsB;MAAE,GAAG;MAC7B;MACA,GAAG;IACL;AACA,cAAU,IAAI,QAAQ,eAAe,OAAO,CAAC;AAC7C,WAAO,UAAW,MAAM,eAAe,SAAS;MAC9C;MACA;MACA;MACA;MACA;IACF,CAAC,KAAM;AAEP,UAAM,gBAAgB,CAAC,SAAc,OAAO,SAAS,aAAa,cAAc,IAAI,KAAK,MAAM,QAAQ,IAAI,KAAK,OAAO,KAAK,WAAW;AAEvI,QAAI,CAAC,OAAO,QAAQ,IAAI,cAAc,KAAK,cAAc,OAAO,IAAI,GAAG;AACrE,aAAO,QAAQ,IAAI,gBAAgB,eAAe;IACpD;AAEA,QAAI,cAAc,OAAO,IAAI,KAAK,kBAAkB,OAAO,OAAO,GAAG;AACnE,aAAO,OAAO,KAAK,UAAU,OAAO,MAAM,YAAY;IACxD;AAEA,QAAI,QAAQ;AACV,YAAM,UAAU,CAAC,IAAI,QAAQ,GAAG,IAAI,MAAM;AAC1C,YAAM,QAAQ,mBAAmB,iBAAiB,MAAM,IAAI,IAAI,gBAAgB,eAAe,MAAM,CAAC;AACtG,aAAO,UAAU;IACnB;AAEA,UAAM,SAAS,SAAS,GAAG;AAC3B,UAAM,UAAU,IAAI,QAAQ,KAAK,MAAM;AACvC,UAAM,eAAe,IAAI,QAAQ,KAAK,MAAM;AAC5C,WAAO;MACL,SAAS;IACX;AACA,QAAI,UACA,WAAW,OACX,YAAY,WAAW,WAAW,MAAM;AAC1C,iBAAW;AACX,UAAI,MAAM;IACZ,GAAG,OAAO;AAEV,QAAI;AACF,iBAAW,MAAM,QAAQ,OAAO;IAClC,SAAS,GAAG;AACV,aAAO;QACL,OAAO;UACL,QAAQ,WAAW,kBAAkB;UACrC,OAAO,OAAO,CAAC;QACjB;QACA;MACF;IACF,UAAA;AACE,UAAI;AAAW,qBAAa,SAAS;IACvC;AAEA,UAAM,gBAAgB,SAAS,MAAM;AACrC,SAAK,WAAW;AAChB,QAAI;AACJ,QAAI,eAAuB;AAE3B,QAAI;AACF,UAAI;AACJ,YAAM,QAAQ,IAAI;QAAC,eAAe,UAAU,eAAe,EAAE,KAAK,CAAA,MAAK,aAAa,GAAG,CAAA,MAAK,sBAAsB,CAAC;;;QAEnH,cAAc,KAAK,EAAE,KAAK,CAAA,MAAK,eAAe,GAAG,MAAM;QAAC,CAAC;MAAC,CAAC;AAC3D,UAAI;AAAqB,cAAM;IACjC,SAAS,GAAG;AACV,aAAO;QACL,OAAO;UACL,QAAQ;UACR,gBAAgB,SAAS;UACzB,MAAM;UACN,OAAO,OAAO,CAAC;QACjB;QACA;MACF;IACF;AAEA,WAAO,eAAe,UAAU,UAAU,IAAI;MAC5C,MAAM;MACN;IACF,IAAI;MACF,OAAO;QACL,QAAQ,SAAS;QACjB,MAAM;MACR;MACA;IACF;EACF;AAEA,iBAAe,eAAe,UAAoB,iBAAkC;AAClF,QAAI,OAAO,oBAAoB,YAAY;AACzC,aAAO,gBAAgB,QAAQ;IACjC;AAEA,QAAI,oBAAoB,gBAAgB;AACtC,wBAAkB,kBAAkB,SAAS,OAAO,IAAI,SAAS;IACnE;AAEA,QAAI,oBAAoB,QAAQ;AAC9B,YAAM,OAAO,MAAM,SAAS,KAAK;AACjC,aAAO,KAAK,SAAS,KAAK,MAAM,IAAI,IAAI;IAC1C;AAEA,WAAO,SAAS,KAAK;EACvB;AACF;AC3TO,IAAM,eAAN,MAAmB;EACxB,YAA4B,OAA4B,OAAY,QAAW;AAAnD,SAAA,QAAA;AAA4B,SAAA,OAAA;EAAwB;AAElF;ACcA,eAAe,eAAe,UAAkB,GAAG,aAAqB,GAAG;AACzE,QAAM,WAAW,KAAK,IAAI,SAAS,UAAU;AAC7C,QAAM,UAAU,CAAC,GAAG,KAAK,OAAO,IAAI,QAAQ,OAAO;AAEnD,QAAM,IAAI,QAAQ,CAAA,YAAW,WAAW,CAAC,QAAa,QAAQ,GAAG,GAAG,OAAO,CAAC;AAC9E;AA2BA,SAAS,KAAK,GAAe;AAC3B,QAAM,OAAO,OAAO,IAAI,aAAa;IACnC,OAAO;EACT,CAAC,GAAG;IACF,kBAAkB;EACpB,CAAC;AACH;AAEA,IAAM,gBAAgB,CAAC;AAEvB,IAAM,mBAAkF,CAAC,WAAW,mBAAmB,OAAO,MAAM,KAAK,iBAAiB;AAIxJ,QAAM,qBAA+B,CAAC,IAAK,kBAA0B,eAAe,aAAc,gBAAwB,eAAe,UAAU,EAAE,OAAO,CAAA,MAAK,MAAM,MAAS;AAChL,QAAM,CAAC,UAAU,IAAI,mBAAmB,MAAM,EAAE;AAEhD,QAAM,wBAAgD,CAAC,GAAG,IAAI;IAC5D;EACF,MAAM,WAAW;AAEjB,QAAM,UAIF;IACF;IACA,SAAS;IACT,gBAAgB;IAChB,GAAG;IACH,GAAG;EACL;AACA,MAAIC,SAAQ;AAEZ,SAAO,MAAM;AACX,QAAI;AACF,YAAM,SAAS,MAAM,UAAU,MAAM,KAAK,YAAY;AAEtD,UAAI,OAAO,OAAO;AAChB,cAAM,IAAI,aAAa,MAAM;MAC/B;AAEA,aAAO;IACT,SAAS,GAAQ;AACfA;AAEA,UAAI,EAAE,kBAAkB;AACtB,YAAI,aAAa,cAAc;AAC7B,iBAAO,EAAE;QACX;AAGA,cAAM;MACR;AAEA,UAAI,aAAa,gBAAgB,CAAC,QAAQ,eAAgB,EAAE,MAAM,OAA+B,MAAM;QACrG,SAASA;QACT,cAAc;QACd;MACF,CAAC,GAAG;AACF,eAAO,EAAE;MACX;AAEA,YAAM,QAAQ,QAAQA,QAAO,QAAQ,UAAU;IACjD;EACF;AACF;AAmCO,IAAM,QAAuB,OAAO,OAAO,kBAAkB;EAClE;AACF,CAAC;ACrJM,IAAM,UAAyB,aAAa,gBAAgB;AAC5D,IAAM,cAA6B,aAAa,kBAAkB;AAClE,IAAM,WAA0B,aAAa,eAAe;AAC5D,IAAM,YAA2B,aAAa,gBAAgB;AACrE,IAAI,cAAc;AAkBX,SAAS,eAAe,UAAwC,eAKrD;AAChB,WAAS,iBAAiB;AACxB,UAAM,cAAc,MAAM,SAAS,QAAQ,CAAC;AAE5C,UAAM,kBAAkB,MAAM,SAAS,YAAY,CAAC;AAEpD,UAAM,eAAe,MAAM,SAAS,SAAS,CAAC;AAE9C,UAAM,gBAAgB,MAAM,SAAS,UAAU,CAAC;AAEhD,UAAM,yBAAyB,MAAM;AACnC,UAAI,OAAO,SAAS,oBAAoB,WAAW;AACjD,oBAAY;MACd,OAAO;AACL,wBAAgB;MAClB;IACF;AAEA,QAAI,CAAC,aAAa;AAChB,UAAI,OAAO,WAAW,eAAe,OAAO,kBAAkB;AAE5D,eAAO,iBAAiB,oBAAoB,wBAAwB,KAAK;AACzE,eAAO,iBAAiB,SAAS,aAAa,KAAK;AAEnD,eAAO,iBAAiB,UAAU,cAAc,KAAK;AACrD,eAAO,iBAAiB,WAAW,eAAe,KAAK;AACvD,sBAAc;MAChB;IACF;AAEA,UAAM,cAAc,MAAM;AACxB,aAAO,oBAAoB,SAAS,WAAW;AAC/C,aAAO,oBAAoB,oBAAoB,sBAAsB;AACrE,aAAO,oBAAoB,UAAU,YAAY;AACjD,aAAO,oBAAoB,WAAW,aAAa;AACnD,oBAAc;IAChB;AAEA,WAAO;EACT;AAEA,SAAO,gBAAgB,cAAc,UAAU;IAC7C;IACA;IACA;IACA;EACF,CAAC,IAAI,eAAe;AACtB;ACwYO,SAAS,kBAAkB,GAAqF;AACrH,SAAO,EAAE,SAAS;AACpB;AACO,SAAS,qBAAqB,GAAwF;AAC3H,SAAO,EAAE,SAAS;AACpB;AA2DO,SAAS,oBAA+D,aAA+F,QAAgC,OAA8B,UAAoB,MAA4B,gBAAuE;AACjW,MAAI,WAAW,WAAW,GAAG;AAC3B,WAAO,YAAa,QAAwB,OAAqB,UAAW,IAAiB,EAAE,IAAI,oBAAoB,EAAE,IAAI,cAAc;EAC7I;AAEA,MAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,WAAO,YAAY,IAAI,oBAAoB,EAAE,IAAI,cAAc;EACjE;AAEA,SAAO,CAAC;AACV;AAEA,SAAS,WAAc,GAAiC;AACtD,SAAO,OAAO,MAAM;AACtB;AAEO,SAAS,qBAAqB,aAAiE;AACpG,SAAO,OAAO,gBAAgB,WAAW;IACvC,MAAM;EACR,IAAI;AACN;AEziBO,SAAS,aAAgB,GAAiC;AAC/D,SAAO,KAAK;AACd;ACEO,SAAS,gBAAgB,KAAuB;AACrD,MAAI,QAAQ;AAEZ,aAAW,QAAQ,KAAK;AACtB;EACF;AAEA,SAAO;AACT;ACkGO,SAAS,cAAkC,SAA4B,UAAwC;AACpH,SAAQ,QAAQ,MAAM,QAAQ;AAChC;AHtFO,IAAM,qBAAqB,OAAO,cAAc;AAChD,IAAM,gBAAgB,CAAC,QAAuB,OAAO,IAAI,kBAAkB,MAAM;AAyHjF,SAAS,cAAc;EAC5B;EACA;EACA;EACA;EACA;AACF,GAMG;AACD,QAAM,iBAA2F,oBAAI,IAAI;AACzG,QAAM,mBAAgG,oBAAI,IAAI;AAC9G,QAAM;IACJ;IACA;IACA;EACF,IAAI,IAAI;AACR,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;EACF;AAEA,WAAS,qBAAqB,cAAsB,WAAgB;AAClE,WAAO,CAAC,aAAuB;;AAC7B,YAAM,qBAAqB,QAAQ,oBAAoB,YAAY;AACnE,YAAM,gBAAgB,mBAAmB;QACvC;QACA;QACA;MACF,CAAC;AACD,cAAQ,oBAAe,IAAI,QAAQ,MAA3B,mBAA+B;IACzC;EACF;AAEA,WAAS,wBAKT,eAAuB,0BAAkC;AACvD,WAAO,CAAC,aAAuB;;AAC7B,cAAQ,sBAAiB,IAAI,QAAQ,MAA7B,mBAAiC;IAC3C;EACF;AAEA,WAAS,yBAAyB;AAChC,WAAO,CAAC,aAAuB,OAAO,OAAO,eAAe,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,YAAY;EACtG;AAEA,WAAS,2BAA2B;AAClC,WAAO,CAAC,aAAuB,OAAO,OAAO,iBAAiB,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,YAAY;EACxG;AAEA,WAAS,kBAAkB,UAAoB;AAC7C,QAAI,MAAuC;AACzC,UAAK,kBAA0B;AAAW;AAC1C,YAAM,gBAAgB,SAAS,IAAI,gBAAgB,8BAA8B,CAAC;AACjF,wBAA0B,YAAY;AAGvC,UAAI,OAAO,kBAAkB,YAAY,QAAO,+CAAe,UAAS,UAAU;AAEhF,cAAM,IAAI,MAAM,QAAwC,uBAAwB,EAAE,IAAI,yDAAyD,IAAI,WAAW;iEACrG;MAC3D;IACF;EACF;AAEA,WAAS,mBAAmB,cAAsB,oBAAyD;AACzG,UAAM,cAA4C,CAAC,KAAK;MACtD,YAAY;MACZ;MACA;MACA,CAAC,qBAAqB;IACxB,IAAI,CAAC,MAAM,CAAC,UAAU,aAAa;;AACjC,YAAM,gBAAgB,mBAAmB;QACvC,WAAW;QACX;QACA;MACF,CAAC;AACD,YAAM,QAAQ,WAAW;QACvB,MAAM;QACN;QACA;QACA;QACA;QACA,cAAc;QACd;QACA,CAAC,kBAAkB,GAAG;MACxB,CAAC;AACD,YAAM,WAAY,IAAI,UAAU,YAAY,EAAiC,OAAO,GAAG;AACvF,YAAM,cAAc,SAAS,KAAK;AAClC,YAAM,aAAa,SAAS,SAAS,CAAC;AACtC,wBAAkB,QAAQ;AAC1B,YAAM;QACJ;QACA;MACF,IAAI;AACJ,YAAM,uBAAuB,WAAW,cAAc;AACtD,YAAM,gBAAe,oBAAe,IAAI,QAAQ,MAA3B,mBAA+B;AAEpD,YAAM,kBAAkB,MAAM,SAAS,SAAS,CAAC;AAEjD,YAAM,eAA8C,OAAO,OAAS;;;QAEpE,YAAY,KAAK,eAAe;UAAI,wBAAwB,CAAC;;;QAE7D,QAAQ,QAAQ,UAAU;;;;QAE1B,QAAQ,IAAI,CAAC,cAAc,WAAW,CAAC,EAAE,KAAK,eAAe;SAAyB;QACpF;QACA;QACA;QACA;QACA;QAEA,MAAM,SAAS;AACb,gBAAM,SAAS,MAAM;AAErB,cAAI,OAAO,SAAS;AAClB,kBAAM,OAAO;UACf;AAEA,iBAAO,OAAO;QAChB;QAEA,SAAS,MAAM,SAAS,YAAY,KAAK;UACvC,WAAW;UACX,cAAc;QAChB,CAAC,CAAC;QAEF,cAAc;AACZ,cAAI;AAAW,qBAAS,uBAAuB;cAC7C;cACA;YACF,CAAC,CAAC;QACJ;QAEA,0BAA0B,SAA8B;AACtD,uBAAa,sBAAsB;AACnC,mBAAS,0BAA0B;YACjC;YACA;YACA;YACA;UACF,CAAC,CAAC;QACJ;MAEF,CAAC;AAED,UAAI,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,cAAc;AAC3D,cAAM,UAAU,eAAe,IAAI,QAAQ,KAAK,CAAC;AACjD,gBAAQ,aAAa,IAAI;AACzB,uBAAe,IAAI,UAAU,OAAO;AACpC,qBAAa,KAAK,MAAM;AACtB,iBAAO,QAAQ,aAAa;AAE5B,cAAI,CAAC,gBAAgB,OAAO,GAAG;AAC7B,2BAAe,OAAO,QAAQ;UAChC;QACF,CAAC;MACH;AAEA,aAAO;IACT;AAEA,WAAO;EACT;AAEA,WAAS,sBAAsB,cAAuD;AACpF,WAAO,CAAC,KAAK;MACX,QAAQ;MACR;IACF,IAAI,CAAC,MAAM,CAAC,UAAU,aAAa;AACjC,YAAM,QAAQ,cAAc;QAC1B,MAAM;QACN;QACA,cAAc;QACd;QACA;MACF,CAAC;AACD,YAAM,cAAc,SAAS,KAAK;AAClC,wBAAkB,QAAQ;AAC1B,YAAM;QACJ;QACA;QACA;MACF,IAAI;AACJ,YAAM,qBAAqB,cAAc,YAAY,OAAO,EAAE,KAAK,CAAA,UAAS;QAC1E;MACF,EAAE,GAAG,CAAA,WAAU;QACb;MACF,EAAE;AAEF,YAAM,QAAQ,MAAM;AAClB,iBAAS,qBAAqB;UAC5B;UACA;QACF,CAAC,CAAC;MACJ;AAEA,YAAM,MAAM,OAAO,OAAO,oBAAoB;QAC5C,KAAK,YAAY;QACjB;QACA;QACA;QACA;MACF,CAAC;AACD,YAAM,UAAU,iBAAiB,IAAI,QAAQ,KAAK,CAAC;AACnD,uBAAiB,IAAI,UAAU,OAAO;AACtC,cAAQ,SAAS,IAAI;AACrB,UAAI,KAAK,MAAM;AACb,eAAO,QAAQ,SAAS;AAExB,YAAI,CAAC,gBAAgB,OAAO,GAAG;AAC7B,2BAAiB,OAAO,QAAQ;QAClC;MACF,CAAC;AAED,UAAI,eAAe;AACjB,gBAAQ,aAAa,IAAI;AACzB,YAAI,KAAK,MAAM;AACb,cAAI,QAAQ,aAAa,MAAM,KAAK;AAClC,mBAAO,QAAQ,aAAa;AAE5B,gBAAI,CAAC,gBAAgB,OAAO,GAAG;AAC7B,+BAAiB,OAAO,QAAQ;YAClC;UACF;QACF,CAAC;MACH;AAEA,aAAO;IACT;EACF;AACF;AIjUA,SAAS,yBAAyB,sBAA+B;AAC/D,SAAO;AACT;AA4BO,SAAS,YAAgH;EAC9H;EACA;EACA,SAAS;IACP;EACF;EACA;EACA;EACA;AACF,GAOG;AAGD,QAAM,iBAAkE,CAAC,cAAc,MAAM,SAAS,mBAAmB,CAAC,UAAU,aAAa;AAC/I,UAAM,qBAAqB,oBAAoB,YAAY;AAC3D,UAAM,gBAAgB,mBAAmB;MACvC,WAAW;MACX;MACA;IACF,CAAC;AACD,aAAS,IAAI,gBAAgB,mBAAmB;MAC9C;MACA;IACF,CAAC,CAAC;AAEF,QAAI,CAAC,gBAAgB;AACnB;IACF;AAEA,UAAM,WAAW,IAAI,UAAU,YAAY,EAAE,OAAO,IAAI;;MACxD,SAAS;IAA8B;AACvC,UAAM,eAAe,oBAAoB,mBAAmB,cAAc,SAAS,MAAM,QAAW,MAAM,CAAC,GAAG,aAAa;AAC3H,aAAS,IAAI,gBAAgB,iBAAiB;MAC5C;MACA;IACF,CAAC,CAAC;EACJ;AAEA,QAAM,kBAAoE,CAAC,cAAc,MAAM,cAAc,iBAAiB,SAAS,CAAC,UAAU,aAAa;AAC7J,UAAM,qBAAqB,IAAI,UAAU,YAAY;AACrD,UAAM,eAAe,mBAAmB,OAAO,IAAI;;MACnD,SAAS;IAA8B;AACvC,QAAI,MAAuB;MACzB,SAAS,CAAC;MACV,gBAAgB,CAAC;MACjB,MAAM,MAAM,SAAS,IAAI,KAAK,eAAe,cAAc,MAAM,IAAI,gBAAgB,cAAc,CAAC;IACtG;AAEA,QAAI,aAAa,WAAA,iBAAsC;AACrD,aAAO;IACT;AAEA,QAAI;AAEJ,QAAI,UAAU,cAAc;AAC1B,UAAI,YAAY,aAAa,IAAI,GAAG;AAClC,cAAM,CAAC,OAAO,SAAS,cAAc,IAAI,mBAAmB,aAAa,MAAM,YAAY;AAC3F,YAAI,QAAQ,KAAK,GAAG,OAAO;AAC3B,YAAI,eAAe,KAAK,GAAG,cAAc;AACzC,mBAAW;MACb,OAAO;AACL,mBAAW,aAAa,aAAa,IAAI;AACzC,YAAI,QAAQ,KAAK;UACf,IAAI;UACJ,MAAM,CAAC;UACP,OAAO;QACT,CAAC;AACD,YAAI,eAAe,KAAK;UACtB,IAAI;UACJ,MAAM,CAAC;UACP,OAAO,aAAa;QACtB,CAAC;MACH;IACF;AAEA,aAAS,IAAI,KAAK,eAAe,cAAc,MAAM,IAAI,SAAS,cAAc,CAAC;AACjF,WAAO;EACT;AAEA,QAAM,kBAA4D,CAAC,cAAc,MAAM,UAAU,CAAA,aAAY;AAC3G,WAAO,SAAU,IAAI,UAAU,YAAY,EAA8E,SAAS,MAAM;MACtI,WAAW;MACX,cAAc;MACd,CAAC,kBAAkB,GAAG,OAAO;QAC3B,MAAM;MACR;IACF,CAAC,CAAC;EACJ;AAEA,QAAM,kBAED,OAAO,KAAK;IACf;IACA;IACA;IACA;IACA;IACA;IACA;EACF,MAAM;AACJ,UAAM,qBAAqB,oBAAoB,IAAI,YAAY;AAE/D,QAAI;AACF,UAAI,oBAA6E;AACjF,UAAI;AACJ,YAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA,UAAU,IAAI;QACd,MAAM,IAAI;QACV,QAAQ,IAAI,SAAS,UAAU,cAAc,KAAK,SAAS,CAAC,IAAI;MAClE;AACA,YAAM,eAAe,IAAI,SAAS,UAAU,IAAI,kBAAkB,IAAI;AAEtE,UAAI,cAAc;AAChB,iBAAS,aAAa;MACxB,WAAW,mBAAmB,OAAO;AACnC,iBAAS,MAAM,UAAU,mBAAmB,MAAM,IAAI,YAAY,GAAG,cAAe,mBAAmB,YAAoB;AAE3H,YAAI,mBAAmB,mBAAmB;AACxC,8BAAoB,mBAAmB;QACzC;MACF,OAAO;AACL,iBAAS,MAAM,mBAAmB,QAAQ,IAAI,cAAc,cAAe,mBAAmB,cAAsB,CAAAC,SAAO,UAAUA,MAAK,cAAe,mBAAmB,YAAoB,CAAC;MACnM;AAEA,UAAI,OAAO,YAAY,eAAe,MAAwC;AAC5E,cAAM,OAAO,mBAAmB,QAAQ,gBAAgB;AACxD,YAAI;AAEJ,YAAI,CAAC,QAAQ;AACX,gBAAM,GAAG,IAAI;QACf,WAAW,OAAO,WAAW,UAAU;AACrC,gBAAM,GAAG,IAAI;QACf,WAAW,OAAO,SAAS,OAAO,MAAM;AACtC,gBAAM,GAAG,IAAI;QACf,WAAW,OAAO,UAAU,UAAa,OAAO,SAAS,QAAW;AAClE,gBAAM,GAAG,IAAI;QACf,OAAO;AACL,qBAAW,OAAO,OAAO,KAAK,MAAM,GAAG;AACrC,gBAAI,QAAQ,WAAW,QAAQ,UAAU,QAAQ,QAAQ;AACvD,oBAAM,0BAA0B,IAAI,6BAA6B,GAAG;AACpE;YACF;UACF;QACF;AAEA,YAAI,KAAK;AACP,kBAAQ,MAAM,2CAA2C,IAAI,YAAY;gBACnE,GAAG;;qCAEkB,MAAM;QACnC;MACF;AAEA,UAAI,OAAO;AAAO,cAAM,IAAI,aAAa,OAAO,OAAO,OAAO,IAAI;AAClE,aAAO,iBAAiB,MAAM,kBAAkB,OAAO,MAAM,OAAO,MAAM,IAAI,YAAY,GAAG;QAC3F,oBAAoB,KAAK,IAAI;QAC7B,eAAe,OAAO;QACtB,CAAC,gBAAgB,GAAG;MACtB,CAAC;IACH,SAAS,OAAO;AACd,UAAI,eAAe;AAEnB,UAAI,wBAAwB,cAAc;AACxC,YAAI,yBAAkF;AAEtF,YAAI,mBAAmB,SAAS,mBAAmB,wBAAwB;AACzE,mCAAyB,mBAAmB;QAC9C;AAEA,YAAI;AACF,iBAAO,gBAAgB,MAAM,uBAAuB,aAAa,OAAO,aAAa,MAAM,IAAI,YAAY,GAAG;YAC5G,eAAe,aAAa;YAC5B,CAAC,gBAAgB,GAAG;UACtB,CAAC;QACH,SAAS,GAAG;AACV,yBAAe;QACjB;MACF;AAEA,UAAI,OAAO,YAAY,eAAe,MAAuC;AAC3E,gBAAQ,MAAM,sEAAsE,IAAI,YAAY;kFAC1B,YAAY;MACxF,OAAO;AACL,gBAAQ,MAAM,YAAY;MAC5B;AAEA,YAAM;IACR;EACF;AAEA,WAAS,cAAc,KAAoB,OAA4C;;AACrF,UAAM,gBAAe,iBAAM,WAAW,MAAjB,mBAAoB,YAApB,mBAA8B,IAAI;AACvD,UAAM,+BAA8B,WAAM,WAAW,MAAjB,mBAAoB,OAAO;AAC/D,UAAM,eAAe,6CAAc;AACnC,UAAM,aAAa,IAAI,iBAAiB,IAAI,aAAa;AAEzD,QAAI,YAAY;AAEd,aAAO,eAAe,SAAS,OAAO,oBAAI,KAAK,CAAC,IAAI,OAAO,YAAY,KAAK,OAAQ;IACtF;AAEA,WAAO;EACT;AAEA,QAAM,aAAa,iBAEhB,GAAG,WAAW,iBAAiB,iBAAiB;IACjD,iBAAiB;AACf,aAAO;QACL,kBAAkB,KAAK,IAAI;QAC3B,CAAC,gBAAgB,GAAG;MACtB;IACF;IAEA,UAAU,gBAAgB;MACxB;IACF,GAAG;;AACD,YAAM,QAAQ,SAAS;AACvB,YAAM,gBAAe,iBAAM,WAAW,MAAjB,mBAAoB,YAApB,mBAA8B,eAAe;AAClE,YAAM,eAAe,6CAAc;AACnC,YAAM,aAAa,eAAe;AAClC,YAAM,cAAc,6CAAc;AAClC,YAAM,qBAAqB,oBAAoB,eAAe,YAAY;AAI1E,UAAI,cAAc,cAAc,GAAG;AACjC,eAAO;MACT;AAGA,WAAI,6CAAc,YAAW,WAAW;AACtC,eAAO;MACT;AAGA,UAAI,cAAc,gBAAgB,KAAK,GAAG;AACxC,eAAO;MACT;AAEA,UAAI,kBAAkB,kBAAkB,OAAK,8DAAoB,iBAApB,4CAAmC;QAC9E;QACA;QACA,eAAe;QACf;MACF,KAAI;AACF,eAAO;MACT;AAGA,UAAI,cAAc;AAEhB,eAAO;MACT;AAEA,aAAO;IACT;IAEA,4BAA4B;EAC9B,CAAC;AACD,QAAM,gBAAgB,iBAEnB,GAAG,WAAW,oBAAoB,iBAAiB;IACpD,iBAAiB;AACf,aAAO;QACL,kBAAkB,KAAK,IAAI;QAC3B,CAAC,gBAAgB,GAAG;MACtB;IACF;EAEF,CAAC;AAED,QAAM,cAAc,CAAC,YAEhB,WAAW;AAEhB,QAAM,YAAY,CAAC,YAEd,iBAAiB;AAEtB,QAAM,WAAW,CAA+C,cAA4B,KAAU,YAAyE,CAAC,UAAwC,aAAwB;AAC9O,UAAM,QAAQ,YAAY,OAAO,KAAK,QAAQ;AAC9C,UAAM,SAAS,UAAU,OAAO,KAAK,QAAQ;AAE7C,UAAM,cAAc,CAACC,SAAiB,SAAU,IAAI,UAAU,YAAY,EAAiC,SAAS,KAAK;MACvH,cAAcA;IAChB,CAAC;AAED,UAAM,mBAAoB,IAAI,UAAU,YAAY,EAAiC,OAAO,GAAG,EAAE,SAAS,CAAC;AAE3G,QAAI,OAAO;AACT,eAAS,YAAY,CAAC;IACxB,WAAW,QAAQ;AACjB,YAAM,kBAAkB,qDAAkB;AAE1C,UAAI,CAAC,iBAAiB;AACpB,iBAAS,YAAY,CAAC;AACtB;MACF;AAEA,YAAM,mBAAmB,OAAO,oBAAI,KAAK,CAAC,IAAI,OAAO,IAAI,KAAK,eAAe,CAAC,KAAK,OAAQ;AAE3F,UAAI,iBAAiB;AACnB,iBAAS,YAAY,CAAC;MACxB;IACF,OAAO;AAEL,eAAS,YAAY,KAAK,CAAC;IAC7B;EACF;AAEA,WAAS,gBAAgB,cAAsB;AAC7C,WAAO,CAAC,WAAA;;AAAyC,2DAAQ,SAAR,mBAAc,QAAd,mBAAmB,kBAAiB;;EACvF;AAEA,WAAS,uBAAiJ,OAAc,cAAsB;AAC5L,WAAQ;MACN,cAAc,QAAQ,UAAU,KAAK,GAAG,gBAAgB,YAAY,CAAC;MACrE,gBAAgB,QAAQ,YAAY,KAAK,GAAG,gBAAgB,YAAY,CAAC;MACzE,eAAe,QAAQ,WAAW,KAAK,GAAG,gBAAgB,YAAY,CAAC;IACzE;EACF;AAEA,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;IACA;EACF;AACF;AACO,SAAS,yBAAyB,QAAmG,MAA0C,qBAA0C,eAA+B;AAC7P,SAAO,oBAAoB,oBAAoB,OAAO,KAAK,IAAI,YAAY,EAAE,IAAI,GAAG,YAAY,MAAM,IAAI,OAAO,UAAU,QAAW,oBAAoB,MAAM,IAAI,OAAO,UAAU,QAAW,OAAO,KAAK,IAAI,cAAc,mBAAmB,OAAO,OAAO,OAAO,KAAK,gBAAgB,QAAW,aAAa;AACrT;AC9aA,SAAS,4BAA4B,OAAwB,eAA8B,QAAgD;AACzI,QAAM,WAAW,MAAM,aAAa;AAEpC,MAAI,UAAU;AACZ,WAAO,QAAQ;EACjB;AACF;AAYO,SAAS,oBAAoB,IAQb;AACrB,UAAQ,SAAS,KAAK,GAAG,IAAI,gBAAgB,GAAG,kBAAkB,GAAG;AACvE;AAEA,SAAS,+BAA+B,OAA2B,IAKhE,QAAmD;AACpD,QAAM,WAAW,MAAM,oBAAoB,EAAE,CAAC;AAE9C,MAAI,UAAU;AACZ,WAAO,QAAQ;EACjB;AACF;AAEA,IAAM,eAAgB,CAAC;AAChB,SAAS,WAAW;EACzB;EACA;EACA;EACA,SAAS;IACP,qBAAqB;IACrB;IACA;IACA;EACF;EACA;EACA;AACF,GAOG;AACD,QAAM,gBAAgB,aAAa,GAAG,WAAW,gBAAgB;AACjE,QAAM,aAAa,YAAY;IAC7B,MAAM,GAAG,WAAW;IACpB;IACA,UAAU;MACR,mBAAmB;QACjB,QAAQ,OAAO;UACb,SAAS;YACP;UACF;QACF,GAA2C;AACzC,iBAAO,MAAM,aAAa;QAC5B;QAEA,SAAS,mBAA4C;MACvD;MACA,oBAAoB;QAClB,QAAQ,OAAO;UACb,SAAS;YACP;YACA;UACF;QACF,GAEI;AACF,sCAA4B,OAAO,eAAe,CAAA,aAAY;AAC5D,qBAAS,OAAO,aAAc,SAAS,MAAc,QAAQ,OAAO,CAAC;UACvE,CAAC;QACH;QAEA,SAAS,mBAEN;MACL;IACF;IAEA,cAAc,SAAS;AACrB,cAAQ,QAAQ,WAAW,SAAS,CAAC,OAAO;QAC1C;QACA,MAAM;UACJ;QACF;MACF,MAAM;;AACJ,cAAM,YAAY,cAAc,GAAG;AACnC,mBAAM,IAAI,mBAAV,YAA6B;UAC3B,QAAA;UACA,cAAc,IAAI;QACpB;AACA,oCAA4B,OAAO,IAAI,eAAe,CAAA,aAAY;AAChE,mBAAS,SAAA;AACT,mBAAS,YAAY,aAAa,SAAS;;YAC3C,SAAS;;;YACT,KAAK;;AAEL,cAAI,IAAI,iBAAiB,QAAW;AAClC,qBAAS,eAAe,IAAI;UAC9B;AAEA,mBAAS,mBAAmB,KAAK;QACnC,CAAC;MACH,CAAC,EAAE,QAAQ,WAAW,WAAW,CAAC,OAAO;QACvC;QACA;MACF,MAAM;AACJ,oCAA4B,OAAO,KAAK,IAAI,eAAe,CAAA,aAAY;AACrE,cAAI,SAAS,cAAc,KAAK,aAAa,CAAC,cAAc,KAAK,GAAG;AAAG;AACvE,gBAAM;YACJ;UACF,IAAK,YAAY,KAAK,IAAI,YAAY;AACtC,mBAAS,SAAA;AAET,cAAI,OAAO;AACT,gBAAI,SAAS,SAAS,QAAW;AAC/B,oBAAM;gBACJ;gBACA;gBACA;gBACA;cACF,IAAI;AAKJ,kBAAI,UAAU,QAAgB,SAAS,MAAM,CAAA,sBAAqB;AAEhE,uBAAO,MAAM,mBAAmB,SAAS;kBACvC,KAAK,IAAI;kBACT;kBACA;kBACA;gBACF,CAAC;cACH,CAAC;AACD,uBAAS,OAAO;YAClB,OAAO;AAEL,uBAAS,OAAO;YAClB;UACF,OAAO;AAEL,qBAAS,OAAO,YAAY,KAAK,IAAI,YAAY,EAAE,qBAAqB,OAAO,0BAA0B,QAAQ,SAAS,IAAI,IAAI,SAAS,SAAS,IAAI,IAAI,SAAS,MAAM,OAAO,IAAI;UACxL;AAEA,iBAAO,SAAS;AAChB,mBAAS,qBAAqB,KAAK;QACrC,CAAC;MACH,CAAC,EAAE,QAAQ,WAAW,UAAU,CAAC,OAAO;QACtC,MAAM;UACJ;UACA;UACA;QACF;QACA;QACA;MACF,MAAM;AACJ,oCAA4B,OAAO,IAAI,eAAe,CAAA,aAAY;AAChE,cAAI,WAAW;UACf,OAAO;AAEL,gBAAI,SAAS,cAAc;AAAW;AACtC,qBAAS,SAAA;AACT,qBAAS,QAAS,WAAW;UAC/B;QACF,CAAC;MACH,CAAC,EAAE,WAAW,oBAAoB,CAAC,OAAO,WAAW;AACnD,cAAM;UACJ;QACF,IAAI,uBAAuB,MAAM;AAEjC,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,OAAO,GAAG;AAClD;;aACA,+BAAO,YAAA,gBAAoC,+BAAO,YAAA;YAAiC;AACjF,kBAAM,GAAG,IAAI;UACf;QACF;MACF,CAAC;IACH;EAEF,CAAC;AACD,QAAM,gBAAgB,YAAY;IAChC,MAAM,GAAG,WAAW;IACpB;IACA,UAAU;MACR,sBAAsB;QACpB,QAAQ,OAAO;UACb;QACF,GAA8C;AAC5C,gBAAM,WAAW,oBAAoB,OAAO;AAE5C,cAAI,YAAY,OAAO;AACrB,mBAAO,MAAM,QAAQ;UACvB;QACF;QAEA,SAAS,mBAA+C;MAC1D;IACF;IAEA,cAAc,SAAS;AACrB,cAAQ,QAAQ,cAAc,SAAS,CAAC,OAAO;QAC7C;QACA,MAAM;UACJ;UACA;UACA;QACF;MACF,MAAM;AACJ,YAAI,CAAC,IAAI;AAAO;AAChB,cAAM,oBAAoB,IAAI,CAAC,IAAI;UACjC;UACA,QAAA;UACA,cAAc,IAAI;UAClB;QACF;MACF,CAAC,EAAE,QAAQ,cAAc,WAAW,CAAC,OAAO;QAC1C;QACA;MACF,MAAM;AACJ,YAAI,CAAC,KAAK,IAAI;AAAO;AACrB,uCAA+B,OAAO,MAAM,CAAA,aAAY;AACtD,cAAI,SAAS,cAAc,KAAK;AAAW;AAC3C,mBAAS,SAAA;AACT,mBAAS,OAAO;AAChB,mBAAS,qBAAqB,KAAK;QACrC,CAAC;MACH,CAAC,EAAE,QAAQ,cAAc,UAAU,CAAC,OAAO;QACzC;QACA;QACA;MACF,MAAM;AACJ,YAAI,CAAC,KAAK,IAAI;AAAO;AACrB,uCAA+B,OAAO,MAAM,CAAA,aAAY;AACtD,cAAI,SAAS,cAAc,KAAK;AAAW;AAC3C,mBAAS,SAAA;AACT,mBAAS,QAAS,WAAW;QAC/B,CAAC;MACH,CAAC,EAAE,WAAW,oBAAoB,CAAC,OAAO,WAAW;AACnD,cAAM;UACJ;QACF,IAAI,uBAAuB,MAAM;AAEjC,mBAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,SAAS,GAAG;AACpD;;cACC,+BAAO,YAAA,gBAAoC,+BAAO,YAAA;YACnD,SAAQ,+BAAO;YAAW;AACxB,kBAAM,GAAG,IAAI;UACf;QACF;MACF,CAAC;IACH;EAEF,CAAC;AACD,QAAM,oBAAoB,YAAY;IACpC,MAAM,GAAG,WAAW;IACpB;IACA,UAAU;MACR,kBAAkB;QAChB,QAAQ,OAAO,QAGX;;AACF,gBAAM;YACJ;YACA;UACF,IAAI,OAAO;AAEX,qBAAW,wBAAwB,OAAO,OAAO,KAAK,GAAG;AACvD,uBAAW,mBAAmB,OAAO,OAAO,oBAAoB,GAAG;AACjE,oBAAM,UAAU,gBAAgB,QAAQ,aAAa;AAErD,kBAAI,YAAY,IAAI;AAClB,gCAAgB,OAAO,SAAS,CAAC;cACnC;YACF;UACF;AAEA,qBAAW;YACT;YACA;UACF,KAAK,cAAc;AACjB,kBAAM,qBAAqB,mCAAgB,CAAC,IAAjB,KAAoB,MAAM,6BAA1B,SAAuD,CAAC;AACnF,kBAAM,oBAAoB,kBAAkB,SAAS,aAAa;AAElE,gBAAI,CAAC,mBAAmB;AACtB,gCAAkB,KAAK,aAAa;YACtC;UACF;QACF;QAEA,SAAS,mBAGN;MACL;IACF;IAEA,cAAc,SAAS;AACrB,cAAQ,QAAQ,WAAW,QAAQ,mBAAmB,CAAC,OAAO;QAC5D,SAAS;UACP;QACF;MACF,MAAM;AACJ,mBAAW,wBAAwB,OAAO,OAAO,KAAK,GAAG;AACvD,qBAAW,mBAAmB,OAAO,OAAO,oBAAoB,GAAG;AACjE,kBAAM,UAAU,gBAAgB,QAAQ,aAAa;AAErD,gBAAI,YAAY,IAAI;AAClB,8BAAgB,OAAO,SAAS,CAAC;YACnC;UACF;QACF;MACF,CAAC,EAAE,WAAW,oBAAoB,CAAC,OAAO,WAAW;;AACnD,cAAM;UACJ;QACF,IAAI,uBAAuB,MAAM;AAEjC,mBAAW,CAAC,MAAM,YAAY,KAAK,OAAO,QAAQ,QAAQ,GAAG;AAC3D,qBAAW,CAAC,IAAI,SAAS,KAAK,OAAO,QAAQ,YAAY,GAAG;AAC1D,kBAAM,qBAAqB,mCAAgB,CAAC,IAAjB,KAAoB,MAAM,6BAA1B,SAAuD,CAAC;AAEnF,uBAAW,iBAAiB,WAAW;AACrC,oBAAM,oBAAoB,kBAAkB,SAAS,aAAa;AAElE,kBAAI,CAAC,mBAAmB;AACtB,kCAAkB,KAAK,aAAa;cACtC;YACF;UACF;QACF;MACF,CAAC,EAAE,WAAW,QAAQ,YAAY,UAAU,GAAG,oBAAoB,UAAU,CAAC,GAAG,CAAC,OAAO,WAAW;AAClG,cAAM,eAAe,yBAAyB,QAAQ,gBAAgB,aAAa,aAAa;AAChG,cAAM;UACJ;QACF,IAAI,OAAO,KAAK;AAChB,0BAAkB,aAAa,iBAAiB,OAAO,kBAAkB,QAAQ,iBAAiB;UAChG;UACA;QACF,CAAC,CAAC;MACJ,CAAC;IACH;EAEF,CAAC;AAED,QAAM,oBAAoB,YAAY;IACpC,MAAM,GAAG,WAAW;IACpB;IACA,UAAU;MACR,0BAA0B,GAAG,GAIC;MAC9B;MAEA,uBAAuB,GAAG,GAEI;MAC9B;MAEA,gCAAgC;MAAC;IAEnC;EACF,CAAC;AACD,QAAM,6BAA6B,YAAY;IAC7C,MAAM,GAAG,WAAW;IACpB;IACA,UAAU;MACR,sBAAsB;QACpB,QAAQ,OAAO,QAAgC;AAC7C,iBAAO,aAAa,OAAO,OAAO,OAAO;QAC3C;QAEA,SAAS,mBAA4B;MACvC;IACF;EACF,CAAC;AACD,QAAM,cAAc,YAAY;IAC9B,MAAM,GAAG,WAAW;IACpB,cAAe;MACb,QAAQ,SAAS;MACjB,SAAS,kBAAkB;MAC3B,sBAAsB;MACtB,GAAG;IACL;IACA,UAAU;MACR,qBAAqB,OAAO;QAC1B;MACF,GAA0B;AACxB,cAAM,uBAAuB,MAAM,yBAAyB,cAAc,WAAW,UAAU,aAAa;MAC9G;IAEF;IACA,eAAe,CAAA,YAAW;AACxB,cAAQ,QAAQ,UAAU,CAAA,UAAS;AACjC,cAAM,SAAS;MACjB,CAAC,EAAE,QAAQ,WAAW,CAAA,UAAS;AAC7B,cAAM,SAAS;MACjB,CAAC,EAAE,QAAQ,SAAS,CAAA,UAAS;AAC3B,cAAM,UAAU;MAClB,CAAC,EAAE,QAAQ,aAAa,CAAA,UAAS;AAC/B,cAAM,UAAU;MAClB,CAAC,EAEA,WAAW,oBAAoB,CAAA,WAAU;QAAE,GAAG;MAC/C,EAAE;IACJ;EACF,CAAC;AACD,QAAM,kBAAkB,gBAAgB;IACtC,SAAS,WAAW;IACpB,WAAW,cAAc;IACzB,UAAU,kBAAkB;IAC5B,eAAe,2BAA2B;IAC1C,QAAQ,YAAY;EACtB,CAAC;AAED,QAAM,UAAkC,CAAC,OAAO,WAAW,gBAAgB,cAAc,MAAM,MAAM,IAAI,SAAY,OAAO,MAAM;AAElI,QAAM,UAAU;IAAE,GAAG,YAAY;IAC/B,GAAG,WAAW;IACd,GAAG,kBAAkB;IACrB,GAAG,2BAA2B;IAC9B,GAAG,cAAc;IACjB,GAAG,kBAAkB;IACrB;EACF;AACA,SAAO;IACL;IACA;EACF;AACF;AClbO,IAAM,YAA2B,OAAO,IAAI,gBAAgB;AAgBnE,IAAM,kBAAsC;EAC1C,QAAA;;AACF;AAEA,IAAM,uBAAsC,QAAgB,iBAAiB,MAAM;AAAC,CAAC;AACrF,IAAM,0BAAyC,QAAiB,iBAA2C,MAAM;AAAC,CAAC;AAC5G,SAAS,eAAoF;EAClG;EACA;EACA,gBAAAC;AACF,GAIG;AAGD,QAAM,qBAAqB,CAAC,UAAqB;AAEjD,QAAM,wBAAwB,CAAC,UAAqB;AAEpD,SAAO;IACL;IACA;IACA;IACA;EACF;AAEA,WAAS,iBAEN,UAAqC;AACtC,WAAO;MAAE,GAAG;MACV,GAAG,sBAAsB,SAAS,MAAM;IAC1C;EACF;AAEA,WAAS,oBAAoB,WAAsB;AACjD,UAAM,QAAQ,UAAU,WAAW;AAEnC,QAAI,MAAuC;AACzC,UAAI,CAAC,OAAO;AACV,YAAK,oBAA4B;AAAW,iBAAO;AAClD,4BAA4B,YAAY;AACzC,gBAAQ,MAAM,mCAAmC,WAAW,qDAAqD;MACnH;IACF;AAEA,WAAO;EACT;AAEA,WAAS,mBAAmB,cAAsB,oBAAyD;AACzG,WAAS,CAAC,cAAmB;AAC3B,YAAM,iBAAiB,mBAAmB;QACxC;QACA;QACA;MACF,CAAC;AAED,YAAM,sBAAsB,CAAC,UAAA;;AAAqB,gDAAoB,KAAK,MAAzB,mBAA4B,YAA5B,mBAAsC,oBAAmB;;AAE3G,YAAM,2BAA2B,cAAc,YAAY,qBAAqB;AAChF,aAAOA,gBAAe,0BAA0B,gBAAgB;IAClE;EACF;AAEA,WAAS,wBAAwB;AAC/B,WAAS,CAAA,OAAM;AACb,UAAI;AAEJ,UAAI,OAAO,OAAO,UAAU;AAC1B,qBAAa,oBAAoB,EAAE,KAAK;MAC1C,OAAO;AACL,qBAAa;MACf;AAEA,YAAM,yBAAyB,CAAC,UAAA;;AAAqB,gDAAoB,KAAK,MAAzB,mBAA4B,cAA5B,mBAAyC,gBAA0B;;AAExH,YAAM,8BAA8B,eAAe,YAAY,wBAAwB;AACvF,aAAOA,gBAAe,6BAA6B,gBAAgB;IACrE;EACF;AAEA,WAAS,oBAAoB,OAAkB,MAI5C;AACD,UAAM,WAAW,MAAM,WAAW;AAClC,UAAM,eAAe,oBAAI,IAAmB;AAE5C,eAAW,OAAO,KAAK,IAAI,oBAAoB,GAAG;AAChD,YAAM,WAAW,SAAS,SAAS,IAAI,IAAI;AAE3C,UAAI,CAAC,UAAU;AACb;MACF;AAEA,UAAI,2BAA2B,IAAI,OAAO;;QAC1C,SAAS,IAAI,EAAE;;;QACf,QAAQ,OAAO,OAAO,QAAQ,CAAC;YAAM,CAAC;AAEtC,iBAAW,cAAc,yBAAyB;AAChD,qBAAa,IAAI,UAAU;MAC7B;IACF;AAEA,WAAO,QAAQ,MAAM,KAAK,aAAa,OAAO,CAAC,EAAE,IAAI,CAAA,kBAAiB;AACpE,YAAM,gBAAgB,SAAS,QAAQ,aAAa;AACpD,aAAO,gBAAgB,CAAC;QACtB;QACA,cAAc,cAAc;QAC5B,cAAc,cAAc;MAC9B,CAAC,IAAI,CAAC;IACR,CAAC,CAAC;EACJ;AAEA,WAAS,yBAAmE,OAAkB,WAAmE;AAC/J,WAAO,OAAO,OAAQ,MAAM,WAAW,EAAE,OAA2B,EAAE;MAAO,CAAC,WAExE,+BAAO,kBAAiB,aAAa,MAAM,WAAA;;IAAoC,EAAE,IAAI,CAAA,UAAS,MAAM,YAAY;EACxH;AACF;ACvKA,IAAM,QAA0C,UAAU,oBAAI,QAAQ,IAAI;AACnE,IAAM,4BAAqD,CAAC;EACjE;EACA;AACF,MAAM;AACJ,MAAI,aAAa;AACjB,QAAM,SAAS,+BAAO,IAAI;AAE1B,MAAI,OAAO,WAAW,UAAU;AAC9B,iBAAa;EACf,OAAO;AACL,UAAM,cAAc,KAAK,UAAU,WAAW,CAAC,KAAK,UAAU,cAAc,KAAK,IAAI,OAAO,KAAK,KAAK,EAAE,KAAK,EAAE,OAAY,CAAC,KAAKC,SAAQ;AACvI,UAAIA,IAAG,IAAK,MAAcA,IAAG;AAC7B,aAAO;IACT,GAAG,CAAC,CAAC,IAAI,KAAK;AAEd,QAAI,cAAc,SAAS,GAAG;AAC5B,qCAAO,IAAI,WAAW;IACxB;AAEA,iBAAa;EACf;AAGA,SAAO,GAAG,YAAY,IAAI,UAAU;AACtC;AC6MO,SAAS,kBAAmE,SAAsD;AACvI,SAAO,SAAS,cAAc,SAAS;AACrC,UAAM,yBAAyB,eAAe,CAAC,WAAA;;AAA0B,2BAAQ,2BAAR,iCAAiC,QAAQ;QAChH,aAAc,QAAQ,eAAe;MACvC;KAAE;AACF,UAAM,sBAA4D;MAChE,aAAa;MACb,mBAAmB;MACnB,2BAA2B;MAC3B,gBAAgB;MAChB,oBAAoB;MACpB,sBAAsB;MACtB,GAAG;MACH;MAEA,mBAAmB,cAAc;AAC/B,YAAI,0BAA0B;AAE9B,YAAI,wBAAwB,aAAa,oBAAoB;AAC3D,gBAAM,cAAc,aAAa,mBAAmB;AAEpD,oCAA0B,CAAAC,kBAAgB;AACxC,kBAAM,gBAAgB,YAAYA,aAAY;AAE9C,gBAAI,OAAO,kBAAkB,UAAU;AAErC,qBAAO;YACT,OAAO;AAGL,qBAAO,0BAA0B;gBAAE,GAAGA;gBACpC,WAAW;cACb,CAAC;YACH;UACF;QACF,WAAW,QAAQ,oBAAoB;AACrC,oCAA0B,QAAQ;QACpC;AAEA,eAAO,wBAAwB,YAAY;MAC7C;MAEA,UAAU,CAAC,GAAI,QAAQ,YAAY,CAAC,CAAE;IACxC;AACA,UAAM,UAA2C;MAC/C,qBAAqB,CAAC;MAEtB,MAAM,IAAI;AAER,WAAG;MACL;MAEA,QAAQ,OAAO;MACf;MACA,oBAAoB,eAAe,CAAA,WAAU,uBAAuB,MAAM,KAAK,IAAI;IACrF;AACA,UAAM,MAAO;MACX;MAEA,iBAAiB;QACf;QACA;MACF,GAAG;AACD,YAAI,aAAa;AACf,qBAAW,MAAM,aAAa;AAC5B,gBAAI,CAAC,oBAAoB,SAAU,SAAU,EAAU,GAAG;AACxD;AACC,kCAAoB,SAAmB,KAAK,EAAE;YACjD;UACF;QACF;AAEA,YAAI,WAAW;AACb,qBAAW,CAAC,cAAc,iBAAiB,KAAK,OAAO,QAAQ,SAAS,GAAG;AACzE,gBAAI,OAAO,sBAAsB,YAAY;AAC3C,gCAAkB,QAAQ,oBAAoB,YAAY,CAAC;YAC7D,OAAO;AACL,qBAAO,OAAO,QAAQ,oBAAoB,YAAY,KAAK,CAAC,GAAG,iBAAiB;YAClF;UACF;QACF;AAEA,eAAO;MACT;IAEF;AACA,UAAM,qBAAqB,QAAQ,IAAI,CAAA,MAAK,EAAE,KAAM,KAAc,qBAA6B,OAAO,CAAC;AAEvG,aAAS,gBAAgB,QAAmD;AAC1E,YAAM,qBAAqB,OAAO,UAAU;QAC1C,OAAO,CAAA,OAAM;UAAE,GAAG;UAChB,MAAA;;QACF;QACA,UAAU,CAAA,OAAM;UAAE,GAAG;UACnB,MAAA;;QACF;MACF,CAAC;AAED,iBAAW,CAAC,cAAc,UAAU,KAAK,OAAO,QAAQ,kBAAkB,GAAG;AAC3E,YAAI,CAAC,OAAO,oBAAoB,gBAAgB,QAAQ,qBAAqB;AAC3E,cAAI,OAAO,YAAY,eAAe,MAAwC;AAC5E,oBAAQ,MAAM,wEAAwE,YAAY,gDAAgD;UACpJ;AAEA;QACF;AAEA,gBAAQ,oBAAoB,YAAY,IAAI;AAE5C,mBAAW,KAAK,oBAAoB;AAClC,YAAE,eAAe,cAAc,UAAU;QAC3C;MACF;AAEA,aAAQ;IACV;AAEA,WAAO,IAAI,gBAAgB;MACzB,WAAY,QAAQ;IACtB,CAAC;EACH;AACF;ACvVO,SAAS,gBAAoE;AAClF,SAAO,WAAY;AACjB,UAAM,IAAI,MAAM,QAAwCC,uBAAwB,EAAE,IAAI,+FAA+F;EACvL;AACF;ACTA,SAAS,cAAc,KAAuB;AAG5C,WAAS,KAAK,KAAK;AAEjB,WAAO;EACT;AAEA,SAAO;AACT;AAiBO,IAAM,mCAAmC,aAAgB,MAAQ;AACjE,IAAM,8BAAsD,CAAC;EAClE;EACA;EACA;EACA;AACF,MAAM;AACJ,QAAM;IACJ;IACA;EACF,IAAI,IAAI;AAER,WAAS,gCAAgC,eAAuB;AAC9D,UAAM,gBAAgB,cAAc,qBAAqB,aAAa;AACtE,WAAO,CAAC,CAAC,iBAAiB,CAAC,cAAc,aAAa;EACxD;AAEA,QAAM,yBAAoD,CAAC;AAE3D,QAAM,UAAwC,CAAC,QAAQ,OAAOC,mBAAkB;;AAC9E,QAAI,uBAAuB,MAAM,MAAM,GAAG;AACxC,YAAM,QAAQ,MAAM,SAAS,EAAE,WAAW;AAC1C,YAAM;QACJ;MACF,IAAI,OAAO;AACX,wBAAkB,gBAAe,WAAM,QAAQ,aAAa,MAA3B,mBAA8B,cAAc,OAAO,MAAM,MAAM;IAClG;AAEA,QAAI,IAAI,KAAK,cAAc,MAAM,MAAM,GAAG;AACxC,iBAAW,CAAC,KAAK,OAAO,KAAK,OAAO,QAAQ,sBAAsB,GAAG;AACnE,YAAI;AAAS,uBAAa,OAAO;AACjC,eAAO,uBAAuB,GAAG;MACnC;IACF;AAEA,QAAI,QAAQ,mBAAmB,MAAM,GAAG;AACtC,YAAM,QAAQ,MAAM,SAAS,EAAE,WAAW;AAC1C,YAAM;QACJ;MACF,IAAI,QAAQ,uBAAuB,MAAM;AAEzC,iBAAW,CAAC,eAAe,UAAU,KAAK,OAAO,QAAQ,OAAO,GAAG;AAIjE,0BAAmB,eAAiC,yCAAY,cAAc,OAAO,MAAM,MAAM;MACnG;IACF;EACF;AAEA,WAAS,kBAAkB,eAA8B,cAAkCC,MAAuB,QAA6B;AAC7I,UAAM,qBAAsB,QAAQ,oBAAoB,YAAa;AACrE,UAAM,qBAAoB,yDAAoB,sBAAqB,OAAO;AAE1E,QAAI,sBAAsB,UAAU;AAElC;IACF;AAMA,UAAM,yBAAyB,KAAK,IAAI,GAAG,KAAK,IAAI,mBAAmB,gCAAgC,CAAC;AAExG,QAAI,CAAC,gCAAgC,aAAa,GAAG;AACnD,YAAM,iBAAiB,uBAAuB,aAAa;AAE3D,UAAI,gBAAgB;AAClB,qBAAa,cAAc;MAC7B;AAEA,6BAAuB,aAAa,IAAI,WAAW,MAAM;AACvD,YAAI,CAAC,gCAAgC,aAAa,GAAG;AACnDA,eAAI,SAAS,kBAAkB;YAC7B;UACF,CAAC,CAAC;QACJ;AAEA,eAAO,uBAAwB,aAAa;MAC9C,GAAG,yBAAyB,GAAI;IAClC;EACF;AAEA,SAAO;AACT;AC7GO,IAAM,iCAAyD,CAAC;EACrE;EACA;EACA,SAAS;IACP;EACF;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAAM;AACJ,QAAM;IACJ;EACF,IAAI,IAAI;AACR,QAAM,wBAAwB,QAAQ,YAAY,aAAa,GAAG,oBAAoB,aAAa,CAAC;AACpG,QAAM,aAAa,QAAQ,YAAY,eAAe,UAAU,GAAG,WAAW,eAAe,UAAU,CAAC;AACxG,MAAI,0BAAwD,CAAC;AAE7D,QAAM,UAAwC,CAAC,QAAQ,UAAU;AAC/D,QAAI,sBAAsB,MAAM,GAAG;AACjC,qBAAe,yBAAyB,QAAQ,mBAAmB,qBAAqB,aAAa,GAAG,KAAK;IAC/G,WAAW,WAAW,MAAM,GAAG;AAC7B,qBAAe,CAAC,GAAG,KAAK;IAC1B,WAAW,IAAI,KAAK,eAAe,MAAM,MAAM,GAAG;AAChD,qBAAe,oBAAoB,OAAO,SAAS,QAAW,QAAW,QAAW,QAAW,aAAa,GAAG,KAAK;IACtH;EACF;AAEA,WAAS,mBAAmB,OAA2D;;AACrF,eAAW,OAAO,MAAM,SAAS;AAC/B,YAAI,WAAM,QAAQ,GAAG,MAAjB,mBAAoB,YAAA;AAAgC,eAAO;IACjE;AAEA,eAAW,OAAO,MAAM,WAAW;AACjC,YAAI,WAAM,UAAU,GAAG,MAAnB,mBAAsB,YAAA;AAAgC,eAAO;IACnE;AAEA,WAAO;EACT;AAEA,WAAS,eAAe,SAAgD,OAAyB;AAC/F,UAAM,YAAY,MAAM,SAAS;AACjC,UAAM,QAAQ,UAAU,WAAW;AACnC,4BAAwB,KAAK,GAAG,OAAO;AAEvC,QAAI,MAAM,OAAO,yBAAyB,aAAa,mBAAmB,KAAK,GAAG;AAChF;IACF;AAEA,UAAM,OAAO;AACb,8BAA0B,CAAC;AAC3B,QAAI,KAAK,WAAW;AAAG;AACvB,UAAM,eAAe,IAAI,KAAK,oBAAoB,WAAW,IAAI;AACjE,YAAQ,MAAM,MAAM;AAClB,YAAM,cAAc,MAAM,KAAK,aAAa,OAAO,CAAC;AAEpD,iBAAW;QACT;MACF,KAAK,aAAa;AAChB,cAAM,gBAAgB,MAAM,QAAQ,aAAa;AACjD,cAAM,uBAAuB,cAAc,qBAAqB,aAAa,KAAK,CAAC;AAEnF,YAAI,eAAe;AACjB,cAAI,gBAAgB,oBAAoB,MAAM,GAAG;AAC/C,kBAAM,SAAS,kBAAkB;cAC/B;YACF,CAAC,CAAC;UACJ,WAAW,cAAc,WAAA,iBAAsC;AAC7D,kBAAM,SAAS,aAAa,eAAe,aAAa,CAAC;UAC3D;QACF;MACF;IACF,CAAC;EACH;AAEA,SAAO;AACT;ACnFO,IAAM,sBAA8C,CAAC;EAC1D;EACA;EACA;EACA;EACA;AACF,MAAM;AACJ,QAAM,eAID,CAAC;AAEN,QAAM,UAAwC,CAAC,QAAQ,UAAU;AAC/D,QAAI,IAAI,gBAAgB,0BAA0B,MAAM,MAAM,KAAK,IAAI,gBAAgB,uBAAuB,MAAM,MAAM,GAAG;AAC3H,4BAAsB,OAAO,SAAS,KAAK;IAC7C;AAEA,QAAI,WAAW,QAAQ,MAAM,MAAM,KAAK,WAAW,SAAS,MAAM,MAAM,KAAK,OAAO,KAAK,WAAW;AAClG,4BAAsB,OAAO,KAAK,KAAK,KAAK;IAC9C;AAEA,QAAI,WAAW,UAAU,MAAM,MAAM,KAAK,WAAW,SAAS,MAAM,MAAM,KAAK,CAAC,OAAO,KAAK,WAAW;AACrG,oBAAc,OAAO,KAAK,KAAK,KAAK;IACtC;AAEA,QAAI,IAAI,KAAK,cAAc,MAAM,MAAM,GAAG;AACxC,iBAAW;IACb;EACF;AAEA,WAAS,cAAc;IACrB;EACF,GAA4BA,MAAuB;AACjD,UAAM,QAAQA,KAAI,SAAS,EAAE,WAAW;AACxC,UAAM,gBAAgB,MAAM,QAAQ,aAAa;AACjD,UAAM,gBAAgB,cAAc,qBAAqB,aAAa;AACtE,QAAI,CAAC,iBAAiB,cAAc,WAAA;AAAsC;AAC1E,UAAM;MACJ;MACA;IACF,IAAI,0BAA0B,aAAa;AAC3C,QAAI,CAAC,OAAO,SAAS,qBAAqB;AAAG;AAC7C,UAAM,cAAc,aAAa,aAAa;AAE9C,QAAI,2CAAa,SAAS;AACxB,mBAAa,YAAY,OAAO;AAChC,kBAAY,UAAU;IACxB;AAEA,UAAM,oBAAoB,KAAK,IAAI,IAAI;AACvC,iBAAa,aAAa,IAAI;MAC5B;MACA,iBAAiB;MACjB,SAAS,WAAW,MAAM;AACxB,YAAI,MAAM,OAAO,WAAW,CAAC,wBAAwB;AACnDA,eAAI,SAAS,aAAa,eAAe,aAAa,CAAC;QACzD;AAEA,sBAAc;UACZ;QACF,GAAGA,IAAG;MACR,GAAG,qBAAqB;IAC1B;EACF;AAEA,WAAS,sBAAsB;IAC7B;EACF,GAA4BA,MAAuB;AACjD,UAAM,QAAQA,KAAI,SAAS,EAAE,WAAW;AACxC,UAAM,gBAAgB,MAAM,QAAQ,aAAa;AACjD,UAAM,gBAAgB,cAAc,qBAAqB,aAAa;AAEtE,QAAI,CAAC,iBAAiB,cAAc,WAAA,iBAAsC;AACxE;IACF;AAEA,UAAM;MACJ;IACF,IAAI,0BAA0B,aAAa;AAE3C,QAAI,CAAC,OAAO,SAAS,qBAAqB,GAAG;AAC3C,wBAAkB,aAAa;AAC/B;IACF;AAEA,UAAM,cAAc,aAAa,aAAa;AAC9C,UAAM,oBAAoB,KAAK,IAAI,IAAI;AAEvC,QAAI,CAAC,eAAe,oBAAoB,YAAY,mBAAmB;AACrE,oBAAc;QACZ;MACF,GAAGA,IAAG;IACR;EACF;AAEA,WAAS,kBAAkB,KAAa;AACtC,UAAM,eAAe,aAAa,GAAG;AAErC,QAAI,6CAAc,SAAS;AACzB,mBAAa,aAAa,OAAO;IACnC;AAEA,WAAO,aAAa,GAAG;EACzB;AAEA,WAAS,aAAa;AACpB,eAAW,OAAO,OAAO,KAAK,YAAY,GAAG;AAC3C,wBAAkB,GAAG;IACvB;EACF;AAEA,WAAS,0BAA0B,cAA2B,CAAC,GAAG;AAChE,QAAI,yBAA8C;AAClD,QAAI,wBAAwB,OAAO;AAEnC,aAAS,OAAO,aAAa;AAC3B,UAAI,CAAC,CAAC,YAAY,GAAG,EAAE,iBAAiB;AACtC,gCAAwB,KAAK,IAAI,YAAY,GAAG,EAAE,iBAAkB,qBAAqB;AACzF,iCAAyB,YAAY,GAAG,EAAE,0BAA0B;MACtE;IACF;AAEA,WAAO;MACL;MACA;IACF;EACF;AAEA,SAAO;AACT;AChIO,IAAM,0BAAkD,CAAC;EAC9D;EACA;EACA;EACA;EACA;AACF,MAAM;AACJ,QAAM;IACJ;EACF,IAAI,IAAI;AAER,QAAM,UAAwC,CAAC,QAAQ,UAAU;AAC/D,QAAI,QAAQ,MAAM,MAAM,GAAG;AACzB,0BAAoB,OAAO,gBAAgB;IAC7C;AAEA,QAAI,SAAS,MAAM,MAAM,GAAG;AAC1B,0BAAoB,OAAO,oBAAoB;IACjD;EACF;AAEA,WAAS,oBAAoBA,MAAuB,MAA+C;AACjG,UAAM,QAAQA,KAAI,SAAS,EAAE,WAAW;AACxC,UAAM,UAAU,MAAM;AACtB,UAAM,gBAAgB,cAAc;AACpC,YAAQ,MAAM,MAAM;AAClB,iBAAW,iBAAiB,OAAO,KAAK,aAAa,GAAG;AACtD,cAAM,gBAAgB,QAAQ,aAAa;AAC3C,cAAM,uBAAuB,cAAc,aAAa;AACxD,YAAI,CAAC,wBAAwB,CAAC;AAAe;AAC7C,cAAM,gBAAgB,OAAO,OAAO,oBAAoB,EAAE,KAAK,CAAA,QAAO,IAAI,IAAI,MAAM,IAAI,KAAK,OAAO,OAAO,oBAAoB,EAAE,MAAM,CAAA,QAAO,IAAI,IAAI,MAAM,MAAS,KAAK,MAAM,OAAO,IAAI;AAE3L,YAAI,eAAe;AACjB,cAAI,gBAAgB,oBAAoB,MAAM,GAAG;AAC/CA,iBAAI,SAAS,kBAAkB;cAC7B;YACF,CAAC,CAAC;UACJ,WAAW,cAAc,WAAA,iBAAsC;AAC7DA,iBAAI,SAAS,aAAa,eAAe,aAAa,CAAC;UACzD;QACF;MACF;IACF,CAAC;EACH;AAEA,SAAO;AACT;AC8CA,IAAM,qBAAsB,IAAI,MAAM,kDAAkD;AAGjF,IAAM,6BAAqD,CAAC;EACjE;EACA;EACA;EACA;EACA;EACA;AACF,MAAM;AACJ,QAAM,eAAe,mBAAmB,UAAU;AAClD,QAAM,kBAAkB,mBAAmB,aAAa;AACxD,QAAM,mBAAmB,YAAY,YAAY,aAAa;AAQ9D,QAAM,eAA+C,CAAC;AAEtD,QAAM,UAAwC,CAAC,QAAQ,OAAO,gBAAgB;AAC5E,UAAM,WAAW,YAAY,MAAM;AAEnC,QAAI,WAAW,QAAQ,MAAM,MAAM,GAAG;AACpC,YAAM,WAAW,YAAY,WAAW,EAAE,QAAQ,QAAQ;AAC1D,YAAM,QAAQ,MAAM,SAAS,EAAE,WAAW,EAAE,QAAQ,QAAQ;AAE5D,UAAI,CAAC,YAAY,OAAO;AACtB,qBAAa,OAAO,KAAK,IAAI,cAAc,OAAO,KAAK,IAAI,cAAc,UAAU,OAAO,OAAO,KAAK,SAAS;MACjH;IACF,WAAW,cAAc,QAAQ,MAAM,MAAM,GAAG;AAC9C,YAAM,QAAQ,MAAM,SAAS,EAAE,WAAW,EAAE,UAAU,QAAQ;AAE9D,UAAI,OAAO;AACT,qBAAa,OAAO,KAAK,IAAI,cAAc,OAAO,KAAK,IAAI,cAAc,UAAU,OAAO,OAAO,KAAK,SAAS;MACjH;IACF,WAAW,iBAAiB,MAAM,GAAG;AACnC,YAAM,YAAY,aAAa,QAAQ;AAEvC,UAAI,uCAAW,eAAe;AAC5B,kBAAU,cAAc;UACtB,MAAM,OAAO;UACb,MAAM,OAAO,KAAK;QACpB,CAAC;AACD,eAAO,UAAU;MACnB;IACF,WAAW,IAAI,gBAAgB,kBAAkB,MAAM,MAAM,KAAK,IAAI,gBAAgB,qBAAqB,MAAM,MAAM,GAAG;AACxH,YAAM,YAAY,aAAa,QAAQ;AAEvC,UAAI,WAAW;AACb,eAAO,aAAa,QAAQ;AAC5B,kBAAU,kBAAkB;MAC9B;IACF,WAAW,IAAI,KAAK,cAAc,MAAM,MAAM,GAAG;AAC/C,iBAAW,CAACC,WAAU,SAAS,KAAK,OAAO,QAAQ,YAAY,GAAG;AAChE,eAAO,aAAaA,SAAQ;AAC5B,kBAAU,kBAAkB;MAC9B;IACF;EACF;AAEA,WAAS,YAAY,QAAa;AAChC,QAAI,aAAa,MAAM;AAAG,aAAO,OAAO,KAAK,IAAI;AAEjD,QAAI,gBAAgB,MAAM,GAAG;AAC3B,aAAO,OAAO,KAAK,IAAI,iBAAiB,OAAO,KAAK;IACtD;AAEA,QAAI,IAAI,gBAAgB,kBAAkB,MAAM,MAAM;AAAG,aAAO,OAAO,QAAQ;AAC/E,QAAI,IAAI,gBAAgB,qBAAqB,MAAM,MAAM;AAAG,aAAO,oBAAoB,OAAO,OAAO;AACrG,WAAO;EACT;AAEA,WAAS,aAAa,cAAsB,cAAmB,eAAuB,OAAyB,WAAmB;AAChI,UAAM,qBAAqB,QAAQ,oBAAoB,YAAY;AACnE,UAAM,oBAAoB,yDAAoB;AAC9C,QAAI,CAAC;AAAmB;AACxB,QAAI,YAAa,CAAC;AAClB,UAAM,oBAAoB,IAAI,QAAc,CAAA,YAAW;AACrD,gBAAU,oBAAoB;IAChC,CAAC;AACD,UAAM,kBAG0B,QAAQ,KAAK,CAAC,IAAI,QAG/C,CAAA,YAAW;AACZ,gBAAU,gBAAgB;IAC5B,CAAC,GAAG,kBAAkB,KAAK,MAAM;AAC/B,YAAM;IACR,CAAC,CAAC,CAAC;AAGH,oBAAgB,MAAM,MAAM;IAAC,CAAC;AAC9B,iBAAa,aAAa,IAAI;AAC9B,UAAM,WAAY,IAAI,UAAU,YAAY,EAAU,OAAO,mBAAmB,SAAA,UAAgC,eAAe,aAAa;AAC5I,UAAM,QAAQ,MAAM,SAAS,CAAC,GAAG,IAAIC,WAAUA,MAAK;AACpD,UAAM,eAAe;MAAE,GAAG;MACxB,eAAe,MAAM,SAAS,MAAM,SAAS,CAAC;MAC9C;MACA;MACA,kBAAoB,mBAAmB,SAAA,UAAgC,CAAC,iBAA8B,MAAM,SAAS,IAAI,KAAK,gBAAiB,cAAwB,cAAc,YAAY,CAAC,IAAI;MACtM;MACA;IACF;AACA,UAAM,iBAAiB,kBAAkB,cAAc,YAAY;AAEnE,YAAQ,QAAQ,cAAc,EAAE,MAAM,CAAA,MAAK;AACzC,UAAI,MAAM;AAAoB;AAC9B,YAAM;IACR,CAAC;EACH;AAEA,SAAO;AACT;ACpEO,IAAM,6BAAqD,CAAC;EACjE;EACA;EACA;EACA;AACF,MAAM;AACJ,QAAM,iBAAiB,UAAU,YAAY,aAAa;AAC1D,QAAM,kBAAkB,WAAW,YAAY,aAAa;AAC5D,QAAM,oBAAoB,YAAY,YAAY,aAAa;AAQ/D,QAAM,eAA+C,CAAC;AAEtD,QAAM,UAAwC,CAAC,QAAQ,UAAU;;AAC/D,QAAI,eAAe,MAAM,GAAG;AAC1B,YAAM;QACJ;QACA,KAAK;UACH;UACA;QACF;MACF,IAAI,OAAO;AACX,YAAM,qBAAqB,QAAQ,oBAAoB,YAAY;AACnE,YAAM,iBAAiB,yDAAoB;AAE3C,UAAI,gBAAgB;AAClB,cAAM,YAAa,CAAC;AACpB,cAAM,iBAAiB,IAAK,QAGW,CAAC,SAAS,WAAW;AAC1D,oBAAU,UAAU;AACpB,oBAAU,SAAS;QACrB,CAAC;AAGD,uBAAe,MAAM,MAAM;QAAC,CAAC;AAC7B,qBAAa,SAAS,IAAI;AAC1B,cAAM,WAAY,IAAI,UAAU,YAAY,EAAU,OAAO,mBAAmB,SAAA,UAAgC,eAAe,SAAS;AACxI,cAAM,QAAQ,MAAM,SAAS,CAAC,GAAG,IAAIA,WAAUA,MAAK;AACpD,cAAM,eAAe;UAAE,GAAG;UACxB,eAAe,MAAM,SAAS,MAAM,SAAS,CAAC;UAC9C;UACA;UACA,kBAAoB,mBAAmB,SAAA,UAAgC,CAAC,iBAA8B,MAAM,SAAS,IAAI,KAAK,gBAAiB,cAAwB,cAAc,YAAY,CAAC,IAAI;UACtM;QACF;AACA,uBAAe,cAAc,YAAY;MAC3C;IACF,WAAW,kBAAkB,MAAM,GAAG;AACpC,YAAM;QACJ;QACA;MACF,IAAI,OAAO;AACX,yBAAa,SAAS,MAAtB,mBAAyB,QAAQ;QAC/B,MAAM,OAAO;QACb,MAAM;MACR;AACA,aAAO,aAAa,SAAS;IAC/B,WAAW,gBAAgB,MAAM,GAAG;AAClC,YAAM;QACJ;QACA;QACA;MACF,IAAI,OAAO;AACX,yBAAa,SAAS,MAAtB,mBAAyB,OAAO;QAC9B,OAAO,OAAO,WAAW,OAAO;QAChC,kBAAkB,CAAC;QACnB,MAAO;MACT;AACA,aAAO,aAAa,SAAS;IAC/B;EACF;AAEA,SAAO;AACT;AClOO,IAAM,uBAA+C,CAAC;EAC3D;EACA,SAAS;IACP;EACF;EACA;AACF,MAAM;AACJ,SAAO,CAAC,QAAQ,UAAU;;AACxB,QAAI,IAAI,KAAK,cAAc,MAAM,MAAM,GAAG;AAExC,YAAM,SAAS,IAAI,gBAAgB,qBAAqB,MAAM,CAAC;IACjE;AAEA,QAAI,OAAO,YAAY,eAAe,MAAwC;AAC5E,UAAI,IAAI,gBAAgB,qBAAqB,MAAM,MAAM,KAAK,OAAO,YAAY,YAAU,iBAAM,SAAS,EAAE,WAAW,MAA5B,mBAA+B,WAA/B,mBAAuC,0BAAyB,YAAY;AACrK,gBAAQ,KAAK,yEAAyE,WAAW;8FACX,gBAAgB,QAAQ;iGACrB,EAAE,EAAE;MAC/F;IACF;EACF;AACF;ACjBO,IAAM,6BAAoI,CAAC;EAChJ;EACA;EACA;AACF,MAAM;AACJ,QAAM,sBAAsB,GAAG,IAAI,WAAW;AAC9C,MAAI,wBAA6C;AACjD,MAAI,kBAA+D;AACnE,QAAM;IACJ;IACA;EACF,IAAI,IAAI;AAGR,QAAM,8BAA8B,CAAC,cAAiC,WAAmB;;AACvF,QAAI,0BAA0B,MAAM,MAAM,GAAG;AAC3C,YAAM;QACJ;QACA;QACA;MACF,IAAI,OAAO;AAEX,WAAI,kDAAe,mBAAf,mBAAgC,YAAY;AAC9C,qBAAa,aAAa,EAAG,SAAS,IAAI;MAC5C;AAEA,aAAO;IACT;AAEA,QAAI,uBAAuB,MAAM,MAAM,GAAG;AACxC,YAAM;QACJ;QACA;MACF,IAAI,OAAO;AAEX,UAAI,aAAa,aAAa,GAAG;AAC/B,eAAO,aAAa,aAAa,EAAG,SAAS;MAC/C;AAEA,aAAO;IACT;AAEA,QAAI,IAAI,gBAAgB,kBAAkB,MAAM,MAAM,GAAG;AACvD,aAAO,aAAa,OAAO,QAAQ,aAAa;AAChD,aAAO;IACT;AAEA,QAAI,WAAW,QAAQ,MAAM,MAAM,GAAG;AACpC,YAAM;QACJ,MAAM;UACJ;UACA;QACF;MACF,IAAI;AACJ,YAAM,WAAW,kBAAa,IAAI,mBAAjB,mBAAoC,CAAC;AACtD,eAAS,GAAG,SAAS,UAAU,IAAI,CAAC;AAEpC,UAAI,IAAI,WAAW;AACjB,iBAAS,SAAS,IAAI,IAAI,uBAAuB,SAAS,SAAS,KAAK,CAAC;MAC3E;AAEA,aAAO;IACT;AAEA,QAAI,UAAU;AAEd,QAAI,WAAW,UAAU,MAAM,MAAM,KAAK,WAAW,SAAS,MAAM,MAAM,GAAG;AAC3E,YAAM,QAAQ,aAAa,OAAO,KAAK,IAAI,aAAa,KAAK,CAAC;AAC9D,YAAM,MAAM,GAAG,OAAO,KAAK,SAAS;AACpC,4BAAY,CAAC,CAAC,MAAM,GAAG;AACvB,aAAO,MAAM,GAAG;IAClB;AAEA,QAAI,WAAW,SAAS,MAAM,MAAM,GAAG;AACrC,YAAM;QACJ,MAAM;UACJ;UACA;UACA;QACF;MACF,IAAI;AAEJ,UAAI,aAAa,IAAI,WAAW;AAC9B,cAAM,WAAW,kBAAa,IAAI,mBAAjB,mBAAoC,CAAC;AACtD,iBAAS,SAAS,IAAI,IAAI,uBAAuB,SAAS,SAAS,KAAK,CAAC;AACzE,kBAAU;MACZ;IACF;AAEA,WAAO;EACT;AAEA,QAAM,mBAAmB,MAAM,cAAc;AAE7C,QAAM,uBAAuB,CAAC,kBAA0B;AACtD,UAAM,gBAAgB,iBAAiB;AACvC,UAAM,2BAA2B,cAAc,aAAa,KAAK,CAAC;AAClE,WAAO,gBAAgB,wBAAwB;EACjD;AAEA,QAAM,sBAAsB,CAAC,eAAuB,cAAsB;;AACxE,UAAM,gBAAgB,iBAAiB;AACvC,WAAO,CAAC,GAAC,oDAAgB,mBAAhB,mBAAiC;EAC5C;AAEA,QAAM,wBAA+C;IACnD;IACA;IACA;EACF;AACA,SAAO,CAAC,QAAQ,UAAoF;AAClG,QAAI,CAAC,uBAAuB;AAE1B,8BAAwB,KAAK,MAAM,KAAK,UAAU,cAAc,oBAAoB,CAAC;IACvF;AAEA,QAAI,IAAI,KAAK,cAAc,MAAM,MAAM,GAAG;AACxC,8BAAwB,cAAc,uBAAuB,CAAC;AAC9D,wBAAkB;AAClB,aAAO,CAAC,MAAM,KAAK;IACrB;AAMA,QAAI,IAAI,gBAAgB,8BAA8B,MAAM,MAAM,GAAG;AACnE,aAAO,CAAC,OAAO,qBAAqB;IACtC;AAGA,UAAM,YAAY,4BAA4B,cAAc,sBAAsB,MAAM;AACxF,QAAI,uBAAuB;AAE3B,QAAI,WAAW;AACb,UAAI,CAAC,iBAAiB;AAMpB,0BAAkB,WAAW,MAAM;AAEjC,gBAAM,mBAAsC,KAAK,MAAM,KAAK,UAAU,cAAc,oBAAoB,CAAC;AAEzG,gBAAM,CAAC,EAAE,OAAO,IAAIC,mBAAmB,uBAAuB,MAAM,gBAAgB;AAEpF,gBAAM,KAAK,IAAI,gBAAgB,qBAAqB,OAAO,CAAC;AAE5D,kCAAwB;AACxB,4BAAkB;QACpB,GAAG,GAAG;MACR;AAEA,YAAM,4BAA4B,OAAO,OAAO,QAAQ,YAAY,CAAC,CAAC,OAAO,KAAK,WAAW,mBAAmB;AAChH,YAAM,iCAAiC,WAAW,SAAS,MAAM,MAAM,KAAK,OAAO,KAAK,aAAa,CAAC,CAAC,OAAO,KAAK,IAAI;AACvH,6BAAuB,CAAC,6BAA6B,CAAC;IACxD;AAEA,WAAO,CAAC,sBAAsB,KAAK;EACrC;AACF;ACxJO,SAAS,gBAA8G,OAAiE;AAC7L,QAAM;IACJ;IACA;IACA;IACA;EACF,IAAI;AACJ,QAAM;IACJ;EACF,IAAI;AACJ,QAAM,UAAU;IACd,gBAAgB,aAA6D,GAAG,WAAW,iBAAiB;EAC9G;AAEA,QAAM,uBAAuB,CAAC,WAAmB,OAAO,KAAK,WAAW,GAAG,WAAW,GAAG;AAEzF,QAAM,kBAA4C,CAAC,sBAAsB,6BAA6B,gCAAgC,qBAAqB,4BAA4B,0BAA0B;AAEjN,QAAM,aAAkH,CAAA,UAAS;AAC/H,QAAIC,eAAc;AAClB,QAAI,gBAAyC;MAC3C,sBAAsB,CAAC;IACzB;AACA,UAAM,cAAc;MAAE,GAAK;MACzB;MACA;MACA;IACF;AACA,UAAM,WAAW,gBAAgB,IAAI,CAAA,UAAS,MAAM,WAAW,CAAC;AAChE,UAAM,wBAAwB,2BAA2B,WAAW;AACpE,UAAM,sBAAsB,wBAAwB,WAAW;AAC/D,WAAO,CAAA,SAAQ;AACb,aAAO,CAAA,WAAU;AACf,YAAI,CAAC,SAAS,MAAM,GAAG;AACrB,iBAAO,KAAK,MAAM;QACpB;AAEA,YAAI,CAACA,cAAa;AAChBA,yBAAc;AAEd,gBAAM,SAAS,IAAI,gBAAgB,qBAAqB,MAAM,CAAC;QACjE;AAEA,cAAM,gBAAgB;UAAE,GAAG;UACzB;QACF;AACA,cAAM,cAAc,MAAM,SAAS;AACnC,cAAM,CAAC,sBAAsB,mBAAmB,IAAI,sBAAsB,QAAQ,eAAe,WAAW;AAC5G,YAAI;AAEJ,YAAI,sBAAsB;AACxB,gBAAM,KAAK,MAAM;QACnB,OAAO;AACL,gBAAM;QACR;AAEA,YAAI,CAAC,CAAC,MAAM,SAAS,EAAE,WAAW,GAAG;AAGnC,8BAAoB,QAAQ,eAAe,WAAW;AAEtD,cAAI,qBAAqB,MAAM,KAAK,QAAQ,mBAAmB,MAAM,GAAG;AAGtE,qBAAS,WAAW,UAAU;AAC5B,sBAAQ,QAAQ,eAAe,WAAW;YAC5C;UACF;QACF;AAEA,eAAO;MACT;IACF;EACF;AAEA,SAAO;IACL;IACA;EACF;AAEA,WAAS,aAAa,eAElB,eAAuB,WAAmC,CAAC,GAAG;AAChE,WAAO,WAAW;MAChB,MAAM;MACN,cAAc,cAAc;MAC5B,cAAc,cAAc;MAC5B,WAAW;MACX,cAAc;MACd;MACA,GAAG;IACL,CAAC;EACH;AACF;ACxGO,SAAS,WAAc,GAAwB;AAAC;AAChD,SAAS,WAA6B,WAAc,MAAqC;AAC9F,SAAO,OAAO,OAAO,QAAQ,GAAG,IAAI;AACtC;ACiCO,IAAM,iBAAgC,OAAO;AAyT7C,IAAM,aAAa,CAAC;EACzB,gBAAAT,kBAAiB;AACnB,IAAuB,CAAC,OAA2B;EACjD,MAAM;EAEN,KAAK,KAAK;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF,GAAG,SAAS;AACV,kBAAc;AACd,eAAuC,kBAAkB;AAEzD,UAAM,gBAAgC,CAAA,QAAO;AAC3C,UAAI,OAAO,YAAY,eAAe,MAAwC;AAC5E,YAAI,CAAC,SAAS,SAAU,IAAI,IAAY,GAAG;AACzC,kBAAQ,MAAM,aAAa,IAAI,IAAI,gDAAgD;QACrF;MACF;AAEA,aAAO;IACT;AAEA,WAAO,OAAO,KAAK;MACjB;MACA,WAAW,CAAC;MACZ,iBAAiB;QACf;QACA;QACA;QACA;MACF;MACA,MAAM,CAAC;IACT,CAAC;AACD,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;MACA;IACF,IAAI,YAAY;MACd;MACA;MACA;MACA;MACA;MACA;IACF,CAAC;AACD,UAAM;MACJ;MACA,SAAS;IACX,IAAI,WAAW;MACb;MACA;MACA;MACA;MACA;MACA,QAAQ;QACN;QACA;QACA;QACA;QACA;QACA;MACF;IACF,CAAC;AACD,eAAW,IAAI,MAAM;MACnB;MACA;MACA;MACA;MACA,eAAe,aAAa;IAC9B,CAAC;AACD,eAAW,IAAI,iBAAiB,YAAY;AAC5C,UAAM;MACJ;MACA,SAAS;IACX,IAAI,gBAAgB;MAClB;MACA;MACA;MACA;MACA;MACA;IACF,CAAC;AACD,eAAW,IAAI,MAAM,iBAAiB;AACtC,eAAW,KAAK;MACd;MACA;IACF,CAAC;AACD,UAAM;MACJ;MACA;MACA;MACA;IACF,IAAI,eAAe;MACjB;MACA;MACA,gBAAAA;IACF,CAAC;AACD,eAAW,IAAI,MAAM;MACnB;MACA;IACF,CAAC;AACD,UAAM;MACJ;MACA;MACA;MACA;MACA;MACA;IACF,IAAI,cAAc;MAChB;MACA;MACA;MACA;MACA;IACF,CAAC;AACD,eAAW,IAAI,MAAM;MACnB;MACA;MACA;MACA;IACF,CAAC;AACD,WAAO;MACL,MAAM;MAEN,eAAe,cAAc,YAAY;;AACvC,cAAM,SAAW;AACjB,qBAAO,WAAP,qCAAoC,CAAC;AAErC,YAAI,kBAAkB,UAAU,GAAG;AACjC,qBAAW,OAAO,UAAU,YAAY,GAAG;YACzC,MAAM;YACN,QAAQ,mBAAmB,cAAc,UAAU;YACnD,UAAU,mBAAmB,cAAc,UAAU;UACvD,GAAG,uBAAuB,YAAY,YAAY,CAAC;QACrD,WAAW,qBAAqB,UAAU,GAAG;AAC3C,qBAAW,OAAO,UAAU,YAAY,GAAG;YACzC,MAAM;YACN,QAAQ,sBAAsB;YAC9B,UAAU,sBAAsB,YAAY;UAC9C,GAAG,uBAAuB,eAAe,YAAY,CAAC;QACxD;MACF;IAEF;EACF;AAEF;AC3fA,IAAM,YAA2B,eAAe,WAAW,CAAC;", "names": ["QueryStatus", "isPlainObject", "retry", "arg", "force", "createSelector", "key", "queryArgsApi", "_formatProdErrorMessage", "internalState", "api", "cache<PERSON>ey", "extra", "produceWithPatches", "initialized"]}