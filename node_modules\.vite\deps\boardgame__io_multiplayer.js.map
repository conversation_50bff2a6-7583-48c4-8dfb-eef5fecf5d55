{"version": 3, "sources": ["../../boardgame.io/dist/esm/multiplayer.js"], "sourcesContent": ["import 'redux';\nimport './turn-order-8cc4909b.js';\nimport 'immer';\nimport './plugin-random-087f861e.js';\nimport 'lodash.isplainobject';\nimport './reducer-24ea3e4c.js';\nimport 'rfc6902';\nimport './initialize-7316768f.js';\nimport './transport-ce07b771.js';\nimport './util-991e76bb.js';\nexport { L as Local, S as SocketIO } from './socketio-a82b84e4.js';\nimport './master-17425f07.js';\nimport './filter-player-view-43ed49b0.js';\nimport 'socket.io-client';\n"], "mappings": ";;;;;;;;;;;;;AAIA,oBAAO;AAEP,qBAAO;", "names": []}