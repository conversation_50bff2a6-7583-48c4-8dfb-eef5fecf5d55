import React, { useRef, useState, useCallback, useEffect } from 'react';
import { useFrame, useThree } from '@react-three/fiber';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { updateTerrain } from '../../store/editor';
import * as THREE from 'three';

interface TerrainSystemProps {
  configuration: any;
  editable: boolean;
}

export const TerrainSystem: React.FC<TerrainSystemProps> = ({
  configuration,
  editable
}) => {
  const dispatch = useDispatch();
  const { camera, raycaster, scene, gl } = useThree();
  const editorState = useSelector((state: RootState) => state.editor.editorState);
  const currentMap = useSelector((state: RootState) => state.editor.currentMap);

  const terrainRef = useRef<THREE.Mesh>(null);
  const [terrainGeometry, setTerrainGeometry] = useState<THREE.PlaneGeometry | null>(null);
  const [heightData, setHeightData] = useState<Float32Array | null>(null);
  const [mousePosition, setMousePosition] = useState(new THREE.Vector2());
  const [isMouseDown, setIsMouseDown] = useState(false);

  // Terrain settings
  const terrainSize = 100;
  const terrainResolution = 128;
  const maxHeight = 10;

  // Initialize terrain
  useEffect(() => {
    const geometry = new THREE.PlaneGeometry(
      terrainSize,
      terrainSize,
      terrainResolution - 1,
      terrainResolution - 1
    );
    geometry.rotateX(-Math.PI / 2);

    // Initialize height data
    const heights = new Float32Array(terrainResolution * terrainResolution);
    heights.fill(0);

    setTerrainGeometry(geometry);
    setHeightData(heights);
  }, []);

  // Reset terrain when requested
  useEffect(() => {
    if (currentMap?.terrain?.resetRequested && heightData) {
      console.log('Resetting terrain to flat surface');
      const newHeights = new Float32Array(terrainResolution * terrainResolution);
      newHeights.fill(0);
      setHeightData(newHeights);

      // Clear the reset flag to prevent infinite loop
      dispatch(updateTerrain({ resetRequested: false }));
    }
  }, [currentMap?.terrain?.resetRequested, heightData, dispatch]);

  // Generate random terrain when requested
  useEffect(() => {
    if (currentMap?.terrain?.generateRandom && heightData) {
      console.log('Generating random terrain');
      const newHeights = new Float32Array(terrainResolution * terrainResolution);

      // Generate random terrain using noise
      for (let x = 0; x < terrainResolution; x++) {
        for (let z = 0; z < terrainResolution; z++) {
          const index = z * terrainResolution + x;

          // Simple noise function for random terrain
          const nx = x / terrainResolution;
          const nz = z / terrainResolution;

          // Multiple octaves of noise for more natural terrain
          let height = 0;
          height += Math.sin(nx * Math.PI * 4) * Math.cos(nz * Math.PI * 4) * 2;
          height += Math.sin(nx * Math.PI * 8) * Math.cos(nz * Math.PI * 8) * 1;
          height += Math.sin(nx * Math.PI * 16) * Math.cos(nz * Math.PI * 16) * 0.5;
          height += (Math.random() - 0.5) * 0.5; // Add some randomness

          newHeights[index] = height;
        }
      }

      setHeightData(newHeights);

      // Clear the generate flag to prevent infinite loop
      dispatch(updateTerrain({ generateRandom: false }));
    }
  }, [currentMap?.terrain?.generateRandom, heightData, dispatch]);

  // Update terrain geometry when height data changes
  useEffect(() => {
    if (terrainGeometry && heightData && terrainRef.current) {
      const positions = terrainGeometry.attributes.position.array as Float32Array;

      for (let i = 0; i < heightData.length; i++) {
        positions[i * 3 + 1] = heightData[i]; // Y coordinate
      }

      terrainGeometry.attributes.position.needsUpdate = true;
      terrainGeometry.computeVertexNormals();
    }
  }, [terrainGeometry, heightData]);

  // Mouse event handlers for terrain sculpting
  useEffect(() => {
    if (!editable) return;

    const handleMouseMove = (event: MouseEvent) => {
      const rect = gl.domElement.getBoundingClientRect();
      mousePosition.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mousePosition.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
      setMousePosition(mousePosition.clone());

      if (isMouseDown) {
        sculptTerrain();
      }
    };

    const handleMouseDown = (event: MouseEvent) => {
      if (event.button === 0) { // Left mouse button
        setIsMouseDown(true);
        sculptTerrain();
      }
    };

    const handleMouseUp = (event: MouseEvent) => {
      if (event.button === 0) {
        setIsMouseDown(false);
      }
    };

    gl.domElement.addEventListener('mousemove', handleMouseMove);
    gl.domElement.addEventListener('mousedown', handleMouseDown);
    gl.domElement.addEventListener('mouseup', handleMouseUp);

    return () => {
      gl.domElement.removeEventListener('mousemove', handleMouseMove);
      gl.domElement.removeEventListener('mousedown', handleMouseDown);
      gl.domElement.removeEventListener('mouseup', handleMouseUp);
    };
  }, [editable, isMouseDown, mousePosition, gl]);

  const sculptTerrain = useCallback(() => {
    if (!terrainRef.current || !heightData || !terrainGeometry) return;

    // Update raycaster
    raycaster.setFromCamera(mousePosition, camera);

    // Intersect with terrain
    const intersects = raycaster.intersectObject(terrainRef.current);
    if (intersects.length === 0) return;

    const intersection = intersects[0];
    const point = intersection.point;

    // Convert world position to terrain grid coordinates
    const x = Math.floor(((point.x + terrainSize / 2) / terrainSize) * terrainResolution);
    const z = Math.floor(((point.z + terrainSize / 2) / terrainSize) * terrainResolution);

    if (x < 0 || x >= terrainResolution || z < 0 || z >= terrainResolution) return;

    // Get brush settings from editor state
    const brushSize = editorState.terrainBrushSize || 5;
    const brushStrength = editorState.terrainBrushStrength || 0.5;

    // Map editor tool to terrain tool
    let tool = editorState.terrainTool || 'raise';
    if (editorState.selectedTool) {
      switch (editorState.selectedTool) {
        case 'terrain_raise':
          tool = 'raise';
          break;
        case 'terrain_lower':
          tool = 'lower';
          break;
        case 'terrain_smooth':
          tool = 'smooth';
          break;
        case 'terrain_flatten':
          tool = 'flatten';
          break;
      }
    }

    // Apply brush effect
    const newHeightData = new Float32Array(heightData);

    for (let dx = -brushSize; dx <= brushSize; dx++) {
      for (let dz = -brushSize; dz <= brushSize; dz++) {
        const nx = x + dx;
        const nz = z + dz;

        if (nx < 0 || nx >= terrainResolution || nz < 0 || nz >= terrainResolution) continue;

        const distance = Math.sqrt(dx * dx + dz * dz);
        if (distance > brushSize) continue;

        const falloff = 1 - (distance / brushSize);
        const index = nz * terrainResolution + nx;

        switch (tool) {
          case 'raise':
            newHeightData[index] += brushStrength * falloff;
            break;
          case 'lower':
            newHeightData[index] -= brushStrength * falloff;
            break;
          case 'smooth':
            // Average with neighboring heights
            let sum = 0;
            let count = 0;
            for (let sx = -1; sx <= 1; sx++) {
              for (let sz = -1; sz <= 1; sz++) {
                const sampleX = nx + sx;
                const sampleZ = nz + sz;
                if (sampleX >= 0 && sampleX < terrainResolution &&
                    sampleZ >= 0 && sampleZ < terrainResolution) {
                  sum += heightData[sampleZ * terrainResolution + sampleX];
                  count++;
                }
              }
            }
            const average = sum / count;
            newHeightData[index] = THREE.MathUtils.lerp(
              newHeightData[index],
              average,
              brushStrength * falloff
            );
            break;
          case 'flatten':
            newHeightData[index] = THREE.MathUtils.lerp(
              newHeightData[index],
              0,
              brushStrength * falloff
            );
            break;
        }

        // Clamp height values
        newHeightData[index] = THREE.MathUtils.clamp(
          newHeightData[index],
          -maxHeight,
          maxHeight
        );
      }
    }

    setHeightData(newHeightData);
  }, [
    mousePosition,
    camera,
    raycaster,
    heightData,
    terrainGeometry,
    editorState.terrainBrushSize,
    editorState.terrainBrushStrength,
    editorState.terrainTool,
    editorState.selectedTool
  ]);

  if (!terrainGeometry) return null;

  return (
    <mesh
      ref={terrainRef}
      geometry={terrainGeometry}
      receiveShadow
      castShadow
    >
      <meshLambertMaterial
        color={editorState.terrainColor || '#8B7355'}
        wireframe={editorState.terrainWireframe || false}
      />
    </mesh>
  );
};
