import React from 'react';
import { Grid } from '@react-three/drei';

interface EditorGridProps {
  size?: number;
  divisions?: number;
  gridSize?: number;
  color?: string;
  opacity?: number;
}

export const EditorGrid: React.FC<EditorGridProps> = ({
  size = 100,
  divisions = 100,
  gridSize = 1,
  color = '#888888',
  opacity = 0.3,
}) => {
  return (
    <>
      {/* Main grid */}
      <Grid
        position={[0, 0, 0]}
        args={[size, size]}
        cellSize={gridSize}
        cellThickness={0.5}
        cellColor={color}
        sectionSize={gridSize * 10}
        sectionThickness={1}
        sectionColor={color}
        fadeDistance={size}
        fadeStrength={1}
        infiniteGrid={false}
      />
      
      {/* Center axes */}
      <group>
        {/* X-axis (red) */}
        <line>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={2}
              array={new Float32Array([-size/2, 0, 0, size/2, 0, 0])}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial color="#ff0000" opacity={0.8} transparent />
        </line>
        
        {/* Z-axis (blue) */}
        <line>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={2}
              array={new Float32Array([0, 0, -size/2, 0, 0, size/2])}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial color="#0000ff" opacity={0.8} transparent />
        </line>
        
        {/* Y-axis (green) */}
        <line>
          <bufferGeometry>
            <bufferAttribute
              attach="attributes-position"
              count={2}
              array={new Float32Array([0, 0, 0, 0, size/4, 0])}
              itemSize={3}
            />
          </bufferGeometry>
          <lineBasicMaterial color="#00ff00" opacity={0.8} transparent />
        </line>
      </group>
    </>
  );
};
