import {
  __commonJS,
  __toESM
} from "./chunk-UV5CTPV7.js";

// node_modules/lodash.isplainobject/index.js
var require_lodash = __commonJS({
  "node_modules/lodash.isplainobject/index.js"(exports, module) {
    var objectTag = "[object Object]";
    function isHostObject(value) {
      var result = false;
      if (value != null && typeof value.toString != "function") {
        try {
          result = !!(value + "");
        } catch (e) {
        }
      }
      return result;
    }
    function overArg(func, transform) {
      return function(arg) {
        return func(transform(arg));
      };
    }
    var funcProto = Function.prototype;
    var objectProto = Object.prototype;
    var funcToString = funcProto.toString;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var objectCtorString = funcToString.call(Object);
    var objectToString = objectProto.toString;
    var getPrototype = overArg(Object.getPrototypeOf, Object);
    function isObjectLike(value) {
      return !!value && typeof value == "object";
    }
    function isPlainObject3(value) {
      if (!isObjectLike(value) || objectToString.call(value) != objectTag || isHostObject(value)) {
        return false;
      }
      var proto = getPrototype(value);
      if (proto === null) {
        return true;
      }
      var Ctor = hasOwnProperty.call(proto, "constructor") && proto.constructor;
      return typeof Ctor == "function" && Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString;
    }
    module.exports = isPlainObject3;
  }
});

// node_modules/rfc6902/pointer.js
var require_pointer = __commonJS({
  "node_modules/rfc6902/pointer.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Pointer = void 0;
    function unescape(token) {
      return token.replace(/~1/g, "/").replace(/~0/g, "~");
    }
    function escape(token) {
      return token.replace(/~/g, "~0").replace(/\//g, "~1");
    }
    var Pointer = (
      /** @class */
      function() {
        function Pointer2(tokens) {
          if (tokens === void 0) {
            tokens = [""];
          }
          this.tokens = tokens;
        }
        Pointer2.fromJSON = function(path) {
          var tokens = path.split("/").map(unescape);
          if (tokens[0] !== "")
            throw new Error("Invalid JSON Pointer: ".concat(path));
          return new Pointer2(tokens);
        };
        Pointer2.prototype.toString = function() {
          return this.tokens.map(escape).join("/");
        };
        Pointer2.prototype.evaluate = function(object) {
          var parent = null;
          var key = "";
          var value = object;
          for (var i2 = 1, l2 = this.tokens.length; i2 < l2; i2++) {
            parent = value;
            key = this.tokens[i2];
            if (key == "__proto__" || key == "constructor" || key == "prototype") {
              continue;
            }
            value = (parent || {})[key];
          }
          return { parent, key, value };
        };
        Pointer2.prototype.get = function(object) {
          return this.evaluate(object).value;
        };
        Pointer2.prototype.set = function(object, value) {
          var endpoint = this.evaluate(object);
          if (endpoint.parent) {
            endpoint.parent[endpoint.key] = value;
          }
        };
        Pointer2.prototype.push = function(token) {
          this.tokens.push(token);
        };
        Pointer2.prototype.add = function(token) {
          var tokens = this.tokens.concat(String(token));
          return new Pointer2(tokens);
        };
        return Pointer2;
      }()
    );
    exports.Pointer = Pointer;
  }
});

// node_modules/rfc6902/util.js
var require_util = __commonJS({
  "node_modules/rfc6902/util.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.clone = exports.objectType = exports.hasOwnProperty = void 0;
    exports.hasOwnProperty = Object.prototype.hasOwnProperty;
    function objectType(object) {
      if (object === void 0) {
        return "undefined";
      }
      if (object === null) {
        return "null";
      }
      if (Array.isArray(object)) {
        return "array";
      }
      return typeof object;
    }
    exports.objectType = objectType;
    function isNonPrimitive(value) {
      return value != null && typeof value == "object";
    }
    function clone(source) {
      if (!isNonPrimitive(source)) {
        return source;
      }
      if (source.constructor == Array) {
        var length_1 = source.length;
        var arrayTarget = new Array(length_1);
        for (var i2 = 0; i2 < length_1; i2++) {
          arrayTarget[i2] = clone(source[i2]);
        }
        return arrayTarget;
      }
      if (source.constructor == Date) {
        var dateTarget = /* @__PURE__ */ new Date(+source);
        return dateTarget;
      }
      var objectTarget = {};
      for (var key in source) {
        if (exports.hasOwnProperty.call(source, key)) {
          objectTarget[key] = clone(source[key]);
        }
      }
      return objectTarget;
    }
    exports.clone = clone;
  }
});

// node_modules/rfc6902/diff.js
var require_diff = __commonJS({
  "node_modules/rfc6902/diff.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.diffAny = exports.diffObjects = exports.diffArrays = exports.intersection = exports.subtract = exports.isDestructive = void 0;
    var util_1 = require_util();
    function isDestructive(_a) {
      var op = _a.op;
      return op === "remove" || op === "replace" || op === "copy" || op === "move";
    }
    exports.isDestructive = isDestructive;
    function subtract(minuend, subtrahend) {
      var obj = {};
      for (var add_key in minuend) {
        if (util_1.hasOwnProperty.call(minuend, add_key) && minuend[add_key] !== void 0) {
          obj[add_key] = 1;
        }
      }
      for (var del_key in subtrahend) {
        if (util_1.hasOwnProperty.call(subtrahend, del_key) && subtrahend[del_key] !== void 0) {
          delete obj[del_key];
        }
      }
      return Object.keys(obj);
    }
    exports.subtract = subtract;
    function intersection(objects) {
      var length = objects.length;
      var counter = {};
      for (var i2 = 0; i2 < length; i2++) {
        var object = objects[i2];
        for (var key in object) {
          if (util_1.hasOwnProperty.call(object, key) && object[key] !== void 0) {
            counter[key] = (counter[key] || 0) + 1;
          }
        }
      }
      for (var key in counter) {
        if (counter[key] < length) {
          delete counter[key];
        }
      }
      return Object.keys(counter);
    }
    exports.intersection = intersection;
    function isArrayAdd(array_operation) {
      return array_operation.op === "add";
    }
    function isArrayRemove(array_operation) {
      return array_operation.op === "remove";
    }
    function appendArrayOperation(base, operation) {
      return {
        // the new operation must be pushed on the end
        operations: base.operations.concat(operation),
        cost: base.cost + 1
      };
    }
    function diffArrays(input, output, ptr, diff) {
      if (diff === void 0) {
        diff = diffAny;
      }
      var memo = {
        "0,0": { operations: [], cost: 0 }
      };
      function dist(i2, j2) {
        var memo_key = "".concat(i2, ",").concat(j2);
        var memoized = memo[memo_key];
        if (memoized === void 0) {
          if (i2 > 0 && j2 > 0 && !diff(input[i2 - 1], output[j2 - 1], ptr.add(String(i2 - 1))).length) {
            memoized = dist(i2 - 1, j2 - 1);
          } else {
            var alternatives = [];
            if (i2 > 0) {
              var remove_base = dist(i2 - 1, j2);
              var remove_operation = {
                op: "remove",
                index: i2 - 1
              };
              alternatives.push(appendArrayOperation(remove_base, remove_operation));
            }
            if (j2 > 0) {
              var add_base = dist(i2, j2 - 1);
              var add_operation = {
                op: "add",
                index: i2 - 1,
                value: output[j2 - 1]
              };
              alternatives.push(appendArrayOperation(add_base, add_operation));
            }
            if (i2 > 0 && j2 > 0) {
              var replace_base = dist(i2 - 1, j2 - 1);
              var replace_operation = {
                op: "replace",
                index: i2 - 1,
                original: input[i2 - 1],
                value: output[j2 - 1]
              };
              alternatives.push(appendArrayOperation(replace_base, replace_operation));
            }
            var best = alternatives.sort(function(a2, b2) {
              return a2.cost - b2.cost;
            })[0];
            memoized = best;
          }
          memo[memo_key] = memoized;
        }
        return memoized;
      }
      var input_length = isNaN(input.length) || input.length <= 0 ? 0 : input.length;
      var output_length = isNaN(output.length) || output.length <= 0 ? 0 : output.length;
      var array_operations = dist(input_length, output_length).operations;
      var padded_operations = array_operations.reduce(function(_a, array_operation) {
        var operations = _a[0], padding = _a[1];
        if (isArrayAdd(array_operation)) {
          var padded_index = array_operation.index + 1 + padding;
          var index_token = padded_index < input_length + padding ? String(padded_index) : "-";
          var operation = {
            op: array_operation.op,
            path: ptr.add(index_token).toString(),
            value: array_operation.value
          };
          return [operations.concat(operation), padding + 1];
        } else if (isArrayRemove(array_operation)) {
          var operation = {
            op: array_operation.op,
            path: ptr.add(String(array_operation.index + padding)).toString()
          };
          return [operations.concat(operation), padding - 1];
        } else {
          var replace_ptr = ptr.add(String(array_operation.index + padding));
          var replace_operations = diff(array_operation.original, array_operation.value, replace_ptr);
          return [operations.concat.apply(operations, replace_operations), padding];
        }
      }, [[], 0])[0];
      return padded_operations;
    }
    exports.diffArrays = diffArrays;
    function diffObjects(input, output, ptr, diff) {
      if (diff === void 0) {
        diff = diffAny;
      }
      var operations = [];
      subtract(input, output).forEach(function(key) {
        operations.push({ op: "remove", path: ptr.add(key).toString() });
      });
      subtract(output, input).forEach(function(key) {
        operations.push({ op: "add", path: ptr.add(key).toString(), value: output[key] });
      });
      intersection([input, output]).forEach(function(key) {
        operations.push.apply(operations, diff(input[key], output[key], ptr.add(key)));
      });
      return operations;
    }
    exports.diffObjects = diffObjects;
    function diffAny(input, output, ptr, diff) {
      if (diff === void 0) {
        diff = diffAny;
      }
      if (input === output) {
        return [];
      }
      var input_type = (0, util_1.objectType)(input);
      var output_type = (0, util_1.objectType)(output);
      if (input_type == "array" && output_type == "array") {
        return diffArrays(input, output, ptr, diff);
      }
      if (input_type == "object" && output_type == "object") {
        return diffObjects(input, output, ptr, diff);
      }
      return [{ op: "replace", path: ptr.toString(), value: output }];
    }
    exports.diffAny = diffAny;
  }
});

// node_modules/rfc6902/patch.js
var require_patch = __commonJS({
  "node_modules/rfc6902/patch.js"(exports) {
    "use strict";
    var __extends = exports && exports.__extends || /* @__PURE__ */ function() {
      var extendStatics = function(d2, b2) {
        extendStatics = Object.setPrototypeOf || { __proto__: [] } instanceof Array && function(d3, b3) {
          d3.__proto__ = b3;
        } || function(d3, b3) {
          for (var p2 in b3)
            if (Object.prototype.hasOwnProperty.call(b3, p2))
              d3[p2] = b3[p2];
        };
        return extendStatics(d2, b2);
      };
      return function(d2, b2) {
        if (typeof b2 !== "function" && b2 !== null)
          throw new TypeError("Class extends value " + String(b2) + " is not a constructor or null");
        extendStatics(d2, b2);
        function __() {
          this.constructor = d2;
        }
        d2.prototype = b2 === null ? Object.create(b2) : (__.prototype = b2.prototype, new __());
      };
    }();
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.apply = exports.InvalidOperationError = exports.test = exports.copy = exports.move = exports.replace = exports.remove = exports.add = exports.TestError = exports.MissingError = void 0;
    var pointer_1 = require_pointer();
    var util_1 = require_util();
    var diff_1 = require_diff();
    var MissingError = (
      /** @class */
      function(_super) {
        __extends(MissingError2, _super);
        function MissingError2(path) {
          var _this = _super.call(this, "Value required at path: ".concat(path)) || this;
          _this.path = path;
          _this.name = "MissingError";
          return _this;
        }
        return MissingError2;
      }(Error)
    );
    exports.MissingError = MissingError;
    var TestError = (
      /** @class */
      function(_super) {
        __extends(TestError2, _super);
        function TestError2(actual, expected) {
          var _this = _super.call(this, "Test failed: ".concat(actual, " != ").concat(expected)) || this;
          _this.actual = actual;
          _this.expected = expected;
          _this.name = "TestError";
          return _this;
        }
        return TestError2;
      }(Error)
    );
    exports.TestError = TestError;
    function _add(object, key, value) {
      if (Array.isArray(object)) {
        if (key == "-") {
          object.push(value);
        } else {
          var index = parseInt(key, 10);
          object.splice(index, 0, value);
        }
      } else {
        object[key] = value;
      }
    }
    function _remove(object, key) {
      if (Array.isArray(object)) {
        var index = parseInt(key, 10);
        object.splice(index, 1);
      } else {
        delete object[key];
      }
    }
    function add(object, operation) {
      var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);
      if (endpoint.parent === void 0) {
        return new MissingError(operation.path);
      }
      _add(endpoint.parent, endpoint.key, (0, util_1.clone)(operation.value));
      return null;
    }
    exports.add = add;
    function remove(object, operation) {
      var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);
      if (endpoint.value === void 0) {
        return new MissingError(operation.path);
      }
      _remove(endpoint.parent, endpoint.key);
      return null;
    }
    exports.remove = remove;
    function replace(object, operation) {
      var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);
      if (endpoint.parent === null) {
        return new MissingError(operation.path);
      }
      if (Array.isArray(endpoint.parent)) {
        if (parseInt(endpoint.key, 10) >= endpoint.parent.length) {
          return new MissingError(operation.path);
        }
      } else if (endpoint.value === void 0) {
        return new MissingError(operation.path);
      }
      endpoint.parent[endpoint.key] = (0, util_1.clone)(operation.value);
      return null;
    }
    exports.replace = replace;
    function move(object, operation) {
      var from_endpoint = pointer_1.Pointer.fromJSON(operation.from).evaluate(object);
      if (from_endpoint.value === void 0) {
        return new MissingError(operation.from);
      }
      var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);
      if (endpoint.parent === void 0) {
        return new MissingError(operation.path);
      }
      _remove(from_endpoint.parent, from_endpoint.key);
      _add(endpoint.parent, endpoint.key, from_endpoint.value);
      return null;
    }
    exports.move = move;
    function copy2(object, operation) {
      var from_endpoint = pointer_1.Pointer.fromJSON(operation.from).evaluate(object);
      if (from_endpoint.value === void 0) {
        return new MissingError(operation.from);
      }
      var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);
      if (endpoint.parent === void 0) {
        return new MissingError(operation.path);
      }
      _add(endpoint.parent, endpoint.key, (0, util_1.clone)(from_endpoint.value));
      return null;
    }
    exports.copy = copy2;
    function test(object, operation) {
      var endpoint = pointer_1.Pointer.fromJSON(operation.path).evaluate(object);
      if ((0, diff_1.diffAny)(endpoint.value, operation.value, new pointer_1.Pointer()).length) {
        return new TestError(endpoint.value, operation.value);
      }
      return null;
    }
    exports.test = test;
    var InvalidOperationError = (
      /** @class */
      function(_super) {
        __extends(InvalidOperationError2, _super);
        function InvalidOperationError2(operation) {
          var _this = _super.call(this, "Invalid operation: ".concat(operation.op)) || this;
          _this.operation = operation;
          _this.name = "InvalidOperationError";
          return _this;
        }
        return InvalidOperationError2;
      }(Error)
    );
    exports.InvalidOperationError = InvalidOperationError;
    function apply(object, operation) {
      switch (operation.op) {
        case "add":
          return add(object, operation);
        case "remove":
          return remove(object, operation);
        case "replace":
          return replace(object, operation);
        case "move":
          return move(object, operation);
        case "copy":
          return copy2(object, operation);
        case "test":
          return test(object, operation);
      }
      return new InvalidOperationError(operation);
    }
    exports.apply = apply;
  }
});

// node_modules/rfc6902/index.js
var require_rfc6902 = __commonJS({
  "node_modules/rfc6902/index.js"(exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.createTests = exports.createPatch = exports.applyPatch = exports.Pointer = void 0;
    var pointer_1 = require_pointer();
    Object.defineProperty(exports, "Pointer", { enumerable: true, get: function() {
      return pointer_1.Pointer;
    } });
    var patch_1 = require_patch();
    var diff_1 = require_diff();
    function applyPatch2(object, patch2) {
      return patch2.map(function(operation) {
        return (0, patch_1.apply)(object, operation);
      });
    }
    exports.applyPatch = applyPatch2;
    function wrapVoidableDiff(diff) {
      function wrappedDiff(input, output, ptr) {
        var custom_patch = diff(input, output, ptr);
        return Array.isArray(custom_patch) ? custom_patch : (0, diff_1.diffAny)(input, output, ptr, wrappedDiff);
      }
      return wrappedDiff;
    }
    function createPatch(input, output, diff) {
      var ptr = new pointer_1.Pointer();
      return (diff ? wrapVoidableDiff(diff) : diff_1.diffAny)(input, output, ptr);
    }
    exports.createPatch = createPatch;
    function createTest(input, path) {
      var endpoint = pointer_1.Pointer.fromJSON(path).evaluate(input);
      if (endpoint !== void 0) {
        return { op: "test", path, value: endpoint.value };
      }
    }
    function createTests(input, patch2) {
      var tests = new Array();
      patch2.filter(diff_1.isDestructive).forEach(function(operation) {
        var pathTest = createTest(input, operation.path);
        if (pathTest)
          tests.push(pathTest);
        if ("from" in operation) {
          var fromTest = createTest(input, operation.from);
          if (fromTest)
            tests.push(fromTest);
        }
      });
      return tests;
    }
    exports.createTests = createTests;
  }
});

// node_modules/@babel/runtime/helpers/esm/typeof.js
function _typeof(o2) {
  "@babel/helpers - typeof";
  return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o3) {
    return typeof o3;
  } : function(o3) {
    return o3 && "function" == typeof Symbol && o3.constructor === Symbol && o3 !== Symbol.prototype ? "symbol" : typeof o3;
  }, _typeof(o2);
}

// node_modules/@babel/runtime/helpers/esm/toPrimitive.js
function toPrimitive(t2, r2) {
  if ("object" != _typeof(t2) || !t2)
    return t2;
  var e = t2[Symbol.toPrimitive];
  if (void 0 !== e) {
    var i2 = e.call(t2, r2 || "default");
    if ("object" != _typeof(i2))
      return i2;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return ("string" === r2 ? String : Number)(t2);
}

// node_modules/@babel/runtime/helpers/esm/toPropertyKey.js
function toPropertyKey(t2) {
  var i2 = toPrimitive(t2, "string");
  return "symbol" == _typeof(i2) ? i2 : String(i2);
}

// node_modules/@babel/runtime/helpers/esm/defineProperty.js
function _defineProperty(obj, key, value) {
  key = toPropertyKey(key);
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value,
      enumerable: true,
      configurable: true,
      writable: true
    });
  } else {
    obj[key] = value;
  }
  return obj;
}

// node_modules/@babel/runtime/helpers/esm/objectSpread2.js
function ownKeys(e, r2) {
  var t2 = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var o2 = Object.getOwnPropertySymbols(e);
    r2 && (o2 = o2.filter(function(r3) {
      return Object.getOwnPropertyDescriptor(e, r3).enumerable;
    })), t2.push.apply(t2, o2);
  }
  return t2;
}
function _objectSpread2(e) {
  for (var r2 = 1; r2 < arguments.length; r2++) {
    var t2 = null != arguments[r2] ? arguments[r2] : {};
    r2 % 2 ? ownKeys(Object(t2), true).forEach(function(r3) {
      _defineProperty(e, r3, t2[r3]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t2)) : ownKeys(Object(t2)).forEach(function(r3) {
      Object.defineProperty(e, r3, Object.getOwnPropertyDescriptor(t2, r3));
    });
  }
  return e;
}

// node_modules/boardgame.io/node_modules/redux/es/redux.js
var $$observable = function() {
  return typeof Symbol === "function" && Symbol.observable || "@@observable";
}();
var randomString = function randomString2() {
  return Math.random().toString(36).substring(7).split("").join(".");
};
var ActionTypes = {
  INIT: "@@redux/INIT" + randomString(),
  REPLACE: "@@redux/REPLACE" + randomString(),
  PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {
    return "@@redux/PROBE_UNKNOWN_ACTION" + randomString();
  }
};
function isPlainObject(obj) {
  if (typeof obj !== "object" || obj === null)
    return false;
  var proto = obj;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  return Object.getPrototypeOf(obj) === proto;
}
function miniKindOf(val) {
  if (val === void 0)
    return "undefined";
  if (val === null)
    return "null";
  var type = typeof val;
  switch (type) {
    case "boolean":
    case "string":
    case "number":
    case "symbol":
    case "function": {
      return type;
    }
  }
  if (Array.isArray(val))
    return "array";
  if (isDate(val))
    return "date";
  if (isError(val))
    return "error";
  var constructorName = ctorName(val);
  switch (constructorName) {
    case "Symbol":
    case "Promise":
    case "WeakMap":
    case "WeakSet":
    case "Map":
    case "Set":
      return constructorName;
  }
  return type.slice(8, -1).toLowerCase().replace(/\s/g, "");
}
function ctorName(val) {
  return typeof val.constructor === "function" ? val.constructor.name : null;
}
function isError(val) {
  return val instanceof Error || typeof val.message === "string" && val.constructor && typeof val.constructor.stackTraceLimit === "number";
}
function isDate(val) {
  if (val instanceof Date)
    return true;
  return typeof val.toDateString === "function" && typeof val.getDate === "function" && typeof val.setDate === "function";
}
function kindOf(val) {
  var typeOfVal = typeof val;
  if (true) {
    typeOfVal = miniKindOf(val);
  }
  return typeOfVal;
}
function createStore(reducer, preloadedState, enhancer) {
  var _ref2;
  if (typeof preloadedState === "function" && typeof enhancer === "function" || typeof enhancer === "function" && typeof arguments[3] === "function") {
    throw new Error(false ? formatProdErrorMessage(0) : "It looks like you are passing several store enhancers to createStore(). This is not supported. Instead, compose them together to a single function. See https://redux.js.org/tutorials/fundamentals/part-4-store#creating-a-store-with-enhancers for an example.");
  }
  if (typeof preloadedState === "function" && typeof enhancer === "undefined") {
    enhancer = preloadedState;
    preloadedState = void 0;
  }
  if (typeof enhancer !== "undefined") {
    if (typeof enhancer !== "function") {
      throw new Error(false ? formatProdErrorMessage(1) : "Expected the enhancer to be a function. Instead, received: '" + kindOf(enhancer) + "'");
    }
    return enhancer(createStore)(reducer, preloadedState);
  }
  if (typeof reducer !== "function") {
    throw new Error(false ? formatProdErrorMessage(2) : "Expected the root reducer to be a function. Instead, received: '" + kindOf(reducer) + "'");
  }
  var currentReducer = reducer;
  var currentState = preloadedState;
  var currentListeners = [];
  var nextListeners = currentListeners;
  var isDispatching = false;
  function ensureCanMutateNextListeners() {
    if (nextListeners === currentListeners) {
      nextListeners = currentListeners.slice();
    }
  }
  function getState() {
    if (isDispatching) {
      throw new Error(false ? formatProdErrorMessage(3) : "You may not call store.getState() while the reducer is executing. The reducer has already received the state as an argument. Pass it down from the top reducer instead of reading it from the store.");
    }
    return currentState;
  }
  function subscribe(listener) {
    if (typeof listener !== "function") {
      throw new Error(false ? formatProdErrorMessage(4) : "Expected the listener to be a function. Instead, received: '" + kindOf(listener) + "'");
    }
    if (isDispatching) {
      throw new Error(false ? formatProdErrorMessage(5) : "You may not call store.subscribe() while the reducer is executing. If you would like to be notified after the store has been updated, subscribe from a component and invoke store.getState() in the callback to access the latest state. See https://redux.js.org/api/store#subscribelistener for more details.");
    }
    var isSubscribed = true;
    ensureCanMutateNextListeners();
    nextListeners.push(listener);
    return function unsubscribe() {
      if (!isSubscribed) {
        return;
      }
      if (isDispatching) {
        throw new Error(false ? formatProdErrorMessage(6) : "You may not unsubscribe from a store listener while the reducer is executing. See https://redux.js.org/api/store#subscribelistener for more details.");
      }
      isSubscribed = false;
      ensureCanMutateNextListeners();
      var index = nextListeners.indexOf(listener);
      nextListeners.splice(index, 1);
      currentListeners = null;
    };
  }
  function dispatch(action) {
    if (!isPlainObject(action)) {
      throw new Error(false ? formatProdErrorMessage(7) : "Actions must be plain objects. Instead, the actual type was: '" + kindOf(action) + "'. You may need to add middleware to your store setup to handle dispatching other values, such as 'redux-thunk' to handle dispatching functions. See https://redux.js.org/tutorials/fundamentals/part-4-store#middleware and https://redux.js.org/tutorials/fundamentals/part-6-async-logic#using-the-redux-thunk-middleware for examples.");
    }
    if (typeof action.type === "undefined") {
      throw new Error(false ? formatProdErrorMessage(8) : 'Actions may not have an undefined "type" property. You may have misspelled an action type string constant.');
    }
    if (isDispatching) {
      throw new Error(false ? formatProdErrorMessage(9) : "Reducers may not dispatch actions.");
    }
    try {
      isDispatching = true;
      currentState = currentReducer(currentState, action);
    } finally {
      isDispatching = false;
    }
    var listeners = currentListeners = nextListeners;
    for (var i2 = 0; i2 < listeners.length; i2++) {
      var listener = listeners[i2];
      listener();
    }
    return action;
  }
  function replaceReducer(nextReducer) {
    if (typeof nextReducer !== "function") {
      throw new Error(false ? formatProdErrorMessage(10) : "Expected the nextReducer to be a function. Instead, received: '" + kindOf(nextReducer));
    }
    currentReducer = nextReducer;
    dispatch({
      type: ActionTypes.REPLACE
    });
  }
  function observable() {
    var _ref;
    var outerSubscribe = subscribe;
    return _ref = {
      /**
       * The minimal observable subscription method.
       * @param {Object} observer Any object that can be used as an observer.
       * The observer object should have a `next` method.
       * @returns {subscription} An object with an `unsubscribe` method that can
       * be used to unsubscribe the observable from the store, and prevent further
       * emission of values from the observable.
       */
      subscribe: function subscribe2(observer) {
        if (typeof observer !== "object" || observer === null) {
          throw new Error(false ? formatProdErrorMessage(11) : "Expected the observer to be an object. Instead, received: '" + kindOf(observer) + "'");
        }
        function observeState() {
          if (observer.next) {
            observer.next(getState());
          }
        }
        observeState();
        var unsubscribe = outerSubscribe(observeState);
        return {
          unsubscribe
        };
      }
    }, _ref[$$observable] = function() {
      return this;
    }, _ref;
  }
  dispatch({
    type: ActionTypes.INIT
  });
  return _ref2 = {
    dispatch,
    subscribe,
    getState,
    replaceReducer
  }, _ref2[$$observable] = observable, _ref2;
}
function compose() {
  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {
    funcs[_key] = arguments[_key];
  }
  if (funcs.length === 0) {
    return function(arg) {
      return arg;
    };
  }
  if (funcs.length === 1) {
    return funcs[0];
  }
  return funcs.reduce(function(a2, b2) {
    return function() {
      return a2(b2.apply(void 0, arguments));
    };
  });
}
function applyMiddleware() {
  for (var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++) {
    middlewares[_key] = arguments[_key];
  }
  return function(createStore2) {
    return function() {
      var store = createStore2.apply(void 0, arguments);
      var _dispatch = function dispatch() {
        throw new Error(false ? formatProdErrorMessage(15) : "Dispatching while constructing your middleware is not allowed. Other middleware would not be applied to this dispatch.");
      };
      var middlewareAPI = {
        getState: store.getState,
        dispatch: function dispatch() {
          return _dispatch.apply(void 0, arguments);
        }
      };
      var chain = middlewares.map(function(middleware) {
        return middleware(middlewareAPI);
      });
      _dispatch = compose.apply(void 0, chain)(store.dispatch);
      return _objectSpread2(_objectSpread2({}, store), {}, {
        dispatch: _dispatch
      });
    };
  };
}

// node_modules/immer/dist/immer.esm.mjs
function n(n2) {
  for (var r2 = arguments.length, t2 = Array(r2 > 1 ? r2 - 1 : 0), e = 1; e < r2; e++)
    t2[e - 1] = arguments[e];
  if (true) {
    var i2 = Y[n2], o2 = i2 ? "function" == typeof i2 ? i2.apply(null, t2) : i2 : "unknown error nr: " + n2;
    throw Error("[Immer] " + o2);
  }
  throw Error("[Immer] minified error nr: " + n2 + (t2.length ? " " + t2.map(function(n3) {
    return "'" + n3 + "'";
  }).join(",") : "") + ". Find the full error at: https://bit.ly/3cXEKWf");
}
function r(n2) {
  return !!n2 && !!n2[Q];
}
function t(n2) {
  var r2;
  return !!n2 && (function(n3) {
    if (!n3 || "object" != typeof n3)
      return false;
    var r3 = Object.getPrototypeOf(n3);
    if (null === r3)
      return true;
    var t2 = Object.hasOwnProperty.call(r3, "constructor") && r3.constructor;
    return t2 === Object || "function" == typeof t2 && Function.toString.call(t2) === Z;
  }(n2) || Array.isArray(n2) || !!n2[L] || !!(null === (r2 = n2.constructor) || void 0 === r2 ? void 0 : r2[L]) || s(n2) || v(n2));
}
function i(n2, r2, t2) {
  void 0 === t2 && (t2 = false), 0 === o(n2) ? (t2 ? Object.keys : nn)(n2).forEach(function(e) {
    t2 && "symbol" == typeof e || r2(e, n2[e], n2);
  }) : n2.forEach(function(t3, e) {
    return r2(e, t3, n2);
  });
}
function o(n2) {
  var r2 = n2[Q];
  return r2 ? r2.i > 3 ? r2.i - 4 : r2.i : Array.isArray(n2) ? 1 : s(n2) ? 2 : v(n2) ? 3 : 0;
}
function u(n2, r2) {
  return 2 === o(n2) ? n2.has(r2) : Object.prototype.hasOwnProperty.call(n2, r2);
}
function a(n2, r2) {
  return 2 === o(n2) ? n2.get(r2) : n2[r2];
}
function f(n2, r2, t2) {
  var e = o(n2);
  2 === e ? n2.set(r2, t2) : 3 === e ? n2.add(t2) : n2[r2] = t2;
}
function c(n2, r2) {
  return n2 === r2 ? 0 !== n2 || 1 / n2 == 1 / r2 : n2 != n2 && r2 != r2;
}
function s(n2) {
  return X && n2 instanceof Map;
}
function v(n2) {
  return q && n2 instanceof Set;
}
function p(n2) {
  return n2.o || n2.t;
}
function l(n2) {
  if (Array.isArray(n2))
    return Array.prototype.slice.call(n2);
  var r2 = rn(n2);
  delete r2[Q];
  for (var t2 = nn(r2), e = 0; e < t2.length; e++) {
    var i2 = t2[e], o2 = r2[i2];
    false === o2.writable && (o2.writable = true, o2.configurable = true), (o2.get || o2.set) && (r2[i2] = { configurable: true, writable: true, enumerable: o2.enumerable, value: n2[i2] });
  }
  return Object.create(Object.getPrototypeOf(n2), r2);
}
function d(n2, e) {
  return void 0 === e && (e = false), y(n2) || r(n2) || !t(n2) || (o(n2) > 1 && (n2.set = n2.add = n2.clear = n2.delete = h), Object.freeze(n2), e && i(n2, function(n3, r2) {
    return d(r2, true);
  }, true)), n2;
}
function h() {
  n(2);
}
function y(n2) {
  return null == n2 || "object" != typeof n2 || Object.isFrozen(n2);
}
function b(r2) {
  var t2 = tn[r2];
  return t2 || n(18, r2), t2;
}
function _() {
  return U || n(0), U;
}
function j(n2, r2) {
  r2 && (b("Patches"), n2.u = [], n2.s = [], n2.v = r2);
}
function g(n2) {
  O(n2), n2.p.forEach(S), n2.p = null;
}
function O(n2) {
  n2 === U && (U = n2.l);
}
function w(n2) {
  return U = { p: [], l: U, h: n2, m: true, _: 0 };
}
function S(n2) {
  var r2 = n2[Q];
  0 === r2.i || 1 === r2.i ? r2.j() : r2.g = true;
}
function P(r2, e) {
  e._ = e.p.length;
  var i2 = e.p[0], o2 = void 0 !== r2 && r2 !== i2;
  return e.h.O || b("ES5").S(e, r2, o2), o2 ? (i2[Q].P && (g(e), n(4)), t(r2) && (r2 = M(e, r2), e.l || x(e, r2)), e.u && b("Patches").M(i2[Q].t, r2, e.u, e.s)) : r2 = M(e, i2, []), g(e), e.u && e.v(e.u, e.s), r2 !== H ? r2 : void 0;
}
function M(n2, r2, t2) {
  if (y(r2))
    return r2;
  var e = r2[Q];
  if (!e)
    return i(r2, function(i2, o3) {
      return A(n2, e, r2, i2, o3, t2);
    }, true), r2;
  if (e.A !== n2)
    return r2;
  if (!e.P)
    return x(n2, e.t, true), e.t;
  if (!e.I) {
    e.I = true, e.A._--;
    var o2 = 4 === e.i || 5 === e.i ? e.o = l(e.k) : e.o, u2 = o2, a2 = false;
    3 === e.i && (u2 = new Set(o2), o2.clear(), a2 = true), i(u2, function(r3, i2) {
      return A(n2, e, o2, r3, i2, t2, a2);
    }), x(n2, o2, false), t2 && n2.u && b("Patches").N(e, t2, n2.u, n2.s);
  }
  return e.o;
}
function A(e, i2, o2, a2, c2, s2, v2) {
  if (c2 === o2 && n(5), r(c2)) {
    var p2 = M(e, c2, s2 && i2 && 3 !== i2.i && !u(i2.R, a2) ? s2.concat(a2) : void 0);
    if (f(o2, a2, p2), !r(p2))
      return;
    e.m = false;
  } else
    v2 && o2.add(c2);
  if (t(c2) && !y(c2)) {
    if (!e.h.D && e._ < 1)
      return;
    M(e, c2), i2 && i2.A.l || x(e, c2);
  }
}
function x(n2, r2, t2) {
  void 0 === t2 && (t2 = false), !n2.l && n2.h.D && n2.m && d(r2, t2);
}
function z(n2, r2) {
  var t2 = n2[Q];
  return (t2 ? p(t2) : n2)[r2];
}
function I(n2, r2) {
  if (r2 in n2)
    for (var t2 = Object.getPrototypeOf(n2); t2; ) {
      var e = Object.getOwnPropertyDescriptor(t2, r2);
      if (e)
        return e;
      t2 = Object.getPrototypeOf(t2);
    }
}
function k(n2) {
  n2.P || (n2.P = true, n2.l && k(n2.l));
}
function E(n2) {
  n2.o || (n2.o = l(n2.t));
}
function N(n2, r2, t2) {
  var e = s(r2) ? b("MapSet").F(r2, t2) : v(r2) ? b("MapSet").T(r2, t2) : n2.O ? function(n3, r3) {
    var t3 = Array.isArray(n3), e2 = { i: t3 ? 1 : 0, A: r3 ? r3.A : _(), P: false, I: false, R: {}, l: r3, t: n3, k: null, o: null, j: null, C: false }, i2 = e2, o2 = en;
    t3 && (i2 = [e2], o2 = on);
    var u2 = Proxy.revocable(i2, o2), a2 = u2.revoke, f2 = u2.proxy;
    return e2.k = f2, e2.j = a2, f2;
  }(r2, t2) : b("ES5").J(r2, t2);
  return (t2 ? t2.A : _()).p.push(e), e;
}
function R(e) {
  return r(e) || n(22, e), function n2(r2) {
    if (!t(r2))
      return r2;
    var e2, u2 = r2[Q], c2 = o(r2);
    if (u2) {
      if (!u2.P && (u2.i < 4 || !b("ES5").K(u2)))
        return u2.t;
      u2.I = true, e2 = D(r2, c2), u2.I = false;
    } else
      e2 = D(r2, c2);
    return i(e2, function(r3, t2) {
      u2 && a(u2.t, r3) === t2 || f(e2, r3, n2(t2));
    }), 3 === c2 ? new Set(e2) : e2;
  }(e);
}
function D(n2, r2) {
  switch (r2) {
    case 2:
      return new Map(n2);
    case 3:
      return Array.from(n2);
  }
  return l(n2);
}
var G;
var U;
var W = "undefined" != typeof Symbol && "symbol" == typeof Symbol("x");
var X = "undefined" != typeof Map;
var q = "undefined" != typeof Set;
var B = "undefined" != typeof Proxy && void 0 !== Proxy.revocable && "undefined" != typeof Reflect;
var H = W ? Symbol.for("immer-nothing") : ((G = {})["immer-nothing"] = true, G);
var L = W ? Symbol.for("immer-draftable") : "__$immer_draftable";
var Q = W ? Symbol.for("immer-state") : "__$immer_state";
var Y = { 0: "Illegal state", 1: "Immer drafts cannot have computed properties", 2: "This object has been frozen and should not be mutated", 3: function(n2) {
  return "Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? " + n2;
}, 4: "An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.", 5: "Immer forbids circular references", 6: "The first or second argument to `produce` must be a function", 7: "The third argument to `produce` must be a function or undefined", 8: "First argument to `createDraft` must be a plain object, an array, or an immerable object", 9: "First argument to `finishDraft` must be a draft returned by `createDraft`", 10: "The given draft is already finalized", 11: "Object.defineProperty() cannot be used on an Immer draft", 12: "Object.setPrototypeOf() cannot be used on an Immer draft", 13: "Immer only supports deleting array indices", 14: "Immer only supports setting array indices and the 'length' property", 15: function(n2) {
  return "Cannot apply patch, path doesn't resolve: " + n2;
}, 16: 'Sets cannot have "replace" patches.', 17: function(n2) {
  return "Unsupported patch operation: " + n2;
}, 18: function(n2) {
  return "The plugin for '" + n2 + "' has not been loaded into Immer. To enable the plugin, import and call `enable" + n2 + "()` when initializing your application.";
}, 20: "Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available", 21: function(n2) {
  return "produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '" + n2 + "'";
}, 22: function(n2) {
  return "'current' expects a draft, got: " + n2;
}, 23: function(n2) {
  return "'original' expects a draft, got: " + n2;
}, 24: "Patching reserved attributes like __proto__, prototype and constructor is not allowed" };
var Z = "" + Object.prototype.constructor;
var nn = "undefined" != typeof Reflect && Reflect.ownKeys ? Reflect.ownKeys : void 0 !== Object.getOwnPropertySymbols ? function(n2) {
  return Object.getOwnPropertyNames(n2).concat(Object.getOwnPropertySymbols(n2));
} : Object.getOwnPropertyNames;
var rn = Object.getOwnPropertyDescriptors || function(n2) {
  var r2 = {};
  return nn(n2).forEach(function(t2) {
    r2[t2] = Object.getOwnPropertyDescriptor(n2, t2);
  }), r2;
};
var tn = {};
var en = { get: function(n2, r2) {
  if (r2 === Q)
    return n2;
  var e = p(n2);
  if (!u(e, r2))
    return function(n3, r3, t2) {
      var e2, i3 = I(r3, t2);
      return i3 ? "value" in i3 ? i3.value : null === (e2 = i3.get) || void 0 === e2 ? void 0 : e2.call(n3.k) : void 0;
    }(n2, e, r2);
  var i2 = e[r2];
  return n2.I || !t(i2) ? i2 : i2 === z(n2.t, r2) ? (E(n2), n2.o[r2] = N(n2.A.h, i2, n2)) : i2;
}, has: function(n2, r2) {
  return r2 in p(n2);
}, ownKeys: function(n2) {
  return Reflect.ownKeys(p(n2));
}, set: function(n2, r2, t2) {
  var e = I(p(n2), r2);
  if (null == e ? void 0 : e.set)
    return e.set.call(n2.k, t2), true;
  if (!n2.P) {
    var i2 = z(p(n2), r2), o2 = null == i2 ? void 0 : i2[Q];
    if (o2 && o2.t === t2)
      return n2.o[r2] = t2, n2.R[r2] = false, true;
    if (c(t2, i2) && (void 0 !== t2 || u(n2.t, r2)))
      return true;
    E(n2), k(n2);
  }
  return n2.o[r2] === t2 && (void 0 !== t2 || r2 in n2.o) || Number.isNaN(t2) && Number.isNaN(n2.o[r2]) || (n2.o[r2] = t2, n2.R[r2] = true), true;
}, deleteProperty: function(n2, r2) {
  return void 0 !== z(n2.t, r2) || r2 in n2.t ? (n2.R[r2] = false, E(n2), k(n2)) : delete n2.R[r2], n2.o && delete n2.o[r2], true;
}, getOwnPropertyDescriptor: function(n2, r2) {
  var t2 = p(n2), e = Reflect.getOwnPropertyDescriptor(t2, r2);
  return e ? { writable: true, configurable: 1 !== n2.i || "length" !== r2, enumerable: e.enumerable, value: t2[r2] } : e;
}, defineProperty: function() {
  n(11);
}, getPrototypeOf: function(n2) {
  return Object.getPrototypeOf(n2.t);
}, setPrototypeOf: function() {
  n(12);
} };
var on = {};
i(en, function(n2, r2) {
  on[n2] = function() {
    return arguments[0] = arguments[0][0], r2.apply(this, arguments);
  };
}), on.deleteProperty = function(r2, t2) {
  return isNaN(parseInt(t2)) && n(13), on.set.call(this, r2, t2, void 0);
}, on.set = function(r2, t2, e) {
  return "length" !== t2 && isNaN(parseInt(t2)) && n(14), en.set.call(this, r2[0], t2, e, r2[0]);
};
var un = function() {
  function e(r2) {
    var e2 = this;
    this.O = B, this.D = true, this.produce = function(r3, i3, o2) {
      if ("function" == typeof r3 && "function" != typeof i3) {
        var u2 = i3;
        i3 = r3;
        var a2 = e2;
        return function(n2) {
          var r4 = this;
          void 0 === n2 && (n2 = u2);
          for (var t2 = arguments.length, e3 = Array(t2 > 1 ? t2 - 1 : 0), o3 = 1; o3 < t2; o3++)
            e3[o3 - 1] = arguments[o3];
          return a2.produce(n2, function(n3) {
            var t3;
            return (t3 = i3).call.apply(t3, [r4, n3].concat(e3));
          });
        };
      }
      var f2;
      if ("function" != typeof i3 && n(6), void 0 !== o2 && "function" != typeof o2 && n(7), t(r3)) {
        var c2 = w(e2), s2 = N(e2, r3, void 0), v2 = true;
        try {
          f2 = i3(s2), v2 = false;
        } finally {
          v2 ? g(c2) : O(c2);
        }
        return "undefined" != typeof Promise && f2 instanceof Promise ? f2.then(function(n2) {
          return j(c2, o2), P(n2, c2);
        }, function(n2) {
          throw g(c2), n2;
        }) : (j(c2, o2), P(f2, c2));
      }
      if (!r3 || "object" != typeof r3) {
        if (void 0 === (f2 = i3(r3)) && (f2 = r3), f2 === H && (f2 = void 0), e2.D && d(f2, true), o2) {
          var p2 = [], l2 = [];
          b("Patches").M(r3, f2, p2, l2), o2(p2, l2);
        }
        return f2;
      }
      n(21, r3);
    }, this.produceWithPatches = function(n2, r3) {
      if ("function" == typeof n2)
        return function(r4) {
          for (var t3 = arguments.length, i4 = Array(t3 > 1 ? t3 - 1 : 0), o3 = 1; o3 < t3; o3++)
            i4[o3 - 1] = arguments[o3];
          return e2.produceWithPatches(r4, function(r5) {
            return n2.apply(void 0, [r5].concat(i4));
          });
        };
      var t2, i3, o2 = e2.produce(n2, r3, function(n3, r4) {
        t2 = n3, i3 = r4;
      });
      return "undefined" != typeof Promise && o2 instanceof Promise ? o2.then(function(n3) {
        return [n3, t2, i3];
      }) : [o2, t2, i3];
    }, "boolean" == typeof (null == r2 ? void 0 : r2.useProxies) && this.setUseProxies(r2.useProxies), "boolean" == typeof (null == r2 ? void 0 : r2.autoFreeze) && this.setAutoFreeze(r2.autoFreeze);
  }
  var i2 = e.prototype;
  return i2.createDraft = function(e2) {
    t(e2) || n(8), r(e2) && (e2 = R(e2));
    var i3 = w(this), o2 = N(this, e2, void 0);
    return o2[Q].C = true, O(i3), o2;
  }, i2.finishDraft = function(r2, t2) {
    var e2 = r2 && r2[Q];
    e2 && e2.C || n(9), e2.I && n(10);
    var i3 = e2.A;
    return j(i3, t2), P(void 0, i3);
  }, i2.setAutoFreeze = function(n2) {
    this.D = n2;
  }, i2.setUseProxies = function(r2) {
    r2 && !B && n(20), this.O = r2;
  }, i2.applyPatches = function(n2, t2) {
    var e2;
    for (e2 = t2.length - 1; e2 >= 0; e2--) {
      var i3 = t2[e2];
      if (0 === i3.path.length && "replace" === i3.op) {
        n2 = i3.value;
        break;
      }
    }
    e2 > -1 && (t2 = t2.slice(e2 + 1));
    var o2 = b("Patches").$;
    return r(n2) ? o2(n2, t2) : this.produce(n2, function(n3) {
      return o2(n3, t2);
    });
  }, e;
}();
var an = new un();
var fn = an.produce;
var cn = an.produceWithPatches.bind(an);
var sn = an.setAutoFreeze.bind(an);
var vn = an.setUseProxies.bind(an);
var pn = an.applyPatches.bind(an);
var ln = an.createDraft.bind(an);
var dn = an.finishDraft.bind(an);
var immer_esm_default = fn;

// node_modules/boardgame.io/dist/esm/plugin-random-087f861e.js
var Alea = class {
  constructor(seed) {
    const mash = Mash();
    this.c = 1;
    this.s0 = mash(" ");
    this.s1 = mash(" ");
    this.s2 = mash(" ");
    this.s0 -= mash(seed);
    if (this.s0 < 0) {
      this.s0 += 1;
    }
    this.s1 -= mash(seed);
    if (this.s1 < 0) {
      this.s1 += 1;
    }
    this.s2 -= mash(seed);
    if (this.s2 < 0) {
      this.s2 += 1;
    }
  }
  next() {
    const t2 = 2091639 * this.s0 + this.c * 23283064365386963e-26;
    this.s0 = this.s1;
    this.s1 = this.s2;
    return this.s2 = t2 - (this.c = Math.trunc(t2));
  }
};
function Mash() {
  let n2 = 4022871197;
  const mash = function(data) {
    const str = data.toString();
    for (let i2 = 0; i2 < str.length; i2++) {
      n2 += str.charCodeAt(i2);
      let h2 = 0.02519603282416938 * n2;
      n2 = h2 >>> 0;
      h2 -= n2;
      h2 *= n2;
      n2 = h2 >>> 0;
      h2 -= n2;
      n2 += h2 * 4294967296;
    }
    return (n2 >>> 0) * 23283064365386963e-26;
  };
  return mash;
}
function copy(f2, t2) {
  t2.c = f2.c;
  t2.s0 = f2.s0;
  t2.s1 = f2.s1;
  t2.s2 = f2.s2;
  return t2;
}
function alea(seed, state) {
  const xg = new Alea(seed);
  const prng = xg.next.bind(xg);
  if (state)
    copy(state, xg);
  prng.state = () => copy(xg, {});
  return prng;
}
var Random = class {
  /**
   * constructor
   * @param {object} ctx - The ctx object to initialize from.
   */
  constructor(state) {
    this.state = state || { seed: "0" };
    this.used = false;
  }
  /**
   * Generates a new seed from the current date / time.
   */
  static seed() {
    return Date.now().toString(36).slice(-10);
  }
  isUsed() {
    return this.used;
  }
  getState() {
    return this.state;
  }
  /**
   * Generate a random number.
   */
  _random() {
    this.used = true;
    const R2 = this.state;
    const seed = R2.prngstate ? "" : R2.seed;
    const rand = alea(seed, R2.prngstate);
    const number = rand();
    this.state = {
      ...R2,
      prngstate: rand.state()
    };
    return number;
  }
  api() {
    const random = this._random.bind(this);
    const SpotValue = {
      D4: 4,
      D6: 6,
      D8: 8,
      D10: 10,
      D12: 12,
      D20: 20
    };
    const predefined = {};
    for (const key in SpotValue) {
      const spotvalue = SpotValue[key];
      predefined[key] = (diceCount) => {
        return diceCount === void 0 ? Math.floor(random() * spotvalue) + 1 : Array.from({ length: diceCount }).map(() => Math.floor(random() * spotvalue) + 1);
      };
    }
    function Die(spotvalue = 6, diceCount) {
      return diceCount === void 0 ? Math.floor(random() * spotvalue) + 1 : Array.from({ length: diceCount }).map(() => Math.floor(random() * spotvalue) + 1);
    }
    return {
      /**
       * Similar to Die below, but with fixed spot values.
       * Supports passing a diceCount
       *    if not defined, defaults to 1 and returns the value directly.
       *    if defined, returns an array containing the random dice values.
       *
       * D4: (diceCount) => value
       * D6: (diceCount) => value
       * D8: (diceCount) => value
       * D10: (diceCount) => value
       * D12: (diceCount) => value
       * D20: (diceCount) => value
       */
      ...predefined,
      /**
       * Roll a die of specified spot value.
       *
       * @param {number} spotvalue - The die dimension (default: 6).
       * @param {number} diceCount - number of dice to throw.
       *                             if not defined, defaults to 1 and returns the value directly.
       *                             if defined, returns an array containing the random dice values.
       */
      Die,
      /**
       * Generate a random number between 0 and 1.
       */
      Number: () => {
        return random();
      },
      /**
       * Shuffle an array.
       *
       * @param {Array} deck - The array to shuffle. Does not mutate
       *                       the input, but returns the shuffled array.
       */
      Shuffle: (deck) => {
        const clone = [...deck];
        let sourceIndex = deck.length;
        let destinationIndex = 0;
        const shuffled = Array.from({ length: sourceIndex });
        while (sourceIndex) {
          const randomIndex = Math.trunc(sourceIndex * random());
          shuffled[destinationIndex++] = clone[randomIndex];
          clone[randomIndex] = clone[--sourceIndex];
        }
        return shuffled;
      },
      _private: this
    };
  }
};
var RandomPlugin = {
  name: "random",
  noClient: ({ api }) => {
    return api._private.isUsed();
  },
  flush: ({ api }) => {
    return api._private.getState();
  },
  api: ({ data }) => {
    const random = new Random(data);
    return random.api();
  },
  setup: ({ game }) => {
    let { seed } = game;
    if (seed === void 0) {
      seed = Random.seed();
    }
    return { seed };
  },
  playerView: () => void 0
};

// node_modules/boardgame.io/dist/esm/turn-order-8cc4909b.js
var import_lodash = __toESM(require_lodash());
var MAKE_MOVE = "MAKE_MOVE";
var GAME_EVENT = "GAME_EVENT";
var REDO = "REDO";
var RESET = "RESET";
var SYNC = "SYNC";
var UNDO = "UNDO";
var UPDATE = "UPDATE";
var PATCH = "PATCH";
var PLUGIN = "PLUGIN";
var STRIP_TRANSIENTS = "STRIP_TRANSIENTS";
var makeMove = (type, args, playerID, credentials) => ({
  type: MAKE_MOVE,
  payload: { type, args, playerID, credentials }
});
var gameEvent = (type, args, playerID, credentials) => ({
  type: GAME_EVENT,
  payload: { type, args, playerID, credentials }
});
var automaticGameEvent = (type, args, playerID, credentials) => ({
  type: GAME_EVENT,
  payload: { type, args, playerID, credentials },
  automatic: true
});
var sync = (info2) => ({
  type: SYNC,
  state: info2.state,
  log: info2.log,
  initialState: info2.initialState,
  clientOnly: true
});
var patch = (prevStateID, stateID, patch2, deltalog) => ({
  type: PATCH,
  prevStateID,
  stateID,
  patch: patch2,
  deltalog,
  clientOnly: true
});
var update = (state, deltalog) => ({
  type: UPDATE,
  state,
  deltalog,
  clientOnly: true
});
var reset = (state) => ({
  type: RESET,
  state,
  clientOnly: true
});
var undo = (playerID, credentials) => ({
  type: UNDO,
  payload: { type: null, args: null, playerID, credentials }
});
var redo = (playerID, credentials) => ({
  type: REDO,
  payload: { type: null, args: null, playerID, credentials }
});
var plugin = (type, args, playerID, credentials) => ({
  type: PLUGIN,
  payload: { type, args, playerID, credentials }
});
var stripTransients = () => ({
  type: STRIP_TRANSIENTS
});
var ActionCreators = Object.freeze({
  __proto__: null,
  makeMove,
  gameEvent,
  automaticGameEvent,
  sync,
  patch,
  update,
  reset,
  undo,
  redo,
  plugin,
  stripTransients
});
var INVALID_MOVE = "INVALID_MOVE";
var ImmerPlugin = {
  name: "plugin-immer",
  fnWrap: (move) => (context, ...args) => {
    let isInvalid = false;
    const newG = immer_esm_default(context.G, (G2) => {
      const result = move({ ...context, G: G2 }, ...args);
      if (result === INVALID_MOVE) {
        isInvalid = true;
        return;
      }
      return result;
    });
    if (isInvalid)
      return INVALID_MOVE;
    return newG;
  }
};
var GameMethod;
(function(GameMethod2) {
  GameMethod2["MOVE"] = "MOVE";
  GameMethod2["GAME_ON_END"] = "GAME_ON_END";
  GameMethod2["PHASE_ON_BEGIN"] = "PHASE_ON_BEGIN";
  GameMethod2["PHASE_ON_END"] = "PHASE_ON_END";
  GameMethod2["TURN_ON_BEGIN"] = "TURN_ON_BEGIN";
  GameMethod2["TURN_ON_MOVE"] = "TURN_ON_MOVE";
  GameMethod2["TURN_ON_END"] = "TURN_ON_END";
})(GameMethod || (GameMethod = {}));
var Errors;
(function(Errors2) {
  Errors2["CalledOutsideHook"] = "Events must be called from moves or the `onBegin`, `onEnd`, and `onMove` hooks.\nThis error probably means you called an event from other game code, like an `endIf` trigger or one of the `turn.order` methods.";
  Errors2["EndTurnInOnEnd"] = "`endTurn` is disallowed in `onEnd` hooks — the turn is already ending.";
  Errors2["MaxTurnEndings"] = "Maximum number of turn endings exceeded for this update.\nThis likely means game code is triggering an infinite loop.";
  Errors2["PhaseEventInOnEnd"] = "`setPhase` & `endPhase` are disallowed in a phase’s `onEnd` hook — the phase is already ending.\nIf you’re trying to dynamically choose the next phase when a phase ends, use the phase’s `next` trigger.";
  Errors2["StageEventInOnEnd"] = "`setStage`, `endStage` & `setActivePlayers` are disallowed in `onEnd` hooks.";
  Errors2["StageEventInPhaseBegin"] = "`setStage`, `endStage` & `setActivePlayers` are disallowed in a phase’s `onBegin` hook.\nUse `setActivePlayers` in a `turn.onBegin` hook or declare stages with `turn.activePlayers` instead.";
  Errors2["StageEventInTurnBegin"] = "`setStage` & `endStage` are disallowed in `turn.onBegin`.\nUse `setActivePlayers` or declare stages with `turn.activePlayers` instead.";
})(Errors || (Errors = {}));
var Events = class {
  constructor(flow, ctx, playerID) {
    this.flow = flow;
    this.playerID = playerID;
    this.dispatch = [];
    this.initialTurn = ctx.turn;
    this.updateTurnContext(ctx, void 0);
    this.maxEndedTurnsPerAction = ctx.numPlayers * 100;
  }
  api() {
    const events = {
      _private: this
    };
    for (const type of this.flow.eventNames) {
      events[type] = (...args) => {
        this.dispatch.push({
          type,
          args,
          phase: this.currentPhase,
          turn: this.currentTurn,
          calledFrom: this.currentMethod,
          // Used to capture a stack trace in case it is needed later.
          error: new Error("Events Plugin Error")
        });
      };
    }
    return events;
  }
  isUsed() {
    return this.dispatch.length > 0;
  }
  updateTurnContext(ctx, methodType) {
    this.currentPhase = ctx.phase;
    this.currentTurn = ctx.turn;
    this.currentMethod = methodType;
  }
  unsetCurrentMethod() {
    this.currentMethod = void 0;
  }
  /**
   * Updates ctx with the triggered events.
   * @param {object} state - The state object { G, ctx }.
   */
  update(state) {
    const initialState = state;
    const stateWithError = ({ stack }, message) => ({
      ...initialState,
      plugins: {
        ...initialState.plugins,
        events: {
          ...initialState.plugins.events,
          data: { error: message + "\n" + stack }
        }
      }
    });
    EventQueue:
      for (let i2 = 0; i2 < this.dispatch.length; i2++) {
        const event = this.dispatch[i2];
        const turnHasEnded = event.turn !== state.ctx.turn;
        const endedTurns = this.currentTurn - this.initialTurn;
        if (endedTurns >= this.maxEndedTurnsPerAction) {
          return stateWithError(event.error, Errors.MaxTurnEndings);
        }
        if (event.calledFrom === void 0) {
          return stateWithError(event.error, Errors.CalledOutsideHook);
        }
        if (state.ctx.gameover)
          break EventQueue;
        switch (event.type) {
          case "endStage":
          case "setStage":
          case "setActivePlayers": {
            switch (event.calledFrom) {
              case GameMethod.TURN_ON_END:
              case GameMethod.PHASE_ON_END:
                return stateWithError(event.error, Errors.StageEventInOnEnd);
              case GameMethod.PHASE_ON_BEGIN:
                return stateWithError(event.error, Errors.StageEventInPhaseBegin);
              case GameMethod.TURN_ON_BEGIN:
                if (event.type === "setActivePlayers")
                  break;
                return stateWithError(event.error, Errors.StageEventInTurnBegin);
            }
            if (turnHasEnded)
              continue EventQueue;
            break;
          }
          case "endTurn": {
            if (event.calledFrom === GameMethod.TURN_ON_END || event.calledFrom === GameMethod.PHASE_ON_END) {
              return stateWithError(event.error, Errors.EndTurnInOnEnd);
            }
            if (turnHasEnded)
              continue EventQueue;
            break;
          }
          case "endPhase":
          case "setPhase": {
            if (event.calledFrom === GameMethod.PHASE_ON_END) {
              return stateWithError(event.error, Errors.PhaseEventInOnEnd);
            }
            if (event.phase !== state.ctx.phase)
              continue EventQueue;
            break;
          }
        }
        const action = automaticGameEvent(event.type, event.args, this.playerID);
        state = this.flow.processEvent(state, action);
      }
    return state;
  }
};
var EventsPlugin = {
  name: "events",
  noClient: ({ api }) => api._private.isUsed(),
  isInvalid: ({ data }) => data.error || false,
  // Update the events plugin’s internal turn context each time a move
  // or hook is called. This allows events called after turn or phase
  // endings to dispatch the current turn and phase correctly.
  fnWrap: (method, methodType) => (context, ...args) => {
    const api = context.events;
    if (api)
      api._private.updateTurnContext(context.ctx, methodType);
    const G2 = method(context, ...args);
    if (api)
      api._private.unsetCurrentMethod();
    return G2;
  },
  dangerouslyFlushRawState: ({ state, api }) => api._private.update(state),
  api: ({ game, ctx, playerID }) => new Events(game.flow, ctx, playerID).api()
};
var LogPlugin = {
  name: "log",
  flush: () => ({}),
  api: ({ data }) => {
    return {
      setMetadata: (metadata) => {
        data.metadata = metadata;
      }
    };
  },
  setup: () => ({})
};
function isSerializable(value) {
  if (value === void 0 || value === null || typeof value === "boolean" || typeof value === "number" || typeof value === "string") {
    return true;
  }
  if (!(0, import_lodash.default)(value) && !Array.isArray(value)) {
    return false;
  }
  for (const key in value) {
    if (!isSerializable(value[key]))
      return false;
  }
  return true;
}
var SerializablePlugin = {
  name: "plugin-serializable",
  fnWrap: (move) => (context, ...args) => {
    const result = move(context, ...args);
    if (!isSerializable(result)) {
      throw new Error("Move state is not JSON-serialiazable.\nSee https://boardgame.io/documentation/#/?id=state for more information.");
    }
    return result;
  }
};
var production = false;
var logfn = production ? () => {
} : (...msg) => console.log(...msg);
var errorfn = (...msg) => console.error(...msg);
function info(msg) {
  logfn(`INFO: ${msg}`);
}
function error(error2) {
  errorfn("ERROR:", error2);
}
var CORE_PLUGINS = [ImmerPlugin, RandomPlugin, LogPlugin, SerializablePlugin];
var DEFAULT_PLUGINS = [...CORE_PLUGINS, EventsPlugin];
var ProcessAction = (state, action, opts) => {
  opts.game.plugins.filter((plugin2) => plugin2.action !== void 0).filter((plugin2) => plugin2.name === action.payload.type).forEach((plugin2) => {
    const name = plugin2.name;
    const pluginState = state.plugins[name] || { data: {} };
    const data = plugin2.action(pluginState.data, action.payload);
    state = {
      ...state,
      plugins: {
        ...state.plugins,
        [name]: { ...pluginState, data }
      }
    };
  });
  return state;
};
var GetAPIs = ({ plugins }) => Object.entries(plugins || {}).reduce((apis, [name, { api }]) => {
  apis[name] = api;
  return apis;
}, {});
var FnWrap = (methodToWrap, methodType, plugins) => {
  return [...CORE_PLUGINS, ...plugins, EventsPlugin].filter((plugin2) => plugin2.fnWrap !== void 0).reduce((method, { fnWrap }) => fnWrap(method, methodType), methodToWrap);
};
var Setup = (state, opts) => {
  [...DEFAULT_PLUGINS, ...opts.game.plugins].filter((plugin2) => plugin2.setup !== void 0).forEach((plugin2) => {
    const name = plugin2.name;
    const data = plugin2.setup({
      G: state.G,
      ctx: state.ctx,
      game: opts.game
    });
    state = {
      ...state,
      plugins: {
        ...state.plugins,
        [name]: { data }
      }
    };
  });
  return state;
};
var Enhance = (state, opts) => {
  [...DEFAULT_PLUGINS, ...opts.game.plugins].filter((plugin2) => plugin2.api !== void 0).forEach((plugin2) => {
    const name = plugin2.name;
    const pluginState = state.plugins[name] || { data: {} };
    const api = plugin2.api({
      G: state.G,
      ctx: state.ctx,
      data: pluginState.data,
      game: opts.game,
      playerID: opts.playerID
    });
    state = {
      ...state,
      plugins: {
        ...state.plugins,
        [name]: { ...pluginState, api }
      }
    };
  });
  return state;
};
var Flush = (state, opts) => {
  [...CORE_PLUGINS, ...opts.game.plugins, EventsPlugin].reverse().forEach((plugin2) => {
    const name = plugin2.name;
    const pluginState = state.plugins[name] || { data: {} };
    if (plugin2.flush) {
      const newData = plugin2.flush({
        G: state.G,
        ctx: state.ctx,
        game: opts.game,
        api: pluginState.api,
        data: pluginState.data
      });
      state = {
        ...state,
        plugins: {
          ...state.plugins,
          [plugin2.name]: { data: newData }
        }
      };
    } else if (plugin2.dangerouslyFlushRawState) {
      state = plugin2.dangerouslyFlushRawState({
        state,
        game: opts.game,
        api: pluginState.api,
        data: pluginState.data
      });
      const data = state.plugins[name].data;
      state = {
        ...state,
        plugins: {
          ...state.plugins,
          [plugin2.name]: { data }
        }
      };
    }
  });
  return state;
};
var NoClient = (state, opts) => {
  return [...DEFAULT_PLUGINS, ...opts.game.plugins].filter((plugin2) => plugin2.noClient !== void 0).map((plugin2) => {
    const name = plugin2.name;
    const pluginState = state.plugins[name];
    if (pluginState) {
      return plugin2.noClient({
        G: state.G,
        ctx: state.ctx,
        game: opts.game,
        api: pluginState.api,
        data: pluginState.data
      });
    }
    return false;
  }).includes(true);
};
var IsInvalid = (state, opts) => {
  const firstInvalidReturn = [...DEFAULT_PLUGINS, ...opts.game.plugins].filter((plugin2) => plugin2.isInvalid !== void 0).map((plugin2) => {
    const { name } = plugin2;
    const pluginState = state.plugins[name];
    const message = plugin2.isInvalid({
      G: state.G,
      ctx: state.ctx,
      game: opts.game,
      data: pluginState && pluginState.data
    });
    return message ? { plugin: name, message } : false;
  }).find((value) => value);
  return firstInvalidReturn || false;
};
var FlushAndValidate = (state, opts) => {
  const updatedState = Flush(state, opts);
  const isInvalid = IsInvalid(updatedState, opts);
  if (!isInvalid)
    return [updatedState];
  const { plugin: plugin2, message } = isInvalid;
  error(`${plugin2} plugin declared action invalid:
${message}`);
  return [state, isInvalid];
};
var PlayerView = ({ G: G2, ctx, plugins = {} }, { game, playerID }) => {
  [...DEFAULT_PLUGINS, ...game.plugins].forEach(({ name, playerView }) => {
    if (!playerView)
      return;
    const { data } = plugins[name] || { data: {} };
    const newData = playerView({ G: G2, ctx, game, data, playerID });
    plugins = {
      ...plugins,
      [name]: { data: newData }
    };
  });
  return plugins;
};
function supportDeprecatedMoveLimit(options, enforceMinMoves = false) {
  if (options.moveLimit) {
    if (enforceMinMoves) {
      options.minMoves = options.moveLimit;
    }
    options.maxMoves = options.moveLimit;
    delete options.moveLimit;
  }
}
function SetActivePlayers(ctx, arg) {
  let activePlayers = {};
  let _prevActivePlayers = [];
  let _nextActivePlayers = null;
  let _activePlayersMinMoves = {};
  let _activePlayersMaxMoves = {};
  if (Array.isArray(arg)) {
    const value = {};
    arg.forEach((v2) => value[v2] = Stage.NULL);
    activePlayers = value;
  } else {
    supportDeprecatedMoveLimit(arg);
    if (arg.next) {
      _nextActivePlayers = arg.next;
    }
    if (arg.revert) {
      _prevActivePlayers = [
        ...ctx._prevActivePlayers,
        {
          activePlayers: ctx.activePlayers,
          _activePlayersMinMoves: ctx._activePlayersMinMoves,
          _activePlayersMaxMoves: ctx._activePlayersMaxMoves,
          _activePlayersNumMoves: ctx._activePlayersNumMoves
        }
      ];
    }
    if (arg.currentPlayer !== void 0) {
      ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, ctx.currentPlayer, arg.currentPlayer);
    }
    if (arg.others !== void 0) {
      for (let i2 = 0; i2 < ctx.playOrder.length; i2++) {
        const id = ctx.playOrder[i2];
        if (id !== ctx.currentPlayer) {
          ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, id, arg.others);
        }
      }
    }
    if (arg.all !== void 0) {
      for (let i2 = 0; i2 < ctx.playOrder.length; i2++) {
        const id = ctx.playOrder[i2];
        ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, id, arg.all);
      }
    }
    if (arg.value) {
      for (const id in arg.value) {
        ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, id, arg.value[id]);
      }
    }
    if (arg.minMoves) {
      for (const id in activePlayers) {
        if (_activePlayersMinMoves[id] === void 0) {
          _activePlayersMinMoves[id] = arg.minMoves;
        }
      }
    }
    if (arg.maxMoves) {
      for (const id in activePlayers) {
        if (_activePlayersMaxMoves[id] === void 0) {
          _activePlayersMaxMoves[id] = arg.maxMoves;
        }
      }
    }
  }
  if (Object.keys(activePlayers).length === 0) {
    activePlayers = null;
  }
  if (Object.keys(_activePlayersMinMoves).length === 0) {
    _activePlayersMinMoves = null;
  }
  if (Object.keys(_activePlayersMaxMoves).length === 0) {
    _activePlayersMaxMoves = null;
  }
  const _activePlayersNumMoves = {};
  for (const id in activePlayers) {
    _activePlayersNumMoves[id] = 0;
  }
  return {
    ...ctx,
    activePlayers,
    _activePlayersMinMoves,
    _activePlayersMaxMoves,
    _activePlayersNumMoves,
    _prevActivePlayers,
    _nextActivePlayers
  };
}
function UpdateActivePlayersOnceEmpty(ctx) {
  let { activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, _activePlayersNumMoves, _prevActivePlayers, _nextActivePlayers } = ctx;
  if (activePlayers && Object.keys(activePlayers).length === 0) {
    if (_nextActivePlayers) {
      ctx = SetActivePlayers(ctx, _nextActivePlayers);
      ({
        activePlayers,
        _activePlayersMinMoves,
        _activePlayersMaxMoves,
        _activePlayersNumMoves,
        _prevActivePlayers
      } = ctx);
    } else if (_prevActivePlayers.length > 0) {
      const lastIndex = _prevActivePlayers.length - 1;
      ({
        activePlayers,
        _activePlayersMinMoves,
        _activePlayersMaxMoves,
        _activePlayersNumMoves
      } = _prevActivePlayers[lastIndex]);
      _prevActivePlayers = _prevActivePlayers.slice(0, lastIndex);
    } else {
      activePlayers = null;
      _activePlayersMinMoves = null;
      _activePlayersMaxMoves = null;
    }
  }
  return {
    ...ctx,
    activePlayers,
    _activePlayersMinMoves,
    _activePlayersMaxMoves,
    _activePlayersNumMoves,
    _prevActivePlayers
  };
}
function ApplyActivePlayerArgument(activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, playerID, arg) {
  if (typeof arg !== "object" || arg === Stage.NULL) {
    arg = { stage: arg };
  }
  if (arg.stage !== void 0) {
    supportDeprecatedMoveLimit(arg);
    activePlayers[playerID] = arg.stage;
    if (arg.minMoves)
      _activePlayersMinMoves[playerID] = arg.minMoves;
    if (arg.maxMoves)
      _activePlayersMaxMoves[playerID] = arg.maxMoves;
  }
}
function getCurrentPlayer(playOrder, playOrderPos) {
  return playOrder[playOrderPos] + "";
}
function InitTurnOrderState(state, turn) {
  let { G: G2, ctx } = state;
  const { numPlayers } = ctx;
  const pluginAPIs = GetAPIs(state);
  const context = { ...pluginAPIs, G: G2, ctx };
  const order = turn.order;
  let playOrder = [...Array.from({ length: numPlayers })].map((_2, i2) => i2 + "");
  if (order.playOrder !== void 0) {
    playOrder = order.playOrder(context);
  }
  const playOrderPos = order.first(context);
  const posType = typeof playOrderPos;
  if (posType !== "number") {
    error(`invalid value returned by turn.order.first — expected number got ${posType} “${playOrderPos}”.`);
  }
  const currentPlayer = getCurrentPlayer(playOrder, playOrderPos);
  ctx = { ...ctx, currentPlayer, playOrderPos, playOrder };
  ctx = SetActivePlayers(ctx, turn.activePlayers || {});
  return ctx;
}
function UpdateTurnOrderState(state, currentPlayer, turn, endTurnArg) {
  const order = turn.order;
  let { G: G2, ctx } = state;
  let playOrderPos = ctx.playOrderPos;
  let endPhase = false;
  if (endTurnArg && endTurnArg !== true) {
    if (typeof endTurnArg !== "object") {
      error(`invalid argument to endTurn: ${endTurnArg}`);
    }
    Object.keys(endTurnArg).forEach((arg) => {
      switch (arg) {
        case "remove":
          currentPlayer = getCurrentPlayer(ctx.playOrder, playOrderPos);
          break;
        case "next":
          playOrderPos = ctx.playOrder.indexOf(endTurnArg.next);
          currentPlayer = endTurnArg.next;
          break;
        default:
          error(`invalid argument to endTurn: ${arg}`);
      }
    });
  } else {
    const pluginAPIs = GetAPIs(state);
    const context = { ...pluginAPIs, G: G2, ctx };
    const t2 = order.next(context);
    const type = typeof t2;
    if (t2 !== void 0 && type !== "number") {
      error(`invalid value returned by turn.order.next — expected number or undefined got ${type} “${t2}”.`);
    }
    if (t2 === void 0) {
      endPhase = true;
    } else {
      playOrderPos = t2;
      currentPlayer = getCurrentPlayer(ctx.playOrder, playOrderPos);
    }
  }
  ctx = {
    ...ctx,
    playOrderPos,
    currentPlayer
  };
  return { endPhase, ctx };
}
var TurnOrder = {
  /**
   * DEFAULT
   *
   * The default round-robin turn order.
   */
  DEFAULT: {
    first: ({ ctx }) => ctx.turn === 0 ? ctx.playOrderPos : (ctx.playOrderPos + 1) % ctx.playOrder.length,
    next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length
  },
  /**
   * RESET
   *
   * Similar to DEFAULT, but starts from 0 each time.
   */
  RESET: {
    first: () => 0,
    next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length
  },
  /**
   * CONTINUE
   *
   * Similar to DEFAULT, but starts with the player who ended the last phase.
   */
  CONTINUE: {
    first: ({ ctx }) => ctx.playOrderPos,
    next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length
  },
  /**
   * ONCE
   *
   * Another round-robin turn order, but goes around just once.
   * The phase ends after all players have played.
   */
  ONCE: {
    first: () => 0,
    next: ({ ctx }) => {
      if (ctx.playOrderPos < ctx.playOrder.length - 1) {
        return ctx.playOrderPos + 1;
      }
    }
  },
  /**
   * CUSTOM
   *
   * Identical to DEFAULT, but also sets playOrder at the
   * beginning of the phase.
   *
   * @param {Array} playOrder - The play order.
   */
  CUSTOM: (playOrder) => ({
    playOrder: () => playOrder,
    first: () => 0,
    next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length
  }),
  /**
   * CUSTOM_FROM
   *
   * Identical to DEFAULT, but also sets playOrder at the
   * beginning of the phase to a value specified by a field
   * in G.
   *
   * @param {string} playOrderField - Field in G.
   */
  CUSTOM_FROM: (playOrderField) => ({
    playOrder: ({ G: G2 }) => G2[playOrderField],
    first: () => 0,
    next: ({ ctx }) => (ctx.playOrderPos + 1) % ctx.playOrder.length
  })
};
var Stage = {
  NULL: null
};
var ActivePlayers = {
  /**
   * ALL
   *
   * The turn stays with one player, but any player can play (in any order)
   * until the phase ends.
   */
  ALL: { all: Stage.NULL },
  /**
   * ALL_ONCE
   *
   * The turn stays with one player, but any player can play (once, and in any order).
   * This is typically used in a phase where you want to elicit a response
   * from every player in the game.
   */
  ALL_ONCE: { all: Stage.NULL, minMoves: 1, maxMoves: 1 },
  /**
   * OTHERS
   *
   * The turn stays with one player, and every *other* player can play (in any order)
   * until the phase ends.
   */
  OTHERS: { others: Stage.NULL },
  /**
   * OTHERS_ONCE
   *
   * The turn stays with one player, and every *other* player can play (once, and in any order).
   * This is typically used in a phase where you want to elicit a response
   * from every *other* player in the game.
   */
  OTHERS_ONCE: { others: Stage.NULL, minMoves: 1, maxMoves: 1 }
};

// node_modules/boardgame.io/dist/esm/reducer-24ea3e4c.js
var import_rfc6902 = __toESM(require_rfc6902());
function Flow({ moves, phases, endIf, onEnd, turn, events, plugins }) {
  if (moves === void 0) {
    moves = {};
  }
  if (events === void 0) {
    events = {};
  }
  if (plugins === void 0) {
    plugins = [];
  }
  if (phases === void 0) {
    phases = {};
  }
  if (!endIf)
    endIf = () => void 0;
  if (!onEnd)
    onEnd = ({ G: G2 }) => G2;
  if (!turn)
    turn = {};
  const phaseMap = { ...phases };
  if ("" in phaseMap) {
    error("cannot specify phase with empty name");
  }
  phaseMap[""] = {};
  const moveMap = {};
  const moveNames = /* @__PURE__ */ new Set();
  let startingPhase = null;
  Object.keys(moves).forEach((name) => moveNames.add(name));
  const HookWrapper = (hook, hookType) => {
    const withPlugins = FnWrap(hook, hookType, plugins);
    return (state) => {
      const pluginAPIs = GetAPIs(state);
      return withPlugins({
        ...pluginAPIs,
        G: state.G,
        ctx: state.ctx,
        playerID: state.playerID
      });
    };
  };
  const TriggerWrapper = (trigger) => {
    return (state) => {
      const pluginAPIs = GetAPIs(state);
      return trigger({
        ...pluginAPIs,
        G: state.G,
        ctx: state.ctx
      });
    };
  };
  const wrapped = {
    onEnd: HookWrapper(onEnd, GameMethod.GAME_ON_END),
    endIf: TriggerWrapper(endIf)
  };
  for (const phase in phaseMap) {
    const phaseConfig = phaseMap[phase];
    if (phaseConfig.start === true) {
      startingPhase = phase;
    }
    if (phaseConfig.moves !== void 0) {
      for (const move of Object.keys(phaseConfig.moves)) {
        moveMap[phase + "." + move] = phaseConfig.moves[move];
        moveNames.add(move);
      }
    }
    if (phaseConfig.endIf === void 0) {
      phaseConfig.endIf = () => void 0;
    }
    if (phaseConfig.onBegin === void 0) {
      phaseConfig.onBegin = ({ G: G2 }) => G2;
    }
    if (phaseConfig.onEnd === void 0) {
      phaseConfig.onEnd = ({ G: G2 }) => G2;
    }
    if (phaseConfig.turn === void 0) {
      phaseConfig.turn = turn;
    }
    if (phaseConfig.turn.order === void 0) {
      phaseConfig.turn.order = TurnOrder.DEFAULT;
    }
    if (phaseConfig.turn.onBegin === void 0) {
      phaseConfig.turn.onBegin = ({ G: G2 }) => G2;
    }
    if (phaseConfig.turn.onEnd === void 0) {
      phaseConfig.turn.onEnd = ({ G: G2 }) => G2;
    }
    if (phaseConfig.turn.endIf === void 0) {
      phaseConfig.turn.endIf = () => false;
    }
    if (phaseConfig.turn.onMove === void 0) {
      phaseConfig.turn.onMove = ({ G: G2 }) => G2;
    }
    if (phaseConfig.turn.stages === void 0) {
      phaseConfig.turn.stages = {};
    }
    supportDeprecatedMoveLimit(phaseConfig.turn, true);
    for (const stage in phaseConfig.turn.stages) {
      const stageConfig = phaseConfig.turn.stages[stage];
      const moves2 = stageConfig.moves || {};
      for (const move of Object.keys(moves2)) {
        const key = phase + "." + stage + "." + move;
        moveMap[key] = moves2[move];
        moveNames.add(move);
      }
    }
    phaseConfig.wrapped = {
      onBegin: HookWrapper(phaseConfig.onBegin, GameMethod.PHASE_ON_BEGIN),
      onEnd: HookWrapper(phaseConfig.onEnd, GameMethod.PHASE_ON_END),
      endIf: TriggerWrapper(phaseConfig.endIf)
    };
    phaseConfig.turn.wrapped = {
      onMove: HookWrapper(phaseConfig.turn.onMove, GameMethod.TURN_ON_MOVE),
      onBegin: HookWrapper(phaseConfig.turn.onBegin, GameMethod.TURN_ON_BEGIN),
      onEnd: HookWrapper(phaseConfig.turn.onEnd, GameMethod.TURN_ON_END),
      endIf: TriggerWrapper(phaseConfig.turn.endIf)
    };
    if (typeof phaseConfig.next !== "function") {
      const { next } = phaseConfig;
      phaseConfig.next = () => next || null;
    }
    phaseConfig.wrapped.next = TriggerWrapper(phaseConfig.next);
  }
  function GetPhase(ctx) {
    return ctx.phase ? phaseMap[ctx.phase] : phaseMap[""];
  }
  function OnMove(state) {
    return state;
  }
  function Process(state, events2) {
    const phasesEnded = /* @__PURE__ */ new Set();
    const turnsEnded = /* @__PURE__ */ new Set();
    for (let i2 = 0; i2 < events2.length; i2++) {
      const { fn: fn2, arg, ...rest } = events2[i2];
      if (fn2 === EndPhase) {
        turnsEnded.clear();
        const phase = state.ctx.phase;
        if (phasesEnded.has(phase)) {
          const ctx = { ...state.ctx, phase: null };
          return { ...state, ctx };
        }
        phasesEnded.add(phase);
      }
      const next = [];
      state = fn2(state, {
        ...rest,
        arg,
        next
      });
      if (fn2 === EndGame) {
        break;
      }
      const shouldEndGame = ShouldEndGame(state);
      if (shouldEndGame) {
        events2.push({
          fn: EndGame,
          arg: shouldEndGame,
          turn: state.ctx.turn,
          phase: state.ctx.phase,
          automatic: true
        });
        continue;
      }
      const shouldEndPhase = ShouldEndPhase(state);
      if (shouldEndPhase) {
        events2.push({
          fn: EndPhase,
          arg: shouldEndPhase,
          turn: state.ctx.turn,
          phase: state.ctx.phase,
          automatic: true
        });
        continue;
      }
      if ([OnMove, UpdateStage, UpdateActivePlayers].includes(fn2)) {
        const shouldEndTurn = ShouldEndTurn(state);
        if (shouldEndTurn) {
          events2.push({
            fn: EndTurn,
            arg: shouldEndTurn,
            turn: state.ctx.turn,
            phase: state.ctx.phase,
            automatic: true
          });
          continue;
        }
      }
      events2.push(...next);
    }
    return state;
  }
  function StartGame(state, { next }) {
    next.push({ fn: StartPhase });
    return state;
  }
  function StartPhase(state, { next }) {
    let { G: G2, ctx } = state;
    const phaseConfig = GetPhase(ctx);
    G2 = phaseConfig.wrapped.onBegin(state);
    next.push({ fn: StartTurn });
    return { ...state, G: G2, ctx };
  }
  function StartTurn(state, { currentPlayer }) {
    let { ctx } = state;
    const phaseConfig = GetPhase(ctx);
    if (currentPlayer) {
      ctx = { ...ctx, currentPlayer };
      if (phaseConfig.turn.activePlayers) {
        ctx = SetActivePlayers(ctx, phaseConfig.turn.activePlayers);
      }
    } else {
      ctx = InitTurnOrderState(state, phaseConfig.turn);
    }
    const turn2 = ctx.turn + 1;
    ctx = { ...ctx, turn: turn2, numMoves: 0, _prevActivePlayers: [] };
    const G2 = phaseConfig.turn.wrapped.onBegin({ ...state, ctx });
    return { ...state, G: G2, ctx, _undo: [], _redo: [] };
  }
  function UpdatePhase(state, { arg, next, phase }) {
    const phaseConfig = GetPhase({ phase });
    let { ctx } = state;
    if (arg && arg.next) {
      if (arg.next in phaseMap) {
        ctx = { ...ctx, phase: arg.next };
      } else {
        error("invalid phase: " + arg.next);
        return state;
      }
    } else {
      ctx = { ...ctx, phase: phaseConfig.wrapped.next(state) || null };
    }
    state = { ...state, ctx };
    next.push({ fn: StartPhase });
    return state;
  }
  function UpdateTurn(state, { arg, currentPlayer, next }) {
    let { G: G2, ctx } = state;
    const phaseConfig = GetPhase(ctx);
    const { endPhase, ctx: newCtx } = UpdateTurnOrderState(state, currentPlayer, phaseConfig.turn, arg);
    ctx = newCtx;
    state = { ...state, G: G2, ctx };
    if (endPhase) {
      next.push({ fn: EndPhase, turn: ctx.turn, phase: ctx.phase });
    } else {
      next.push({ fn: StartTurn, currentPlayer: ctx.currentPlayer });
    }
    return state;
  }
  function UpdateStage(state, { arg, playerID }) {
    if (typeof arg === "string" || arg === Stage.NULL) {
      arg = { stage: arg };
    }
    if (typeof arg !== "object")
      return state;
    supportDeprecatedMoveLimit(arg);
    let { ctx } = state;
    let { activePlayers, _activePlayersMinMoves, _activePlayersMaxMoves, _activePlayersNumMoves } = ctx;
    if (arg.stage !== void 0) {
      if (activePlayers === null) {
        activePlayers = {};
      }
      activePlayers[playerID] = arg.stage;
      _activePlayersNumMoves[playerID] = 0;
      if (arg.minMoves) {
        if (_activePlayersMinMoves === null) {
          _activePlayersMinMoves = {};
        }
        _activePlayersMinMoves[playerID] = arg.minMoves;
      }
      if (arg.maxMoves) {
        if (_activePlayersMaxMoves === null) {
          _activePlayersMaxMoves = {};
        }
        _activePlayersMaxMoves[playerID] = arg.maxMoves;
      }
    }
    ctx = {
      ...ctx,
      activePlayers,
      _activePlayersMinMoves,
      _activePlayersMaxMoves,
      _activePlayersNumMoves
    };
    return { ...state, ctx };
  }
  function UpdateActivePlayers(state, { arg }) {
    return { ...state, ctx: SetActivePlayers(state.ctx, arg) };
  }
  function ShouldEndGame(state) {
    return wrapped.endIf(state);
  }
  function ShouldEndPhase(state) {
    const phaseConfig = GetPhase(state.ctx);
    return phaseConfig.wrapped.endIf(state);
  }
  function ShouldEndTurn(state) {
    const phaseConfig = GetPhase(state.ctx);
    const currentPlayerMoves = state.ctx.numMoves || 0;
    if (phaseConfig.turn.maxMoves && currentPlayerMoves >= phaseConfig.turn.maxMoves) {
      return true;
    }
    return phaseConfig.turn.wrapped.endIf(state);
  }
  function EndGame(state, { arg, phase }) {
    state = EndPhase(state, { phase });
    if (arg === void 0) {
      arg = true;
    }
    state = { ...state, ctx: { ...state.ctx, gameover: arg } };
    const G2 = wrapped.onEnd(state);
    return { ...state, G: G2 };
  }
  function EndPhase(state, { arg, next, turn: initialTurn, automatic }) {
    state = EndTurn(state, { turn: initialTurn, force: true, automatic: true });
    const { phase, turn: turn2 } = state.ctx;
    if (next) {
      next.push({ fn: UpdatePhase, arg, phase });
    }
    if (phase === null) {
      return state;
    }
    const phaseConfig = GetPhase(state.ctx);
    const G2 = phaseConfig.wrapped.onEnd(state);
    const ctx = { ...state.ctx, phase: null };
    const action = gameEvent("endPhase", arg);
    const { _stateID } = state;
    const logEntry = { action, _stateID, turn: turn2, phase };
    if (automatic)
      logEntry.automatic = true;
    const deltalog = [...state.deltalog || [], logEntry];
    return { ...state, G: G2, ctx, deltalog };
  }
  function EndTurn(state, { arg, next, turn: initialTurn, force, automatic, playerID }) {
    if (initialTurn !== state.ctx.turn) {
      return state;
    }
    const { currentPlayer, numMoves, phase, turn: turn2 } = state.ctx;
    const phaseConfig = GetPhase(state.ctx);
    const currentPlayerMoves = numMoves || 0;
    if (!force && phaseConfig.turn.minMoves && currentPlayerMoves < phaseConfig.turn.minMoves) {
      info(`cannot end turn before making ${phaseConfig.turn.minMoves} moves`);
      return state;
    }
    const G2 = phaseConfig.turn.wrapped.onEnd(state);
    if (next) {
      next.push({ fn: UpdateTurn, arg, currentPlayer });
    }
    let ctx = { ...state.ctx, activePlayers: null };
    if (arg && arg.remove) {
      playerID = playerID || currentPlayer;
      const playOrder = ctx.playOrder.filter((i2) => i2 != playerID);
      const playOrderPos = ctx.playOrderPos > playOrder.length - 1 ? 0 : ctx.playOrderPos;
      ctx = { ...ctx, playOrder, playOrderPos };
      if (playOrder.length === 0) {
        next.push({ fn: EndPhase, turn: turn2, phase });
        return state;
      }
    }
    const action = gameEvent("endTurn", arg);
    const { _stateID } = state;
    const logEntry = { action, _stateID, turn: turn2, phase };
    if (automatic)
      logEntry.automatic = true;
    const deltalog = [...state.deltalog || [], logEntry];
    return { ...state, G: G2, ctx, deltalog, _undo: [], _redo: [] };
  }
  function EndStage(state, { arg, next, automatic, playerID }) {
    playerID = playerID || state.ctx.currentPlayer;
    let { ctx, _stateID } = state;
    let { activePlayers, _activePlayersNumMoves, _activePlayersMinMoves, _activePlayersMaxMoves, phase, turn: turn2 } = ctx;
    const playerInStage = activePlayers !== null && playerID in activePlayers;
    const phaseConfig = GetPhase(ctx);
    if (!arg && playerInStage) {
      const stage = phaseConfig.turn.stages[activePlayers[playerID]];
      if (stage && stage.next) {
        arg = stage.next;
      }
    }
    if (next) {
      next.push({ fn: UpdateStage, arg, playerID });
    }
    if (!playerInStage)
      return state;
    const currentPlayerMoves = _activePlayersNumMoves[playerID] || 0;
    if (_activePlayersMinMoves && _activePlayersMinMoves[playerID] && currentPlayerMoves < _activePlayersMinMoves[playerID]) {
      info(`cannot end stage before making ${_activePlayersMinMoves[playerID]} moves`);
      return state;
    }
    activePlayers = { ...activePlayers };
    delete activePlayers[playerID];
    if (_activePlayersMinMoves) {
      _activePlayersMinMoves = { ..._activePlayersMinMoves };
      delete _activePlayersMinMoves[playerID];
    }
    if (_activePlayersMaxMoves) {
      _activePlayersMaxMoves = { ..._activePlayersMaxMoves };
      delete _activePlayersMaxMoves[playerID];
    }
    ctx = UpdateActivePlayersOnceEmpty({
      ...ctx,
      activePlayers,
      _activePlayersMinMoves,
      _activePlayersMaxMoves
    });
    const action = gameEvent("endStage", arg);
    const logEntry = { action, _stateID, turn: turn2, phase };
    if (automatic)
      logEntry.automatic = true;
    const deltalog = [...state.deltalog || [], logEntry];
    return { ...state, ctx, deltalog };
  }
  function GetMove(ctx, name, playerID) {
    const phaseConfig = GetPhase(ctx);
    const stages = phaseConfig.turn.stages;
    const { activePlayers } = ctx;
    if (activePlayers && activePlayers[playerID] !== void 0 && activePlayers[playerID] !== Stage.NULL && stages[activePlayers[playerID]] !== void 0 && stages[activePlayers[playerID]].moves !== void 0) {
      const stage = stages[activePlayers[playerID]];
      const moves2 = stage.moves;
      if (name in moves2) {
        return moves2[name];
      }
    } else if (phaseConfig.moves) {
      if (name in phaseConfig.moves) {
        return phaseConfig.moves[name];
      }
    } else if (name in moves) {
      return moves[name];
    }
    return null;
  }
  function ProcessMove(state, action) {
    const { playerID, type } = action;
    const { currentPlayer, activePlayers, _activePlayersMaxMoves } = state.ctx;
    const move = GetMove(state.ctx, type, playerID);
    const shouldCount = !move || typeof move === "function" || move.noLimit !== true;
    let { numMoves, _activePlayersNumMoves } = state.ctx;
    if (shouldCount) {
      if (playerID === currentPlayer)
        numMoves++;
      if (activePlayers)
        _activePlayersNumMoves[playerID]++;
    }
    state = {
      ...state,
      ctx: {
        ...state.ctx,
        numMoves,
        _activePlayersNumMoves
      }
    };
    if (_activePlayersMaxMoves && _activePlayersNumMoves[playerID] >= _activePlayersMaxMoves[playerID]) {
      state = EndStage(state, { playerID, automatic: true });
    }
    const phaseConfig = GetPhase(state.ctx);
    const G2 = phaseConfig.turn.wrapped.onMove({ ...state, playerID });
    state = { ...state, G: G2 };
    const events2 = [{ fn: OnMove }];
    return Process(state, events2);
  }
  function SetStageEvent(state, playerID, arg) {
    return Process(state, [{ fn: EndStage, arg, playerID }]);
  }
  function EndStageEvent(state, playerID) {
    return Process(state, [{ fn: EndStage, playerID }]);
  }
  function SetActivePlayersEvent(state, _playerID, arg) {
    return Process(state, [{ fn: UpdateActivePlayers, arg }]);
  }
  function SetPhaseEvent(state, _playerID, newPhase) {
    return Process(state, [
      {
        fn: EndPhase,
        phase: state.ctx.phase,
        turn: state.ctx.turn,
        arg: { next: newPhase }
      }
    ]);
  }
  function EndPhaseEvent(state) {
    return Process(state, [
      { fn: EndPhase, phase: state.ctx.phase, turn: state.ctx.turn }
    ]);
  }
  function EndTurnEvent(state, _playerID, arg) {
    return Process(state, [
      { fn: EndTurn, turn: state.ctx.turn, phase: state.ctx.phase, arg }
    ]);
  }
  function PassEvent(state, _playerID, arg) {
    return Process(state, [
      {
        fn: EndTurn,
        turn: state.ctx.turn,
        phase: state.ctx.phase,
        force: true,
        arg
      }
    ]);
  }
  function EndGameEvent(state, _playerID, arg) {
    return Process(state, [
      { fn: EndGame, turn: state.ctx.turn, phase: state.ctx.phase, arg }
    ]);
  }
  const eventHandlers = {
    endStage: EndStageEvent,
    setStage: SetStageEvent,
    endTurn: EndTurnEvent,
    pass: PassEvent,
    endPhase: EndPhaseEvent,
    setPhase: SetPhaseEvent,
    endGame: EndGameEvent,
    setActivePlayers: SetActivePlayersEvent
  };
  const enabledEventNames = [];
  if (events.endTurn !== false) {
    enabledEventNames.push("endTurn");
  }
  if (events.pass !== false) {
    enabledEventNames.push("pass");
  }
  if (events.endPhase !== false) {
    enabledEventNames.push("endPhase");
  }
  if (events.setPhase !== false) {
    enabledEventNames.push("setPhase");
  }
  if (events.endGame !== false) {
    enabledEventNames.push("endGame");
  }
  if (events.setActivePlayers !== false) {
    enabledEventNames.push("setActivePlayers");
  }
  if (events.endStage !== false) {
    enabledEventNames.push("endStage");
  }
  if (events.setStage !== false) {
    enabledEventNames.push("setStage");
  }
  function ProcessEvent(state, action) {
    const { type, playerID, args } = action.payload;
    if (typeof eventHandlers[type] !== "function")
      return state;
    return eventHandlers[type](state, playerID, ...Array.isArray(args) ? args : [args]);
  }
  function IsPlayerActive(_G, ctx, playerID) {
    if (ctx.activePlayers) {
      return playerID in ctx.activePlayers;
    }
    return ctx.currentPlayer === playerID;
  }
  return {
    ctx: (numPlayers) => ({
      numPlayers,
      turn: 0,
      currentPlayer: "0",
      playOrder: [...Array.from({ length: numPlayers })].map((_2, i2) => i2 + ""),
      playOrderPos: 0,
      phase: startingPhase,
      activePlayers: null
    }),
    init: (state) => {
      return Process(state, [{ fn: StartGame }]);
    },
    isPlayerActive: IsPlayerActive,
    eventHandlers,
    eventNames: Object.keys(eventHandlers),
    enabledEventNames,
    moveMap,
    moveNames: [...moveNames.values()],
    processMove: ProcessMove,
    processEvent: ProcessEvent,
    getMove: GetMove
  };
}
function IsProcessed(game) {
  return game.processMove !== void 0;
}
function ProcessGameConfig(game) {
  if (IsProcessed(game)) {
    return game;
  }
  if (game.name === void 0)
    game.name = "default";
  if (game.deltaState === void 0)
    game.deltaState = false;
  if (game.disableUndo === void 0)
    game.disableUndo = false;
  if (game.setup === void 0)
    game.setup = () => ({});
  if (game.moves === void 0)
    game.moves = {};
  if (game.playerView === void 0)
    game.playerView = ({ G: G2 }) => G2;
  if (game.plugins === void 0)
    game.plugins = [];
  game.plugins.forEach((plugin2) => {
    if (plugin2.name === void 0) {
      throw new Error("Plugin missing name attribute");
    }
    if (plugin2.name.includes(" ")) {
      throw new Error(plugin2.name + ": Plugin name must not include spaces");
    }
  });
  if (game.name.includes(" ")) {
    throw new Error(game.name + ": Game name must not include spaces");
  }
  const flow = Flow(game);
  return {
    ...game,
    flow,
    moveNames: flow.moveNames,
    pluginNames: game.plugins.map((p2) => p2.name),
    processMove: (state, action) => {
      let moveFn = flow.getMove(state.ctx, action.type, action.playerID);
      if (IsLongFormMove(moveFn)) {
        moveFn = moveFn.move;
      }
      if (moveFn instanceof Function) {
        const fn2 = FnWrap(moveFn, GameMethod.MOVE, game.plugins);
        let args = [];
        if (action.args !== void 0) {
          args = Array.isArray(action.args) ? action.args : [action.args];
        }
        const context = {
          ...GetAPIs(state),
          G: state.G,
          ctx: state.ctx,
          playerID: action.playerID
        };
        return fn2(context, ...args);
      }
      error(`invalid move object: ${action.type}`);
      return state.G;
    }
  };
}
function IsLongFormMove(move) {
  return move instanceof Object && move.move !== void 0;
}
var UpdateErrorType;
(function(UpdateErrorType2) {
  UpdateErrorType2["UnauthorizedAction"] = "update/unauthorized_action";
  UpdateErrorType2["MatchNotFound"] = "update/match_not_found";
  UpdateErrorType2["PatchFailed"] = "update/patch_failed";
})(UpdateErrorType || (UpdateErrorType = {}));
var ActionErrorType;
(function(ActionErrorType2) {
  ActionErrorType2["StaleStateId"] = "action/stale_state_id";
  ActionErrorType2["UnavailableMove"] = "action/unavailable_move";
  ActionErrorType2["InvalidMove"] = "action/invalid_move";
  ActionErrorType2["InactivePlayer"] = "action/inactive_player";
  ActionErrorType2["GameOver"] = "action/gameover";
  ActionErrorType2["ActionDisabled"] = "action/action_disabled";
  ActionErrorType2["ActionInvalid"] = "action/action_invalid";
  ActionErrorType2["PluginActionInvalid"] = "action/plugin_invalid";
})(ActionErrorType || (ActionErrorType = {}));
var actionHasPlayerID = (action) => action.payload.playerID !== null && action.payload.playerID !== void 0;
var CanUndoMove = (G2, ctx, move) => {
  function HasUndoable(move2) {
    return move2.undoable !== void 0;
  }
  function IsFunction(undoable) {
    return undoable instanceof Function;
  }
  if (!HasUndoable(move)) {
    return true;
  }
  if (IsFunction(move.undoable)) {
    return move.undoable({ G: G2, ctx });
  }
  return move.undoable;
};
function updateUndoRedoState(state, opts) {
  if (opts.game.disableUndo)
    return state;
  const undoEntry = {
    G: state.G,
    ctx: state.ctx,
    plugins: state.plugins,
    playerID: opts.action.payload.playerID || state.ctx.currentPlayer
  };
  if (opts.action.type === "MAKE_MOVE") {
    undoEntry.moveType = opts.action.payload.type;
  }
  return {
    ...state,
    _undo: [...state._undo, undoEntry],
    // Always reset redo stack when making a move or event
    _redo: []
  };
}
function initializeDeltalog(state, action, move) {
  const logEntry = {
    action,
    _stateID: state._stateID,
    turn: state.ctx.turn,
    phase: state.ctx.phase
  };
  const pluginLogMetadata = state.plugins.log.data.metadata;
  if (pluginLogMetadata !== void 0) {
    logEntry.metadata = pluginLogMetadata;
  }
  if (typeof move === "object" && move.redact === true) {
    logEntry.redact = true;
  } else if (typeof move === "object" && move.redact instanceof Function) {
    logEntry.redact = move.redact({ G: state.G, ctx: state.ctx });
  }
  return {
    ...state,
    deltalog: [logEntry]
  };
}
function flushAndValidatePlugins(state, oldState, pluginOpts) {
  const [newState, isInvalid] = FlushAndValidate(state, pluginOpts);
  if (!isInvalid)
    return [newState];
  return [
    newState,
    WithError(oldState, ActionErrorType.PluginActionInvalid, isInvalid)
  ];
}
function ExtractTransients(transientState) {
  if (!transientState) {
    return [null, void 0];
  }
  const { transients, ...state } = transientState;
  return [state, transients];
}
function WithError(state, errorType, payload) {
  const error2 = {
    type: errorType,
    payload
  };
  return {
    ...state,
    transients: {
      error: error2
    }
  };
}
var TransientHandlingMiddleware = (store) => (next) => (action) => {
  const result = next(action);
  switch (action.type) {
    case STRIP_TRANSIENTS: {
      return result;
    }
    default: {
      const [, transients] = ExtractTransients(store.getState());
      if (typeof transients !== "undefined") {
        store.dispatch(stripTransients());
        return {
          ...result,
          transients
        };
      }
      return result;
    }
  }
};
function CreateGameReducer({ game, isClient }) {
  game = ProcessGameConfig(game);
  return (stateWithTransients = null, action) => {
    let [
      state
      /*, transients */
    ] = ExtractTransients(stateWithTransients);
    switch (action.type) {
      case STRIP_TRANSIENTS: {
        return state;
      }
      case GAME_EVENT: {
        state = { ...state, deltalog: [] };
        if (isClient) {
          return state;
        }
        if (state.ctx.gameover !== void 0) {
          error(`cannot call event after game end`);
          return WithError(state, ActionErrorType.GameOver);
        }
        if (actionHasPlayerID(action) && !game.flow.isPlayerActive(state.G, state.ctx, action.payload.playerID)) {
          error(`disallowed event: ${action.payload.type}`);
          return WithError(state, ActionErrorType.InactivePlayer);
        }
        state = Enhance(state, {
          game,
          isClient: false,
          playerID: action.payload.playerID
        });
        let newState = game.flow.processEvent(state, action);
        let stateWithError;
        [newState, stateWithError] = flushAndValidatePlugins(newState, state, {
          game,
          isClient: false
        });
        if (stateWithError)
          return stateWithError;
        newState = updateUndoRedoState(newState, { game, action });
        return { ...newState, _stateID: state._stateID + 1 };
      }
      case MAKE_MOVE: {
        const oldState = state = { ...state, deltalog: [] };
        const move = game.flow.getMove(state.ctx, action.payload.type, action.payload.playerID || state.ctx.currentPlayer);
        if (move === null) {
          error(`disallowed move: ${action.payload.type}`);
          return WithError(state, ActionErrorType.UnavailableMove);
        }
        if (isClient && move.client === false) {
          return state;
        }
        if (state.ctx.gameover !== void 0) {
          error(`cannot make move after game end`);
          return WithError(state, ActionErrorType.GameOver);
        }
        if (actionHasPlayerID(action) && !game.flow.isPlayerActive(state.G, state.ctx, action.payload.playerID)) {
          error(`disallowed move: ${action.payload.type}`);
          return WithError(state, ActionErrorType.InactivePlayer);
        }
        state = Enhance(state, {
          game,
          isClient,
          playerID: action.payload.playerID
        });
        const G2 = game.processMove(state, action.payload);
        if (G2 === INVALID_MOVE) {
          error(`invalid move: ${action.payload.type} args: ${action.payload.args}`);
          return WithError(state, ActionErrorType.InvalidMove);
        }
        const newState = { ...state, G: G2 };
        if (isClient && NoClient(newState, { game })) {
          return state;
        }
        state = newState;
        if (isClient) {
          let stateWithError2;
          [state, stateWithError2] = flushAndValidatePlugins(state, oldState, {
            game,
            isClient: true
          });
          if (stateWithError2)
            return stateWithError2;
          return {
            ...state,
            _stateID: state._stateID + 1
          };
        }
        state = initializeDeltalog(state, action, move);
        state = game.flow.processMove(state, action.payload);
        let stateWithError;
        [state, stateWithError] = flushAndValidatePlugins(state, oldState, {
          game
        });
        if (stateWithError)
          return stateWithError;
        state = updateUndoRedoState(state, { game, action });
        return {
          ...state,
          _stateID: state._stateID + 1
        };
      }
      case RESET:
      case UPDATE:
      case SYNC: {
        return action.state;
      }
      case UNDO: {
        state = { ...state, deltalog: [] };
        if (game.disableUndo) {
          error("Undo is not enabled");
          return WithError(state, ActionErrorType.ActionDisabled);
        }
        const { G: G2, ctx, _undo, _redo, _stateID } = state;
        if (_undo.length < 2) {
          error(`No moves to undo`);
          return WithError(state, ActionErrorType.ActionInvalid);
        }
        const last = _undo[_undo.length - 1];
        const restore = _undo[_undo.length - 2];
        if (actionHasPlayerID(action) && action.payload.playerID !== last.playerID) {
          error(`Cannot undo other players' moves`);
          return WithError(state, ActionErrorType.ActionInvalid);
        }
        if (last.moveType) {
          const lastMove = game.flow.getMove(restore.ctx, last.moveType, last.playerID);
          if (!CanUndoMove(G2, ctx, lastMove)) {
            error(`Move cannot be undone`);
            return WithError(state, ActionErrorType.ActionInvalid);
          }
        }
        state = initializeDeltalog(state, action);
        return {
          ...state,
          G: restore.G,
          ctx: restore.ctx,
          plugins: restore.plugins,
          _stateID: _stateID + 1,
          _undo: _undo.slice(0, -1),
          _redo: [last, ..._redo]
        };
      }
      case REDO: {
        state = { ...state, deltalog: [] };
        if (game.disableUndo) {
          error("Redo is not enabled");
          return WithError(state, ActionErrorType.ActionDisabled);
        }
        const { _undo, _redo, _stateID } = state;
        if (_redo.length === 0) {
          error(`No moves to redo`);
          return WithError(state, ActionErrorType.ActionInvalid);
        }
        const first = _redo[0];
        if (actionHasPlayerID(action) && action.payload.playerID !== first.playerID) {
          error(`Cannot redo other players' moves`);
          return WithError(state, ActionErrorType.ActionInvalid);
        }
        state = initializeDeltalog(state, action);
        return {
          ...state,
          G: first.G,
          ctx: first.ctx,
          plugins: first.plugins,
          _stateID: _stateID + 1,
          _undo: [..._undo, first],
          _redo: _redo.slice(1)
        };
      }
      case PLUGIN: {
        return ProcessAction(state, action, { game });
      }
      case PATCH: {
        const oldState = state;
        const newState = JSON.parse(JSON.stringify(oldState));
        const patchError = (0, import_rfc6902.applyPatch)(newState, action.patch);
        const hasError = patchError.some((entry) => entry !== null);
        if (hasError) {
          error(`Patch ${JSON.stringify(action.patch)} apply failed`);
          return WithError(oldState, UpdateErrorType.PatchFailed, patchError);
        } else {
          return newState;
        }
      }
      default: {
        return state;
      }
    }
  };
}

// node_modules/boardgame.io/dist/esm/initialize-7316768f.js
function InitializeGame({ game, numPlayers, setupData }) {
  game = ProcessGameConfig(game);
  if (!numPlayers) {
    numPlayers = 2;
  }
  const ctx = game.flow.ctx(numPlayers);
  let state = {
    // User managed state.
    G: {},
    // Framework managed state.
    ctx,
    // Plugin related state.
    plugins: {}
  };
  state = Setup(state, { game });
  state = Enhance(state, { game, playerID: void 0 });
  const pluginAPIs = GetAPIs(state);
  state.G = game.setup({ ...pluginAPIs, ctx: state.ctx }, setupData);
  let initial = {
    ...state,
    // List of {G, ctx} pairs that can be undone.
    _undo: [],
    // List of {G, ctx} pairs that can be redone.
    _redo: [],
    // A monotonically non-decreasing ID to ensure that
    // state updates are only allowed from clients that
    // are at the same version that the server.
    _stateID: 0
  };
  initial = game.flow.init(initial);
  [initial] = FlushAndValidate(initial, { game });
  if (!game.disableUndo) {
    initial._undo = [
      {
        G: initial.G,
        ctx: initial.ctx,
        plugins: initial.plugins
      }
    ];
  }
  return initial;
}

// node_modules/boardgame.io/dist/esm/transport-ce07b771.js
var Transport = class {
  constructor({ transportDataCallback, gameName, playerID, matchID, credentials, numPlayers }) {
    this.connectionStatusCallback = () => {
    };
    this.isConnected = false;
    this.transportDataCallback = transportDataCallback;
    this.gameName = gameName || "default";
    this.playerID = playerID || null;
    this.matchID = matchID || "default";
    this.credentials = credentials;
    this.numPlayers = numPlayers || 2;
  }
  /** Subscribe to connection state changes. */
  subscribeToConnectionStatus(fn2) {
    this.connectionStatusCallback = fn2;
  }
  /** Transport implementations should call this when they connect/disconnect. */
  setConnectionStatus(isConnected) {
    this.isConnected = isConnected;
    this.connectionStatusCallback();
  }
  /** Transport implementations should call this when they receive data from a master. */
  notifyClient(data) {
    this.transportDataCallback(data);
  }
};

export {
  createStore,
  compose,
  applyMiddleware,
  alea,
  require_lodash,
  MAKE_MOVE,
  GAME_EVENT,
  REDO,
  RESET,
  SYNC,
  UNDO,
  UPDATE,
  PATCH,
  STRIP_TRANSIENTS,
  makeMove,
  gameEvent,
  sync,
  patch,
  update,
  reset,
  undo,
  redo,
  ActionCreators,
  error,
  PlayerView,
  require_rfc6902,
  ProcessGameConfig,
  IsLongFormMove,
  TransientHandlingMiddleware,
  CreateGameReducer,
  InitializeGame,
  Transport
};
//# sourceMappingURL=chunk-CT2AWXLA.js.map
