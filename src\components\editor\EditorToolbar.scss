.editor-toolbar {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  gap: 16px;
  overflow-x: auto;
  min-height: 60px;

  .toolbar-section {
    display: flex;
    flex-direction: column;
    gap: 4px;
    flex-shrink: 0;
  }

  .toolbar-label {
    font-size: 11px;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
  }

  .toolbar-group {
    display: flex;
    gap: 4px;
    align-items: center;
  }

  .toolbar-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    padding: 6px 8px;
    border: 1px solid transparent;
    border-radius: 6px;
    background: transparent;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 50px;
    font-size: 12px;

    &:hover:not(:disabled) {
      background: #f8f9fa;
      border-color: #dee2e6;
    }

    &.active {
      background: #e3f2fd;
      border-color: #2196f3;
      color: #1976d2;

      .btn-icon {
        transform: scale(1.1);
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .btn-icon {
      font-size: 16px;
      line-height: 1;
      transition: transform 0.2s ease;
    }

    .btn-text {
      font-size: 10px;
      font-weight: 500;
      line-height: 1;
      white-space: nowrap;
    }
  }

  .toolbar-separator {
    width: 1px;
    height: 40px;
    background: #dee2e6;
    flex-shrink: 0;
  }

  .toolbar-spacer {
    flex: 1;
    min-width: 20px;
  }

  .toolbar-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    padding: 4px 8px;
    background: #f8f9fa;
    border-radius: 4px;
    min-width: 60px;

    .info-label {
      font-size: 9px;
      font-weight: 600;
      color: #6c757d;
      text-transform: uppercase;
      letter-spacing: 0.3px;
    }

    .info-value {
      font-size: 11px;
      font-weight: 500;
      color: #495057;
      text-transform: capitalize;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 80px;
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .editor-toolbar {
    .toolbar-btn {
      min-width: 45px;
      padding: 5px 6px;

      .btn-icon {
        font-size: 14px;
      }

      .btn-text {
        font-size: 9px;
      }
    }

    .toolbar-info {
      min-width: 50px;

      .info-label {
        font-size: 8px;
      }

      .info-value {
        font-size: 10px;
        max-width: 60px;
      }
    }
  }
}

@media (max-width: 768px) {
  .editor-toolbar {
    padding: 6px 12px;
    gap: 12px;
    min-height: 50px;

    .toolbar-section {
      gap: 2px;
    }

    .toolbar-label {
      font-size: 10px;
      margin-bottom: 1px;
    }

    .toolbar-group {
      gap: 2px;
    }

    .toolbar-btn {
      min-width: 40px;
      padding: 4px 5px;

      .btn-icon {
        font-size: 12px;
      }

      .btn-text {
        font-size: 8px;
      }
    }

    .toolbar-separator {
      height: 30px;
    }

    .toolbar-info {
      min-width: 40px;
      padding: 3px 6px;

      .info-label {
        font-size: 7px;
      }

      .info-value {
        font-size: 9px;
        max-width: 50px;
      }
    }

    // Hide some sections on very small screens
    .toolbar-section:last-child {
      display: none;
    }
  }
}

// Compact mode for very small screens
@media (max-width: 480px) {
  .editor-toolbar {
    flex-wrap: wrap;
    min-height: auto;
    max-height: 100px;
    overflow-y: auto;

    .toolbar-section {
      flex-direction: row;
      align-items: center;
      gap: 8px;

      .toolbar-label {
        margin-bottom: 0;
        margin-right: 4px;
      }
    }

    .toolbar-btn {
      flex-direction: row;
      gap: 4px;
      min-width: auto;
      padding: 4px 8px;

      .btn-text {
        display: none;
      }

      .btn-icon {
        font-size: 14px;
      }
    }

    .toolbar-separator {
      width: 100%;
      height: 1px;
      margin: 4px 0;
    }

    .toolbar-spacer {
      display: none;
    }
  }
}

// Dark mode support (if needed)
@media (prefers-color-scheme: dark) {
  .editor-toolbar {
    background: #2d3748;
    border-bottom-color: #4a5568;

    .toolbar-label {
      color: #a0aec0;
    }

    .toolbar-btn {
      color: #e2e8f0;

      &:hover:not(:disabled) {
        background: #4a5568;
        border-color: #718096;
      }

      &.active {
        background: #2b6cb0;
        border-color: #3182ce;
        color: #bee3f8;
      }
    }

    .toolbar-separator {
      background: #4a5568;
    }

    .toolbar-info {
      background: #4a5568;

      .info-label {
        color: #a0aec0;
      }

      .info-value {
        color: #e2e8f0;
      }
    }
  }
}

// Scrollbar styling for horizontal overflow
.editor-toolbar {
  &::-webkit-scrollbar {
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Animation for active state
.toolbar-btn.active {
  animation: toolbarActivate 0.3s ease;
}

@keyframes toolbarActivate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// Tooltip enhancement
.toolbar-btn[title] {
  position: relative;

  &:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 4px;
    pointer-events: none;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease forwards;
  }

  &:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    margin-bottom: -4px;
    pointer-events: none;
    opacity: 0;
    animation: tooltipFadeIn 0.2s ease forwards;
  }
}

@keyframes tooltipFadeIn {
  to {
    opacity: 1;
  }
}
