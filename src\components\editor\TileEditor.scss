.tile-editor {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 16px;
  height: 100%;
  overflow-y: auto;

  &__header {
    h3 {
      margin: 0 0 8px 0;
      color: #2c3e50;
      font-size: 18px;
      font-weight: 600;
    }

    p {
      margin: 0;
      color: #6c757d;
      font-size: 14px;
      line-height: 1.4;
    }
  }

  h4 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 16px;
    font-weight: 600;
  }

  h5 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
  }
}

.tile-types {
  .tile-type-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 8px;
  }

  .tile-type-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px 8px;
    border: 2px solid transparent;
    border-radius: 8px;
    background: #f8f9fa;
    cursor: pointer;
    transition: all 0.2s ease;
    min-height: 80px;

    &:hover {
      border-color: #007bff;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border-color: #28a745;
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    .tile-type-icon {
      font-size: 24px;
      line-height: 1;
    }

    .tile-type-name {
      font-size: 12px;
      font-weight: 600;
      text-transform: capitalize;
      text-align: center;
      color: #2c3e50;
    }

    .tile-type-count {
      font-size: 11px;
      color: #6c757d;
      background: rgba(255, 255, 255, 0.8);
      padding: 2px 6px;
      border-radius: 10px;
      min-width: 20px;
      text-align: center;
    }
  }
}

.tile-stats {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
    margin-bottom: 16px;
  }

  .stat-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
    padding: 8px;
    background: white;
    border-radius: 6px;
    text-align: center;

    .stat-label {
      font-size: 12px;
      color: #6c757d;
      font-weight: 500;
    }

    .stat-value {
      font-size: 18px;
      font-weight: 700;
      color: #2c3e50;
    }
  }

  .tile-distribution {
    .distribution-item {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 8px;

      .distribution-bar {
        flex: 1;
        height: 20px;
        background: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        position: relative;

        .distribution-fill {
          height: 100%;
          transition: width 0.3s ease;
          border-radius: 10px;
        }
      }

      .distribution-label {
        font-size: 12px;
        color: #495057;
        font-weight: 500;
        min-width: 120px;
        text-align: right;
      }
    }
  }
}

.layout-tools {
  .tool-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  .tool-btn {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    background: white;
    color: #495057;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 4px;

    &:hover {
      background: #f8f9fa;
      border-color: #007bff;
      color: #007bff;
    }

    &:active {
      transform: translateY(1px);
    }
  }
}

.tile-properties {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;

  .property-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;

    &:last-child {
      border-bottom: none;
    }

    label {
      font-size: 14px;
      font-weight: 500;
      color: #495057;
    }

    .property-value {
      font-size: 14px;
      color: #2c3e50;
      font-weight: 600;
    }
  }
}

.validation-warnings {
  .warning,
  .info {
    padding: 12px;
    border-radius: 6px;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
  }

  .info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
  }
}

// Responsive design
@media (max-width: 768px) {
  .tile-editor {
    padding: 12px;
    gap: 16px;

    &__header {
      h3 {
        font-size: 16px;
      }

      p {
        font-size: 13px;
      }
    }

    h4 {
      font-size: 14px;
    }
  }

  .tile-types {
    .tile-type-grid {
      grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
      gap: 6px;
    }

    .tile-type-btn {
      padding: 8px 6px;
      min-height: 70px;

      .tile-type-icon {
        font-size: 20px;
      }

      .tile-type-name {
        font-size: 11px;
      }

      .tile-type-count {
        font-size: 10px;
      }
    }
  }

  .tile-stats {
    padding: 12px;

    .stats-grid {
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 8px;
      margin-bottom: 12px;
    }

    .stat-item {
      padding: 6px;

      .stat-label {
        font-size: 11px;
      }

      .stat-value {
        font-size: 16px;
      }
    }

    .distribution-item {
      .distribution-label {
        font-size: 11px;
        min-width: 100px;
      }

      .distribution-bar {
        height: 16px;
      }
    }
  }

  .layout-tools {
    .tool-btn {
      padding: 6px 10px;
      font-size: 11px;
    }
  }

  .tile-properties {
    padding: 12px;

    .property-group {
      padding: 6px 0;

      label,
      .property-value {
        font-size: 13px;
      }
    }
  }

  .validation-warnings {
    .warning,
    .info {
      padding: 10px;
      font-size: 12px;
    }
  }
}

// Scrollbar styling
.tile-editor {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }
}

// Animation for tile type selection
.tile-type-btn.selected {
  animation: tileSelect 0.3s ease;
}

@keyframes tileSelect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

// Hover effects for distribution bars
.distribution-item:hover {
  .distribution-bar {
    .distribution-fill {
      opacity: 0.8;
    }
  }

  .distribution-label {
    color: #2c3e50;
    font-weight: 600;
  }
}
