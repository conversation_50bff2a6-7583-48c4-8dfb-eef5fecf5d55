import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';

export const EnvironmentEditor: React.FC = () => {
  const { currentMap } = useSelector((state: RootState) => state.editor);
  const [activeTab, setActiveTab] = useState<'lighting' | 'atmosphere' | 'effects'>('lighting');

  if (!currentMap) return null;

  const renderLightingTab = () => (
    <div className="lighting-controls">
      <div className="light-section">
        <h5>Ambient Light</h5>
        <div className="control-group">
          <label>Color:</label>
          <input 
            type="color" 
            defaultValue={currentMap.lighting.ambientLight.color}
          />
        </div>
        <div className="control-group">
          <label>Intensity:</label>
          <input 
            type="range" 
            min="0" 
            max="2" 
            step="0.1" 
            defaultValue={currentMap.lighting.ambientLight.intensity}
          />
        </div>
      </div>

      <div className="light-section">
        <h5>Directional Light (Sun)</h5>
        <div className="control-group">
          <label>Color:</label>
          <input 
            type="color" 
            defaultValue={currentMap.lighting.directionalLight.color}
          />
        </div>
        <div className="control-group">
          <label>Intensity:</label>
          <input 
            type="range" 
            min="0" 
            max="3" 
            step="0.1" 
            defaultValue={currentMap.lighting.directionalLight.intensity}
          />
        </div>
        <div className="control-group">
          <label>Cast Shadows:</label>
          <input 
            type="checkbox" 
            defaultChecked={currentMap.lighting.directionalLight.castShadow}
          />
        </div>
      </div>

      <div className="light-presets">
        <h5>Lighting Presets</h5>
        <div className="preset-buttons">
          <button className="preset-btn">☀️ Noon</button>
          <button className="preset-btn">🌅 Dawn</button>
          <button className="preset-btn">🌇 Dusk</button>
          <button className="preset-btn">🌙 Night</button>
        </div>
      </div>
    </div>
  );

  const renderAtmosphereTab = () => (
    <div className="atmosphere-controls">
      <div className="atmosphere-section">
        <h5>Sky</h5>
        <div className="control-group">
          <label>Sky Type:</label>
          <select defaultValue={currentMap.environment.skybox?.type || 'gradient'}>
            <option value="color">Solid Color</option>
            <option value="gradient">Gradient</option>
            <option value="hdri">HDRI</option>
          </select>
        </div>
        <div className="control-group">
          <label>Top Color:</label>
          <input 
            type="color" 
            defaultValue={currentMap.environment.skybox?.topColor || '#87CEEB'}
          />
        </div>
        <div className="control-group">
          <label>Bottom Color:</label>
          <input 
            type="color" 
            defaultValue={currentMap.environment.skybox?.bottomColor || '#F4A460'}
          />
        </div>
      </div>

      <div className="atmosphere-section">
        <h5>Fog</h5>
        <div className="control-group">
          <label>Enable Fog:</label>
          <input 
            type="checkbox" 
            defaultChecked={currentMap.environment.fog?.enabled || false}
          />
        </div>
        <div className="control-group">
          <label>Fog Color:</label>
          <input 
            type="color" 
            defaultValue={currentMap.environment.fog?.color || '#F4A460'}
          />
        </div>
        <div className="control-group">
          <label>Near Distance:</label>
          <input 
            type="number" 
            min="1" 
            max="100" 
            defaultValue={currentMap.environment.fog?.near || 50}
          />
        </div>
        <div className="control-group">
          <label>Far Distance:</label>
          <input 
            type="number" 
            min="50" 
            max="500" 
            defaultValue={currentMap.environment.fog?.far || 200}
          />
        </div>
      </div>
    </div>
  );

  const renderEffectsTab = () => (
    <div className="effects-controls">
      <div className="effects-section">
        <h5>Wind</h5>
        <div className="control-group">
          <label>Enable Wind:</label>
          <input 
            type="checkbox" 
            defaultChecked={currentMap.environment.wind?.enabled || false}
          />
        </div>
        <div className="control-group">
          <label>Wind Strength:</label>
          <input 
            type="range" 
            min="0" 
            max="2" 
            step="0.1" 
            defaultValue={currentMap.environment.wind?.strength || 1}
          />
        </div>
      </div>

      <div className="effects-section">
        <h5>Particles</h5>
        <div className="control-group">
          <label>Sand Particles:</label>
          <input type="checkbox" />
        </div>
        <div className="control-group">
          <label>Heat Shimmer:</label>
          <input type="checkbox" />
        </div>
      </div>

      <div className="effects-presets">
        <h5>Environment Presets</h5>
        <div className="preset-buttons">
          <button className="preset-btn">🏜️ Clear Desert</button>
          <button className="preset-btn">🌪️ Sandstorm</button>
          <button className="preset-btn">🌅 Mirage</button>
          <button className="preset-btn">🌙 Desert Night</button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="environment-editor">
      <div className="environment-editor__header">
        <h3>Lighting & Environment</h3>
        <p>Configure lighting, atmosphere, and environmental effects.</p>
      </div>

      <div className="environment-tabs">
        <button 
          className={`tab-btn ${activeTab === 'lighting' ? 'active' : ''}`}
          onClick={() => setActiveTab('lighting')}
        >
          💡 Lighting
        </button>
        <button 
          className={`tab-btn ${activeTab === 'atmosphere' ? 'active' : ''}`}
          onClick={() => setActiveTab('atmosphere')}
        >
          🌤️ Atmosphere
        </button>
        <button 
          className={`tab-btn ${activeTab === 'effects' ? 'active' : ''}`}
          onClick={() => setActiveTab('effects')}
        >
          ✨ Effects
        </button>
      </div>

      <div className="tab-content">
        {activeTab === 'lighting' && renderLightingTab()}
        {activeTab === 'atmosphere' && renderAtmosphereTab()}
        {activeTab === 'effects' && renderEffectsTab()}
      </div>
    </div>
  );
};
