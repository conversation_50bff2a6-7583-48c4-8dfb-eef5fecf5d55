{"version": 3, "sources": ["../../boardgame.io/dist/esm/util-991e76bb.js", "../../boardgame.io/dist/esm/master-17425f07.js", "../../boardgame.io/dist/esm/filter-player-view-43ed49b0.js", "../../engine.io-parser/build/esm/commons.js", "../../engine.io-parser/build/esm/encodePacket.browser.js", "../../engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../engine.io-parser/build/esm/decodePacket.browser.js", "../../engine.io-parser/build/esm/index.js", "../../@socket.io/component-emitter/index.mjs", "../../engine.io-client/build/esm/globalThis.browser.js", "../../engine.io-client/build/esm/util.js", "../../engine.io-client/build/esm/contrib/parseqs.js", "../../engine.io-client/build/esm/transport.js", "../../engine.io-client/build/esm/contrib/yeast.js", "../../engine.io-client/build/esm/contrib/has-cors.js", "../../engine.io-client/build/esm/transports/xmlhttprequest.browser.js", "../../engine.io-client/build/esm/transports/polling.js", "../../engine.io-client/build/esm/transports/websocket-constructor.browser.js", "../../engine.io-client/build/esm/transports/websocket.js", "../../engine.io-client/build/esm/transports/webtransport.js", "../../engine.io-client/build/esm/transports/index.js", "../../engine.io-client/build/esm/contrib/parseuri.js", "../../engine.io-client/build/esm/socket.js", "../../engine.io-client/build/esm/index.js", "../../socket.io-client/build/esm/url.js", "../../socket.io-parser/build/esm/index.js", "../../socket.io-parser/build/esm/is-binary.js", "../../socket.io-parser/build/esm/binary.js", "../../socket.io-client/build/esm/on.js", "../../socket.io-client/build/esm/socket.js", "../../socket.io-client/build/esm/contrib/backo2.js", "../../socket.io-client/build/esm/manager.js", "../../socket.io-client/build/esm/index.js", "../../boardgame.io/dist/esm/socketio-a82b84e4.js"], "sourcesContent": ["import { I as InitializeGame } from './initialize-7316768f.js';\n\nvar Type;\r\n(function (Type) {\r\n    Type[Type[\"SYNC\"] = 0] = \"SYNC\";\r\n    Type[Type[\"ASYNC\"] = 1] = \"ASYNC\";\r\n})(Type || (Type = {}));\r\n/**\r\n * Type guard that checks if a storage implementation is synchronous.\r\n */\r\nfunction isSynchronous(storageAPI) {\r\n    return storageAPI.type() === Type.SYNC;\r\n}\r\nclass Async {\r\n    /* istanbul ignore next */\r\n    type() {\r\n        /* istanbul ignore next */\r\n        return Type.ASYNC;\r\n    }\r\n    /**\r\n     * Create a new match.\r\n     *\r\n     * This might just need to call setState and setMetadata in\r\n     * most implementations.\r\n     *\r\n     * However, it exists as a separate call so that the\r\n     * implementation can provision things differently when\r\n     * a match is created.  For example, it might stow away the\r\n     * initial match state in a separate field for easier retrieval.\r\n     */\r\n    /* istanbul ignore next */\r\n    async createMatch(matchID, opts) {\r\n        if (this.createGame) {\r\n            console.warn('The database connector does not implement a createMatch method.', '\\nUsing the deprecated createGame method instead.');\r\n            return this.createGame(matchID, opts);\r\n        }\r\n        else {\r\n            console.error('The database connector does not implement a createMatch method.');\r\n        }\r\n    }\r\n    /**\r\n     * Return all matches.\r\n     */\r\n    /* istanbul ignore next */\r\n    async listMatches(opts) {\r\n        if (this.listGames) {\r\n            console.warn('The database connector does not implement a listMatches method.', '\\nUsing the deprecated listGames method instead.');\r\n            return this.listGames(opts);\r\n        }\r\n        else {\r\n            console.error('The database connector does not implement a listMatches method.');\r\n        }\r\n    }\r\n}\r\nclass Sync {\r\n    type() {\r\n        return Type.SYNC;\r\n    }\r\n    /**\r\n     * Connect.\r\n     */\r\n    connect() {\r\n        return;\r\n    }\r\n    /**\r\n     * Create a new match.\r\n     *\r\n     * This might just need to call setState and setMetadata in\r\n     * most implementations.\r\n     *\r\n     * However, it exists as a separate call so that the\r\n     * implementation can provision things differently when\r\n     * a match is created.  For example, it might stow away the\r\n     * initial match state in a separate field for easier retrieval.\r\n     */\r\n    /* istanbul ignore next */\r\n    createMatch(matchID, opts) {\r\n        if (this.createGame) {\r\n            console.warn('The database connector does not implement a createMatch method.', '\\nUsing the deprecated createGame method instead.');\r\n            return this.createGame(matchID, opts);\r\n        }\r\n        else {\r\n            console.error('The database connector does not implement a createMatch method.');\r\n        }\r\n    }\r\n    /**\r\n     * Return all matches.\r\n     */\r\n    /* istanbul ignore next */\r\n    listMatches(opts) {\r\n        if (this.listGames) {\r\n            console.warn('The database connector does not implement a listMatches method.', '\\nUsing the deprecated listGames method instead.');\r\n            return this.listGames(opts);\r\n        }\r\n        else {\r\n            console.error('The database connector does not implement a listMatches method.');\r\n        }\r\n    }\r\n}\n\n/**\r\n * Creates a new match metadata object.\r\n */\r\nconst createMetadata = ({ game, unlisted, setupData, numPlayers, }) => {\r\n    const metadata = {\r\n        gameName: game.name,\r\n        unlisted: !!unlisted,\r\n        players: {},\r\n        createdAt: Date.now(),\r\n        updatedAt: Date.now(),\r\n    };\r\n    if (setupData !== undefined)\r\n        metadata.setupData = setupData;\r\n    for (let playerIndex = 0; playerIndex < numPlayers; playerIndex++) {\r\n        metadata.players[playerIndex] = { id: playerIndex };\r\n    }\r\n    return metadata;\r\n};\r\n/**\r\n * Creates initial state and metadata for a new match.\r\n * If the provided `setupData` doesn’t pass the game’s validation,\r\n * an error object is returned instead.\r\n */\r\nconst createMatch = ({ game, numPlayers, setupData, unlisted, }) => {\r\n    if (!numPlayers || typeof numPlayers !== 'number')\r\n        numPlayers = 2;\r\n    const setupDataError = game.validateSetupData && game.validateSetupData(setupData, numPlayers);\r\n    if (setupDataError !== undefined)\r\n        return { setupDataError };\r\n    const metadata = createMetadata({ game, numPlayers, setupData, unlisted });\r\n    const initialState = InitializeGame({ game, numPlayers, setupData });\r\n    return { metadata, initialState };\r\n};\n\nexport { Async as A, Sync as S, createMatch as c, isSynchronous as i };\n", "import { applyMiddleware, createStore } from 'redux';\nimport { e as error, j as UNDO, R as REDO, M as MAKE_MOVE } from './turn-order-8cc4909b.js';\nimport { P as ProcessGameConfig, C as CreateGameReducer, T as TransientHandlingMiddleware, I as IsLongFormMove } from './reducer-24ea3e4c.js';\nimport { i as isSynchronous, c as createMatch } from './util-991e76bb.js';\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Filter match data to get a player metadata object with credentials stripped.\r\n */\r\nconst filterMatchData = (matchData) => Object.values(matchData.players).map((player) => {\r\n    const { credentials, ...filteredData } = player;\r\n    return filteredData;\r\n});\r\n/**\r\n * Remove player credentials from action payload\r\n */\r\nconst stripCredentialsFromAction = (action) => {\r\n    const { credentials, ...payload } = action.payload;\r\n    return { ...action, payload };\r\n};\r\n/**\r\n * Master\r\n *\r\n * Class that runs the game and maintains the authoritative state.\r\n * It uses the transportAPI to communicate with clients and the\r\n * storageAPI to communicate with the database.\r\n */\r\nclass Master {\r\n    constructor(game, storageAPI, transportAPI, auth) {\r\n        this.game = ProcessGameConfig(game);\r\n        this.storageAPI = storageAPI;\r\n        this.transportAPI = transportAPI;\r\n        this.subscribeCallback = () => { };\r\n        this.auth = auth;\r\n    }\r\n    subscribe(fn) {\r\n        this.subscribeCallback = fn;\r\n    }\r\n    /**\r\n     * Called on each move / event made by the client.\r\n     * Computes the new value of the game state and returns it\r\n     * along with a deltalog.\r\n     */\r\n    async onUpdate(credAction, stateID, matchID, playerID) {\r\n        if (!credAction || !credAction.payload) {\r\n            return { error: 'missing action or action payload' };\r\n        }\r\n        let metadata;\r\n        if (isSynchronous(this.storageAPI)) {\r\n            ({ metadata } = this.storageAPI.fetch(matchID, { metadata: true }));\r\n        }\r\n        else {\r\n            ({ metadata } = await this.storageAPI.fetch(matchID, { metadata: true }));\r\n        }\r\n        if (this.auth) {\r\n            const isAuthentic = await this.auth.authenticateCredentials({\r\n                playerID,\r\n                credentials: credAction.payload.credentials,\r\n                metadata,\r\n            });\r\n            if (!isAuthentic) {\r\n                return { error: 'unauthorized action' };\r\n            }\r\n        }\r\n        const action = stripCredentialsFromAction(credAction);\r\n        const key = matchID;\r\n        let state;\r\n        if (isSynchronous(this.storageAPI)) {\r\n            ({ state } = this.storageAPI.fetch(key, { state: true }));\r\n        }\r\n        else {\r\n            ({ state } = await this.storageAPI.fetch(key, { state: true }));\r\n        }\r\n        if (state === undefined) {\r\n            error(`game not found, matchID=[${key}]`);\r\n            return { error: 'game not found' };\r\n        }\r\n        if (state.ctx.gameover !== undefined) {\r\n            error(`game over - matchID=[${key}] - playerID=[${playerID}]` +\r\n                ` - action[${action.payload.type}]`);\r\n            return;\r\n        }\r\n        const reducer = CreateGameReducer({\r\n            game: this.game,\r\n        });\r\n        const middleware = applyMiddleware(TransientHandlingMiddleware);\r\n        const store = createStore(reducer, state, middleware);\r\n        // Only allow UNDO / REDO if there is exactly one player\r\n        // that can make moves right now and the person doing the\r\n        // action is that player.\r\n        if (action.type == UNDO || action.type == REDO) {\r\n            const hasActivePlayers = state.ctx.activePlayers !== null;\r\n            const isCurrentPlayer = state.ctx.currentPlayer === playerID;\r\n            if (\r\n            // If activePlayers is empty, non-current players can’t undo.\r\n            (!hasActivePlayers && !isCurrentPlayer) ||\r\n                // If player is not active or multiple players are active, can’t undo.\r\n                (hasActivePlayers &&\r\n                    (state.ctx.activePlayers[playerID] === undefined ||\r\n                        Object.keys(state.ctx.activePlayers).length > 1))) {\r\n                error(`playerID=[${playerID}] cannot undo / redo right now`);\r\n                return;\r\n            }\r\n        }\r\n        // Check whether the player is active.\r\n        if (!this.game.flow.isPlayerActive(state.G, state.ctx, playerID)) {\r\n            error(`player not active - playerID=[${playerID}]` +\r\n                ` - action[${action.payload.type}]`);\r\n            return;\r\n        }\r\n        // Get move for further checks\r\n        const move = action.type == MAKE_MOVE\r\n            ? this.game.flow.getMove(state.ctx, action.payload.type, playerID)\r\n            : null;\r\n        // Check whether the player is allowed to make the move.\r\n        if (action.type == MAKE_MOVE && !move) {\r\n            error(`move not processed - canPlayerMakeMove=false - playerID=[${playerID}]` +\r\n                ` - action[${action.payload.type}]`);\r\n            return;\r\n        }\r\n        // Check if action's stateID is different than store's stateID\r\n        // and if move does not have ignoreStaleStateID truthy.\r\n        if (state._stateID !== stateID &&\r\n            !(move && IsLongFormMove(move) && move.ignoreStaleStateID)) {\r\n            error(`invalid stateID, was=[${stateID}], expected=[${state._stateID}]` +\r\n                ` - playerID=[${playerID}] - action[${action.payload.type}]`);\r\n            return;\r\n        }\r\n        const prevState = store.getState();\r\n        // Update server's version of the store.\r\n        store.dispatch(action);\r\n        state = store.getState();\r\n        this.subscribeCallback({\r\n            state,\r\n            action,\r\n            matchID,\r\n        });\r\n        if (this.game.deltaState) {\r\n            this.transportAPI.sendAll({\r\n                type: 'patch',\r\n                args: [matchID, stateID, prevState, state],\r\n            });\r\n        }\r\n        else {\r\n            this.transportAPI.sendAll({\r\n                type: 'update',\r\n                args: [matchID, state],\r\n            });\r\n        }\r\n        const { deltalog, ...stateWithoutDeltalog } = state;\r\n        let newMetadata;\r\n        if (metadata &&\r\n            (metadata.gameover === undefined || metadata.gameover === null)) {\r\n            newMetadata = {\r\n                ...metadata,\r\n                updatedAt: Date.now(),\r\n            };\r\n            if (state.ctx.gameover !== undefined) {\r\n                newMetadata.gameover = state.ctx.gameover;\r\n            }\r\n        }\r\n        if (isSynchronous(this.storageAPI)) {\r\n            this.storageAPI.setState(key, stateWithoutDeltalog, deltalog);\r\n            if (newMetadata)\r\n                this.storageAPI.setMetadata(key, newMetadata);\r\n        }\r\n        else {\r\n            const writes = [\r\n                this.storageAPI.setState(key, stateWithoutDeltalog, deltalog),\r\n            ];\r\n            if (newMetadata) {\r\n                writes.push(this.storageAPI.setMetadata(key, newMetadata));\r\n            }\r\n            await Promise.all(writes);\r\n        }\r\n    }\r\n    /**\r\n     * Called when the client connects / reconnects.\r\n     * Returns the latest game state and the entire log.\r\n     */\r\n    async onSync(matchID, playerID, credentials, numPlayers = 2) {\r\n        const key = matchID;\r\n        const fetchOpts = {\r\n            state: true,\r\n            metadata: true,\r\n            log: true,\r\n            initialState: true,\r\n        };\r\n        const fetchResult = isSynchronous(this.storageAPI)\r\n            ? this.storageAPI.fetch(key, fetchOpts)\r\n            : await this.storageAPI.fetch(key, fetchOpts);\r\n        let { state, initialState, log, metadata } = fetchResult;\r\n        if (this.auth && playerID !== undefined && playerID !== null) {\r\n            const isAuthentic = await this.auth.authenticateCredentials({\r\n                playerID,\r\n                credentials,\r\n                metadata,\r\n            });\r\n            if (!isAuthentic) {\r\n                return { error: 'unauthorized' };\r\n            }\r\n        }\r\n        // If the game doesn't exist, then create one on demand.\r\n        // TODO: Move this out of the sync call.\r\n        if (state === undefined) {\r\n            const match = createMatch({\r\n                game: this.game,\r\n                unlisted: true,\r\n                numPlayers,\r\n                setupData: undefined,\r\n            });\r\n            if ('setupDataError' in match) {\r\n                return { error: 'game requires setupData' };\r\n            }\r\n            initialState = state = match.initialState;\r\n            metadata = match.metadata;\r\n            this.subscribeCallback({ state, matchID });\r\n            if (isSynchronous(this.storageAPI)) {\r\n                this.storageAPI.createMatch(key, { initialState, metadata });\r\n            }\r\n            else {\r\n                await this.storageAPI.createMatch(key, { initialState, metadata });\r\n            }\r\n        }\r\n        const filteredMetadata = metadata ? filterMatchData(metadata) : undefined;\r\n        const syncInfo = {\r\n            state,\r\n            log,\r\n            filteredMetadata,\r\n            initialState,\r\n        };\r\n        this.transportAPI.send({\r\n            playerID,\r\n            type: 'sync',\r\n            args: [matchID, syncInfo],\r\n        });\r\n        return;\r\n    }\r\n    /**\r\n     * Called when a client connects or disconnects.\r\n     * Updates and sends out metadata to reflect the player’s connection status.\r\n     */\r\n    async onConnectionChange(matchID, playerID, credentials, connected) {\r\n        const key = matchID;\r\n        // Ignore changes for clients without a playerID, e.g. spectators.\r\n        if (playerID === undefined || playerID === null) {\r\n            return;\r\n        }\r\n        let metadata;\r\n        if (isSynchronous(this.storageAPI)) {\r\n            ({ metadata } = this.storageAPI.fetch(key, { metadata: true }));\r\n        }\r\n        else {\r\n            ({ metadata } = await this.storageAPI.fetch(key, { metadata: true }));\r\n        }\r\n        if (metadata === undefined) {\r\n            error(`metadata not found for matchID=[${key}]`);\r\n            return { error: 'metadata not found' };\r\n        }\r\n        if (metadata.players[playerID] === undefined) {\r\n            error(`Player not in the match, matchID=[${key}] playerID=[${playerID}]`);\r\n            return { error: 'player not in the match' };\r\n        }\r\n        if (this.auth) {\r\n            const isAuthentic = await this.auth.authenticateCredentials({\r\n                playerID,\r\n                credentials,\r\n                metadata,\r\n            });\r\n            if (!isAuthentic) {\r\n                return { error: 'unauthorized' };\r\n            }\r\n        }\r\n        metadata.players[playerID].isConnected = connected;\r\n        const filteredMetadata = filterMatchData(metadata);\r\n        this.transportAPI.sendAll({\r\n            type: 'matchData',\r\n            args: [matchID, filteredMetadata],\r\n        });\r\n        if (isSynchronous(this.storageAPI)) {\r\n            this.storageAPI.setMetadata(key, metadata);\r\n        }\r\n        else {\r\n            await this.storageAPI.setMetadata(key, metadata);\r\n        }\r\n    }\r\n    async onChatMessage(matchID, chatMessage, credentials) {\r\n        const key = matchID;\r\n        if (this.auth) {\r\n            const { metadata } = await this.storageAPI.fetch(key, {\r\n                metadata: true,\r\n            });\r\n            if (!(chatMessage && typeof chatMessage.sender === 'string')) {\r\n                return { error: 'unauthorized' };\r\n            }\r\n            const isAuthentic = await this.auth.authenticateCredentials({\r\n                playerID: chatMessage.sender,\r\n                credentials,\r\n                metadata,\r\n            });\r\n            if (!isAuthentic) {\r\n                return { error: 'unauthorized' };\r\n            }\r\n        }\r\n        this.transportAPI.sendAll({\r\n            type: 'chat',\r\n            args: [matchID, chatMessage],\r\n        });\r\n    }\r\n}\n\nexport { Master as M };\n", "import { x as PlayerView } from './turn-order-8cc4909b.js';\nimport { createPatch } from 'rfc6902';\n\nconst applyPlayerView = (game, playerID, state) => ({\r\n    ...state,\r\n    G: game.playerView({ G: state.G, ctx: state.ctx, playerID }),\r\n    plugins: PlayerView(state, { playerID, game }),\r\n    deltalog: undefined,\r\n    _undo: [],\r\n    _redo: [],\r\n});\r\n/** Gets a function that filters the TransportData for a given player and game. */\r\nconst getFilterPlayerView = (game) => (playerID, payload) => {\r\n    switch (payload.type) {\r\n        case 'patch': {\r\n            const [matchID, stateID, prevState, state] = payload.args;\r\n            const log = redactLog(state.deltalog, playerID);\r\n            const filteredState = applyPlayerView(game, playerID, state);\r\n            const newStateID = state._stateID;\r\n            const prevFilteredState = applyPlayerView(game, playerID, prevState);\r\n            const patch = createPatch(prevFilteredState, filteredState);\r\n            return {\r\n                type: 'patch',\r\n                args: [matchID, stateID, newStateID, patch, log],\r\n            };\r\n        }\r\n        case 'update': {\r\n            const [matchID, state] = payload.args;\r\n            const log = redactLog(state.deltalog, playerID);\r\n            const filteredState = applyPlayerView(game, playerID, state);\r\n            return {\r\n                type: 'update',\r\n                args: [matchID, filteredState, log],\r\n            };\r\n        }\r\n        case 'sync': {\r\n            const [matchID, syncInfo] = payload.args;\r\n            const filteredState = applyPlayerView(game, playerID, syncInfo.state);\r\n            const log = redactLog(syncInfo.log, playerID);\r\n            const newSyncInfo = {\r\n                ...syncInfo,\r\n                state: filteredState,\r\n                log,\r\n            };\r\n            return {\r\n                type: 'sync',\r\n                args: [matchID, newSyncInfo],\r\n            };\r\n        }\r\n        default: {\r\n            return payload;\r\n        }\r\n    }\r\n};\r\n/**\r\n * Redact the log.\r\n *\r\n * @param {Array} log - The game log (or deltalog).\r\n * @param {String} playerID - The playerID that this log is\r\n *                            to be sent to.\r\n */\r\nfunction redactLog(log, playerID) {\r\n    if (log === undefined) {\r\n        return log;\r\n    }\r\n    return log.map((logEvent) => {\r\n        // filter for all other players and spectators.\r\n        if (playerID !== null && +playerID === +logEvent.action.payload.playerID) {\r\n            return logEvent;\r\n        }\r\n        if (logEvent.redact !== true) {\r\n            return logEvent;\r\n        }\r\n        const payload = {\r\n            ...logEvent.action.payload,\r\n            args: null,\r\n        };\r\n        const filteredEvent = {\r\n            ...logEvent,\r\n            action: { ...logEvent.action, payload },\r\n        };\r\n        const { redact, ...remaining } = filteredEvent;\r\n        return remaining;\r\n    });\r\n}\n\nexport { getFilterPlayerView as g };\n", "const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    // @ts-expect-error\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    // @ts-expect-error\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else if (state === 2 /* READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\n", "import { globalThisShim as globalThis } from \"./globalThis.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/unshiftio/yeast\n'use strict';\nconst alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''), length = 64, map = {};\nlet seed = 0, i = 0, prev;\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nexport function encode(num) {\n    let encoded = '';\n    do {\n        encoded = alphabet[num % length] + encoded;\n        num = Math.floor(num / length);\n    } while (num > 0);\n    return encoded;\n}\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nexport function decode(str) {\n    let decoded = 0;\n    for (i = 0; i < str.length; i++) {\n        decoded = decoded * length + map[str.charAt(i)];\n    }\n    return decoded;\n}\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nexport function yeast() {\n    const now = encode(+new Date());\n    if (now !== prev)\n        return seed = 0, prev = now;\n    return now + '.' + encode(seed++);\n}\n//\n// Map each character to its index.\n//\nfor (; i < length; i++)\n    map[alphabet[i]] = i;\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "// browser shim for xmlhttprequest module\nimport { hasCORS } from \"../contrib/has-cors.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nexport function XHR(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\nexport function createCookieJar() { }\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nimport { createCookieJar, XHR as XMLHttpRequest, } from \"./xmlhttprequest.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globalThis.js\";\nfunction empty() { }\nconst hasXHR2 = (function () {\n    const xhr = new XMLHttpRequest({\n        xdomain: false,\n    });\n    return null != xhr.responseType;\n})();\nexport class Polling extends Transport {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        this.polling = false;\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n        /**\n         * XHR supports binary\n         */\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n        if (this.opts.withCredentials) {\n            this.cookieJar = createCookieJar();\n        }\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this.poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this.polling || !this.writable) {\n            let total = 0;\n            if (this.polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    poll() {\n        this.polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this.polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this.poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Creates a request.\n     *\n     * @param {String} method\n     * @private\n     */\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd, cookieJar: this.cookieJar }, this.opts);\n        return new Request(this.uri(), opts);\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(uri, opts) {\n        super();\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.method = opts.method || \"GET\";\n        this.uri = uri;\n        this.data = undefined !== opts.data ? opts.data : null;\n        this.create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    create() {\n        var _a;\n        const opts = pick(this.opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this.opts.xd;\n        const xhr = (this.xhr = new XMLHttpRequest(opts));\n        try {\n            xhr.open(this.method, this.uri, true);\n            try {\n                if (this.opts.extraHeaders) {\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this.opts.extraHeaders) {\n                        if (this.opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this.opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this.method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this.opts.withCredentials;\n            }\n            if (this.opts.requestTimeout) {\n                xhr.timeout = this.opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this.opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(xhr);\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this.onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this.onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this.data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this.onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this.index = Request.requestsCount++;\n            Request.requests[this.index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    onError(err) {\n        this.emitReserved(\"error\", err, this.xhr);\n        this.cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    cleanup(fromError) {\n        if (\"undefined\" === typeof this.xhr || null === this.xhr) {\n            return;\n        }\n        this.xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this.xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this.index];\n        }\n        this.xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    onLoad() {\n        const data = this.xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this.cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this.cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\n", "import { globalThisShim as globalThis } from \"../globalThis.js\";\nexport const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const WebSocket = globalThis.WebSocket || globalThis.MozWebSocket;\nexport const usingBrowserWebSocket = true;\nexport const defaultBinaryType = \"arraybuffer\";\n", "import { Transport } from \"../transport.js\";\nimport { yeast } from \"../contrib/yeast.js\";\nimport { pick } from \"../util.js\";\nimport { nextTick, usingBrowserWebSocket, WebSocket, } from \"./websocket-constructor.js\";\nimport { encodePacket } from \"engine.io-parser\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class WS extends Transport {\n    /**\n     * WebSocket transport constructor.\n     *\n     * @param {Object} opts - connection options\n     * @protected\n     */\n    constructor(opts) {\n        super(opts);\n        this.supportsBinary = !opts.forceBase64;\n    }\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        if (!this.check()) {\n            // let probe timeout\n            return;\n        }\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws =\n                usingBrowserWebSocket && !isReactNative\n                    ? protocols\n                        ? new WebSocket(uri, protocols)\n                        : new WebSocket(uri)\n                    : new WebSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // always create a new object (GH-437)\n                const opts = {};\n                if (!usingBrowserWebSocket) {\n                    if (packet.options) {\n                        opts.compress = packet.options.compress;\n                    }\n                    if (this.opts.perMessageDeflate) {\n                        const len = \n                        // @ts-ignore\n                        \"string\" === typeof data ? Buffer.byteLength(data) : data.length;\n                        if (len < this.opts.perMessageDeflate.threshold) {\n                            opts.compress = false;\n                        }\n                    }\n                }\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    if (usingBrowserWebSocket) {\n                        // TypeError is thrown when passing the second argument on Safari\n                        this.ws.send(data);\n                    }\n                    else {\n                        this.ws.send(data, opts);\n                    }\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = yeast();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n    /**\n     * Feature detection for WebSocket.\n     *\n     * @return {Boolean} whether this transport is available.\n     * @private\n     */\n    check() {\n        return !!WebSocket;\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"./websocket-constructor.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        // @ts-ignore\n        if (typeof WebTransport !== \"function\") {\n            return;\n        }\n        // @ts-ignore\n        this.transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        this.transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this.transport.ready.then(() => {\n            this.transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this.writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this.writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this.writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this.transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { Polling } from \"./polling.js\";\nimport { WS } from \"./websocket.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: Polling,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 2000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { defaultBinaryType } from \"./transports/websocket-constructor.js\";\nexport class Socket extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts = {}) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            uri = parse(uri);\n            opts.hostname = uri.host;\n            opts.secure = uri.protocol === \"https\" || uri.protocol === \"wss\";\n            opts.port = uri.port;\n            if (uri.query)\n                opts.query = uri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = opts.transports || [\n            \"polling\",\n            \"websocket\",\n            \"webtransport\",\n        ];\n        this.writeBuffer = [];\n        this.prevBufferLen = 0;\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        // set on handshake\n        this.id = null;\n        this.upgrades = null;\n        this.pingInterval = null;\n        this.pingTimeout = null;\n        // set on heartbeat\n        this.pingTimeoutTimer = null;\n        if (typeof addEventListener === \"function\") {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this.beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this.offlineEventListener = () => {\n                    this.onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                addEventListener(\"offline\", this.offlineEventListener, false);\n            }\n        }\n        this.open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new transports[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    open() {\n        let transport;\n        if (this.opts.rememberUpgrade &&\n            Socket.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1) {\n            transport = \"websocket\";\n        }\n        else if (0 === this.transports.length) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        else {\n            transport = this.transports[0];\n        }\n        this.readyState = \"opening\";\n        // Retry with the next transport if the transport is disabled (jsonp: false)\n        try {\n            transport = this.createTransport(transport);\n        }\n        catch (e) {\n            this.transports.shift();\n            this.open();\n            return;\n        }\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this.onDrain.bind(this))\n            .on(\"packet\", this.onPacket.bind(this))\n            .on(\"error\", this.onError.bind(this))\n            .on(\"close\", (reason) => this.onClose(\"transport close\", reason));\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        Socket.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    Socket.priorWebsocketSuccess = \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this.upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        Socket.priorWebsocketSuccess = \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n        // we check for `readyState` in case an `open`\n        // listener already closed the socket\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            let i = 0;\n            const l = this.upgrades.length;\n            for (; i < l; i++) {\n                this.probe(this.upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            this.resetPingTimeout();\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this.sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this.onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this.upgrades = this.filterUpgrades(data.upgrades);\n        this.pingInterval = data.pingInterval;\n        this.pingTimeout = data.pingTimeout;\n        this.maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this.resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    resetPingTimeout() {\n        this.clearTimeoutFn(this.pingTimeoutTimer);\n        this.pingTimeoutTimer = this.setTimeoutFn(() => {\n            this.onClose(\"ping timeout\");\n        }, this.pingInterval + this.pingTimeout);\n        if (this.opts.autoUnref) {\n            this.pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    onDrain() {\n        this.writeBuffer.splice(0, this.prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this.prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this.getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this.prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    getWritablePackets() {\n        const shouldCheckPayloadSize = this.maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this.maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    send(msg, options, fn) {\n        this.sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this.onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    onError(err) {\n        Socket.priorWebsocketSuccess = false;\n        this.emitReserved(\"error\", err);\n        this.onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this.pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (typeof removeEventListener === \"function\") {\n                removeEventListener(\"beforeunload\", this.beforeunloadEventListener, false);\n                removeEventListener(\"offline\", this.offlineEventListener, false);\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this.prevBufferLen = 0;\n        }\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        let i = 0;\n        const j = upgrades.length;\n        for (; i < j; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\nSocket.protocol = protocol;\n", "import { Socket } from \"./socket.js\";\nexport { Socket };\nexport const protocol = Socket.protocol;\nexport { Transport, TransportError } from \"./transport.js\";\nexport { transports } from \"./transports/index.js\";\nexport { installTimerFunctions } from \"./util.js\";\nexport { parse } from \"./contrib/parseuri.js\";\nexport { nextTick } from \"./transports/websocket-constructor.js\";\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\",\n    \"connect_error\",\n    \"disconnect\",\n    \"disconnecting\",\n    \"newListener\",\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = this.io.engine &&\n            this.io.engine.transport &&\n            this.io.engine.transport.writable;\n        const discardPacket = this.flags.volatile && (!isTransportWritable || !this.connected);\n        if (discardPacket) {\n        }\n        else if (this.connected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        this.acks[id] = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, [null, ...args]);\n        };\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        // the timeout flag is optional\n        const withErr = this.flags.timeout !== undefined || this._opts.ackTimeout !== undefined;\n        return new Promise((resolve, reject) => {\n            args.push((arg1, arg2) => {\n                if (withErr) {\n                    return arg1 ? reject(arg1) : resolve(arg2);\n                }\n                else {\n                    return resolve(arg1);\n                }\n            });\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowlegement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (\"function\" === typeof ack) {\n            ack.apply(this, packet.data);\n            delete this.acks[packet.id];\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n        if (this.engine)\n            this.engine.close();\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called upon engine close.\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        this.cleanup();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\n", "import { T as Transport } from './transport-ce07b771.js';\nimport { S as Sync } from './util-991e76bb.js';\nimport { M as Master } from './master-17425f07.js';\nimport { g as getFilterPlayerView } from './filter-player-view-43ed49b0.js';\nimport ioNamespace__default from 'socket.io-client';\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * InMemory data storage.\r\n */\r\nclass InMemory extends Sync {\r\n    /**\r\n     * Creates a new InMemory storage.\r\n     */\r\n    constructor() {\r\n        super();\r\n        this.state = new Map();\r\n        this.initial = new Map();\r\n        this.metadata = new Map();\r\n        this.log = new Map();\r\n    }\r\n    /**\r\n     * Create a new match.\r\n     *\r\n     * @override\r\n     */\r\n    createMatch(matchID, opts) {\r\n        this.initial.set(matchID, opts.initialState);\r\n        this.setState(matchID, opts.initialState);\r\n        this.setMetadata(matchID, opts.metadata);\r\n    }\r\n    /**\r\n     * Write the match metadata to the in-memory object.\r\n     */\r\n    setMetadata(matchID, metadata) {\r\n        this.metadata.set(matchID, metadata);\r\n    }\r\n    /**\r\n     * Write the match state to the in-memory object.\r\n     */\r\n    setState(matchID, state, deltalog) {\r\n        if (deltalog && deltalog.length > 0) {\r\n            const log = this.log.get(matchID) || [];\r\n            this.log.set(matchID, [...log, ...deltalog]);\r\n        }\r\n        this.state.set(matchID, state);\r\n    }\r\n    /**\r\n     * Fetches state for a particular matchID.\r\n     */\r\n    fetch(matchID, opts) {\r\n        const result = {};\r\n        if (opts.state) {\r\n            result.state = this.state.get(matchID);\r\n        }\r\n        if (opts.metadata) {\r\n            result.metadata = this.metadata.get(matchID);\r\n        }\r\n        if (opts.log) {\r\n            result.log = this.log.get(matchID) || [];\r\n        }\r\n        if (opts.initialState) {\r\n            result.initialState = this.initial.get(matchID);\r\n        }\r\n        return result;\r\n    }\r\n    /**\r\n     * Remove the match state from the in-memory object.\r\n     */\r\n    wipe(matchID) {\r\n        this.state.delete(matchID);\r\n        this.metadata.delete(matchID);\r\n    }\r\n    /**\r\n     * Return all keys.\r\n     *\r\n     * @override\r\n     */\r\n    listMatches(opts) {\r\n        return [...this.metadata.entries()]\r\n            .filter(([, metadata]) => {\r\n            if (!opts) {\r\n                return true;\r\n            }\r\n            if (opts.gameName !== undefined &&\r\n                metadata.gameName !== opts.gameName) {\r\n                return false;\r\n            }\r\n            if (opts.where !== undefined) {\r\n                if (opts.where.isGameover !== undefined) {\r\n                    const isGameover = metadata.gameover !== undefined;\r\n                    if (isGameover !== opts.where.isGameover) {\r\n                        return false;\r\n                    }\r\n                }\r\n                if (opts.where.updatedBefore !== undefined &&\r\n                    metadata.updatedAt >= opts.where.updatedBefore) {\r\n                    return false;\r\n                }\r\n                if (opts.where.updatedAfter !== undefined &&\r\n                    metadata.updatedAt <= opts.where.updatedAfter) {\r\n                    return false;\r\n                }\r\n            }\r\n            return true;\r\n        })\r\n            .map(([key]) => key);\r\n    }\r\n}\n\nclass WithLocalStorageMap extends Map {\r\n    constructor(key) {\r\n        super();\r\n        this.key = key;\r\n        const cache = JSON.parse(localStorage.getItem(this.key)) || [];\r\n        cache.forEach((entry) => this.set(...entry));\r\n    }\r\n    sync() {\r\n        const entries = [...this.entries()];\r\n        localStorage.setItem(this.key, JSON.stringify(entries));\r\n    }\r\n    set(key, value) {\r\n        super.set(key, value);\r\n        this.sync();\r\n        return this;\r\n    }\r\n    delete(key) {\r\n        const result = super.delete(key);\r\n        this.sync();\r\n        return result;\r\n    }\r\n}\r\n/**\r\n * locaStorage data storage.\r\n */\r\nclass LocalStorage extends InMemory {\r\n    constructor(storagePrefix = 'bgio') {\r\n        super();\r\n        const StorageMap = (stateKey) => new WithLocalStorageMap(`${storagePrefix}_${stateKey}`);\r\n        this.state = StorageMap('state');\r\n        this.initial = StorageMap('initial');\r\n        this.metadata = StorageMap('metadata');\r\n        this.log = StorageMap('log');\r\n    }\r\n}\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Returns null if it is not a bot's turn.\r\n * Otherwise, returns a playerID of a bot that may play now.\r\n */\r\nfunction GetBotPlayer(state, bots) {\r\n    if (state.ctx.gameover !== undefined) {\r\n        return null;\r\n    }\r\n    if (state.ctx.activePlayers) {\r\n        for (const key of Object.keys(bots)) {\r\n            if (key in state.ctx.activePlayers) {\r\n                return key;\r\n            }\r\n        }\r\n    }\r\n    else if (state.ctx.currentPlayer in bots) {\r\n        return state.ctx.currentPlayer;\r\n    }\r\n    return null;\r\n}\r\n/**\r\n * Creates a local version of the master that the client\r\n * can interact with.\r\n */\r\nclass LocalMaster extends Master {\r\n    constructor({ game, bots, storageKey, persist }) {\r\n        const clientCallbacks = {};\r\n        const initializedBots = {};\r\n        if (game && game.ai && bots) {\r\n            for (const playerID in bots) {\r\n                const bot = bots[playerID];\r\n                initializedBots[playerID] = new bot({\r\n                    game,\r\n                    enumerate: game.ai.enumerate,\r\n                    seed: game.seed,\r\n                });\r\n            }\r\n        }\r\n        const send = ({ playerID, ...data }) => {\r\n            const callback = clientCallbacks[playerID];\r\n            if (callback !== undefined) {\r\n                callback(filterPlayerView(playerID, data));\r\n            }\r\n        };\r\n        const filterPlayerView = getFilterPlayerView(game);\r\n        const transportAPI = {\r\n            send,\r\n            sendAll: (payload) => {\r\n                for (const playerID in clientCallbacks) {\r\n                    send({ playerID, ...payload });\r\n                }\r\n            },\r\n        };\r\n        const storage = persist ? new LocalStorage(storageKey) : new InMemory();\r\n        super(game, storage, transportAPI);\r\n        this.connect = (playerID, callback) => {\r\n            clientCallbacks[playerID] = callback;\r\n        };\r\n        this.subscribe(({ state, matchID }) => {\r\n            if (!bots) {\r\n                return;\r\n            }\r\n            const botPlayer = GetBotPlayer(state, initializedBots);\r\n            if (botPlayer !== null) {\r\n                setTimeout(async () => {\r\n                    const botAction = await initializedBots[botPlayer].play(state, botPlayer);\r\n                    await this.onUpdate(botAction.action, state._stateID, matchID, botAction.action.payload.playerID);\r\n                }, 100);\r\n            }\r\n        });\r\n    }\r\n}\r\n/**\r\n * Local\r\n *\r\n * Transport interface that embeds a GameMaster within it\r\n * that you can connect multiple clients to.\r\n */\r\nclass LocalTransport extends Transport {\r\n    /**\r\n     * Creates a new Mutiplayer instance.\r\n     * @param {string} matchID - The game ID to connect to.\r\n     * @param {string} playerID - The player ID associated with this client.\r\n     * @param {string} gameName - The game type (the `name` field in `Game`).\r\n     * @param {string} numPlayers - The number of players.\r\n     */\r\n    constructor({ master, ...opts }) {\r\n        super(opts);\r\n        this.master = master;\r\n    }\r\n    sendChatMessage(matchID, chatMessage) {\r\n        const args = [\r\n            matchID,\r\n            chatMessage,\r\n            this.credentials,\r\n        ];\r\n        this.master.onChatMessage(...args);\r\n    }\r\n    sendAction(state, action) {\r\n        this.master.onUpdate(action, state._stateID, this.matchID, this.playerID);\r\n    }\r\n    requestSync() {\r\n        this.master.onSync(this.matchID, this.playerID, this.credentials, this.numPlayers);\r\n    }\r\n    connect() {\r\n        this.setConnectionStatus(true);\r\n        this.master.connect(this.playerID, (data) => this.notifyClient(data));\r\n        this.requestSync();\r\n    }\r\n    disconnect() {\r\n        this.setConnectionStatus(false);\r\n    }\r\n    updateMatchID(id) {\r\n        this.matchID = id;\r\n        this.connect();\r\n    }\r\n    updatePlayerID(id) {\r\n        this.playerID = id;\r\n        this.connect();\r\n    }\r\n    updateCredentials(credentials) {\r\n        this.credentials = credentials;\r\n        this.connect();\r\n    }\r\n}\r\n/**\r\n * Global map storing local master instances.\r\n */\r\nconst localMasters = new Map();\r\n/**\r\n * Create a local transport.\r\n */\r\nfunction Local({ bots, persist, storageKey } = {}) {\r\n    return (transportOpts) => {\r\n        const { gameKey, game } = transportOpts;\r\n        let master;\r\n        const instance = localMasters.get(gameKey);\r\n        if (instance &&\r\n            instance.bots === bots &&\r\n            instance.storageKey === storageKey &&\r\n            instance.persist === persist) {\r\n            master = instance.master;\r\n        }\r\n        if (!master) {\r\n            master = new LocalMaster({ game, bots, persist, storageKey });\r\n            localMasters.set(gameKey, { master, bots, persist, storageKey });\r\n        }\r\n        return new LocalTransport({ master, ...transportOpts });\r\n    };\r\n}\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nconst io = ioNamespace__default;\r\n/**\r\n * SocketIO\r\n *\r\n * Transport interface that interacts with the Master via socket.io.\r\n */\r\nclass SocketIOTransport extends Transport {\r\n    /**\r\n     * Creates a new Multiplayer instance.\r\n     * @param {object} socket - Override for unit tests.\r\n     * @param {object} socketOpts - Options to pass to socket.io.\r\n     * @param {object} store - Redux store\r\n     * @param {string} matchID - The game ID to connect to.\r\n     * @param {string} playerID - The player ID associated with this client.\r\n     * @param {string} credentials - Authentication credentials\r\n     * @param {string} gameName - The game type (the `name` field in `Game`).\r\n     * @param {string} numPlayers - The number of players.\r\n     * @param {string} server - The game server in the form of 'hostname:port'. Defaults to the server serving the client if not provided.\r\n     */\r\n    constructor({ socket, socketOpts, server, ...opts }) {\r\n        super(opts);\r\n        this.server = server;\r\n        this.socket = socket;\r\n        this.socketOpts = socketOpts;\r\n    }\r\n    sendAction(state, action) {\r\n        const args = [\r\n            action,\r\n            state._stateID,\r\n            this.matchID,\r\n            this.playerID,\r\n        ];\r\n        this.socket.emit('update', ...args);\r\n    }\r\n    sendChatMessage(matchID, chatMessage) {\r\n        const args = [\r\n            matchID,\r\n            chatMessage,\r\n            this.credentials,\r\n        ];\r\n        this.socket.emit('chat', ...args);\r\n    }\r\n    connect() {\r\n        if (!this.socket) {\r\n            if (this.server) {\r\n                let server = this.server;\r\n                if (server.search(/^https?:\\/\\//) == -1) {\r\n                    server = 'http://' + this.server;\r\n                }\r\n                if (server.slice(-1) != '/') {\r\n                    // add trailing slash if not already present\r\n                    server = server + '/';\r\n                }\r\n                this.socket = io(server + this.gameName, this.socketOpts);\r\n            }\r\n            else {\r\n                this.socket = io('/' + this.gameName, this.socketOpts);\r\n            }\r\n        }\r\n        // Called when another player makes a move and the\r\n        // master broadcasts the update as a patch to other clients (including\r\n        // this one).\r\n        this.socket.on('patch', (matchID, prevStateID, stateID, patch, deltalog) => {\r\n            this.notifyClient({\r\n                type: 'patch',\r\n                args: [matchID, prevStateID, stateID, patch, deltalog],\r\n            });\r\n        });\r\n        // Called when another player makes a move and the\r\n        // master broadcasts the update to other clients (including\r\n        // this one).\r\n        this.socket.on('update', (matchID, state, deltalog) => {\r\n            this.notifyClient({\r\n                type: 'update',\r\n                args: [matchID, state, deltalog],\r\n            });\r\n        });\r\n        // Called when the client first connects to the master\r\n        // and requests the current game state.\r\n        this.socket.on('sync', (matchID, syncInfo) => {\r\n            this.notifyClient({ type: 'sync', args: [matchID, syncInfo] });\r\n        });\r\n        // Called when new player joins the match or changes\r\n        // it's connection status\r\n        this.socket.on('matchData', (matchID, matchData) => {\r\n            this.notifyClient({ type: 'matchData', args: [matchID, matchData] });\r\n        });\r\n        this.socket.on('chat', (matchID, chatMessage) => {\r\n            this.notifyClient({ type: 'chat', args: [matchID, chatMessage] });\r\n        });\r\n        // Keep track of connection status.\r\n        this.socket.on('connect', () => {\r\n            // Initial sync to get game state.\r\n            this.requestSync();\r\n            this.setConnectionStatus(true);\r\n        });\r\n        this.socket.on('disconnect', () => {\r\n            this.setConnectionStatus(false);\r\n        });\r\n    }\r\n    disconnect() {\r\n        this.socket.close();\r\n        this.socket = null;\r\n        this.setConnectionStatus(false);\r\n    }\r\n    requestSync() {\r\n        if (this.socket) {\r\n            const args = [\r\n                this.matchID,\r\n                this.playerID,\r\n                this.credentials,\r\n                this.numPlayers,\r\n            ];\r\n            this.socket.emit('sync', ...args);\r\n        }\r\n    }\r\n    updateMatchID(id) {\r\n        this.matchID = id;\r\n        this.requestSync();\r\n    }\r\n    updatePlayerID(id) {\r\n        this.playerID = id;\r\n        this.requestSync();\r\n    }\r\n    updateCredentials(credentials) {\r\n        this.credentials = credentials;\r\n        this.requestSync();\r\n    }\r\n}\r\nfunction SocketIO({ server, socketOpts } = {}) {\r\n    return (transportOpts) => new SocketIOTransport({\r\n        server,\r\n        socketOpts,\r\n        ...transportOpts,\r\n    });\r\n}\n\nexport { Local as L, SocketIO as S };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI;AAAA,CACH,SAAUA,OAAM;AACb,EAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AACzB,EAAAA,MAAKA,MAAK,OAAO,IAAI,CAAC,IAAI;AAC9B,GAAG,SAAS,OAAO,CAAC,EAAE;AAItB,SAAS,cAAc,YAAY;AAC/B,SAAO,WAAW,KAAK,MAAM,KAAK;AACtC;AA0CA,IAAM,OAAN,MAAW;AAAA,EACP,OAAO;AACH,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,YAAY,SAAS,MAAM;AACvB,QAAI,KAAK,YAAY;AACjB,cAAQ,KAAK,mEAAmE,mDAAmD;AACnI,aAAO,KAAK,WAAW,SAAS,IAAI;AAAA,IACxC,OACK;AACD,cAAQ,MAAM,iEAAiE;AAAA,IACnF;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM;AACd,QAAI,KAAK,WAAW;AAChB,cAAQ,KAAK,mEAAmE,kDAAkD;AAClI,aAAO,KAAK,UAAU,IAAI;AAAA,IAC9B,OACK;AACD,cAAQ,MAAM,iEAAiE;AAAA,IACnF;AAAA,EACJ;AACJ;AAKA,IAAM,iBAAiB,CAAC,EAAE,MAAM,UAAU,WAAW,WAAY,MAAM;AACnE,QAAM,WAAW;AAAA,IACb,UAAU,KAAK;AAAA,IACf,UAAU,CAAC,CAAC;AAAA,IACZ,SAAS,CAAC;AAAA,IACV,WAAW,KAAK,IAAI;AAAA,IACpB,WAAW,KAAK,IAAI;AAAA,EACxB;AACA,MAAI,cAAc;AACd,aAAS,YAAY;AACzB,WAAS,cAAc,GAAG,cAAc,YAAY,eAAe;AAC/D,aAAS,QAAQ,WAAW,IAAI,EAAE,IAAI,YAAY;AAAA,EACtD;AACA,SAAO;AACX;AAMA,IAAM,cAAc,CAAC,EAAE,MAAM,YAAY,WAAW,SAAU,MAAM;AAChE,MAAI,CAAC,cAAc,OAAO,eAAe;AACrC,iBAAa;AACjB,QAAM,iBAAiB,KAAK,qBAAqB,KAAK,kBAAkB,WAAW,UAAU;AAC7F,MAAI,mBAAmB;AACnB,WAAO,EAAE,eAAe;AAC5B,QAAM,WAAW,eAAe,EAAE,MAAM,YAAY,WAAW,SAAS,CAAC;AACzE,QAAM,eAAe,eAAe,EAAE,MAAM,YAAY,UAAU,CAAC;AACnE,SAAO,EAAE,UAAU,aAAa;AACpC;;;ACrHA,IAAM,kBAAkB,CAAC,cAAc,OAAO,OAAO,UAAU,OAAO,EAAE,IAAI,CAAC,WAAW;AACpF,QAAM,EAAE,aAAa,GAAG,aAAa,IAAI;AACzC,SAAO;AACX,CAAC;AAID,IAAM,6BAA6B,CAAC,WAAW;AAC3C,QAAM,EAAE,aAAa,GAAG,QAAQ,IAAI,OAAO;AAC3C,SAAO,EAAE,GAAG,QAAQ,QAAQ;AAChC;AAQA,IAAM,SAAN,MAAa;AAAA,EACT,YAAY,MAAM,YAAY,cAAc,MAAM;AAC9C,SAAK,OAAO,kBAAkB,IAAI;AAClC,SAAK,aAAa;AAClB,SAAK,eAAe;AACpB,SAAK,oBAAoB,MAAM;AAAA,IAAE;AACjC,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,UAAU,IAAI;AACV,SAAK,oBAAoB;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAS,YAAY,SAAS,SAAS,UAAU;AACnD,QAAI,CAAC,cAAc,CAAC,WAAW,SAAS;AACpC,aAAO,EAAE,OAAO,mCAAmC;AAAA,IACvD;AACA,QAAI;AACJ,QAAI,cAAc,KAAK,UAAU,GAAG;AAChC,OAAC,EAAE,SAAS,IAAI,KAAK,WAAW,MAAM,SAAS,EAAE,UAAU,KAAK,CAAC;AAAA,IACrE,OACK;AACD,OAAC,EAAE,SAAS,IAAI,MAAM,KAAK,WAAW,MAAM,SAAS,EAAE,UAAU,KAAK,CAAC;AAAA,IAC3E;AACA,QAAI,KAAK,MAAM;AACX,YAAM,cAAc,MAAM,KAAK,KAAK,wBAAwB;AAAA,QACxD;AAAA,QACA,aAAa,WAAW,QAAQ;AAAA,QAChC;AAAA,MACJ,CAAC;AACD,UAAI,CAAC,aAAa;AACd,eAAO,EAAE,OAAO,sBAAsB;AAAA,MAC1C;AAAA,IACJ;AACA,UAAM,SAAS,2BAA2B,UAAU;AACpD,UAAM,MAAM;AACZ,QAAI;AACJ,QAAI,cAAc,KAAK,UAAU,GAAG;AAChC,OAAC,EAAE,MAAM,IAAI,KAAK,WAAW,MAAM,KAAK,EAAE,OAAO,KAAK,CAAC;AAAA,IAC3D,OACK;AACD,OAAC,EAAE,MAAM,IAAI,MAAM,KAAK,WAAW,MAAM,KAAK,EAAE,OAAO,KAAK,CAAC;AAAA,IACjE;AACA,QAAI,UAAU,QAAW;AACrB,YAAM,4BAA4B,GAAG,GAAG;AACxC,aAAO,EAAE,OAAO,iBAAiB;AAAA,IACrC;AACA,QAAI,MAAM,IAAI,aAAa,QAAW;AAClC,YAAM,wBAAwB,GAAG,iBAAiB,QAAQ,cACzC,OAAO,QAAQ,IAAI,GAAG;AACvC;AAAA,IACJ;AACA,UAAM,UAAU,kBAAkB;AAAA,MAC9B,MAAM,KAAK;AAAA,IACf,CAAC;AACD,UAAM,aAAa,gBAAgB,2BAA2B;AAC9D,UAAM,QAAQ,YAAY,SAAS,OAAO,UAAU;AAIpD,QAAI,OAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAC5C,YAAM,mBAAmB,MAAM,IAAI,kBAAkB;AACrD,YAAM,kBAAkB,MAAM,IAAI,kBAAkB;AACpD;AAAA;AAAA,QAEC,CAAC,oBAAoB,CAAC;AAAA,QAElB,qBACI,MAAM,IAAI,cAAc,QAAQ,MAAM,UACnC,OAAO,KAAK,MAAM,IAAI,aAAa,EAAE,SAAS;AAAA,QAAK;AAC3D,cAAM,aAAa,QAAQ,gCAAgC;AAC3D;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,CAAC,KAAK,KAAK,KAAK,eAAe,MAAM,GAAG,MAAM,KAAK,QAAQ,GAAG;AAC9D,YAAM,iCAAiC,QAAQ,cAC9B,OAAO,QAAQ,IAAI,GAAG;AACvC;AAAA,IACJ;AAEA,UAAM,OAAO,OAAO,QAAQ,YACtB,KAAK,KAAK,KAAK,QAAQ,MAAM,KAAK,OAAO,QAAQ,MAAM,QAAQ,IAC/D;AAEN,QAAI,OAAO,QAAQ,aAAa,CAAC,MAAM;AACnC,YAAM,4DAA4D,QAAQ,cACzD,OAAO,QAAQ,IAAI,GAAG;AACvC;AAAA,IACJ;AAGA,QAAI,MAAM,aAAa,WACnB,EAAE,QAAQ,eAAe,IAAI,KAAK,KAAK,qBAAqB;AAC5D,YAAM,yBAAyB,OAAO,gBAAgB,MAAM,QAAQ,iBAChD,QAAQ,cAAc,OAAO,QAAQ,IAAI,GAAG;AAChE;AAAA,IACJ;AACA,UAAM,YAAY,MAAM,SAAS;AAEjC,UAAM,SAAS,MAAM;AACrB,YAAQ,MAAM,SAAS;AACvB,SAAK,kBAAkB;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,IACJ,CAAC;AACD,QAAI,KAAK,KAAK,YAAY;AACtB,WAAK,aAAa,QAAQ;AAAA,QACtB,MAAM;AAAA,QACN,MAAM,CAAC,SAAS,SAAS,WAAW,KAAK;AAAA,MAC7C,CAAC;AAAA,IACL,OACK;AACD,WAAK,aAAa,QAAQ;AAAA,QACtB,MAAM;AAAA,QACN,MAAM,CAAC,SAAS,KAAK;AAAA,MACzB,CAAC;AAAA,IACL;AACA,UAAM,EAAE,UAAU,GAAG,qBAAqB,IAAI;AAC9C,QAAI;AACJ,QAAI,aACC,SAAS,aAAa,UAAa,SAAS,aAAa,OAAO;AACjE,oBAAc;AAAA,QACV,GAAG;AAAA,QACH,WAAW,KAAK,IAAI;AAAA,MACxB;AACA,UAAI,MAAM,IAAI,aAAa,QAAW;AAClC,oBAAY,WAAW,MAAM,IAAI;AAAA,MACrC;AAAA,IACJ;AACA,QAAI,cAAc,KAAK,UAAU,GAAG;AAChC,WAAK,WAAW,SAAS,KAAK,sBAAsB,QAAQ;AAC5D,UAAI;AACA,aAAK,WAAW,YAAY,KAAK,WAAW;AAAA,IACpD,OACK;AACD,YAAM,SAAS;AAAA,QACX,KAAK,WAAW,SAAS,KAAK,sBAAsB,QAAQ;AAAA,MAChE;AACA,UAAI,aAAa;AACb,eAAO,KAAK,KAAK,WAAW,YAAY,KAAK,WAAW,CAAC;AAAA,MAC7D;AACA,YAAM,QAAQ,IAAI,MAAM;AAAA,IAC5B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO,SAAS,UAAU,aAAa,aAAa,GAAG;AACzD,UAAM,MAAM;AACZ,UAAM,YAAY;AAAA,MACd,OAAO;AAAA,MACP,UAAU;AAAA,MACV,KAAK;AAAA,MACL,cAAc;AAAA,IAClB;AACA,UAAM,cAAc,cAAc,KAAK,UAAU,IAC3C,KAAK,WAAW,MAAM,KAAK,SAAS,IACpC,MAAM,KAAK,WAAW,MAAM,KAAK,SAAS;AAChD,QAAI,EAAE,OAAO,cAAc,KAAK,SAAS,IAAI;AAC7C,QAAI,KAAK,QAAQ,aAAa,UAAa,aAAa,MAAM;AAC1D,YAAM,cAAc,MAAM,KAAK,KAAK,wBAAwB;AAAA,QACxD;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,UAAI,CAAC,aAAa;AACd,eAAO,EAAE,OAAO,eAAe;AAAA,MACnC;AAAA,IACJ;AAGA,QAAI,UAAU,QAAW;AACrB,YAAM,QAAQ,YAAY;AAAA,QACtB,MAAM,KAAK;AAAA,QACX,UAAU;AAAA,QACV;AAAA,QACA,WAAW;AAAA,MACf,CAAC;AACD,UAAI,oBAAoB,OAAO;AAC3B,eAAO,EAAE,OAAO,0BAA0B;AAAA,MAC9C;AACA,qBAAe,QAAQ,MAAM;AAC7B,iBAAW,MAAM;AACjB,WAAK,kBAAkB,EAAE,OAAO,QAAQ,CAAC;AACzC,UAAI,cAAc,KAAK,UAAU,GAAG;AAChC,aAAK,WAAW,YAAY,KAAK,EAAE,cAAc,SAAS,CAAC;AAAA,MAC/D,OACK;AACD,cAAM,KAAK,WAAW,YAAY,KAAK,EAAE,cAAc,SAAS,CAAC;AAAA,MACrE;AAAA,IACJ;AACA,UAAM,mBAAmB,WAAW,gBAAgB,QAAQ,IAAI;AAChE,UAAM,WAAW;AAAA,MACb;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,SAAK,aAAa,KAAK;AAAA,MACnB;AAAA,MACA,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,QAAQ;AAAA,IAC5B,CAAC;AACD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,mBAAmB,SAAS,UAAU,aAAa,WAAW;AAChE,UAAM,MAAM;AAEZ,QAAI,aAAa,UAAa,aAAa,MAAM;AAC7C;AAAA,IACJ;AACA,QAAI;AACJ,QAAI,cAAc,KAAK,UAAU,GAAG;AAChC,OAAC,EAAE,SAAS,IAAI,KAAK,WAAW,MAAM,KAAK,EAAE,UAAU,KAAK,CAAC;AAAA,IACjE,OACK;AACD,OAAC,EAAE,SAAS,IAAI,MAAM,KAAK,WAAW,MAAM,KAAK,EAAE,UAAU,KAAK,CAAC;AAAA,IACvE;AACA,QAAI,aAAa,QAAW;AACxB,YAAM,mCAAmC,GAAG,GAAG;AAC/C,aAAO,EAAE,OAAO,qBAAqB;AAAA,IACzC;AACA,QAAI,SAAS,QAAQ,QAAQ,MAAM,QAAW;AAC1C,YAAM,qCAAqC,GAAG,eAAe,QAAQ,GAAG;AACxE,aAAO,EAAE,OAAO,0BAA0B;AAAA,IAC9C;AACA,QAAI,KAAK,MAAM;AACX,YAAM,cAAc,MAAM,KAAK,KAAK,wBAAwB;AAAA,QACxD;AAAA,QACA;AAAA,QACA;AAAA,MACJ,CAAC;AACD,UAAI,CAAC,aAAa;AACd,eAAO,EAAE,OAAO,eAAe;AAAA,MACnC;AAAA,IACJ;AACA,aAAS,QAAQ,QAAQ,EAAE,cAAc;AACzC,UAAM,mBAAmB,gBAAgB,QAAQ;AACjD,SAAK,aAAa,QAAQ;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,gBAAgB;AAAA,IACpC,CAAC;AACD,QAAI,cAAc,KAAK,UAAU,GAAG;AAChC,WAAK,WAAW,YAAY,KAAK,QAAQ;AAAA,IAC7C,OACK;AACD,YAAM,KAAK,WAAW,YAAY,KAAK,QAAQ;AAAA,IACnD;AAAA,EACJ;AAAA,EACA,MAAM,cAAc,SAAS,aAAa,aAAa;AACnD,UAAM,MAAM;AACZ,QAAI,KAAK,MAAM;AACX,YAAM,EAAE,SAAS,IAAI,MAAM,KAAK,WAAW,MAAM,KAAK;AAAA,QAClD,UAAU;AAAA,MACd,CAAC;AACD,UAAI,EAAE,eAAe,OAAO,YAAY,WAAW,WAAW;AAC1D,eAAO,EAAE,OAAO,eAAe;AAAA,MACnC;AACA,YAAM,cAAc,MAAM,KAAK,KAAK,wBAAwB;AAAA,QACxD,UAAU,YAAY;AAAA,QACtB;AAAA,QACA;AAAA,MACJ,CAAC;AACD,UAAI,CAAC,aAAa;AACd,eAAO,EAAE,OAAO,eAAe;AAAA,MACnC;AAAA,IACJ;AACA,SAAK,aAAa,QAAQ;AAAA,MACtB,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,WAAW;AAAA,IAC/B,CAAC;AAAA,EACL;AACJ;;;AC1TA,qBAA4B;AAE5B,IAAM,kBAAkB,CAAC,MAAM,UAAU,WAAW;AAAA,EAChD,GAAG;AAAA,EACH,GAAG,KAAK,WAAW,EAAE,GAAG,MAAM,GAAG,KAAK,MAAM,KAAK,SAAS,CAAC;AAAA,EAC3D,SAAS,WAAW,OAAO,EAAE,UAAU,KAAK,CAAC;AAAA,EAC7C,UAAU;AAAA,EACV,OAAO,CAAC;AAAA,EACR,OAAO,CAAC;AACZ;AAEA,IAAM,sBAAsB,CAAC,SAAS,CAAC,UAAU,YAAY;AACzD,UAAQ,QAAQ,MAAM;AAAA,IAClB,KAAK,SAAS;AACV,YAAM,CAAC,SAAS,SAAS,WAAW,KAAK,IAAI,QAAQ;AACrD,YAAM,MAAM,UAAU,MAAM,UAAU,QAAQ;AAC9C,YAAM,gBAAgB,gBAAgB,MAAM,UAAU,KAAK;AAC3D,YAAM,aAAa,MAAM;AACzB,YAAM,oBAAoB,gBAAgB,MAAM,UAAU,SAAS;AACnE,YAAM,YAAQ,4BAAY,mBAAmB,aAAa;AAC1D,aAAO;AAAA,QACH,MAAM;AAAA,QACN,MAAM,CAAC,SAAS,SAAS,YAAY,OAAO,GAAG;AAAA,MACnD;AAAA,IACJ;AAAA,IACA,KAAK,UAAU;AACX,YAAM,CAAC,SAAS,KAAK,IAAI,QAAQ;AACjC,YAAM,MAAM,UAAU,MAAM,UAAU,QAAQ;AAC9C,YAAM,gBAAgB,gBAAgB,MAAM,UAAU,KAAK;AAC3D,aAAO;AAAA,QACH,MAAM;AAAA,QACN,MAAM,CAAC,SAAS,eAAe,GAAG;AAAA,MACtC;AAAA,IACJ;AAAA,IACA,KAAK,QAAQ;AACT,YAAM,CAAC,SAAS,QAAQ,IAAI,QAAQ;AACpC,YAAM,gBAAgB,gBAAgB,MAAM,UAAU,SAAS,KAAK;AACpE,YAAM,MAAM,UAAU,SAAS,KAAK,QAAQ;AAC5C,YAAM,cAAc;AAAA,QAChB,GAAG;AAAA,QACH,OAAO;AAAA,QACP;AAAA,MACJ;AACA,aAAO;AAAA,QACH,MAAM;AAAA,QACN,MAAM,CAAC,SAAS,WAAW;AAAA,MAC/B;AAAA,IACJ;AAAA,IACA,SAAS;AACL,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AAQA,SAAS,UAAU,KAAK,UAAU;AAC9B,MAAI,QAAQ,QAAW;AACnB,WAAO;AAAA,EACX;AACA,SAAO,IAAI,IAAI,CAAC,aAAa;AAEzB,QAAI,aAAa,QAAQ,CAAC,aAAa,CAAC,SAAS,OAAO,QAAQ,UAAU;AACtE,aAAO;AAAA,IACX;AACA,QAAI,SAAS,WAAW,MAAM;AAC1B,aAAO;AAAA,IACX;AACA,UAAM,UAAU;AAAA,MACZ,GAAG,SAAS,OAAO;AAAA,MACnB,MAAM;AAAA,IACV;AACA,UAAM,gBAAgB;AAAA,MAClB,GAAG;AAAA,MACH,QAAQ,EAAE,GAAG,SAAS,QAAQ,QAAQ;AAAA,IAC1C;AACA,UAAM,EAAE,QAAQ,GAAG,UAAU,IAAI;AACjC,WAAO;AAAA,EACX,CAAC;AACL;;;ACpFA,IAAM,eAAe,uBAAO,OAAO,IAAI;AACvC,aAAa,MAAM,IAAI;AACvB,aAAa,OAAO,IAAI;AACxB,aAAa,MAAM,IAAI;AACvB,aAAa,MAAM,IAAI;AACvB,aAAa,SAAS,IAAI;AAC1B,aAAa,SAAS,IAAI;AAC1B,aAAa,MAAM,IAAI;AACvB,IAAM,uBAAuB,uBAAO,OAAO,IAAI;AAC/C,OAAO,KAAK,YAAY,EAAE,QAAQ,CAAC,QAAQ;AACvC,uBAAqB,aAAa,GAAG,CAAC,IAAI;AAC9C,CAAC;AACD,IAAM,eAAe,EAAE,MAAM,SAAS,MAAM,eAAe;;;ACX3D,IAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AACjD,IAAM,wBAAwB,OAAO,gBAAgB;AAErD,IAAM,SAAS,CAAC,QAAQ;AACpB,SAAO,OAAO,YAAY,WAAW,aAC/B,YAAY,OAAO,GAAG,IACtB,OAAO,IAAI,kBAAkB;AACvC;AACA,IAAM,eAAe,CAAC,EAAE,MAAM,KAAK,GAAG,gBAAgB,aAAa;AAC/D,MAAI,kBAAkB,gBAAgB,MAAM;AACxC,QAAI,gBAAgB;AAChB,aAAO,SAAS,IAAI;AAAA,IACxB,OACK;AACD,aAAO,mBAAmB,MAAM,QAAQ;AAAA,IAC5C;AAAA,EACJ,WACS,0BACJ,gBAAgB,eAAe,OAAO,IAAI,IAAI;AAC/C,QAAI,gBAAgB;AAChB,aAAO,SAAS,IAAI;AAAA,IACxB,OACK;AACD,aAAO,mBAAmB,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,QAAQ;AAAA,IACxD;AAAA,EACJ;AAEA,SAAO,SAAS,aAAa,IAAI,KAAK,QAAQ,GAAG;AACrD;AACA,IAAM,qBAAqB,CAAC,MAAM,aAAa;AAC3C,QAAM,aAAa,IAAI,WAAW;AAClC,aAAW,SAAS,WAAY;AAC5B,UAAM,UAAU,WAAW,OAAO,MAAM,GAAG,EAAE,CAAC;AAC9C,aAAS,OAAO,WAAW,GAAG;AAAA,EAClC;AACA,SAAO,WAAW,cAAc,IAAI;AACxC;AACA,SAAS,QAAQ,MAAM;AACnB,MAAI,gBAAgB,YAAY;AAC5B,WAAO;AAAA,EACX,WACS,gBAAgB,aAAa;AAClC,WAAO,IAAI,WAAW,IAAI;AAAA,EAC9B,OACK;AACD,WAAO,IAAI,WAAW,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;AAAA,EACvE;AACJ;AACA,IAAI;AACG,SAAS,qBAAqB,QAAQ,UAAU;AACnD,MAAI,kBAAkB,OAAO,gBAAgB,MAAM;AAC/C,WAAO,OAAO,KAAK,YAAY,EAAE,KAAK,OAAO,EAAE,KAAK,QAAQ;AAAA,EAChE,WACS,0BACJ,OAAO,gBAAgB,eAAe,OAAO,OAAO,IAAI,IAAI;AAC7D,WAAO,SAAS,QAAQ,OAAO,IAAI,CAAC;AAAA,EACxC;AACA,eAAa,QAAQ,OAAO,CAAC,YAAY;AACrC,QAAI,CAAC,cAAc;AACf,qBAAe,IAAI,YAAY;AAAA,IACnC;AACA,aAAS,aAAa,OAAO,OAAO,CAAC;AAAA,EACzC,CAAC;AACL;;;ACjEA,IAAM,QAAQ;AAEd,IAAM,SAAS,OAAO,eAAe,cAAc,CAAC,IAAI,IAAI,WAAW,GAAG;AAC1E,SAASC,KAAI,GAAGA,KAAI,MAAM,QAAQA,MAAK;AACnC,SAAO,MAAM,WAAWA,EAAC,CAAC,IAAIA;AAClC;AAiBO,IAAM,SAAS,CAAC,WAAW;AAC9B,MAAI,eAAe,OAAO,SAAS,MAAM,MAAM,OAAO,QAAQC,IAAG,IAAI,GAAG,UAAU,UAAU,UAAU;AACtG,MAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK;AACnC;AACA,QAAI,OAAO,OAAO,SAAS,CAAC,MAAM,KAAK;AACnC;AAAA,IACJ;AAAA,EACJ;AACA,QAAM,cAAc,IAAI,YAAY,YAAY,GAAG,QAAQ,IAAI,WAAW,WAAW;AACrF,OAAKA,KAAI,GAAGA,KAAI,KAAKA,MAAK,GAAG;AACzB,eAAW,OAAO,OAAO,WAAWA,EAAC,CAAC;AACtC,eAAW,OAAO,OAAO,WAAWA,KAAI,CAAC,CAAC;AAC1C,eAAW,OAAO,OAAO,WAAWA,KAAI,CAAC,CAAC;AAC1C,eAAW,OAAO,OAAO,WAAWA,KAAI,CAAC,CAAC;AAC1C,UAAM,GAAG,IAAK,YAAY,IAAM,YAAY;AAC5C,UAAM,GAAG,KAAM,WAAW,OAAO,IAAM,YAAY;AACnD,UAAM,GAAG,KAAM,WAAW,MAAM,IAAM,WAAW;AAAA,EACrD;AACA,SAAO;AACX;;;ACxCA,IAAMC,yBAAwB,OAAO,gBAAgB;AAC9C,IAAM,eAAe,CAAC,eAAe,eAAe;AACvD,MAAI,OAAO,kBAAkB,UAAU;AACnC,WAAO;AAAA,MACH,MAAM;AAAA,MACN,MAAM,UAAU,eAAe,UAAU;AAAA,IAC7C;AAAA,EACJ;AACA,QAAM,OAAO,cAAc,OAAO,CAAC;AACnC,MAAI,SAAS,KAAK;AACd,WAAO;AAAA,MACH,MAAM;AAAA,MACN,MAAM,mBAAmB,cAAc,UAAU,CAAC,GAAG,UAAU;AAAA,IACnE;AAAA,EACJ;AACA,QAAM,aAAa,qBAAqB,IAAI;AAC5C,MAAI,CAAC,YAAY;AACb,WAAO;AAAA,EACX;AACA,SAAO,cAAc,SAAS,IACxB;AAAA,IACE,MAAM,qBAAqB,IAAI;AAAA,IAC/B,MAAM,cAAc,UAAU,CAAC;AAAA,EACnC,IACE;AAAA,IACE,MAAM,qBAAqB,IAAI;AAAA,EACnC;AACR;AACA,IAAM,qBAAqB,CAAC,MAAM,eAAe;AAC7C,MAAIA,wBAAuB;AACvB,UAAM,UAAU,OAAO,IAAI;AAC3B,WAAO,UAAU,SAAS,UAAU;AAAA,EACxC,OACK;AACD,WAAO,EAAE,QAAQ,MAAM,KAAK;AAAA,EAChC;AACJ;AACA,IAAM,YAAY,CAAC,MAAM,eAAe;AACpC,UAAQ,YAAY;AAAA,IAChB,KAAK;AACD,UAAI,gBAAgB,MAAM;AAEtB,eAAO;AAAA,MACX,OACK;AAED,eAAO,IAAI,KAAK,CAAC,IAAI,CAAC;AAAA,MAC1B;AAAA,IACJ,KAAK;AAAA,IACL;AACI,UAAI,gBAAgB,aAAa;AAE7B,eAAO;AAAA,MACX,OACK;AAED,eAAO,KAAK;AAAA,MAChB;AAAA,EACR;AACJ;;;AC1DA,IAAM,YAAY,OAAO,aAAa,EAAE;AACxC,IAAM,gBAAgB,CAAC,SAAS,aAAa;AAEzC,QAAMC,UAAS,QAAQ;AACvB,QAAM,iBAAiB,IAAI,MAAMA,OAAM;AACvC,MAAI,QAAQ;AACZ,UAAQ,QAAQ,CAAC,QAAQC,OAAM;AAE3B,iBAAa,QAAQ,OAAO,CAAC,kBAAkB;AAC3C,qBAAeA,EAAC,IAAI;AACpB,UAAI,EAAE,UAAUD,SAAQ;AACpB,iBAAS,eAAe,KAAK,SAAS,CAAC;AAAA,MAC3C;AAAA,IACJ,CAAC;AAAA,EACL,CAAC;AACL;AACA,IAAM,gBAAgB,CAAC,gBAAgB,eAAe;AAClD,QAAM,iBAAiB,eAAe,MAAM,SAAS;AACrD,QAAM,UAAU,CAAC;AACjB,WAASC,KAAI,GAAGA,KAAI,eAAe,QAAQA,MAAK;AAC5C,UAAM,gBAAgB,aAAa,eAAeA,EAAC,GAAG,UAAU;AAChE,YAAQ,KAAK,aAAa;AAC1B,QAAI,cAAc,SAAS,SAAS;AAChC;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AACO,SAAS,4BAA4B;AAExC,SAAO,IAAI,gBAAgB;AAAA,IACvB,UAAU,QAAQ,YAAY;AAC1B,2BAAqB,QAAQ,CAAC,kBAAkB;AAC5C,cAAM,gBAAgB,cAAc;AACpC,YAAI;AAEJ,YAAI,gBAAgB,KAAK;AACrB,mBAAS,IAAI,WAAW,CAAC;AACzB,cAAI,SAAS,OAAO,MAAM,EAAE,SAAS,GAAG,aAAa;AAAA,QACzD,WACS,gBAAgB,OAAO;AAC5B,mBAAS,IAAI,WAAW,CAAC;AACzB,gBAAM,OAAO,IAAI,SAAS,OAAO,MAAM;AACvC,eAAK,SAAS,GAAG,GAAG;AACpB,eAAK,UAAU,GAAG,aAAa;AAAA,QACnC,OACK;AACD,mBAAS,IAAI,WAAW,CAAC;AACzB,gBAAM,OAAO,IAAI,SAAS,OAAO,MAAM;AACvC,eAAK,SAAS,GAAG,GAAG;AACpB,eAAK,aAAa,GAAG,OAAO,aAAa,CAAC;AAAA,QAC9C;AAEA,YAAI,OAAO,QAAQ,OAAO,OAAO,SAAS,UAAU;AAChD,iBAAO,CAAC,KAAK;AAAA,QACjB;AACA,mBAAW,QAAQ,MAAM;AACzB,mBAAW,QAAQ,aAAa;AAAA,MACpC,CAAC;AAAA,IACL;AAAA,EACJ,CAAC;AACL;AACA,IAAI;AACJ,SAAS,YAAY,QAAQ;AACzB,SAAO,OAAO,OAAO,CAAC,KAAK,UAAU,MAAM,MAAM,QAAQ,CAAC;AAC9D;AACA,SAAS,aAAa,QAAQ,MAAM;AAChC,MAAI,OAAO,CAAC,EAAE,WAAW,MAAM;AAC3B,WAAO,OAAO,MAAM;AAAA,EACxB;AACA,QAAM,SAAS,IAAI,WAAW,IAAI;AAClC,MAAI,IAAI;AACR,WAASA,KAAI,GAAGA,KAAI,MAAMA,MAAK;AAC3B,WAAOA,EAAC,IAAI,OAAO,CAAC,EAAE,GAAG;AACzB,QAAI,MAAM,OAAO,CAAC,EAAE,QAAQ;AACxB,aAAO,MAAM;AACb,UAAI;AAAA,IACR;AAAA,EACJ;AACA,MAAI,OAAO,UAAU,IAAI,OAAO,CAAC,EAAE,QAAQ;AACvC,WAAO,CAAC,IAAI,OAAO,CAAC,EAAE,MAAM,CAAC;AAAA,EACjC;AACA,SAAO;AACX;AACO,SAAS,0BAA0B,YAAY,YAAY;AAC9D,MAAI,CAAC,cAAc;AACf,mBAAe,IAAI,YAAY;AAAA,EACnC;AACA,QAAM,SAAS,CAAC;AAChB,MAAI,QAAQ;AACZ,MAAI,iBAAiB;AACrB,MAAIC,YAAW;AAEf,SAAO,IAAI,gBAAgB;AAAA,IACvB,UAAU,OAAO,YAAY;AACzB,aAAO,KAAK,KAAK;AACjB,aAAO,MAAM;AACT,YAAI,UAAU,GAAqB;AAC/B,cAAI,YAAY,MAAM,IAAI,GAAG;AACzB;AAAA,UACJ;AACA,gBAAM,SAAS,aAAa,QAAQ,CAAC;AACrC,UAAAA,aAAY,OAAO,CAAC,IAAI,SAAU;AAClC,2BAAiB,OAAO,CAAC,IAAI;AAC7B,cAAI,iBAAiB,KAAK;AACtB,oBAAQ;AAAA,UACZ,WACS,mBAAmB,KAAK;AAC7B,oBAAQ;AAAA,UACZ,OACK;AACD,oBAAQ;AAAA,UACZ;AAAA,QACJ,WACS,UAAU,GAAiC;AAChD,cAAI,YAAY,MAAM,IAAI,GAAG;AACzB;AAAA,UACJ;AACA,gBAAM,cAAc,aAAa,QAAQ,CAAC;AAC1C,2BAAiB,IAAI,SAAS,YAAY,QAAQ,YAAY,YAAY,YAAY,MAAM,EAAE,UAAU,CAAC;AACzG,kBAAQ;AAAA,QACZ,WACS,UAAU,GAAiC;AAChD,cAAI,YAAY,MAAM,IAAI,GAAG;AACzB;AAAA,UACJ;AACA,gBAAM,cAAc,aAAa,QAAQ,CAAC;AAC1C,gBAAM,OAAO,IAAI,SAAS,YAAY,QAAQ,YAAY,YAAY,YAAY,MAAM;AACxF,gBAAM,IAAI,KAAK,UAAU,CAAC;AAC1B,cAAI,IAAI,KAAK,IAAI,GAAG,KAAK,EAAE,IAAI,GAAG;AAE9B,uBAAW,QAAQ,YAAY;AAC/B;AAAA,UACJ;AACA,2BAAiB,IAAI,KAAK,IAAI,GAAG,EAAE,IAAI,KAAK,UAAU,CAAC;AACvD,kBAAQ;AAAA,QACZ,OACK;AACD,cAAI,YAAY,MAAM,IAAI,gBAAgB;AACtC;AAAA,UACJ;AACA,gBAAM,OAAO,aAAa,QAAQ,cAAc;AAChD,qBAAW,QAAQ,aAAaA,YAAW,OAAO,aAAa,OAAO,IAAI,GAAG,UAAU,CAAC;AACxF,kBAAQ;AAAA,QACZ;AACA,YAAI,mBAAmB,KAAK,iBAAiB,YAAY;AACrD,qBAAW,QAAQ,YAAY;AAC/B;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACO,IAAM,WAAW;;;ACtJjB,SAAS,QAAQ,KAAK;AAC3B,MAAI;AAAK,WAAO,MAAM,GAAG;AAC3B;AAUA,SAAS,MAAM,KAAK;AAClB,WAAS,OAAO,QAAQ,WAAW;AACjC,QAAI,GAAG,IAAI,QAAQ,UAAU,GAAG;AAAA,EAClC;AACA,SAAO;AACT;AAWA,QAAQ,UAAU,KAClB,QAAQ,UAAU,mBAAmB,SAAS,OAAO,IAAG;AACtD,OAAK,aAAa,KAAK,cAAc,CAAC;AACtC,GAAC,KAAK,WAAW,MAAM,KAAK,IAAI,KAAK,WAAW,MAAM,KAAK,KAAK,CAAC,GAC9D,KAAK,EAAE;AACV,SAAO;AACT;AAYA,QAAQ,UAAU,OAAO,SAAS,OAAO,IAAG;AAC1C,WAASC,MAAK;AACZ,SAAK,IAAI,OAAOA,GAAE;AAClB,OAAG,MAAM,MAAM,SAAS;AAAA,EAC1B;AAEA,EAAAA,IAAG,KAAK;AACR,OAAK,GAAG,OAAOA,GAAE;AACjB,SAAO;AACT;AAYA,QAAQ,UAAU,MAClB,QAAQ,UAAU,iBAClB,QAAQ,UAAU,qBAClB,QAAQ,UAAU,sBAAsB,SAAS,OAAO,IAAG;AACzD,OAAK,aAAa,KAAK,cAAc,CAAC;AAGtC,MAAI,KAAK,UAAU,QAAQ;AACzB,SAAK,aAAa,CAAC;AACnB,WAAO;AAAA,EACT;AAGA,MAAI,YAAY,KAAK,WAAW,MAAM,KAAK;AAC3C,MAAI,CAAC;AAAW,WAAO;AAGvB,MAAI,KAAK,UAAU,QAAQ;AACzB,WAAO,KAAK,WAAW,MAAM,KAAK;AAClC,WAAO;AAAA,EACT;AAGA,MAAI;AACJ,WAASC,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,SAAK,UAAUA,EAAC;AAChB,QAAI,OAAO,MAAM,GAAG,OAAO,IAAI;AAC7B,gBAAU,OAAOA,IAAG,CAAC;AACrB;AAAA,IACF;AAAA,EACF;AAIA,MAAI,UAAU,WAAW,GAAG;AAC1B,WAAO,KAAK,WAAW,MAAM,KAAK;AAAA,EACpC;AAEA,SAAO;AACT;AAUA,QAAQ,UAAU,OAAO,SAAS,OAAM;AACtC,OAAK,aAAa,KAAK,cAAc,CAAC;AAEtC,MAAI,OAAO,IAAI,MAAM,UAAU,SAAS,CAAC,GACrC,YAAY,KAAK,WAAW,MAAM,KAAK;AAE3C,WAASA,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACzC,SAAKA,KAAI,CAAC,IAAI,UAAUA,EAAC;AAAA,EAC3B;AAEA,MAAI,WAAW;AACb,gBAAY,UAAU,MAAM,CAAC;AAC7B,aAASA,KAAI,GAAG,MAAM,UAAU,QAAQA,KAAI,KAAK,EAAEA,IAAG;AACpD,gBAAUA,EAAC,EAAE,MAAM,MAAM,IAAI;AAAA,IAC/B;AAAA,EACF;AAEA,SAAO;AACT;AAGA,QAAQ,UAAU,eAAe,QAAQ,UAAU;AAUnD,QAAQ,UAAU,YAAY,SAAS,OAAM;AAC3C,OAAK,aAAa,KAAK,cAAc,CAAC;AACtC,SAAO,KAAK,WAAW,MAAM,KAAK,KAAK,CAAC;AAC1C;AAUA,QAAQ,UAAU,eAAe,SAAS,OAAM;AAC9C,SAAO,CAAC,CAAE,KAAK,UAAU,KAAK,EAAE;AAClC;;;ACxKO,IAAM,kBAAkB,MAAM;AACjC,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAO;AAAA,EACX,WACS,OAAO,WAAW,aAAa;AACpC,WAAO;AAAA,EACX,OACK;AACD,WAAO,SAAS,aAAa,EAAE;AAAA,EACnC;AACJ,GAAG;;;ACTI,SAAS,KAAK,QAAQ,MAAM;AAC/B,SAAO,KAAK,OAAO,CAAC,KAAK,MAAM;AAC3B,QAAI,IAAI,eAAe,CAAC,GAAG;AACvB,UAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AAEA,IAAM,qBAAqB,eAAW;AACtC,IAAM,uBAAuB,eAAW;AACjC,SAAS,sBAAsB,KAAK,MAAM;AAC7C,MAAI,KAAK,iBAAiB;AACtB,QAAI,eAAe,mBAAmB,KAAK,cAAU;AACrD,QAAI,iBAAiB,qBAAqB,KAAK,cAAU;AAAA,EAC7D,OACK;AACD,QAAI,eAAe,eAAW,WAAW,KAAK,cAAU;AACxD,QAAI,iBAAiB,eAAW,aAAa,KAAK,cAAU;AAAA,EAChE;AACJ;AAEA,IAAM,kBAAkB;AAEjB,SAAS,WAAW,KAAK;AAC5B,MAAI,OAAO,QAAQ,UAAU;AACzB,WAAO,WAAW,GAAG;AAAA,EACzB;AAEA,SAAO,KAAK,MAAM,IAAI,cAAc,IAAI,QAAQ,eAAe;AACnE;AACA,SAAS,WAAW,KAAK;AACrB,MAAI,IAAI,GAAGC,UAAS;AACpB,WAASC,KAAI,GAAG,IAAI,IAAI,QAAQA,KAAI,GAAGA,MAAK;AACxC,QAAI,IAAI,WAAWA,EAAC;AACpB,QAAI,IAAI,KAAM;AACV,MAAAD,WAAU;AAAA,IACd,WACS,IAAI,MAAO;AAChB,MAAAA,WAAU;AAAA,IACd,WACS,IAAI,SAAU,KAAK,OAAQ;AAChC,MAAAA,WAAU;AAAA,IACd,OACK;AACD,MAAAC;AACA,MAAAD,WAAU;AAAA,IACd;AAAA,EACJ;AACA,SAAOA;AACX;;;AC3CO,SAAS,OAAO,KAAK;AACxB,MAAI,MAAM;AACV,WAASE,MAAK,KAAK;AACf,QAAI,IAAI,eAAeA,EAAC,GAAG;AACvB,UAAI,IAAI;AACJ,eAAO;AACX,aAAO,mBAAmBA,EAAC,IAAI,MAAM,mBAAmB,IAAIA,EAAC,CAAC;AAAA,IAClE;AAAA,EACJ;AACA,SAAO;AACX;AAOO,SAASC,QAAO,IAAI;AACvB,MAAI,MAAM,CAAC;AACX,MAAI,QAAQ,GAAG,MAAM,GAAG;AACxB,WAASD,KAAI,GAAG,IAAI,MAAM,QAAQA,KAAI,GAAGA,MAAK;AAC1C,QAAI,OAAO,MAAMA,EAAC,EAAE,MAAM,GAAG;AAC7B,QAAI,mBAAmB,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAmB,KAAK,CAAC,CAAC;AAAA,EACjE;AACA,SAAO;AACX;;;AC7BO,IAAM,iBAAN,cAA6B,MAAM;AAAA,EACtC,YAAY,QAAQ,aAAa,SAAS;AACtC,UAAM,MAAM;AACZ,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,OAAO;AAAA,EAChB;AACJ;AACO,IAAME,aAAN,cAAwB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnC,YAAY,MAAM;AACd,UAAM;AACN,SAAK,WAAW;AAChB,0BAAsB,MAAM,IAAI;AAChC,SAAK,OAAO;AACZ,SAAK,QAAQ,KAAK;AAClB,SAAK,SAAS,KAAK;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,QAAQ,aAAa,SAAS;AAClC,UAAM,aAAa,SAAS,IAAI,eAAe,QAAQ,aAAa,OAAO,CAAC;AAC5E,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACH,SAAK,aAAa;AAClB,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,QAAI,KAAK,eAAe,aAAa,KAAK,eAAe,QAAQ;AAC7D,WAAK,QAAQ;AACb,WAAK,QAAQ;AAAA,IACjB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,SAAS;AACV,QAAI,KAAK,eAAe,QAAQ;AAC5B,WAAK,MAAM,OAAO;AAAA,IACtB,OACK;AAAA,IAEL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,UAAM,aAAa,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,MAAM;AACT,UAAM,SAAS,aAAa,MAAM,KAAK,OAAO,UAAU;AACxD,SAAK,SAAS,MAAM;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,QAAQ;AACb,UAAM,aAAa,UAAU,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS;AACb,SAAK,aAAa;AAClB,UAAM,aAAa,SAAS,OAAO;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM,SAAS;AAAA,EAAE;AAAA,EACjB,UAAU,QAAQ,QAAQ,CAAC,GAAG;AAC1B,WAAQ,SACJ,QACA,KAAK,UAAU,IACf,KAAK,MAAM,IACX,KAAK,KAAK,OACV,KAAK,OAAO,KAAK;AAAA,EACzB;AAAA,EACA,YAAY;AACR,UAAM,WAAW,KAAK,KAAK;AAC3B,WAAO,SAAS,QAAQ,GAAG,MAAM,KAAK,WAAW,MAAM,WAAW;AAAA,EACtE;AAAA,EACA,QAAQ;AACJ,QAAI,KAAK,KAAK,SACR,KAAK,KAAK,UAAU,OAAO,KAAK,KAAK,SAAS,GAAG,KAC9C,CAAC,KAAK,KAAK,UAAU,OAAO,KAAK,KAAK,IAAI,MAAM,KAAM;AAC3D,aAAO,MAAM,KAAK,KAAK;AAAA,IAC3B,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAO,OAAO;AACV,UAAM,eAAe,OAAO,KAAK;AACjC,WAAO,aAAa,SAAS,MAAM,eAAe;AAAA,EACtD;AACJ;;;AC1IA,IAAM,WAAW,mEAAmE,MAAM,EAAE;AAA5F,IAA+F,SAAS;AAAxG,IAA4G,MAAM,CAAC;AACnH,IAAI,OAAO;AAAX,IAAc,IAAI;AAAlB,IAAqB;AAQd,SAASC,QAAO,KAAK;AACxB,MAAI,UAAU;AACd,KAAG;AACC,cAAU,SAAS,MAAM,MAAM,IAAI;AACnC,UAAM,KAAK,MAAM,MAAM,MAAM;AAAA,EACjC,SAAS,MAAM;AACf,SAAO;AACX;AAqBO,SAAS,QAAQ;AACpB,QAAM,MAAMC,QAAO,CAAC,oBAAI,KAAK,CAAC;AAC9B,MAAI,QAAQ;AACR,WAAO,OAAO,GAAG,OAAO;AAC5B,SAAO,MAAM,MAAMA,QAAO,MAAM;AACpC;AAIA,OAAO,IAAI,QAAQ;AACf,MAAI,SAAS,CAAC,CAAC,IAAI;;;AChDvB,IAAI,QAAQ;AACZ,IAAI;AACA,UAAQ,OAAO,mBAAmB,eAC9B,qBAAqB,IAAI,eAAe;AAChD,SACO,KAAK;AAGZ;AACO,IAAM,UAAU;;;ACPhB,SAAS,IAAI,MAAM;AACtB,QAAM,UAAU,KAAK;AAErB,MAAI;AACA,QAAI,gBAAgB,OAAO,mBAAmB,CAAC,WAAW,UAAU;AAChE,aAAO,IAAI,eAAe;AAAA,IAC9B;AAAA,EACJ,SACO,GAAG;AAAA,EAAE;AACZ,MAAI,CAAC,SAAS;AACV,QAAI;AACA,aAAO,IAAI,eAAW,CAAC,QAAQ,EAAE,OAAO,QAAQ,EAAE,KAAK,GAAG,CAAC,EAAE,mBAAmB;AAAA,IACpF,SACO,GAAG;AAAA,IAAE;AAAA,EAChB;AACJ;AACO,SAAS,kBAAkB;AAAE;;;ACZpC,SAAS,QAAQ;AAAE;AACnB,IAAM,UAAW,WAAY;AACzB,QAAM,MAAM,IAAI,IAAe;AAAA,IAC3B,SAAS;AAAA,EACb,CAAC;AACD,SAAO,QAAQ,IAAI;AACvB,EAAG;AACI,IAAM,UAAN,cAAsBC,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOnC,YAAY,MAAM;AACd,UAAM,IAAI;AACV,SAAK,UAAU;AACf,QAAI,OAAO,aAAa,aAAa;AACjC,YAAM,QAAQ,aAAa,SAAS;AACpC,UAAI,OAAO,SAAS;AAEpB,UAAI,CAAC,MAAM;AACP,eAAO,QAAQ,QAAQ;AAAA,MAC3B;AACA,WAAK,KACA,OAAO,aAAa,eACjB,KAAK,aAAa,SAAS,YAC3B,SAAS,KAAK;AAAA,IAC1B;AAIA,UAAM,cAAc,QAAQ,KAAK;AACjC,SAAK,iBAAiB,WAAW,CAAC;AAClC,QAAI,KAAK,KAAK,iBAAiB;AAC3B,WAAK,YAAY,gBAAgB;AAAA,IACrC;AAAA,EACJ;AAAA,EACA,IAAI,OAAO;AACP,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACL,SAAK,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,SAAS;AACX,SAAK,aAAa;AAClB,UAAM,QAAQ,MAAM;AAChB,WAAK,aAAa;AAClB,cAAQ;AAAA,IACZ;AACA,QAAI,KAAK,WAAW,CAAC,KAAK,UAAU;AAChC,UAAI,QAAQ;AACZ,UAAI,KAAK,SAAS;AACd;AACA,aAAK,KAAK,gBAAgB,WAAY;AAClC,YAAE,SAAS,MAAM;AAAA,QACrB,CAAC;AAAA,MACL;AACA,UAAI,CAAC,KAAK,UAAU;AAChB;AACA,aAAK,KAAK,SAAS,WAAY;AAC3B,YAAE,SAAS,MAAM;AAAA,QACrB,CAAC;AAAA,MACL;AAAA,IACJ,OACK;AACD,YAAM;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,aAAa,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,MAAM;AACT,UAAM,WAAW,CAAC,WAAW;AAEzB,UAAI,cAAc,KAAK,cAAc,OAAO,SAAS,QAAQ;AACzD,aAAK,OAAO;AAAA,MAChB;AAEA,UAAI,YAAY,OAAO,MAAM;AACzB,aAAK,QAAQ,EAAE,aAAa,iCAAiC,CAAC;AAC9D,eAAO;AAAA,MACX;AAEA,WAAK,SAAS,MAAM;AAAA,IACxB;AAEA,kBAAc,MAAM,KAAK,OAAO,UAAU,EAAE,QAAQ,QAAQ;AAE5D,QAAI,aAAa,KAAK,YAAY;AAE9B,WAAK,UAAU;AACf,WAAK,aAAa,cAAc;AAChC,UAAI,WAAW,KAAK,YAAY;AAC5B,aAAK,KAAK;AAAA,MACd,OACK;AAAA,MACL;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACN,UAAM,QAAQ,MAAM;AAChB,WAAK,MAAM,CAAC,EAAE,MAAM,QAAQ,CAAC,CAAC;AAAA,IAClC;AACA,QAAI,WAAW,KAAK,YAAY;AAC5B,YAAM;AAAA,IACV,OACK;AAGD,WAAK,KAAK,QAAQ,KAAK;AAAA,IAC3B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,SAAS;AACX,SAAK,WAAW;AAChB,kBAAc,SAAS,CAAC,SAAS;AAC7B,WAAK,QAAQ,MAAM,MAAM;AACrB,aAAK,WAAW;AAChB,aAAK,aAAa,OAAO;AAAA,MAC7B,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM;AACF,UAAM,SAAS,KAAK,KAAK,SAAS,UAAU;AAC5C,UAAM,QAAQ,KAAK,SAAS,CAAC;AAE7B,QAAI,UAAU,KAAK,KAAK,mBAAmB;AACvC,YAAM,KAAK,KAAK,cAAc,IAAI,MAAM;AAAA,IAC5C;AACA,QAAI,CAAC,KAAK,kBAAkB,CAAC,MAAM,KAAK;AACpC,YAAM,MAAM;AAAA,IAChB;AACA,WAAO,KAAK,UAAU,QAAQ,KAAK;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,OAAO,CAAC,GAAG;AACf,WAAO,OAAO,MAAM,EAAE,IAAI,KAAK,IAAI,WAAW,KAAK,UAAU,GAAG,KAAK,IAAI;AACzE,WAAO,IAAI,QAAQ,KAAK,IAAI,GAAG,IAAI;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,MAAM,IAAI;AACd,UAAM,MAAM,KAAK,QAAQ;AAAA,MACrB,QAAQ;AAAA,MACR;AAAA,IACJ,CAAC;AACD,QAAI,GAAG,WAAW,EAAE;AACpB,QAAI,GAAG,SAAS,CAAC,WAAW,YAAY;AACpC,WAAK,QAAQ,kBAAkB,WAAW,OAAO;AAAA,IACrD,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,UAAM,MAAM,KAAK,QAAQ;AACzB,QAAI,GAAG,QAAQ,KAAK,OAAO,KAAK,IAAI,CAAC;AACrC,QAAI,GAAG,SAAS,CAAC,WAAW,YAAY;AACpC,WAAK,QAAQ,kBAAkB,WAAW,OAAO;AAAA,IACrD,CAAC;AACD,SAAK,UAAU;AAAA,EACnB;AACJ;AACO,IAAM,UAAN,MAAM,iBAAgB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjC,YAAY,KAAK,MAAM;AACnB,UAAM;AACN,0BAAsB,MAAM,IAAI;AAChC,SAAK,OAAO;AACZ,SAAK,SAAS,KAAK,UAAU;AAC7B,SAAK,MAAM;AACX,SAAK,OAAO,WAAc,KAAK,OAAO,KAAK,OAAO;AAClD,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,QAAI;AACJ,UAAM,OAAO,KAAK,KAAK,MAAM,SAAS,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB,WAAW;AAC5H,SAAK,UAAU,CAAC,CAAC,KAAK,KAAK;AAC3B,UAAM,MAAO,KAAK,MAAM,IAAI,IAAe,IAAI;AAC/C,QAAI;AACA,UAAI,KAAK,KAAK,QAAQ,KAAK,KAAK,IAAI;AACpC,UAAI;AACA,YAAI,KAAK,KAAK,cAAc;AACxB,cAAI,yBAAyB,IAAI,sBAAsB,IAAI;AAC3D,mBAASC,MAAK,KAAK,KAAK,cAAc;AAClC,gBAAI,KAAK,KAAK,aAAa,eAAeA,EAAC,GAAG;AAC1C,kBAAI,iBAAiBA,IAAG,KAAK,KAAK,aAAaA,EAAC,CAAC;AAAA,YACrD;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ,SACO,GAAG;AAAA,MAAE;AACZ,UAAI,WAAW,KAAK,QAAQ;AACxB,YAAI;AACA,cAAI,iBAAiB,gBAAgB,0BAA0B;AAAA,QACnE,SACO,GAAG;AAAA,QAAE;AAAA,MAChB;AACA,UAAI;AACA,YAAI,iBAAiB,UAAU,KAAK;AAAA,MACxC,SACO,GAAG;AAAA,MAAE;AACZ,OAAC,KAAK,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,GAAG;AAEjF,UAAI,qBAAqB,KAAK;AAC1B,YAAI,kBAAkB,KAAK,KAAK;AAAA,MACpC;AACA,UAAI,KAAK,KAAK,gBAAgB;AAC1B,YAAI,UAAU,KAAK,KAAK;AAAA,MAC5B;AACA,UAAI,qBAAqB,MAAM;AAC3B,YAAIC;AACJ,YAAI,IAAI,eAAe,GAAG;AACtB,WAACA,MAAK,KAAK,KAAK,eAAe,QAAQA,QAAO,SAAS,SAASA,IAAG,aAAa,GAAG;AAAA,QACvF;AACA,YAAI,MAAM,IAAI;AACV;AACJ,YAAI,QAAQ,IAAI,UAAU,SAAS,IAAI,QAAQ;AAC3C,eAAK,OAAO;AAAA,QAChB,OACK;AAGD,eAAK,aAAa,MAAM;AACpB,iBAAK,QAAQ,OAAO,IAAI,WAAW,WAAW,IAAI,SAAS,CAAC;AAAA,UAChE,GAAG,CAAC;AAAA,QACR;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,IAAI;AAAA,IACtB,SACO,GAAG;AAIN,WAAK,aAAa,MAAM;AACpB,aAAK,QAAQ,CAAC;AAAA,MAClB,GAAG,CAAC;AACJ;AAAA,IACJ;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,WAAK,QAAQ,SAAQ;AACrB,eAAQ,SAAS,KAAK,KAAK,IAAI;AAAA,IACnC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,KAAK;AACT,SAAK,aAAa,SAAS,KAAK,KAAK,GAAG;AACxC,SAAK,QAAQ,IAAI;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,WAAW;AACf,QAAI,gBAAgB,OAAO,KAAK,OAAO,SAAS,KAAK,KAAK;AACtD;AAAA,IACJ;AACA,SAAK,IAAI,qBAAqB;AAC9B,QAAI,WAAW;AACX,UAAI;AACA,aAAK,IAAI,MAAM;AAAA,MACnB,SACO,GAAG;AAAA,MAAE;AAAA,IAChB;AACA,QAAI,OAAO,aAAa,aAAa;AACjC,aAAO,SAAQ,SAAS,KAAK,KAAK;AAAA,IACtC;AACA,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,UAAM,OAAO,KAAK,IAAI;AACtB,QAAI,SAAS,MAAM;AACf,WAAK,aAAa,QAAQ,IAAI;AAC9B,WAAK,aAAa,SAAS;AAC3B,WAAK,QAAQ;AAAA,IACjB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACJ,SAAK,QAAQ;AAAA,EACjB;AACJ;AACA,QAAQ,gBAAgB;AACxB,QAAQ,WAAW,CAAC;AAMpB,IAAI,OAAO,aAAa,aAAa;AAEjC,MAAI,OAAO,gBAAgB,YAAY;AAEnC,gBAAY,YAAY,aAAa;AAAA,EACzC,WACS,OAAO,qBAAqB,YAAY;AAC7C,UAAM,mBAAmB,gBAAgB,iBAAa,aAAa;AACnE,qBAAiB,kBAAkB,eAAe,KAAK;AAAA,EAC3D;AACJ;AACA,SAAS,gBAAgB;AACrB,WAASD,MAAK,QAAQ,UAAU;AAC5B,QAAI,QAAQ,SAAS,eAAeA,EAAC,GAAG;AACpC,cAAQ,SAASA,EAAC,EAAE,MAAM;AAAA,IAC9B;AAAA,EACJ;AACJ;;;ACpYO,IAAM,YAAY,MAAM;AAC3B,QAAM,qBAAqB,OAAO,YAAY,cAAc,OAAO,QAAQ,YAAY;AACvF,MAAI,oBAAoB;AACpB,WAAO,CAAC,OAAO,QAAQ,QAAQ,EAAE,KAAK,EAAE;AAAA,EAC5C,OACK;AACD,WAAO,CAAC,IAAI,iBAAiB,aAAa,IAAI,CAAC;AAAA,EACnD;AACJ,GAAG;AACI,IAAM,YAAY,eAAW,aAAa,eAAW;AACrD,IAAM,wBAAwB;AAC9B,IAAM,oBAAoB;;;ACNjC,IAAM,gBAAgB,OAAO,cAAc,eACvC,OAAO,UAAU,YAAY,YAC7B,UAAU,QAAQ,YAAY,MAAM;AACjC,IAAM,KAAN,cAAiBE,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO9B,YAAY,MAAM;AACd,UAAM,IAAI;AACV,SAAK,iBAAiB,CAAC,KAAK;AAAA,EAChC;AAAA,EACA,IAAI,OAAO;AACP,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,QAAI,CAAC,KAAK,MAAM,GAAG;AAEf;AAAA,IACJ;AACA,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,YAAY,KAAK,KAAK;AAE5B,UAAM,OAAO,gBACP,CAAC,IACD,KAAK,KAAK,MAAM,SAAS,qBAAqB,OAAO,OAAO,cAAc,QAAQ,MAAM,WAAW,sBAAsB,gBAAgB,mBAAmB,UAAU,cAAc,UAAU,qBAAqB;AACzN,QAAI,KAAK,KAAK,cAAc;AACxB,WAAK,UAAU,KAAK,KAAK;AAAA,IAC7B;AACA,QAAI;AACA,WAAK,KACD,yBAAyB,CAAC,gBACpB,YACI,IAAI,UAAU,KAAK,SAAS,IAC5B,IAAI,UAAU,GAAG,IACrB,IAAI,UAAU,KAAK,WAAW,IAAI;AAAA,IAChD,SACO,KAAK;AACR,aAAO,KAAK,aAAa,SAAS,GAAG;AAAA,IACzC;AACA,SAAK,GAAG,aAAa,KAAK,OAAO;AACjC,SAAK,kBAAkB;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,oBAAoB;AAChB,SAAK,GAAG,SAAS,MAAM;AACnB,UAAI,KAAK,KAAK,WAAW;AACrB,aAAK,GAAG,QAAQ,MAAM;AAAA,MAC1B;AACA,WAAK,OAAO;AAAA,IAChB;AACA,SAAK,GAAG,UAAU,CAAC,eAAe,KAAK,QAAQ;AAAA,MAC3C,aAAa;AAAA,MACb,SAAS;AAAA,IACb,CAAC;AACD,SAAK,GAAG,YAAY,CAAC,OAAO,KAAK,OAAO,GAAG,IAAI;AAC/C,SAAK,GAAG,UAAU,CAAC,MAAM,KAAK,QAAQ,mBAAmB,CAAC;AAAA,EAC9D;AAAA,EACA,MAAM,SAAS;AACX,SAAK,WAAW;AAGhB,aAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACrC,YAAM,SAAS,QAAQA,EAAC;AACxB,YAAM,aAAaA,OAAM,QAAQ,SAAS;AAC1C,mBAAa,QAAQ,KAAK,gBAAgB,CAAC,SAAS;AAEhD,cAAM,OAAO,CAAC;AACd,YAAI,CAAC,uBAAuB;AACxB,cAAI,OAAO,SAAS;AAChB,iBAAK,WAAW,OAAO,QAAQ;AAAA,UACnC;AACA,cAAI,KAAK,KAAK,mBAAmB;AAC7B,kBAAM;AAAA;AAAA,cAEN,aAAa,OAAO,OAAO,OAAO,WAAW,IAAI,IAAI,KAAK;AAAA;AAC1D,gBAAI,MAAM,KAAK,KAAK,kBAAkB,WAAW;AAC7C,mBAAK,WAAW;AAAA,YACpB;AAAA,UACJ;AAAA,QACJ;AAIA,YAAI;AACA,cAAI,uBAAuB;AAEvB,iBAAK,GAAG,KAAK,IAAI;AAAA,UACrB,OACK;AACD,iBAAK,GAAG,KAAK,MAAM,IAAI;AAAA,UAC3B;AAAA,QACJ,SACO,GAAG;AAAA,QACV;AACA,YAAI,YAAY;AAGZ,mBAAS,MAAM;AACX,iBAAK,WAAW;AAChB,iBAAK,aAAa,OAAO;AAAA,UAC7B,GAAG,KAAK,YAAY;AAAA,QACxB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI,OAAO,KAAK,OAAO,aAAa;AAChC,WAAK,GAAG,MAAM;AACd,WAAK,KAAK;AAAA,IACd;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAM;AACF,UAAM,SAAS,KAAK,KAAK,SAAS,QAAQ;AAC1C,UAAM,QAAQ,KAAK,SAAS,CAAC;AAE7B,QAAI,KAAK,KAAK,mBAAmB;AAC7B,YAAM,KAAK,KAAK,cAAc,IAAI,MAAM;AAAA,IAC5C;AAEA,QAAI,CAAC,KAAK,gBAAgB;AACtB,YAAM,MAAM;AAAA,IAChB;AACA,WAAO,KAAK,UAAU,QAAQ,KAAK;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACJ,WAAO,CAAC,CAAC;AAAA,EACb;AACJ;;;ACpJO,IAAM,KAAN,cAAiBC,WAAU;AAAA,EAC9B,IAAI,OAAO;AACP,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AAEL,QAAI,OAAO,iBAAiB,YAAY;AACpC;AAAA,IACJ;AAEA,SAAK,YAAY,IAAI,aAAa,KAAK,UAAU,OAAO,GAAG,KAAK,KAAK,iBAAiB,KAAK,IAAI,CAAC;AAChG,SAAK,UAAU,OACV,KAAK,MAAM;AACZ,WAAK,QAAQ;AAAA,IACjB,CAAC,EACI,MAAM,CAAC,QAAQ;AAChB,WAAK,QAAQ,sBAAsB,GAAG;AAAA,IAC1C,CAAC;AAED,SAAK,UAAU,MAAM,KAAK,MAAM;AAC5B,WAAK,UAAU,0BAA0B,EAAE,KAAK,CAAC,WAAW;AACxD,cAAM,gBAAgB,0BAA0B,OAAO,kBAAkB,KAAK,OAAO,UAAU;AAC/F,cAAM,SAAS,OAAO,SAAS,YAAY,aAAa,EAAE,UAAU;AACpE,cAAM,gBAAgB,0BAA0B;AAChD,sBAAc,SAAS,OAAO,OAAO,QAAQ;AAC7C,aAAK,SAAS,cAAc,SAAS,UAAU;AAC/C,cAAM,OAAO,MAAM;AACf,iBACK,KAAK,EACL,KAAK,CAAC,EAAE,MAAM,OAAAC,OAAM,MAAM;AAC3B,gBAAI,MAAM;AACN;AAAA,YACJ;AACA,iBAAK,SAASA,MAAK;AACnB,iBAAK;AAAA,UACT,CAAC,EACI,MAAM,CAAC,QAAQ;AAAA,UACpB,CAAC;AAAA,QACL;AACA,aAAK;AACL,cAAM,SAAS,EAAE,MAAM,OAAO;AAC9B,YAAI,KAAK,MAAM,KAAK;AAChB,iBAAO,OAAO,WAAW,KAAK,MAAM,GAAG;AAAA,QAC3C;AACA,aAAK,OAAO,MAAM,MAAM,EAAE,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA,MACtD,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA,EACA,MAAM,SAAS;AACX,SAAK,WAAW;AAChB,aAASC,KAAI,GAAGA,KAAI,QAAQ,QAAQA,MAAK;AACrC,YAAM,SAAS,QAAQA,EAAC;AACxB,YAAM,aAAaA,OAAM,QAAQ,SAAS;AAC1C,WAAK,OAAO,MAAM,MAAM,EAAE,KAAK,MAAM;AACjC,YAAI,YAAY;AACZ,mBAAS,MAAM;AACX,iBAAK,WAAW;AAChB,iBAAK,aAAa,OAAO;AAAA,UAC7B,GAAG,KAAK,YAAY;AAAA,QACxB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,UAAU;AACN,QAAI;AACJ,KAAC,KAAK,KAAK,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,MAAM;AAAA,EACxE;AACJ;;;ACnEO,IAAM,aAAa;AAAA,EACtB,WAAW;AAAA,EACX,cAAc;AAAA,EACd,SAAS;AACb;;;ACYA,IAAM,KAAK;AACX,IAAM,QAAQ;AAAA,EACV;AAAA,EAAU;AAAA,EAAY;AAAA,EAAa;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAQ;AAAA,EAAY;AAAA,EAAQ;AAAA,EAAa;AAAA,EAAQ;AAAA,EAAS;AACzI;AACO,SAAS,MAAM,KAAK;AACvB,MAAI,IAAI,SAAS,KAAM;AACnB,UAAM;AAAA,EACV;AACA,QAAM,MAAM,KAAK,IAAI,IAAI,QAAQ,GAAG,GAAG,IAAI,IAAI,QAAQ,GAAG;AAC1D,MAAI,KAAK,MAAM,KAAK,IAAI;AACpB,UAAM,IAAI,UAAU,GAAG,CAAC,IAAI,IAAI,UAAU,GAAG,CAAC,EAAE,QAAQ,MAAM,GAAG,IAAI,IAAI,UAAU,GAAG,IAAI,MAAM;AAAA,EACpG;AACA,MAAI,IAAI,GAAG,KAAK,OAAO,EAAE,GAAG,MAAM,CAAC,GAAGC,KAAI;AAC1C,SAAOA,MAAK;AACR,QAAI,MAAMA,EAAC,CAAC,IAAI,EAAEA,EAAC,KAAK;AAAA,EAC5B;AACA,MAAI,KAAK,MAAM,KAAK,IAAI;AACpB,QAAI,SAAS;AACb,QAAI,OAAO,IAAI,KAAK,UAAU,GAAG,IAAI,KAAK,SAAS,CAAC,EAAE,QAAQ,MAAM,GAAG;AACvE,QAAI,YAAY,IAAI,UAAU,QAAQ,KAAK,EAAE,EAAE,QAAQ,KAAK,EAAE,EAAE,QAAQ,MAAM,GAAG;AACjF,QAAI,UAAU;AAAA,EAClB;AACA,MAAI,YAAY,UAAU,KAAK,IAAI,MAAM,CAAC;AAC1C,MAAI,WAAW,SAAS,KAAK,IAAI,OAAO,CAAC;AACzC,SAAO;AACX;AACA,SAAS,UAAU,KAAK,MAAM;AAC1B,QAAM,OAAO,YAAY,QAAQ,KAAK,QAAQ,MAAM,GAAG,EAAE,MAAM,GAAG;AAClE,MAAI,KAAK,MAAM,GAAG,CAAC,KAAK,OAAO,KAAK,WAAW,GAAG;AAC9C,UAAM,OAAO,GAAG,CAAC;AAAA,EACrB;AACA,MAAI,KAAK,MAAM,EAAE,KAAK,KAAK;AACvB,UAAM,OAAO,MAAM,SAAS,GAAG,CAAC;AAAA,EACpC;AACA,SAAO;AACX;AACA,SAAS,SAAS,KAAK,OAAO;AAC1B,QAAM,OAAO,CAAC;AACd,QAAM,QAAQ,6BAA6B,SAAU,IAAI,IAAI,IAAI;AAC7D,QAAI,IAAI;AACJ,WAAK,EAAE,IAAI;AAAA,IACf;AAAA,EACJ,CAAC;AACD,SAAO;AACX;;;ACxDO,IAAM,SAAN,MAAM,gBAAe,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhC,YAAY,KAAK,OAAO,CAAC,GAAG;AACxB,UAAM;AACN,SAAK,aAAa;AAClB,SAAK,cAAc,CAAC;AACpB,QAAI,OAAO,aAAa,OAAO,KAAK;AAChC,aAAO;AACP,YAAM;AAAA,IACV;AACA,QAAI,KAAK;AACL,YAAM,MAAM,GAAG;AACf,WAAK,WAAW,IAAI;AACpB,WAAK,SAAS,IAAI,aAAa,WAAW,IAAI,aAAa;AAC3D,WAAK,OAAO,IAAI;AAChB,UAAI,IAAI;AACJ,aAAK,QAAQ,IAAI;AAAA,IACzB,WACS,KAAK,MAAM;AAChB,WAAK,WAAW,MAAM,KAAK,IAAI,EAAE;AAAA,IACrC;AACA,0BAAsB,MAAM,IAAI;AAChC,SAAK,SACD,QAAQ,KAAK,SACP,KAAK,SACL,OAAO,aAAa,eAAe,aAAa,SAAS;AACnE,QAAI,KAAK,YAAY,CAAC,KAAK,MAAM;AAE7B,WAAK,OAAO,KAAK,SAAS,QAAQ;AAAA,IACtC;AACA,SAAK,WACD,KAAK,aACA,OAAO,aAAa,cAAc,SAAS,WAAW;AAC/D,SAAK,OACD,KAAK,SACA,OAAO,aAAa,eAAe,SAAS,OACvC,SAAS,OACT,KAAK,SACD,QACA;AAClB,SAAK,aAAa,KAAK,cAAc;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,SAAK,cAAc,CAAC;AACpB,SAAK,gBAAgB;AACrB,SAAK,OAAO,OAAO,OAAO;AAAA,MACtB,MAAM;AAAA,MACN,OAAO;AAAA,MACP,iBAAiB;AAAA,MACjB,SAAS;AAAA,MACT,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,kBAAkB;AAAA,MAClB,oBAAoB;AAAA,MACpB,mBAAmB;AAAA,QACf,WAAW;AAAA,MACf;AAAA,MACA,kBAAkB,CAAC;AAAA,MACnB,qBAAqB;AAAA,IACzB,GAAG,IAAI;AACP,SAAK,KAAK,OACN,KAAK,KAAK,KAAK,QAAQ,OAAO,EAAE,KAC3B,KAAK,KAAK,mBAAmB,MAAM;AAC5C,QAAI,OAAO,KAAK,KAAK,UAAU,UAAU;AACrC,WAAK,KAAK,QAAQC,QAAO,KAAK,KAAK,KAAK;AAAA,IAC5C;AAEA,SAAK,KAAK;AACV,SAAK,WAAW;AAChB,SAAK,eAAe;AACpB,SAAK,cAAc;AAEnB,SAAK,mBAAmB;AACxB,QAAI,OAAO,qBAAqB,YAAY;AACxC,UAAI,KAAK,KAAK,qBAAqB;AAI/B,aAAK,4BAA4B,MAAM;AACnC,cAAI,KAAK,WAAW;AAEhB,iBAAK,UAAU,mBAAmB;AAClC,iBAAK,UAAU,MAAM;AAAA,UACzB;AAAA,QACJ;AACA,yBAAiB,gBAAgB,KAAK,2BAA2B,KAAK;AAAA,MAC1E;AACA,UAAI,KAAK,aAAa,aAAa;AAC/B,aAAK,uBAAuB,MAAM;AAC9B,eAAK,QAAQ,mBAAmB;AAAA,YAC5B,aAAa;AAAA,UACjB,CAAC;AAAA,QACL;AACA,yBAAiB,WAAW,KAAK,sBAAsB,KAAK;AAAA,MAChE;AAAA,IACJ;AACA,SAAK,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,gBAAgB,MAAM;AAClB,UAAM,QAAQ,OAAO,OAAO,CAAC,GAAG,KAAK,KAAK,KAAK;AAE/C,UAAM,MAAM;AAEZ,UAAM,YAAY;AAElB,QAAI,KAAK;AACL,YAAM,MAAM,KAAK;AACrB,UAAM,OAAO,OAAO,OAAO,CAAC,GAAG,KAAK,MAAM;AAAA,MACtC;AAAA,MACA,QAAQ;AAAA,MACR,UAAU,KAAK;AAAA,MACf,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK;AAAA,IACf,GAAG,KAAK,KAAK,iBAAiB,IAAI,CAAC;AACnC,WAAO,IAAI,WAAW,IAAI,EAAE,IAAI;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO;AACH,QAAI;AACJ,QAAI,KAAK,KAAK,mBACV,QAAO,yBACP,KAAK,WAAW,QAAQ,WAAW,MAAM,IAAI;AAC7C,kBAAY;AAAA,IAChB,WACS,MAAM,KAAK,WAAW,QAAQ;AAEnC,WAAK,aAAa,MAAM;AACpB,aAAK,aAAa,SAAS,yBAAyB;AAAA,MACxD,GAAG,CAAC;AACJ;AAAA,IACJ,OACK;AACD,kBAAY,KAAK,WAAW,CAAC;AAAA,IACjC;AACA,SAAK,aAAa;AAElB,QAAI;AACA,kBAAY,KAAK,gBAAgB,SAAS;AAAA,IAC9C,SACO,GAAG;AACN,WAAK,WAAW,MAAM;AACtB,WAAK,KAAK;AACV;AAAA,IACJ;AACA,cAAU,KAAK;AACf,SAAK,aAAa,SAAS;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,WAAW;AACpB,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,mBAAmB;AAAA,IACtC;AAEA,SAAK,YAAY;AAEjB,cACK,GAAG,SAAS,KAAK,QAAQ,KAAK,IAAI,CAAC,EACnC,GAAG,UAAU,KAAK,SAAS,KAAK,IAAI,CAAC,EACrC,GAAG,SAAS,KAAK,QAAQ,KAAK,IAAI,CAAC,EACnC,GAAG,SAAS,CAAC,WAAW,KAAK,QAAQ,mBAAmB,MAAM,CAAC;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,MAAM;AACR,QAAI,YAAY,KAAK,gBAAgB,IAAI;AACzC,QAAI,SAAS;AACb,YAAO,wBAAwB;AAC/B,UAAM,kBAAkB,MAAM;AAC1B,UAAI;AACA;AACJ,gBAAU,KAAK,CAAC,EAAE,MAAM,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAChD,gBAAU,KAAK,UAAU,CAAC,QAAQ;AAC9B,YAAI;AACA;AACJ,YAAI,WAAW,IAAI,QAAQ,YAAY,IAAI,MAAM;AAC7C,eAAK,YAAY;AACjB,eAAK,aAAa,aAAa,SAAS;AACxC,cAAI,CAAC;AACD;AACJ,kBAAO,wBAAwB,gBAAgB,UAAU;AACzD,eAAK,UAAU,MAAM,MAAM;AACvB,gBAAI;AACA;AACJ,gBAAI,aAAa,KAAK;AAClB;AACJ,oBAAQ;AACR,iBAAK,aAAa,SAAS;AAC3B,sBAAU,KAAK,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC;AACpC,iBAAK,aAAa,WAAW,SAAS;AACtC,wBAAY;AACZ,iBAAK,YAAY;AACjB,iBAAK,MAAM;AAAA,UACf,CAAC;AAAA,QACL,OACK;AACD,gBAAM,MAAM,IAAI,MAAM,aAAa;AAEnC,cAAI,YAAY,UAAU;AAC1B,eAAK,aAAa,gBAAgB,GAAG;AAAA,QACzC;AAAA,MACJ,CAAC;AAAA,IACL;AACA,aAAS,kBAAkB;AACvB,UAAI;AACA;AAEJ,eAAS;AACT,cAAQ;AACR,gBAAU,MAAM;AAChB,kBAAY;AAAA,IAChB;AAEA,UAAM,UAAU,CAAC,QAAQ;AACrB,YAAMC,SAAQ,IAAI,MAAM,kBAAkB,GAAG;AAE7C,MAAAA,OAAM,YAAY,UAAU;AAC5B,sBAAgB;AAChB,WAAK,aAAa,gBAAgBA,MAAK;AAAA,IAC3C;AACA,aAAS,mBAAmB;AACxB,cAAQ,kBAAkB;AAAA,IAC9B;AAEA,aAAS,UAAU;AACf,cAAQ,eAAe;AAAA,IAC3B;AAEA,aAAS,UAAU,IAAI;AACnB,UAAI,aAAa,GAAG,SAAS,UAAU,MAAM;AACzC,wBAAgB;AAAA,MACpB;AAAA,IACJ;AAEA,UAAM,UAAU,MAAM;AAClB,gBAAU,eAAe,QAAQ,eAAe;AAChD,gBAAU,eAAe,SAAS,OAAO;AACzC,gBAAU,eAAe,SAAS,gBAAgB;AAClD,WAAK,IAAI,SAAS,OAAO;AACzB,WAAK,IAAI,aAAa,SAAS;AAAA,IACnC;AACA,cAAU,KAAK,QAAQ,eAAe;AACtC,cAAU,KAAK,SAAS,OAAO;AAC/B,cAAU,KAAK,SAAS,gBAAgB;AACxC,SAAK,KAAK,SAAS,OAAO;AAC1B,SAAK,KAAK,aAAa,SAAS;AAChC,QAAI,KAAK,SAAS,QAAQ,cAAc,MAAM,MAC1C,SAAS,gBAAgB;AAEzB,WAAK,aAAa,MAAM;AACpB,YAAI,CAAC,QAAQ;AACT,oBAAU,KAAK;AAAA,QACnB;AAAA,MACJ,GAAG,GAAG;AAAA,IACV,OACK;AACD,gBAAU,KAAK;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,SAAK,aAAa;AAClB,YAAO,wBAAwB,gBAAgB,KAAK,UAAU;AAC9D,SAAK,aAAa,MAAM;AACxB,SAAK,MAAM;AAGX,QAAI,WAAW,KAAK,cAAc,KAAK,KAAK,SAAS;AACjD,UAAIC,KAAI;AACR,YAAM,IAAI,KAAK,SAAS;AACxB,aAAOA,KAAI,GAAGA,MAAK;AACf,aAAK,MAAM,KAAK,SAASA,EAAC,CAAC;AAAA,MAC/B;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS,QAAQ;AACb,QAAI,cAAc,KAAK,cACnB,WAAW,KAAK,cAChB,cAAc,KAAK,YAAY;AAC/B,WAAK,aAAa,UAAU,MAAM;AAElC,WAAK,aAAa,WAAW;AAC7B,WAAK,iBAAiB;AACtB,cAAQ,OAAO,MAAM;AAAA,QACjB,KAAK;AACD,eAAK,YAAY,KAAK,MAAM,OAAO,IAAI,CAAC;AACxC;AAAA,QACJ,KAAK;AACD,eAAK,WAAW,MAAM;AACtB,eAAK,aAAa,MAAM;AACxB,eAAK,aAAa,MAAM;AACxB;AAAA,QACJ,KAAK;AACD,gBAAM,MAAM,IAAI,MAAM,cAAc;AAEpC,cAAI,OAAO,OAAO;AAClB,eAAK,QAAQ,GAAG;AAChB;AAAA,QACJ,KAAK;AACD,eAAK,aAAa,QAAQ,OAAO,IAAI;AACrC,eAAK,aAAa,WAAW,OAAO,IAAI;AACxC;AAAA,MACR;AAAA,IACJ,OACK;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,MAAM;AACd,SAAK,aAAa,aAAa,IAAI;AACnC,SAAK,KAAK,KAAK;AACf,SAAK,UAAU,MAAM,MAAM,KAAK;AAChC,SAAK,WAAW,KAAK,eAAe,KAAK,QAAQ;AACjD,SAAK,eAAe,KAAK;AACzB,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa,KAAK;AACvB,SAAK,OAAO;AAEZ,QAAI,aAAa,KAAK;AAClB;AACJ,SAAK,iBAAiB;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,mBAAmB;AACf,SAAK,eAAe,KAAK,gBAAgB;AACzC,SAAK,mBAAmB,KAAK,aAAa,MAAM;AAC5C,WAAK,QAAQ,cAAc;AAAA,IAC/B,GAAG,KAAK,eAAe,KAAK,WAAW;AACvC,QAAI,KAAK,KAAK,WAAW;AACrB,WAAK,iBAAiB,MAAM;AAAA,IAChC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACN,SAAK,YAAY,OAAO,GAAG,KAAK,aAAa;AAI7C,SAAK,gBAAgB;AACrB,QAAI,MAAM,KAAK,YAAY,QAAQ;AAC/B,WAAK,aAAa,OAAO;AAAA,IAC7B,OACK;AACD,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACJ,QAAI,aAAa,KAAK,cAClB,KAAK,UAAU,YACf,CAAC,KAAK,aACN,KAAK,YAAY,QAAQ;AACzB,YAAM,UAAU,KAAK,mBAAmB;AACxC,WAAK,UAAU,KAAK,OAAO;AAG3B,WAAK,gBAAgB,QAAQ;AAC7B,WAAK,aAAa,OAAO;AAAA,IAC7B;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,qBAAqB;AACjB,UAAM,yBAAyB,KAAK,cAChC,KAAK,UAAU,SAAS,aACxB,KAAK,YAAY,SAAS;AAC9B,QAAI,CAAC,wBAAwB;AACzB,aAAO,KAAK;AAAA,IAChB;AACA,QAAI,cAAc;AAClB,aAASA,KAAI,GAAGA,KAAI,KAAK,YAAY,QAAQA,MAAK;AAC9C,YAAM,OAAO,KAAK,YAAYA,EAAC,EAAE;AACjC,UAAI,MAAM;AACN,uBAAe,WAAW,IAAI;AAAA,MAClC;AACA,UAAIA,KAAI,KAAK,cAAc,KAAK,YAAY;AACxC,eAAO,KAAK,YAAY,MAAM,GAAGA,EAAC;AAAA,MACtC;AACA,qBAAe;AAAA,IACnB;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,MAAM,KAAK,SAAS,IAAI;AACpB,SAAK,WAAW,WAAW,KAAK,SAAS,EAAE;AAC3C,WAAO;AAAA,EACX;AAAA,EACA,KAAK,KAAK,SAAS,IAAI;AACnB,SAAK,WAAW,WAAW,KAAK,SAAS,EAAE;AAC3C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,MAAM,MAAM,SAAS,IAAI;AAChC,QAAI,eAAe,OAAO,MAAM;AAC5B,WAAK;AACL,aAAO;AAAA,IACX;AACA,QAAI,eAAe,OAAO,SAAS;AAC/B,WAAK;AACL,gBAAU;AAAA,IACd;AACA,QAAI,cAAc,KAAK,cAAc,aAAa,KAAK,YAAY;AAC/D;AAAA,IACJ;AACA,cAAU,WAAW,CAAC;AACtB,YAAQ,WAAW,UAAU,QAAQ;AACrC,UAAM,SAAS;AAAA,MACX;AAAA,MACA;AAAA,MACA;AAAA,IACJ;AACA,SAAK,aAAa,gBAAgB,MAAM;AACxC,SAAK,YAAY,KAAK,MAAM;AAC5B,QAAI;AACA,WAAK,KAAK,SAAS,EAAE;AACzB,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ;AACJ,UAAM,QAAQ,MAAM;AAChB,WAAK,QAAQ,cAAc;AAC3B,WAAK,UAAU,MAAM;AAAA,IACzB;AACA,UAAM,kBAAkB,MAAM;AAC1B,WAAK,IAAI,WAAW,eAAe;AACnC,WAAK,IAAI,gBAAgB,eAAe;AACxC,YAAM;AAAA,IACV;AACA,UAAM,iBAAiB,MAAM;AAEzB,WAAK,KAAK,WAAW,eAAe;AACpC,WAAK,KAAK,gBAAgB,eAAe;AAAA,IAC7C;AACA,QAAI,cAAc,KAAK,cAAc,WAAW,KAAK,YAAY;AAC7D,WAAK,aAAa;AAClB,UAAI,KAAK,YAAY,QAAQ;AACzB,aAAK,KAAK,SAAS,MAAM;AACrB,cAAI,KAAK,WAAW;AAChB,2BAAe;AAAA,UACnB,OACK;AACD,kBAAM;AAAA,UACV;AAAA,QACJ,CAAC;AAAA,MACL,WACS,KAAK,WAAW;AACrB,uBAAe;AAAA,MACnB,OACK;AACD,cAAM;AAAA,MACV;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,KAAK;AACT,YAAO,wBAAwB;AAC/B,SAAK,aAAa,SAAS,GAAG;AAC9B,SAAK,QAAQ,mBAAmB,GAAG;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,QAAQ,aAAa;AACzB,QAAI,cAAc,KAAK,cACnB,WAAW,KAAK,cAChB,cAAc,KAAK,YAAY;AAE/B,WAAK,eAAe,KAAK,gBAAgB;AAEzC,WAAK,UAAU,mBAAmB,OAAO;AAEzC,WAAK,UAAU,MAAM;AAErB,WAAK,UAAU,mBAAmB;AAClC,UAAI,OAAO,wBAAwB,YAAY;AAC3C,4BAAoB,gBAAgB,KAAK,2BAA2B,KAAK;AACzE,4BAAoB,WAAW,KAAK,sBAAsB,KAAK;AAAA,MACnE;AAEA,WAAK,aAAa;AAElB,WAAK,KAAK;AAEV,WAAK,aAAa,SAAS,QAAQ,WAAW;AAG9C,WAAK,cAAc,CAAC;AACpB,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,eAAe,UAAU;AACrB,UAAM,mBAAmB,CAAC;AAC1B,QAAIA,KAAI;AACR,UAAM,IAAI,SAAS;AACnB,WAAOA,KAAI,GAAGA,MAAK;AACf,UAAI,CAAC,KAAK,WAAW,QAAQ,SAASA,EAAC,CAAC;AACpC,yBAAiB,KAAK,SAASA,EAAC,CAAC;AAAA,IACzC;AACA,WAAO;AAAA,EACX;AACJ;AACA,OAAO,WAAW;;;AC/kBX,IAAMC,YAAW,OAAO;;;ACQxB,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK;AACrC,MAAI,MAAM;AAEV,QAAM,OAAQ,OAAO,aAAa,eAAe;AACjD,MAAI,QAAQ;AACR,UAAM,IAAI,WAAW,OAAO,IAAI;AAEpC,MAAI,OAAO,QAAQ,UAAU;AACzB,QAAI,QAAQ,IAAI,OAAO,CAAC,GAAG;AACvB,UAAI,QAAQ,IAAI,OAAO,CAAC,GAAG;AACvB,cAAM,IAAI,WAAW;AAAA,MACzB,OACK;AACD,cAAM,IAAI,OAAO;AAAA,MACrB;AAAA,IACJ;AACA,QAAI,CAAC,sBAAsB,KAAK,GAAG,GAAG;AAClC,UAAI,gBAAgB,OAAO,KAAK;AAC5B,cAAM,IAAI,WAAW,OAAO;AAAA,MAChC,OACK;AACD,cAAM,aAAa;AAAA,MACvB;AAAA,IACJ;AAEA,UAAM,MAAM,GAAG;AAAA,EACnB;AAEA,MAAI,CAAC,IAAI,MAAM;AACX,QAAI,cAAc,KAAK,IAAI,QAAQ,GAAG;AAClC,UAAI,OAAO;AAAA,IACf,WACS,eAAe,KAAK,IAAI,QAAQ,GAAG;AACxC,UAAI,OAAO;AAAA,IACf;AAAA,EACJ;AACA,MAAI,OAAO,IAAI,QAAQ;AACvB,QAAM,OAAO,IAAI,KAAK,QAAQ,GAAG,MAAM;AACvC,QAAM,OAAO,OAAO,MAAM,IAAI,OAAO,MAAM,IAAI;AAE/C,MAAI,KAAK,IAAI,WAAW,QAAQ,OAAO,MAAM,IAAI,OAAO;AAExD,MAAI,OACA,IAAI,WACA,QACA,QACC,OAAO,IAAI,SAAS,IAAI,OAAO,KAAK,MAAM,IAAI;AACvD,SAAO;AACX;;;AC1DA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAAC;AAAA;;;ACAA,IAAMC,yBAAwB,OAAO,gBAAgB;AACrD,IAAMC,UAAS,CAAC,QAAQ;AACpB,SAAO,OAAO,YAAY,WAAW,aAC/B,YAAY,OAAO,GAAG,IACtB,IAAI,kBAAkB;AAChC;AACA,IAAM,WAAW,OAAO,UAAU;AAClC,IAAMC,kBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,KAAK,IAAI,MAAM;AAChC,IAAM,iBAAiB,OAAO,SAAS,cAClC,OAAO,SAAS,eACb,SAAS,KAAK,IAAI,MAAM;AAMzB,SAAS,SAAS,KAAK;AAC1B,SAASF,2BAA0B,eAAe,eAAeC,QAAO,GAAG,MACtEC,mBAAkB,eAAe,QACjC,kBAAkB,eAAe;AAC1C;AACO,SAAS,UAAU,KAAK,QAAQ;AACnC,MAAI,CAAC,OAAO,OAAO,QAAQ,UAAU;AACjC,WAAO;AAAA,EACX;AACA,MAAI,MAAM,QAAQ,GAAG,GAAG;AACpB,aAASC,KAAI,GAAG,IAAI,IAAI,QAAQA,KAAI,GAAGA,MAAK;AACxC,UAAI,UAAU,IAAIA,EAAC,CAAC,GAAG;AACnB,eAAO;AAAA,MACX;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,MAAI,SAAS,GAAG,GAAG;AACf,WAAO;AAAA,EACX;AACA,MAAI,IAAI,UACJ,OAAO,IAAI,WAAW,cACtB,UAAU,WAAW,GAAG;AACxB,WAAO,UAAU,IAAI,OAAO,GAAG,IAAI;AAAA,EACvC;AACA,aAAW,OAAO,KAAK;AACnB,QAAI,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,KAAK,UAAU,IAAI,GAAG,CAAC,GAAG;AACvE,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;ACzCO,SAAS,kBAAkB,QAAQ;AACtC,QAAM,UAAU,CAAC;AACjB,QAAM,aAAa,OAAO;AAC1B,QAAM,OAAO;AACb,OAAK,OAAO,mBAAmB,YAAY,OAAO;AAClD,OAAK,cAAc,QAAQ;AAC3B,SAAO,EAAE,QAAQ,MAAM,QAAiB;AAC5C;AACA,SAAS,mBAAmB,MAAM,SAAS;AACvC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,SAAS,IAAI,GAAG;AAChB,UAAM,cAAc,EAAE,cAAc,MAAM,KAAK,QAAQ,OAAO;AAC9D,YAAQ,KAAK,IAAI;AACjB,WAAO;AAAA,EACX,WACS,MAAM,QAAQ,IAAI,GAAG;AAC1B,UAAM,UAAU,IAAI,MAAM,KAAK,MAAM;AACrC,aAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AAClC,cAAQA,EAAC,IAAI,mBAAmB,KAAKA,EAAC,GAAG,OAAO;AAAA,IACpD;AACA,WAAO;AAAA,EACX,WACS,OAAO,SAAS,YAAY,EAAE,gBAAgB,OAAO;AAC1D,UAAM,UAAU,CAAC;AACjB,eAAW,OAAO,MAAM;AACpB,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACjD,gBAAQ,GAAG,IAAI,mBAAmB,KAAK,GAAG,GAAG,OAAO;AAAA,MACxD;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,SAAO;AACX;AASO,SAAS,kBAAkB,QAAQ,SAAS;AAC/C,SAAO,OAAO,mBAAmB,OAAO,MAAM,OAAO;AACrD,SAAO,OAAO;AACd,SAAO;AACX;AACA,SAAS,mBAAmB,MAAM,SAAS;AACvC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,QAAQ,KAAK,iBAAiB,MAAM;AACpC,UAAM,eAAe,OAAO,KAAK,QAAQ,YACrC,KAAK,OAAO,KACZ,KAAK,MAAM,QAAQ;AACvB,QAAI,cAAc;AACd,aAAO,QAAQ,KAAK,GAAG;AAAA,IAC3B,OACK;AACD,YAAM,IAAI,MAAM,qBAAqB;AAAA,IACzC;AAAA,EACJ,WACS,MAAM,QAAQ,IAAI,GAAG;AAC1B,aAASA,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AAClC,WAAKA,EAAC,IAAI,mBAAmB,KAAKA,EAAC,GAAG,OAAO;AAAA,IACjD;AAAA,EACJ,WACS,OAAO,SAAS,UAAU;AAC/B,eAAW,OAAO,MAAM;AACpB,UAAI,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AACjD,aAAK,GAAG,IAAI,mBAAmB,KAAK,GAAG,GAAG,OAAO;AAAA,MACrD;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;;;AF5EA,IAAM,kBAAkB;AAAA,EACpB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AACJ;AAMO,IAAMC,YAAW;AACjB,IAAI;AAAA,CACV,SAAUC,aAAY;AACnB,EAAAA,YAAWA,YAAW,SAAS,IAAI,CAAC,IAAI;AACxC,EAAAA,YAAWA,YAAW,YAAY,IAAI,CAAC,IAAI;AAC3C,EAAAA,YAAWA,YAAW,OAAO,IAAI,CAAC,IAAI;AACtC,EAAAA,YAAWA,YAAW,KAAK,IAAI,CAAC,IAAI;AACpC,EAAAA,YAAWA,YAAW,eAAe,IAAI,CAAC,IAAI;AAC9C,EAAAA,YAAWA,YAAW,cAAc,IAAI,CAAC,IAAI;AAC7C,EAAAA,YAAWA,YAAW,YAAY,IAAI,CAAC,IAAI;AAC/C,GAAG,eAAe,aAAa,CAAC,EAAE;AAI3B,IAAM,UAAN,MAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjB,YAAY,UAAU;AAClB,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK;AACR,QAAI,IAAI,SAAS,WAAW,SAAS,IAAI,SAAS,WAAW,KAAK;AAC9D,UAAI,UAAU,GAAG,GAAG;AAChB,eAAO,KAAK,eAAe;AAAA,UACvB,MAAM,IAAI,SAAS,WAAW,QACxB,WAAW,eACX,WAAW;AAAA,UACjB,KAAK,IAAI;AAAA,UACT,MAAM,IAAI;AAAA,UACV,IAAI,IAAI;AAAA,QACZ,CAAC;AAAA,MACL;AAAA,IACJ;AACA,WAAO,CAAC,KAAK,eAAe,GAAG,CAAC;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,KAAK;AAEhB,QAAI,MAAM,KAAK,IAAI;AAEnB,QAAI,IAAI,SAAS,WAAW,gBACxB,IAAI,SAAS,WAAW,YAAY;AACpC,aAAO,IAAI,cAAc;AAAA,IAC7B;AAGA,QAAI,IAAI,OAAO,QAAQ,IAAI,KAAK;AAC5B,aAAO,IAAI,MAAM;AAAA,IACrB;AAEA,QAAI,QAAQ,IAAI,IAAI;AAChB,aAAO,IAAI;AAAA,IACf;AAEA,QAAI,QAAQ,IAAI,MAAM;AAClB,aAAO,KAAK,UAAU,IAAI,MAAM,KAAK,QAAQ;AAAA,IACjD;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,KAAK;AAChB,UAAM,iBAAiB,kBAAkB,GAAG;AAC5C,UAAM,OAAO,KAAK,eAAe,eAAe,MAAM;AACtD,UAAM,UAAU,eAAe;AAC/B,YAAQ,QAAQ,IAAI;AACpB,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,SAASC,QAAO;AACrB,SAAO,OAAO,UAAU,SAAS,KAAKA,MAAK,MAAM;AACrD;AAMO,IAAM,UAAN,MAAM,iBAAgB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,YAAY,SAAS;AACjB,UAAM;AACN,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,KAAK;AACL,QAAI;AACJ,QAAI,OAAO,QAAQ,UAAU;AACzB,UAAI,KAAK,eAAe;AACpB,cAAM,IAAI,MAAM,iDAAiD;AAAA,MACrE;AACA,eAAS,KAAK,aAAa,GAAG;AAC9B,YAAM,gBAAgB,OAAO,SAAS,WAAW;AACjD,UAAI,iBAAiB,OAAO,SAAS,WAAW,YAAY;AACxD,eAAO,OAAO,gBAAgB,WAAW,QAAQ,WAAW;AAE5D,aAAK,gBAAgB,IAAI,oBAAoB,MAAM;AAEnD,YAAI,OAAO,gBAAgB,GAAG;AAC1B,gBAAM,aAAa,WAAW,MAAM;AAAA,QACxC;AAAA,MACJ,OACK;AAED,cAAM,aAAa,WAAW,MAAM;AAAA,MACxC;AAAA,IACJ,WACS,SAAS,GAAG,KAAK,IAAI,QAAQ;AAElC,UAAI,CAAC,KAAK,eAAe;AACrB,cAAM,IAAI,MAAM,kDAAkD;AAAA,MACtE,OACK;AACD,iBAAS,KAAK,cAAc,eAAe,GAAG;AAC9C,YAAI,QAAQ;AAER,eAAK,gBAAgB;AACrB,gBAAM,aAAa,WAAW,MAAM;AAAA,QACxC;AAAA,MACJ;AAAA,IACJ,OACK;AACD,YAAM,IAAI,MAAM,mBAAmB,GAAG;AAAA,IAC1C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,KAAK;AACd,QAAIC,KAAI;AAER,UAAM,IAAI;AAAA,MACN,MAAM,OAAO,IAAI,OAAO,CAAC,CAAC;AAAA,IAC9B;AACA,QAAI,WAAW,EAAE,IAAI,MAAM,QAAW;AAClC,YAAM,IAAI,MAAM,yBAAyB,EAAE,IAAI;AAAA,IACnD;AAEA,QAAI,EAAE,SAAS,WAAW,gBACtB,EAAE,SAAS,WAAW,YAAY;AAClC,YAAM,QAAQA,KAAI;AAClB,aAAO,IAAI,OAAO,EAAEA,EAAC,MAAM,OAAOA,MAAK,IAAI,QAAQ;AAAA,MAAE;AACrD,YAAM,MAAM,IAAI,UAAU,OAAOA,EAAC;AAClC,UAAI,OAAO,OAAO,GAAG,KAAK,IAAI,OAAOA,EAAC,MAAM,KAAK;AAC7C,cAAM,IAAI,MAAM,qBAAqB;AAAA,MACzC;AACA,QAAE,cAAc,OAAO,GAAG;AAAA,IAC9B;AAEA,QAAI,QAAQ,IAAI,OAAOA,KAAI,CAAC,GAAG;AAC3B,YAAM,QAAQA,KAAI;AAClB,aAAO,EAAEA,IAAG;AACR,cAAM,IAAI,IAAI,OAAOA,EAAC;AACtB,YAAI,QAAQ;AACR;AACJ,YAAIA,OAAM,IAAI;AACV;AAAA,MACR;AACA,QAAE,MAAM,IAAI,UAAU,OAAOA,EAAC;AAAA,IAClC,OACK;AACD,QAAE,MAAM;AAAA,IACZ;AAEA,UAAM,OAAO,IAAI,OAAOA,KAAI,CAAC;AAC7B,QAAI,OAAO,QAAQ,OAAO,IAAI,KAAK,MAAM;AACrC,YAAM,QAAQA,KAAI;AAClB,aAAO,EAAEA,IAAG;AACR,cAAM,IAAI,IAAI,OAAOA,EAAC;AACtB,YAAI,QAAQ,KAAK,OAAO,CAAC,KAAK,GAAG;AAC7B,YAAEA;AACF;AAAA,QACJ;AACA,YAAIA,OAAM,IAAI;AACV;AAAA,MACR;AACA,QAAE,KAAK,OAAO,IAAI,UAAU,OAAOA,KAAI,CAAC,CAAC;AAAA,IAC7C;AAEA,QAAI,IAAI,OAAO,EAAEA,EAAC,GAAG;AACjB,YAAM,UAAU,KAAK,SAAS,IAAI,OAAOA,EAAC,CAAC;AAC3C,UAAI,SAAQ,eAAe,EAAE,MAAM,OAAO,GAAG;AACzC,UAAE,OAAO;AAAA,MACb,OACK;AACD,cAAM,IAAI,MAAM,iBAAiB;AAAA,MACrC;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,SAAS,KAAK;AACV,QAAI;AACA,aAAO,KAAK,MAAM,KAAK,KAAK,OAAO;AAAA,IACvC,SACO,GAAG;AACN,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,OAAO,eAAe,MAAM,SAAS;AACjC,YAAQ,MAAM;AAAA,MACV,KAAK,WAAW;AACZ,eAAO,SAAS,OAAO;AAAA,MAC3B,KAAK,WAAW;AACZ,eAAO,YAAY;AAAA,MACvB,KAAK,WAAW;AACZ,eAAO,OAAO,YAAY,YAAY,SAAS,OAAO;AAAA,MAC1D,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AACZ,eAAQ,MAAM,QAAQ,OAAO,MACxB,OAAO,QAAQ,CAAC,MAAM,YAClB,OAAO,QAAQ,CAAC,MAAM,YACnB,gBAAgB,QAAQ,QAAQ,CAAC,CAAC,MAAM;AAAA,MACxD,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AACZ,eAAO,MAAM,QAAQ,OAAO;AAAA,IACpC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,QAAI,KAAK,eAAe;AACpB,WAAK,cAAc,uBAAuB;AAC1C,WAAK,gBAAgB;AAAA,IACzB;AAAA,EACJ;AACJ;AASA,IAAM,sBAAN,MAA0B;AAAA,EACtB,YAAY,QAAQ;AAChB,SAAK,SAAS;AACd,SAAK,UAAU,CAAC;AAChB,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,eAAe,SAAS;AACpB,SAAK,QAAQ,KAAK,OAAO;AACzB,QAAI,KAAK,QAAQ,WAAW,KAAK,UAAU,aAAa;AAEpD,YAAM,SAAS,kBAAkB,KAAK,WAAW,KAAK,OAAO;AAC7D,WAAK,uBAAuB;AAC5B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,yBAAyB;AACrB,SAAK,YAAY;AACjB,SAAK,UAAU,CAAC;AAAA,EACpB;AACJ;;;AGtTO,SAAS,GAAG,KAAK,IAAI,IAAI;AAC5B,MAAI,GAAG,IAAI,EAAE;AACb,SAAO,SAAS,aAAa;AACzB,QAAI,IAAI,IAAI,EAAE;AAAA,EAClB;AACJ;;;ACEA,IAAMC,mBAAkB,OAAO,OAAO;AAAA,EAClC,SAAS;AAAA,EACT,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,eAAe;AAAA;AAAA,EAEf,aAAa;AAAA,EACb,gBAAgB;AACpB,CAAC;AAyBM,IAAMC,UAAN,cAAqB,QAAQ;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAYC,KAAI,KAAK,MAAM;AACvB,UAAM;AAeN,SAAK,YAAY;AAKjB,SAAK,YAAY;AAIjB,SAAK,gBAAgB,CAAC;AAItB,SAAK,aAAa,CAAC;AAOnB,SAAK,SAAS,CAAC;AAKf,SAAK,YAAY;AACjB,SAAK,MAAM;AACX,SAAK,OAAO,CAAC;AACb,SAAK,QAAQ,CAAC;AACd,SAAK,KAAKA;AACV,SAAK,MAAM;AACX,QAAI,QAAQ,KAAK,MAAM;AACnB,WAAK,OAAO,KAAK;AAAA,IACrB;AACA,SAAK,QAAQ,OAAO,OAAO,CAAC,GAAG,IAAI;AACnC,QAAI,KAAK,GAAG;AACR,WAAK,KAAK;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,IAAI,eAAe;AACf,WAAO,CAAC,KAAK;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACR,QAAI,KAAK;AACL;AACJ,UAAMA,MAAK,KAAK;AAChB,SAAK,OAAO;AAAA,MACR,GAAGA,KAAI,QAAQ,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,MACrC,GAAGA,KAAI,UAAU,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,MACzC,GAAGA,KAAI,SAAS,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,MACvC,GAAGA,KAAI,SAAS,KAAK,QAAQ,KAAK,IAAI,CAAC;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,IAAI,SAAS;AACT,WAAO,CAAC,CAAC,KAAK;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,UAAU;AACN,QAAI,KAAK;AACL,aAAO;AACX,SAAK,UAAU;AACf,QAAI,CAAC,KAAK,GAAG,eAAe;AACxB,WAAK,GAAG,KAAK;AACjB,QAAI,WAAW,KAAK,GAAG;AACnB,WAAK,OAAO;AAChB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO;AACH,WAAO,KAAK,QAAQ;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,QAAQ,MAAM;AACV,SAAK,QAAQ,SAAS;AACtB,SAAK,KAAK,MAAM,MAAM,IAAI;AAC1B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkBA,KAAK,OAAO,MAAM;AACd,QAAIF,iBAAgB,eAAe,EAAE,GAAG;AACpC,YAAM,IAAI,MAAM,MAAM,GAAG,SAAS,IAAI,4BAA4B;AAAA,IACtE;AACA,SAAK,QAAQ,EAAE;AACf,QAAI,KAAK,MAAM,WAAW,CAAC,KAAK,MAAM,aAAa,CAAC,KAAK,MAAM,UAAU;AACrE,WAAK,YAAY,IAAI;AACrB,aAAO;AAAA,IACX;AACA,UAAM,SAAS;AAAA,MACX,MAAM,WAAW;AAAA,MACjB,MAAM;AAAA,IACV;AACA,WAAO,UAAU,CAAC;AAClB,WAAO,QAAQ,WAAW,KAAK,MAAM,aAAa;AAElD,QAAI,eAAe,OAAO,KAAK,KAAK,SAAS,CAAC,GAAG;AAC7C,YAAM,KAAK,KAAK;AAChB,YAAM,MAAM,KAAK,IAAI;AACrB,WAAK,qBAAqB,IAAI,GAAG;AACjC,aAAO,KAAK;AAAA,IAChB;AACA,UAAM,sBAAsB,KAAK,GAAG,UAChC,KAAK,GAAG,OAAO,aACf,KAAK,GAAG,OAAO,UAAU;AAC7B,UAAM,gBAAgB,KAAK,MAAM,aAAa,CAAC,uBAAuB,CAAC,KAAK;AAC5E,QAAI,eAAe;AAAA,IACnB,WACS,KAAK,WAAW;AACrB,WAAK,wBAAwB,MAAM;AACnC,WAAK,OAAO,MAAM;AAAA,IACtB,OACK;AACD,WAAK,WAAW,KAAK,MAAM;AAAA,IAC/B;AACA,SAAK,QAAQ,CAAC;AACd,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,qBAAqB,IAAI,KAAK;AAC1B,QAAI;AACJ,UAAM,WAAW,KAAK,KAAK,MAAM,aAAa,QAAQ,OAAO,SAAS,KAAK,KAAK,MAAM;AACtF,QAAI,YAAY,QAAW;AACvB,WAAK,KAAK,EAAE,IAAI;AAChB;AAAA,IACJ;AAEA,UAAM,QAAQ,KAAK,GAAG,aAAa,MAAM;AACrC,aAAO,KAAK,KAAK,EAAE;AACnB,eAASG,KAAI,GAAGA,KAAI,KAAK,WAAW,QAAQA,MAAK;AAC7C,YAAI,KAAK,WAAWA,EAAC,EAAE,OAAO,IAAI;AAC9B,eAAK,WAAW,OAAOA,IAAG,CAAC;AAAA,QAC/B;AAAA,MACJ;AACA,UAAI,KAAK,MAAM,IAAI,MAAM,yBAAyB,CAAC;AAAA,IACvD,GAAG,OAAO;AACV,SAAK,KAAK,EAAE,IAAI,IAAI,SAAS;AAEzB,WAAK,GAAG,eAAe,KAAK;AAC5B,UAAI,MAAM,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;AAAA,IACnC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,YAAY,OAAO,MAAM;AAErB,UAAM,UAAU,KAAK,MAAM,YAAY,UAAa,KAAK,MAAM,eAAe;AAC9E,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpC,WAAK,KAAK,CAAC,MAAM,SAAS;AACtB,YAAI,SAAS;AACT,iBAAO,OAAO,OAAO,IAAI,IAAI,QAAQ,IAAI;AAAA,QAC7C,OACK;AACD,iBAAO,QAAQ,IAAI;AAAA,QACvB;AAAA,MACJ,CAAC;AACD,WAAK,KAAK,IAAI,GAAG,IAAI;AAAA,IACzB,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM;AACd,QAAI;AACJ,QAAI,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,YAAY;AAC7C,YAAM,KAAK,IAAI;AAAA,IACnB;AACA,UAAM,SAAS;AAAA,MACX,IAAI,KAAK;AAAA,MACT,UAAU;AAAA,MACV,SAAS;AAAA,MACT;AAAA,MACA,OAAO,OAAO,OAAO,EAAE,WAAW,KAAK,GAAG,KAAK,KAAK;AAAA,IACxD;AACA,SAAK,KAAK,CAAC,QAAQ,iBAAiB;AAChC,UAAI,WAAW,KAAK,OAAO,CAAC,GAAG;AAE3B;AAAA,MACJ;AACA,YAAM,WAAW,QAAQ;AACzB,UAAI,UAAU;AACV,YAAI,OAAO,WAAW,KAAK,MAAM,SAAS;AACtC,eAAK,OAAO,MAAM;AAClB,cAAI,KAAK;AACL,gBAAI,GAAG;AAAA,UACX;AAAA,QACJ;AAAA,MACJ,OACK;AACD,aAAK,OAAO,MAAM;AAClB,YAAI,KAAK;AACL,cAAI,MAAM,GAAG,YAAY;AAAA,QAC7B;AAAA,MACJ;AACA,aAAO,UAAU;AACjB,aAAO,KAAK,YAAY;AAAA,IAC5B,CAAC;AACD,SAAK,OAAO,KAAK,MAAM;AACvB,SAAK,YAAY;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,QAAQ,OAAO;AACvB,QAAI,CAAC,KAAK,aAAa,KAAK,OAAO,WAAW,GAAG;AAC7C;AAAA,IACJ;AACA,UAAM,SAAS,KAAK,OAAO,CAAC;AAC5B,QAAI,OAAO,WAAW,CAAC,OAAO;AAC1B;AAAA,IACJ;AACA,WAAO,UAAU;AACjB,WAAO;AACP,SAAK,QAAQ,OAAO;AACpB,SAAK,KAAK,MAAM,MAAM,OAAO,IAAI;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ;AACX,WAAO,MAAM,KAAK;AAClB,SAAK,GAAG,QAAQ,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,QAAI,OAAO,KAAK,QAAQ,YAAY;AAChC,WAAK,KAAK,CAAC,SAAS;AAChB,aAAK,mBAAmB,IAAI;AAAA,MAChC,CAAC;AAAA,IACL,OACK;AACD,WAAK,mBAAmB,KAAK,IAAI;AAAA,IACrC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,mBAAmB,MAAM;AACrB,SAAK,OAAO;AAAA,MACR,MAAM,WAAW;AAAA,MACjB,MAAM,KAAK,OACL,OAAO,OAAO,EAAE,KAAK,KAAK,MAAM,QAAQ,KAAK,YAAY,GAAG,IAAI,IAChE;AAAA,IACV,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,KAAK;AACT,QAAI,CAAC,KAAK,WAAW;AACjB,WAAK,aAAa,iBAAiB,GAAG;AAAA,IAC1C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,QAAQ,QAAQ,aAAa;AACzB,SAAK,YAAY;AACjB,WAAO,KAAK;AACZ,SAAK,aAAa,cAAc,QAAQ,WAAW;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,QAAQ;AACb,UAAM,gBAAgB,OAAO,QAAQ,KAAK;AAC1C,QAAI,CAAC;AACD;AACJ,YAAQ,OAAO,MAAM;AAAA,MACjB,KAAK,WAAW;AACZ,YAAI,OAAO,QAAQ,OAAO,KAAK,KAAK;AAChC,eAAK,UAAU,OAAO,KAAK,KAAK,OAAO,KAAK,GAAG;AAAA,QACnD,OACK;AACD,eAAK,aAAa,iBAAiB,IAAI,MAAM,2LAA2L,CAAC;AAAA,QAC7O;AACA;AAAA,MACJ,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AACZ,aAAK,QAAQ,MAAM;AACnB;AAAA,MACJ,KAAK,WAAW;AAAA,MAChB,KAAK,WAAW;AACZ,aAAK,MAAM,MAAM;AACjB;AAAA,MACJ,KAAK,WAAW;AACZ,aAAK,aAAa;AAClB;AAAA,MACJ,KAAK,WAAW;AACZ,aAAK,QAAQ;AACb,cAAM,MAAM,IAAI,MAAM,OAAO,KAAK,OAAO;AAEzC,YAAI,OAAO,OAAO,KAAK;AACvB,aAAK,aAAa,iBAAiB,GAAG;AACtC;AAAA,IACR;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,QAAQ;AACZ,UAAM,OAAO,OAAO,QAAQ,CAAC;AAC7B,QAAI,QAAQ,OAAO,IAAI;AACnB,WAAK,KAAK,KAAK,IAAI,OAAO,EAAE,CAAC;AAAA,IACjC;AACA,QAAI,KAAK,WAAW;AAChB,WAAK,UAAU,IAAI;AAAA,IACvB,OACK;AACD,WAAK,cAAc,KAAK,OAAO,OAAO,IAAI,CAAC;AAAA,IAC/C;AAAA,EACJ;AAAA,EACA,UAAU,MAAM;AACZ,QAAI,KAAK,iBAAiB,KAAK,cAAc,QAAQ;AACjD,YAAM,YAAY,KAAK,cAAc,MAAM;AAC3C,iBAAW,YAAY,WAAW;AAC9B,iBAAS,MAAM,MAAM,IAAI;AAAA,MAC7B;AAAA,IACJ;AACA,UAAM,KAAK,MAAM,MAAM,IAAI;AAC3B,QAAI,KAAK,QAAQ,KAAK,UAAU,OAAO,KAAK,KAAK,SAAS,CAAC,MAAM,UAAU;AACvE,WAAK,cAAc,KAAK,KAAK,SAAS,CAAC;AAAA,IAC3C;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,IAAI;AACJ,UAAMC,QAAO;AACb,QAAI,OAAO;AACX,WAAO,YAAa,MAAM;AAEtB,UAAI;AACA;AACJ,aAAO;AACP,MAAAA,MAAK,OAAO;AAAA,QACR,MAAM,WAAW;AAAA,QACjB;AAAA,QACA,MAAM;AAAA,MACV,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,QAAQ;AACV,UAAM,MAAM,KAAK,KAAK,OAAO,EAAE;AAC/B,QAAI,eAAe,OAAO,KAAK;AAC3B,UAAI,MAAM,MAAM,OAAO,IAAI;AAC3B,aAAO,KAAK,KAAK,OAAO,EAAE;AAAA,IAC9B,OACK;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,IAAI,KAAK;AACf,SAAK,KAAK;AACV,SAAK,YAAY,OAAO,KAAK,SAAS;AACtC,SAAK,OAAO;AACZ,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,aAAa,SAAS;AAC3B,SAAK,YAAY,IAAI;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACX,SAAK,cAAc,QAAQ,CAAC,SAAS,KAAK,UAAU,IAAI,CAAC;AACzD,SAAK,gBAAgB,CAAC;AACtB,SAAK,WAAW,QAAQ,CAAC,WAAW;AAChC,WAAK,wBAAwB,MAAM;AACnC,WAAK,OAAO,MAAM;AAAA,IACtB,CAAC;AACD,SAAK,aAAa,CAAC;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe;AACX,SAAK,QAAQ;AACb,SAAK,QAAQ,sBAAsB;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,UAAU;AACN,QAAI,KAAK,MAAM;AAEX,WAAK,KAAK,QAAQ,CAAC,eAAe,WAAW,CAAC;AAC9C,WAAK,OAAO;AAAA,IAChB;AACA,SAAK,GAAG,UAAU,EAAE,IAAI;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,aAAa;AACT,QAAI,KAAK,WAAW;AAChB,WAAK,OAAO,EAAE,MAAM,WAAW,WAAW,CAAC;AAAA,IAC/C;AAEA,SAAK,QAAQ;AACb,QAAI,KAAK,WAAW;AAEhB,WAAK,QAAQ,sBAAsB;AAAA,IACvC;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AACJ,WAAO,KAAK,WAAW;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,SAAS,UAAU;AACf,SAAK,MAAM,WAAW;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,IAAI,WAAW;AACX,SAAK,MAAM,WAAW;AACtB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,QAAQ,SAAS;AACb,SAAK,MAAM,UAAU;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,MAAM,UAAU;AACZ,SAAK,gBAAgB,KAAK,iBAAiB,CAAC;AAC5C,SAAK,cAAc,KAAK,QAAQ;AAChC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,WAAW,UAAU;AACjB,SAAK,gBAAgB,KAAK,iBAAiB,CAAC;AAC5C,SAAK,cAAc,QAAQ,QAAQ;AACnC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,OAAO,UAAU;AACb,QAAI,CAAC,KAAK,eAAe;AACrB,aAAO;AAAA,IACX;AACA,QAAI,UAAU;AACV,YAAM,YAAY,KAAK;AACvB,eAASD,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACvC,YAAI,aAAa,UAAUA,EAAC,GAAG;AAC3B,oBAAU,OAAOA,IAAG,CAAC;AACrB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ,OACK;AACD,WAAK,gBAAgB,CAAC;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe;AACX,WAAO,KAAK,iBAAiB,CAAC;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,cAAc,UAAU;AACpB,SAAK,wBAAwB,KAAK,yBAAyB,CAAC;AAC5D,SAAK,sBAAsB,KAAK,QAAQ;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,mBAAmB,UAAU;AACzB,SAAK,wBAAwB,KAAK,yBAAyB,CAAC;AAC5D,SAAK,sBAAsB,QAAQ,QAAQ;AAC3C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmBA,eAAe,UAAU;AACrB,QAAI,CAAC,KAAK,uBAAuB;AAC7B,aAAO;AAAA,IACX;AACA,QAAI,UAAU;AACV,YAAM,YAAY,KAAK;AACvB,eAASA,KAAI,GAAGA,KAAI,UAAU,QAAQA,MAAK;AACvC,YAAI,aAAa,UAAUA,EAAC,GAAG;AAC3B,oBAAU,OAAOA,IAAG,CAAC;AACrB,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ,OACK;AACD,WAAK,wBAAwB,CAAC;AAAA,IAClC;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB;AACnB,WAAO,KAAK,yBAAyB,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,wBAAwB,QAAQ;AAC5B,QAAI,KAAK,yBAAyB,KAAK,sBAAsB,QAAQ;AACjE,YAAM,YAAY,KAAK,sBAAsB,MAAM;AACnD,iBAAW,YAAY,WAAW;AAC9B,iBAAS,MAAM,MAAM,OAAO,IAAI;AAAA,MACpC;AAAA,IACJ;AAAA,EACJ;AACJ;;;AC1zBO,SAAS,QAAQ,MAAM;AAC1B,SAAO,QAAQ,CAAC;AAChB,OAAK,KAAK,KAAK,OAAO;AACtB,OAAK,MAAM,KAAK,OAAO;AACvB,OAAK,SAAS,KAAK,UAAU;AAC7B,OAAK,SAAS,KAAK,SAAS,KAAK,KAAK,UAAU,IAAI,KAAK,SAAS;AAClE,OAAK,WAAW;AACpB;AAOA,QAAQ,UAAU,WAAW,WAAY;AACrC,MAAI,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,QAAQ,KAAK,UAAU;AACxD,MAAI,KAAK,QAAQ;AACb,QAAI,OAAO,KAAK,OAAO;AACvB,QAAI,YAAY,KAAK,MAAM,OAAO,KAAK,SAAS,EAAE;AAClD,UAAM,KAAK,MAAM,OAAO,EAAE,IAAI,MAAM,IAAI,KAAK,YAAY,KAAK;AAAA,EAClE;AACA,SAAO,KAAK,IAAI,IAAI,KAAK,GAAG,IAAI;AACpC;AAMA,QAAQ,UAAU,QAAQ,WAAY;AAClC,OAAK,WAAW;AACpB;AAMA,QAAQ,UAAU,SAAS,SAAU,KAAK;AACtC,OAAK,KAAK;AACd;AAMA,QAAQ,UAAU,SAAS,SAAU,KAAK;AACtC,OAAK,MAAM;AACf;AAMA,QAAQ,UAAU,YAAY,SAAU,QAAQ;AAC5C,OAAK,SAAS;AAClB;;;AC3DO,IAAM,UAAN,cAAsB,QAAQ;AAAA,EACjC,YAAY,KAAK,MAAM;AACnB,QAAI;AACJ,UAAM;AACN,SAAK,OAAO,CAAC;AACb,SAAK,OAAO,CAAC;AACb,QAAI,OAAO,aAAa,OAAO,KAAK;AAChC,aAAO;AACP,YAAM;AAAA,IACV;AACA,WAAO,QAAQ,CAAC;AAChB,SAAK,OAAO,KAAK,QAAQ;AACzB,SAAK,OAAO;AACZ,0BAAsB,MAAM,IAAI;AAChC,SAAK,aAAa,KAAK,iBAAiB,KAAK;AAC7C,SAAK,qBAAqB,KAAK,wBAAwB,QAAQ;AAC/D,SAAK,kBAAkB,KAAK,qBAAqB,GAAI;AACrD,SAAK,qBAAqB,KAAK,wBAAwB,GAAI;AAC3D,SAAK,qBAAqB,KAAK,KAAK,yBAAyB,QAAQ,OAAO,SAAS,KAAK,GAAG;AAC7F,SAAK,UAAU,IAAI,QAAQ;AAAA,MACvB,KAAK,KAAK,kBAAkB;AAAA,MAC5B,KAAK,KAAK,qBAAqB;AAAA,MAC/B,QAAQ,KAAK,oBAAoB;AAAA,IACrC,CAAC;AACD,SAAK,QAAQ,QAAQ,KAAK,UAAU,MAAQ,KAAK,OAAO;AACxD,SAAK,cAAc;AACnB,SAAK,MAAM;AACX,UAAM,UAAU,KAAK,UAAU;AAC/B,SAAK,UAAU,IAAI,QAAQ,QAAQ;AACnC,SAAK,UAAU,IAAI,QAAQ,QAAQ;AACnC,SAAK,eAAe,KAAK,gBAAgB;AACzC,QAAI,KAAK;AACL,WAAK,KAAK;AAAA,EAClB;AAAA,EACA,aAAa,GAAG;AACZ,QAAI,CAAC,UAAU;AACX,aAAO,KAAK;AAChB,SAAK,gBAAgB,CAAC,CAAC;AACvB,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,GAAG;AACpB,QAAI,MAAM;AACN,aAAO,KAAK;AAChB,SAAK,wBAAwB;AAC7B,WAAO;AAAA,EACX;AAAA,EACA,kBAAkB,GAAG;AACjB,QAAI;AACJ,QAAI,MAAM;AACN,aAAO,KAAK;AAChB,SAAK,qBAAqB;AAC1B,KAAC,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,CAAC;AACpE,WAAO;AAAA,EACX;AAAA,EACA,oBAAoB,GAAG;AACnB,QAAI;AACJ,QAAI,MAAM;AACN,aAAO,KAAK;AAChB,SAAK,uBAAuB;AAC5B,KAAC,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,UAAU,CAAC;AACvE,WAAO;AAAA,EACX;AAAA,EACA,qBAAqB,GAAG;AACpB,QAAI;AACJ,QAAI,MAAM;AACN,aAAO,KAAK;AAChB,SAAK,wBAAwB;AAC7B,KAAC,KAAK,KAAK,aAAa,QAAQ,OAAO,SAAS,SAAS,GAAG,OAAO,CAAC;AACpE,WAAO;AAAA,EACX;AAAA,EACA,QAAQ,GAAG;AACP,QAAI,CAAC,UAAU;AACX,aAAO,KAAK;AAChB,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,uBAAuB;AAEnB,QAAI,CAAC,KAAK,iBACN,KAAK,iBACL,KAAK,QAAQ,aAAa,GAAG;AAE7B,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,IAAI;AACL,QAAI,CAAC,KAAK,YAAY,QAAQ,MAAM;AAChC,aAAO;AACX,SAAK,SAAS,IAAI,OAAO,KAAK,KAAK,KAAK,IAAI;AAC5C,UAAM,SAAS,KAAK;AACpB,UAAME,QAAO;AACb,SAAK,cAAc;AACnB,SAAK,gBAAgB;AAErB,UAAM,iBAAiB,GAAG,QAAQ,QAAQ,WAAY;AAClD,MAAAA,MAAK,OAAO;AACZ,YAAM,GAAG;AAAA,IACb,CAAC;AACD,UAAM,UAAU,CAAC,QAAQ;AACrB,WAAK,QAAQ;AACb,WAAK,cAAc;AACnB,WAAK,aAAa,SAAS,GAAG;AAC9B,UAAI,IAAI;AACJ,WAAG,GAAG;AAAA,MACV,OACK;AAED,aAAK,qBAAqB;AAAA,MAC9B;AAAA,IACJ;AAEA,UAAM,WAAW,GAAG,QAAQ,SAAS,OAAO;AAC5C,QAAI,UAAU,KAAK,UAAU;AACzB,YAAM,UAAU,KAAK;AAErB,YAAM,QAAQ,KAAK,aAAa,MAAM;AAClC,uBAAe;AACf,gBAAQ,IAAI,MAAM,SAAS,CAAC;AAC5B,eAAO,MAAM;AAAA,MACjB,GAAG,OAAO;AACV,UAAI,KAAK,KAAK,WAAW;AACrB,cAAM,MAAM;AAAA,MAChB;AACA,WAAK,KAAK,KAAK,MAAM;AACjB,aAAK,eAAe,KAAK;AAAA,MAC7B,CAAC;AAAA,IACL;AACA,SAAK,KAAK,KAAK,cAAc;AAC7B,SAAK,KAAK,KAAK,QAAQ;AACvB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,IAAI;AACR,WAAO,KAAK,KAAK,EAAE;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AAEL,SAAK,QAAQ;AAEb,SAAK,cAAc;AACnB,SAAK,aAAa,MAAM;AAExB,UAAM,SAAS,KAAK;AACpB,SAAK,KAAK,KAAK,GAAG,QAAQ,QAAQ,KAAK,OAAO,KAAK,IAAI,CAAC,GAAG,GAAG,QAAQ,QAAQ,KAAK,OAAO,KAAK,IAAI,CAAC,GAAG,GAAG,QAAQ,SAAS,KAAK,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,QAAQ,SAAS,KAAK,QAAQ,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,SAAS,WAAW,KAAK,UAAU,KAAK,IAAI,CAAC,CAAC;AAAA,EAC7P;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,SAAK,aAAa,MAAM;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,MAAM;AACT,QAAI;AACA,WAAK,QAAQ,IAAI,IAAI;AAAA,IACzB,SACO,GAAG;AACN,WAAK,QAAQ,eAAe,CAAC;AAAA,IACjC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,QAAQ;AAEd,aAAS,MAAM;AACX,WAAK,aAAa,UAAU,MAAM;AAAA,IACtC,GAAG,KAAK,YAAY;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,KAAK;AACT,SAAK,aAAa,SAAS,GAAG;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK,MAAM;AACd,QAAI,SAAS,KAAK,KAAK,GAAG;AAC1B,QAAI,CAAC,QAAQ;AACT,eAAS,IAAIC,QAAO,MAAM,KAAK,IAAI;AACnC,WAAK,KAAK,GAAG,IAAI;AAAA,IACrB,WACS,KAAK,gBAAgB,CAAC,OAAO,QAAQ;AAC1C,aAAO,QAAQ;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS,QAAQ;AACb,UAAM,OAAO,OAAO,KAAK,KAAK,IAAI;AAClC,eAAW,OAAO,MAAM;AACpB,YAAMC,UAAS,KAAK,KAAK,GAAG;AAC5B,UAAIA,QAAO,QAAQ;AACf;AAAA,MACJ;AAAA,IACJ;AACA,SAAK,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,QAAQ;AACZ,UAAM,iBAAiB,KAAK,QAAQ,OAAO,MAAM;AACjD,aAASC,KAAI,GAAGA,KAAI,eAAe,QAAQA,MAAK;AAC5C,WAAK,OAAO,MAAM,eAAeA,EAAC,GAAG,OAAO,OAAO;AAAA,IACvD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU;AACN,SAAK,KAAK,QAAQ,CAAC,eAAe,WAAW,CAAC;AAC9C,SAAK,KAAK,SAAS;AACnB,SAAK,QAAQ,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAAS;AACL,SAAK,gBAAgB;AACrB,SAAK,gBAAgB;AACrB,SAAK,QAAQ,cAAc;AAC3B,QAAI,KAAK;AACL,WAAK,OAAO,MAAM;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa;AACT,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,QAAQ,aAAa;AACzB,SAAK,QAAQ;AACb,SAAK,QAAQ,MAAM;AACnB,SAAK,cAAc;AACnB,SAAK,aAAa,SAAS,QAAQ,WAAW;AAC9C,QAAI,KAAK,iBAAiB,CAAC,KAAK,eAAe;AAC3C,WAAK,UAAU;AAAA,IACnB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY;AACR,QAAI,KAAK,iBAAiB,KAAK;AAC3B,aAAO;AACX,UAAMH,QAAO;AACb,QAAI,KAAK,QAAQ,YAAY,KAAK,uBAAuB;AACrD,WAAK,QAAQ,MAAM;AACnB,WAAK,aAAa,kBAAkB;AACpC,WAAK,gBAAgB;AAAA,IACzB,OACK;AACD,YAAM,QAAQ,KAAK,QAAQ,SAAS;AACpC,WAAK,gBAAgB;AACrB,YAAM,QAAQ,KAAK,aAAa,MAAM;AAClC,YAAIA,MAAK;AACL;AACJ,aAAK,aAAa,qBAAqBA,MAAK,QAAQ,QAAQ;AAE5D,YAAIA,MAAK;AACL;AACJ,QAAAA,MAAK,KAAK,CAAC,QAAQ;AACf,cAAI,KAAK;AACL,YAAAA,MAAK,gBAAgB;AACrB,YAAAA,MAAK,UAAU;AACf,iBAAK,aAAa,mBAAmB,GAAG;AAAA,UAC5C,OACK;AACD,YAAAA,MAAK,YAAY;AAAA,UACrB;AAAA,QACJ,CAAC;AAAA,MACL,GAAG,KAAK;AACR,UAAI,KAAK,KAAK,WAAW;AACrB,cAAM,MAAM;AAAA,MAChB;AACA,WAAK,KAAK,KAAK,MAAM;AACjB,aAAK,eAAe,KAAK;AAAA,MAC7B,CAAC;AAAA,IACL;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc;AACV,UAAM,UAAU,KAAK,QAAQ;AAC7B,SAAK,gBAAgB;AACrB,SAAK,QAAQ,MAAM;AACnB,SAAK,aAAa,aAAa,OAAO;AAAA,EAC1C;AACJ;;;AC/VA,IAAM,QAAQ,CAAC;AACf,SAASI,QAAO,KAAK,MAAM;AACvB,MAAI,OAAO,QAAQ,UAAU;AACzB,WAAO;AACP,UAAM;AAAA,EACV;AACA,SAAO,QAAQ,CAAC;AAChB,QAAM,SAAS,IAAI,KAAK,KAAK,QAAQ,YAAY;AACjD,QAAM,SAAS,OAAO;AACtB,QAAM,KAAK,OAAO;AAClB,QAAM,OAAO,OAAO;AACpB,QAAM,gBAAgB,MAAM,EAAE,KAAK,QAAQ,MAAM,EAAE,EAAE,MAAM;AAC3D,QAAM,gBAAgB,KAAK,YACvB,KAAK,sBAAsB,KAC3B,UAAU,KAAK,aACf;AACJ,MAAIC;AACJ,MAAI,eAAe;AACf,IAAAA,MAAK,IAAI,QAAQ,QAAQ,IAAI;AAAA,EACjC,OACK;AACD,QAAI,CAAC,MAAM,EAAE,GAAG;AACZ,YAAM,EAAE,IAAI,IAAI,QAAQ,QAAQ,IAAI;AAAA,IACxC;AACA,IAAAA,MAAK,MAAM,EAAE;AAAA,EACjB;AACA,MAAI,OAAO,SAAS,CAAC,KAAK,OAAO;AAC7B,SAAK,QAAQ,OAAO;AAAA,EACxB;AACA,SAAOA,IAAG,OAAO,OAAO,MAAM,IAAI;AACtC;AAGA,OAAO,OAAOD,SAAQ;AAAA,EAClB;AAAA,EACA,QAAAE;AAAA,EACA,IAAIF;AAAA,EACJ,SAASA;AACb,CAAC;;;AC5BD,IAAM,WAAN,cAAuB,KAAK;AAAA;AAAA;AAAA;AAAA,EAIxB,cAAc;AACV,UAAM;AACN,SAAK,QAAQ,oBAAI,IAAI;AACrB,SAAK,UAAU,oBAAI,IAAI;AACvB,SAAK,WAAW,oBAAI,IAAI;AACxB,SAAK,MAAM,oBAAI,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,SAAS,MAAM;AACvB,SAAK,QAAQ,IAAI,SAAS,KAAK,YAAY;AAC3C,SAAK,SAAS,SAAS,KAAK,YAAY;AACxC,SAAK,YAAY,SAAS,KAAK,QAAQ;AAAA,EAC3C;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,SAAS,UAAU;AAC3B,SAAK,SAAS,IAAI,SAAS,QAAQ;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,SAAS,OAAO,UAAU;AAC/B,QAAI,YAAY,SAAS,SAAS,GAAG;AACjC,YAAM,MAAM,KAAK,IAAI,IAAI,OAAO,KAAK,CAAC;AACtC,WAAK,IAAI,IAAI,SAAS,CAAC,GAAG,KAAK,GAAG,QAAQ,CAAC;AAAA,IAC/C;AACA,SAAK,MAAM,IAAI,SAAS,KAAK;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,SAAS,MAAM;AACjB,UAAM,SAAS,CAAC;AAChB,QAAI,KAAK,OAAO;AACZ,aAAO,QAAQ,KAAK,MAAM,IAAI,OAAO;AAAA,IACzC;AACA,QAAI,KAAK,UAAU;AACf,aAAO,WAAW,KAAK,SAAS,IAAI,OAAO;AAAA,IAC/C;AACA,QAAI,KAAK,KAAK;AACV,aAAO,MAAM,KAAK,IAAI,IAAI,OAAO,KAAK,CAAC;AAAA,IAC3C;AACA,QAAI,KAAK,cAAc;AACnB,aAAO,eAAe,KAAK,QAAQ,IAAI,OAAO;AAAA,IAClD;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,SAAS;AACV,SAAK,MAAM,OAAO,OAAO;AACzB,SAAK,SAAS,OAAO,OAAO;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,MAAM;AACd,WAAO,CAAC,GAAG,KAAK,SAAS,QAAQ,CAAC,EAC7B,OAAO,CAAC,CAAC,EAAE,QAAQ,MAAM;AAC1B,UAAI,CAAC,MAAM;AACP,eAAO;AAAA,MACX;AACA,UAAI,KAAK,aAAa,UAClB,SAAS,aAAa,KAAK,UAAU;AACrC,eAAO;AAAA,MACX;AACA,UAAI,KAAK,UAAU,QAAW;AAC1B,YAAI,KAAK,MAAM,eAAe,QAAW;AACrC,gBAAM,aAAa,SAAS,aAAa;AACzC,cAAI,eAAe,KAAK,MAAM,YAAY;AACtC,mBAAO;AAAA,UACX;AAAA,QACJ;AACA,YAAI,KAAK,MAAM,kBAAkB,UAC7B,SAAS,aAAa,KAAK,MAAM,eAAe;AAChD,iBAAO;AAAA,QACX;AACA,YAAI,KAAK,MAAM,iBAAiB,UAC5B,SAAS,aAAa,KAAK,MAAM,cAAc;AAC/C,iBAAO;AAAA,QACX;AAAA,MACJ;AACA,aAAO;AAAA,IACX,CAAC,EACI,IAAI,CAAC,CAAC,GAAG,MAAM,GAAG;AAAA,EAC3B;AACJ;AAEA,IAAM,sBAAN,cAAkC,IAAI;AAAA,EAClC,YAAY,KAAK;AACb,UAAM;AACN,SAAK,MAAM;AACX,UAAMG,SAAQ,KAAK,MAAM,aAAa,QAAQ,KAAK,GAAG,CAAC,KAAK,CAAC;AAC7D,IAAAA,OAAM,QAAQ,CAAC,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC;AAAA,EAC/C;AAAA,EACA,OAAO;AACH,UAAM,UAAU,CAAC,GAAG,KAAK,QAAQ,CAAC;AAClC,iBAAa,QAAQ,KAAK,KAAK,KAAK,UAAU,OAAO,CAAC;AAAA,EAC1D;AAAA,EACA,IAAI,KAAKC,QAAO;AACZ,UAAM,IAAI,KAAKA,MAAK;AACpB,SAAK,KAAK;AACV,WAAO;AAAA,EACX;AAAA,EACA,OAAO,KAAK;AACR,UAAM,SAAS,MAAM,OAAO,GAAG;AAC/B,SAAK,KAAK;AACV,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,eAAN,cAA2B,SAAS;AAAA,EAChC,YAAY,gBAAgB,QAAQ;AAChC,UAAM;AACN,UAAM,aAAa,CAAC,aAAa,IAAI,oBAAoB,GAAG,aAAa,IAAI,QAAQ,EAAE;AACvF,SAAK,QAAQ,WAAW,OAAO;AAC/B,SAAK,UAAU,WAAW,SAAS;AACnC,SAAK,WAAW,WAAW,UAAU;AACrC,SAAK,MAAM,WAAW,KAAK;AAAA,EAC/B;AACJ;AAaA,SAAS,aAAa,OAAO,MAAM;AAC/B,MAAI,MAAM,IAAI,aAAa,QAAW;AAClC,WAAO;AAAA,EACX;AACA,MAAI,MAAM,IAAI,eAAe;AACzB,eAAW,OAAO,OAAO,KAAK,IAAI,GAAG;AACjC,UAAI,OAAO,MAAM,IAAI,eAAe;AAChC,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ,WACS,MAAM,IAAI,iBAAiB,MAAM;AACtC,WAAO,MAAM,IAAI;AAAA,EACrB;AACA,SAAO;AACX;AAKA,IAAM,cAAN,cAA0B,OAAO;AAAA,EAC7B,YAAY,EAAE,MAAM,MAAM,YAAY,QAAQ,GAAG;AAC7C,UAAM,kBAAkB,CAAC;AACzB,UAAM,kBAAkB,CAAC;AACzB,QAAI,QAAQ,KAAK,MAAM,MAAM;AACzB,iBAAW,YAAY,MAAM;AACzB,cAAM,MAAM,KAAK,QAAQ;AACzB,wBAAgB,QAAQ,IAAI,IAAI,IAAI;AAAA,UAChC;AAAA,UACA,WAAW,KAAK,GAAG;AAAA,UACnB,MAAM,KAAK;AAAA,QACf,CAAC;AAAA,MACL;AAAA,IACJ;AACA,UAAM,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,MAAM;AACpC,YAAM,WAAW,gBAAgB,QAAQ;AACzC,UAAI,aAAa,QAAW;AACxB,iBAAS,iBAAiB,UAAU,IAAI,CAAC;AAAA,MAC7C;AAAA,IACJ;AACA,UAAM,mBAAmB,oBAAoB,IAAI;AACjD,UAAM,eAAe;AAAA,MACjB;AAAA,MACA,SAAS,CAAC,YAAY;AAClB,mBAAW,YAAY,iBAAiB;AACpC,eAAK,EAAE,UAAU,GAAG,QAAQ,CAAC;AAAA,QACjC;AAAA,MACJ;AAAA,IACJ;AACA,UAAM,UAAU,UAAU,IAAI,aAAa,UAAU,IAAI,IAAI,SAAS;AACtE,UAAM,MAAM,SAAS,YAAY;AACjC,SAAK,UAAU,CAAC,UAAU,aAAa;AACnC,sBAAgB,QAAQ,IAAI;AAAA,IAChC;AACA,SAAK,UAAU,CAAC,EAAE,OAAO,QAAQ,MAAM;AACnC,UAAI,CAAC,MAAM;AACP;AAAA,MACJ;AACA,YAAM,YAAY,aAAa,OAAO,eAAe;AACrD,UAAI,cAAc,MAAM;AACpB,mBAAW,YAAY;AACnB,gBAAM,YAAY,MAAM,gBAAgB,SAAS,EAAE,KAAK,OAAO,SAAS;AACxE,gBAAM,KAAK,SAAS,UAAU,QAAQ,MAAM,UAAU,SAAS,UAAU,OAAO,QAAQ,QAAQ;AAAA,QACpG,GAAG,GAAG;AAAA,MACV;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;AAOA,IAAM,iBAAN,cAA6B,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnC,YAAY,EAAE,QAAQ,GAAG,KAAK,GAAG;AAC7B,UAAM,IAAI;AACV,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,gBAAgB,SAAS,aAAa;AAClC,UAAM,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACT;AACA,SAAK,OAAO,cAAc,GAAG,IAAI;AAAA,EACrC;AAAA,EACA,WAAW,OAAO,QAAQ;AACtB,SAAK,OAAO,SAAS,QAAQ,MAAM,UAAU,KAAK,SAAS,KAAK,QAAQ;AAAA,EAC5E;AAAA,EACA,cAAc;AACV,SAAK,OAAO,OAAO,KAAK,SAAS,KAAK,UAAU,KAAK,aAAa,KAAK,UAAU;AAAA,EACrF;AAAA,EACA,UAAU;AACN,SAAK,oBAAoB,IAAI;AAC7B,SAAK,OAAO,QAAQ,KAAK,UAAU,CAAC,SAAS,KAAK,aAAa,IAAI,CAAC;AACpE,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,aAAa;AACT,SAAK,oBAAoB,KAAK;AAAA,EAClC;AAAA,EACA,cAAc,IAAI;AACd,SAAK,UAAU;AACf,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,eAAe,IAAI;AACf,SAAK,WAAW;AAChB,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,kBAAkB,aAAa;AAC3B,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACjB;AACJ;AAIA,IAAM,eAAe,oBAAI,IAAI;AAI7B,SAAS,MAAM,EAAE,MAAM,SAAS,WAAW,IAAI,CAAC,GAAG;AAC/C,SAAO,CAAC,kBAAkB;AACtB,UAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,QAAI;AACJ,UAAM,WAAW,aAAa,IAAI,OAAO;AACzC,QAAI,YACA,SAAS,SAAS,QAClB,SAAS,eAAe,cACxB,SAAS,YAAY,SAAS;AAC9B,eAAS,SAAS;AAAA,IACtB;AACA,QAAI,CAAC,QAAQ;AACT,eAAS,IAAI,YAAY,EAAE,MAAM,MAAM,SAAS,WAAW,CAAC;AAC5D,mBAAa,IAAI,SAAS,EAAE,QAAQ,MAAM,SAAS,WAAW,CAAC;AAAA,IACnE;AACA,WAAO,IAAI,eAAe,EAAE,QAAQ,GAAG,cAAc,CAAC;AAAA,EAC1D;AACJ;AASA,IAAM,KAAKC;AAMX,IAAM,oBAAN,cAAgC,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAatC,YAAY,EAAE,QAAQ,YAAY,QAAQ,GAAG,KAAK,GAAG;AACjD,UAAM,IAAI;AACV,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,WAAW,OAAO,QAAQ;AACtB,UAAM,OAAO;AAAA,MACT;AAAA,MACA,MAAM;AAAA,MACN,KAAK;AAAA,MACL,KAAK;AAAA,IACT;AACA,SAAK,OAAO,KAAK,UAAU,GAAG,IAAI;AAAA,EACtC;AAAA,EACA,gBAAgB,SAAS,aAAa;AAClC,UAAM,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA,KAAK;AAAA,IACT;AACA,SAAK,OAAO,KAAK,QAAQ,GAAG,IAAI;AAAA,EACpC;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,QAAQ;AACd,UAAI,KAAK,QAAQ;AACb,YAAI,SAAS,KAAK;AAClB,YAAI,OAAO,OAAO,cAAc,KAAK,IAAI;AACrC,mBAAS,YAAY,KAAK;AAAA,QAC9B;AACA,YAAI,OAAO,MAAM,EAAE,KAAK,KAAK;AAEzB,mBAAS,SAAS;AAAA,QACtB;AACA,aAAK,SAAS,GAAG,SAAS,KAAK,UAAU,KAAK,UAAU;AAAA,MAC5D,OACK;AACD,aAAK,SAAS,GAAG,MAAM,KAAK,UAAU,KAAK,UAAU;AAAA,MACzD;AAAA,IACJ;AAIA,SAAK,OAAO,GAAG,SAAS,CAAC,SAAS,aAAa,SAAS,OAAO,aAAa;AACxE,WAAK,aAAa;AAAA,QACd,MAAM;AAAA,QACN,MAAM,CAAC,SAAS,aAAa,SAAS,OAAO,QAAQ;AAAA,MACzD,CAAC;AAAA,IACL,CAAC;AAID,SAAK,OAAO,GAAG,UAAU,CAAC,SAAS,OAAO,aAAa;AACnD,WAAK,aAAa;AAAA,QACd,MAAM;AAAA,QACN,MAAM,CAAC,SAAS,OAAO,QAAQ;AAAA,MACnC,CAAC;AAAA,IACL,CAAC;AAGD,SAAK,OAAO,GAAG,QAAQ,CAAC,SAAS,aAAa;AAC1C,WAAK,aAAa,EAAE,MAAM,QAAQ,MAAM,CAAC,SAAS,QAAQ,EAAE,CAAC;AAAA,IACjE,CAAC;AAGD,SAAK,OAAO,GAAG,aAAa,CAAC,SAAS,cAAc;AAChD,WAAK,aAAa,EAAE,MAAM,aAAa,MAAM,CAAC,SAAS,SAAS,EAAE,CAAC;AAAA,IACvE,CAAC;AACD,SAAK,OAAO,GAAG,QAAQ,CAAC,SAAS,gBAAgB;AAC7C,WAAK,aAAa,EAAE,MAAM,QAAQ,MAAM,CAAC,SAAS,WAAW,EAAE,CAAC;AAAA,IACpE,CAAC;AAED,SAAK,OAAO,GAAG,WAAW,MAAM;AAE5B,WAAK,YAAY;AACjB,WAAK,oBAAoB,IAAI;AAAA,IACjC,CAAC;AACD,SAAK,OAAO,GAAG,cAAc,MAAM;AAC/B,WAAK,oBAAoB,KAAK;AAAA,IAClC,CAAC;AAAA,EACL;AAAA,EACA,aAAa;AACT,SAAK,OAAO,MAAM;AAClB,SAAK,SAAS;AACd,SAAK,oBAAoB,KAAK;AAAA,EAClC;AAAA,EACA,cAAc;AACV,QAAI,KAAK,QAAQ;AACb,YAAM,OAAO;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,MACT;AACA,WAAK,OAAO,KAAK,QAAQ,GAAG,IAAI;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,cAAc,IAAI;AACd,SAAK,UAAU;AACf,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,eAAe,IAAI;AACf,SAAK,WAAW;AAChB,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,kBAAkB,aAAa;AAC3B,SAAK,cAAc;AACnB,SAAK,YAAY;AAAA,EACrB;AACJ;AACA,SAAS,SAAS,EAAE,QAAQ,WAAW,IAAI,CAAC,GAAG;AAC3C,SAAO,CAAC,kBAAkB,IAAI,kBAAkB;AAAA,IAC5C;AAAA,IACA;AAAA,IACA,GAAG;AAAA,EACP,CAAC;AACL;", "names": ["Type", "i", "i", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "i", "isBinary", "on", "i", "length", "i", "i", "decode", "Transport", "encode", "encode", "Transport", "i", "_a", "Transport", "i", "Transport", "value", "i", "i", "decode", "error", "i", "protocol", "protocol", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "withNativeBlob", "i", "i", "protocol", "PacketType", "value", "i", "RESERVED_EVENTS", "Socket", "io", "i", "self", "self", "Socket", "socket", "i", "lookup", "io", "Socket", "cache", "value", "lookup"]}