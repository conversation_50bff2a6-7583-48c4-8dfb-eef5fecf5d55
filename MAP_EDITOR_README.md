# 3D Map Editor for Brawl Party AI

A comprehensive 3D map editor for creating custom game boards for the Brawl Party AI multiplayer board game.

## Features

### Core Functionality
- **Visual 3D Scene Editor**: Real-time 3D editing with camera controls (pan, zoom, rotate)
- **Drag-and-Drop Interface**: Intuitive placement of game elements
- **Real-time Preview**: See your map as players will experience it
- **Save/Load Functionality**: Persistent map storage and retrieval

### Asset Management
- **Asset Library Browser**: Organized categories for different asset types
- **Preview Thumbnails**: Visual identification of assets
- **Search and Filter**: Find assets quickly by name, category, or tags
- **Procedural Assets**: Built-in desert-themed objects (rocks, cacti, palm trees, etc.)

### Board Design Tools
- **Grid-based Tile Placement**: Precise positioning with snap-to-grid
- **Tile Spacing Control**: Maintain the preferred 1/3 tile diameter gaps
- **Sequential Layout System**: Compatible with existing board game mechanics
- **Terrain Sculpting**: Create natural desert environments with uneven surfaces

### Game Integration
- **Boardgame.io Compatibility**: Export maps for use with the existing game framework
- **Health/Keys System Support**: Full integration with current game mechanics
- **Treasure Chest Placement**: Win condition configuration
- **Validation System**: Ensures playable board layouts

### Technical Specifications
- **Three.js Rendering**: High-performance 3D graphics using react-three-fiber
- **Responsive Design**: Works on desktop and tablet devices
- **Fullscreen 3D View**: Immersive editing experience with sidebar UI panels
- **Performance Optimization**: Efficient rendering for complex scenes

## Getting Started

### Accessing the Editor
1. Navigate to the main menu
2. Click "Map Editor" button
3. Create a new map or load an existing one

### Basic Workflow
1. **Create New Map**: Set map name and board size
2. **Place Tiles**: Select tile types and click to place on the board
3. **Add Environment**: Place rocks, vegetation, and decorative objects
4. **Configure Lighting**: Adjust ambient and directional lighting
5. **Preview**: Test the map in preview mode
6. **Export**: Save for use in games

## Editor Modes

### Tile Placement Mode
- Place game tiles (Basic, Healing, Damage, Key, Treasure Chest)
- Configure tile properties and spacing
- View tile statistics and distribution
- Auto-layout tools for quick board generation

### Terrain Sculpting Mode
- Raise/lower terrain height
- Smooth and flatten terrain
- Apply terrain presets (flat desert, rolling hills, crater, valley)
- Brush size and strength controls

### Environment Objects Mode
- Place decorative objects (rocks, cacti, palm trees)
- Position environmental features (sand dunes, oasis water)
- Transform objects (move, rotate, scale)
- Object property editing

### Lighting Mode
- Configure ambient lighting
- Adjust directional light (sun)
- Place additional point and spot lights
- Lighting presets (noon, dawn, dusk, night)
- Atmosphere settings (fog, sky, effects)

### Preview Mode
- Test the map as players would see it
- Disable editing tools for clean preview
- Camera movement for inspection

## Asset Categories

### Tiles
- **Basic Tile**: Standard movement tile
- **Healing Tile**: Restores player health
- **Damage Tile**: Damages players
- **Key Tile**: Grants keys to players
- **Treasure Chest**: Win condition tile

### Rocks
- **Small Rock**: Basic decoration
- **Medium Rock**: Moderate-sized formation
- **Large Rock**: Major rock outcropping

### Vegetation
- **Small Cactus**: Desert plant
- **Large Cactus**: Multi-armed cactus
- **Palm Tree**: Oasis vegetation

### Terrain
- **Sand Dune**: Natural terrain feature
- **Oasis Water**: Central water feature

### Decorative
- **Desert Grass**: Small vegetation clumps
- **Bone Pile**: Atmospheric decoration

### Lighting
- **Point Light**: Omnidirectional illumination
- **Spot Light**: Directional focused light

## Controls

### Camera Controls
- **Mouse Drag**: Rotate camera around target
- **Mouse Wheel**: Zoom in/out
- **Right Click + Drag**: Pan camera
- **Middle Click**: Reset camera position

### Keyboard Shortcuts
- **V**: Select tool
- **B**: Place tool
- **E**: Erase tool
- **R**: Rotate tool
- **S**: Scale tool
- **Ctrl+Z**: Undo
- **Ctrl+Shift+Z**: Redo
- **Ctrl+S**: Save map
- **Ctrl+G**: Toggle grid
- **Ctrl+P**: Toggle preview mode

### Tool-Specific Controls
- **Click**: Place object/tile
- **Shift+Click**: Multi-select
- **Ctrl+Click**: Add to selection
- **Delete**: Remove selected objects
- **Ctrl+C**: Copy selected objects
- **Ctrl+V**: Paste objects

## Map Validation

The editor includes validation to ensure maps are playable:

### Required Elements
- At least one tile
- Exactly one treasure chest tile
- Reasonable tile count (20-120 recommended)

### Warnings
- Missing key tiles
- Unbalanced tile distribution
- Performance concerns (too many objects/lights)
- Tile connectivity issues

## Export Formats

### Boardgame.io Format
- Compatible with existing game engine
- Includes game state configuration
- Ready for multiplayer games

### JSON Format
- Complete map configuration
- Includes all assets and properties
- Suitable for sharing and backup

### Performance Optimization
- Automatic object count limiting
- Light count optimization
- Terrain complexity reduction

## File Structure

```
src/components/editor/
├── MapEditor.tsx           # Main editor interface
├── EditorScene.tsx         # 3D scene rendering
├── AssetLibrary.tsx        # Asset browser and management
├── EditorToolbar.tsx       # Tool selection and controls
├── EditorSidebar.tsx       # Properties and settings
├── TileEditor.tsx          # Tile placement tools
├── TerrainEditor.tsx       # Terrain sculpting tools
├── EnvironmentEditor.tsx   # Lighting and atmosphere
├── EditorGrid.tsx          # 3D grid component
├── EditorTile.tsx          # 3D tile representation
├── EditorObject.tsx        # 3D object representation
└── EditorTerrain.tsx       # 3D terrain component

src/types/
├── editorTypes.ts          # Editor-specific type definitions
└── mapTypes.ts             # Map configuration types

src/store/
└── editor.ts               # Redux store for editor state

src/utils/
├── mapExporter.ts          # Map export utilities
└── mapImporter.ts          # Map import utilities
```

## Development Notes

### Adding New Assets
1. Add asset definition to `AssetLibrary.tsx`
2. Implement rendering in `EditorObject.tsx`
3. Add thumbnail image to public/assets/thumbnails/
4. Update asset registry in `mapImporter.ts`

### Performance Considerations
- Limit total objects to < 1000 for good performance
- Keep light count under 10
- Use LOD (Level of Detail) for complex objects
- Optimize textures and materials

### Future Enhancements
- GLTF model loading for custom assets
- Terrain texture painting
- Particle system editor
- Animation timeline
- Collaborative editing
- Asset marketplace integration

## Troubleshooting

### Common Issues
- **Map not loading**: Check browser console for errors
- **Poor performance**: Reduce object count or complexity
- **Assets not appearing**: Verify asset paths and thumbnails
- **Export failing**: Check map validation errors

### Browser Compatibility
- Chrome 90+ (recommended)
- Firefox 88+
- Safari 14+
- Edge 90+

### System Requirements
- WebGL 2.0 support
- 4GB+ RAM recommended
- Dedicated graphics card for complex scenes

## Contributing

When contributing to the map editor:
1. Follow existing code patterns
2. Add proper TypeScript types
3. Include SCSS styling
4. Test with various map configurations
5. Update documentation as needed

## License

Part of the Brawl Party AI project. See main project license for details.
