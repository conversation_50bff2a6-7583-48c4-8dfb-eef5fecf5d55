{"version": 3, "sources": ["../../@tanstack/table-core/src/utils.ts", "../../@tanstack/table-core/src/core/column.ts", "../../@tanstack/table-core/src/core/headers.ts", "../../@tanstack/table-core/src/features/ColumnSizing.ts", "../../@tanstack/table-core/src/features/Expanding.ts", "../../@tanstack/table-core/src/filterFns.ts", "../../@tanstack/table-core/src/features/Filters.ts", "../../@tanstack/table-core/src/aggregationFns.ts", "../../@tanstack/table-core/src/features/Grouping.ts", "../../@tanstack/table-core/src/features/Ordering.ts", "../../@tanstack/table-core/src/features/Pagination.ts", "../../@tanstack/table-core/src/features/Pinning.ts", "../../@tanstack/table-core/src/features/RowSelection.ts", "../../@tanstack/table-core/src/sortingFns.ts", "../../@tanstack/table-core/src/features/Sorting.ts", "../../@tanstack/table-core/src/features/Visibility.ts", "../../@tanstack/table-core/src/core/table.ts", "../../@tanstack/table-core/src/core/cell.ts", "../../@tanstack/table-core/src/core/row.ts", "../../@tanstack/table-core/src/columnHelper.ts", "../../@tanstack/table-core/src/utils/getCoreRowModel.ts", "../../@tanstack/table-core/src/utils/filterRowsUtils.ts", "../../@tanstack/table-core/src/utils/getFilteredRowModel.ts", "../../@tanstack/table-core/src/utils/getFacetedRowModel.ts", "../../@tanstack/table-core/src/utils/getFacetedUniqueValues.ts", "../../@tanstack/table-core/src/utils/getFacetedMinMaxValues.ts", "../../@tanstack/table-core/src/utils/getSortedRowModel.ts", "../../@tanstack/table-core/src/utils/getGroupedRowModel.ts", "../../@tanstack/table-core/src/utils/getExpandedRowModel.ts", "../../@tanstack/table-core/src/utils/getPaginationRowModel.ts", "../../@tanstack/react-table/src/index.tsx"], "sourcesContent": ["import { TableState, Updater } from './types'\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\nexport type RequiredKeys<T, K extends keyof T> = Omit<T, K> &\n  Required<Pick<T, K>>\nexport type Overwrite<T, U extends { [TKey in keyof T]?: any }> = Omit<\n  T,\n  keyof U\n> &\n  U\n\nexport type UnionToIntersection<T> = (\n  T extends any ? (x: T) => any : never\n) extends (x: infer R) => any\n  ? R\n  : never\n\nexport type IsAny<T, Y, N> = 1 extends 0 & T ? Y : N\nexport type IsKnown<T, Y, N> = unknown extends T ? N : Y\n\ntype ComputeRange<\n  N extends number,\n  Result extends Array<unknown> = [],\n> = Result['length'] extends N\n  ? Result\n  : ComputeRange<N, [...Result, Result['length']]>\ntype Index40 = ComputeRange<40>[number]\n\n// Is this type a tuple?\ntype IsTuple<T> = T extends readonly any[] & { length: infer Length }\n  ? Length extends Index40\n    ? T\n    : never\n  : never\n\n// If this type is a tuple, what indices are allowed?\ntype AllowedIndexes<\n  Tuple extends ReadonlyArray<any>,\n  <PERSON> extends number = never,\n> = Tuple extends readonly []\n  ? Keys\n  : Tuple extends readonly [infer _, ...infer Tail]\n    ? AllowedIndexes<Tail, Keys | Tail['length']>\n    : Keys\n\nexport type DeepKeys<T, TDepth extends any[] = []> = TDepth['length'] extends 5\n  ? never\n  : unknown extends T\n    ? string\n    : object extends T\n      ? string\n      : T extends readonly any[] & IsTuple<T>\n        ? AllowedIndexes<T> | DeepKeysPrefix<T, AllowedIndexes<T>, TDepth>\n        : T extends any[]\n          ? DeepKeys<T[number], [...TDepth, any]>\n          : T extends Date\n            ? never\n            : T extends object\n              ? (keyof T & string) | DeepKeysPrefix<T, keyof T, TDepth>\n              : never\n\ntype DeepKeysPrefix<\n  T,\n  TPrefix,\n  TDepth extends any[],\n> = TPrefix extends keyof T & (number | string)\n  ? `${TPrefix}.${DeepKeys<T[TPrefix], [...TDepth, any]> & string}`\n  : never\n\nexport type DeepValue<T, TProp> = T extends Record<string | number, any>\n  ? TProp extends `${infer TBranch}.${infer TDeepProp}`\n    ? DeepValue<T[TBranch], TDeepProp>\n    : T[TProp & string]\n  : never\n\nexport type NoInfer<T> = [T][T extends any ? 0 : never]\n\nexport type Getter<TValue> = <TTValue = TValue>() => NoInfer<TTValue>\n\n///\n\nexport function functionalUpdate<T>(updater: Updater<T>, input: T): T {\n  return typeof updater === 'function'\n    ? (updater as (input: T) => T)(input)\n    : updater\n}\n\nexport function noop() {\n  //\n}\n\nexport function makeStateUpdater<K extends keyof TableState>(\n  key: K,\n  instance: unknown\n) {\n  return (updater: Updater<TableState[K]>) => {\n    ;(instance as any).setState(<TTableState>(old: TTableState) => {\n      return {\n        ...old,\n        [key]: functionalUpdate(updater, (old as any)[key]),\n      }\n    })\n  }\n}\n\ntype AnyFunction = (...args: any) => any\n\nexport function isFunction<T extends AnyFunction>(d: any): d is T {\n  return d instanceof Function\n}\n\nexport function isNumberArray(d: any): d is number[] {\n  return Array.isArray(d) && d.every(val => typeof val === 'number')\n}\n\nexport function flattenBy<TNode>(\n  arr: TNode[],\n  getChildren: (item: TNode) => TNode[]\n) {\n  const flat: TNode[] = []\n\n  const recurse = (subArr: TNode[]) => {\n    subArr.forEach(item => {\n      flat.push(item)\n      const children = getChildren(item)\n      if (children?.length) {\n        recurse(children)\n      }\n    })\n  }\n\n  recurse(arr)\n\n  return flat\n}\n\nexport function memo<TDeps extends readonly any[], TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: any\n    debug?: () => any\n    onChange?: (result: TResult) => void\n  }\n): () => TResult {\n  let deps: any[] = []\n  let result: TResult | undefined\n\n  return () => {\n    let depTime: number\n    if (opts.key && opts.debug) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug) resultTime = Date.now()\n\n    result = fn(...newDeps)\n    opts?.onChange?.(result)\n\n    if (opts.key && opts.debug) {\n      if (opts?.debug()) {\n        const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n        const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n        const resultFpsPercentage = resultEndTime / 16\n\n        const pad = (str: number | string, num: number) => {\n          str = String(str)\n          while (str.length < num) {\n            str = ' ' + str\n          }\n          return str\n        }\n\n        console.info(\n          `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n          `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120)\n            )}deg 100% 31%);`,\n          opts?.key\n        )\n      }\n    }\n\n    return result!\n  }\n}\n", "import {\n  Column,\n  Table,\n  AccessorFn,\n  ColumnDef,\n  RowData,\n  ColumnDefResolved,\n} from '../types'\nimport { memo } from '../utils'\n\nexport interface CoreColumn<TData extends RowData, TValue> {\n  /**\n   * The resolved accessor function to use when extracting the value for the column from each row. Will only be defined if the column def has a valid accessor key or function defined.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#accessorfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  accessorFn?: AccessorFn<TData, TValue>\n  /**\n   * The original column def used to create the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columndef)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columnDef: ColumnDef<TData, TValue>\n  /**\n   * The child column (if the column is a group column). Will be an empty array if the column is not a group column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  columns: Column<TData, TValue>[]\n  /**\n   * The depth of the column (if grouped) relative to the root column def array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  depth: number\n  /**\n   * Returns the flattened array of this column and all child/grand-child columns for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getFlatColumns: () => Column<TData, TValue>[]\n  /**\n   * Returns an array of all leaf-node columns for this column. If a column has no children, it is considered the only leaf-node column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#getleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  getLeafColumns: () => Column<TData, TValue>[]\n  /**\n   * The resolved unique identifier for the column resolved in this priority:\n      - A manual `id` property from the column def\n      - The accessor key from the column def\n      - The header string from the column def\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  id: string\n  /**\n   * The parent column for this column. Will be undefined if this is a root column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/column#parent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-defs)\n   */\n  parent?: Column<TData, TValue>\n}\n\nexport function createColumn<TData extends RowData, TValue>(\n  table: Table<TData>,\n  columnDef: ColumnDef<TData, TValue>,\n  depth: number,\n  parent?: Column<TData, TValue>\n): Column<TData, TValue> {\n  const defaultColumn = table._getDefaultColumnDef()\n\n  const resolvedColumnDef = {\n    ...defaultColumn,\n    ...columnDef,\n  } as ColumnDefResolved<TData>\n\n  const accessorKey = resolvedColumnDef.accessorKey\n\n  let id =\n    resolvedColumnDef.id ??\n    (accessorKey ? accessorKey.replace('.', '_') : undefined) ??\n    (typeof resolvedColumnDef.header === 'string'\n      ? resolvedColumnDef.header\n      : undefined)\n\n  let accessorFn: AccessorFn<TData> | undefined\n\n  if (resolvedColumnDef.accessorFn) {\n    accessorFn = resolvedColumnDef.accessorFn\n  } else if (accessorKey) {\n    // Support deep accessor keys\n    if (accessorKey.includes('.')) {\n      accessorFn = (originalRow: TData) => {\n        let result = originalRow as Record<string, any>\n\n        for (const key of accessorKey.split('.')) {\n          result = result?.[key]\n          if (process.env.NODE_ENV !== 'production' && result === undefined) {\n            console.warn(\n              `\"${key}\" in deeply nested key \"${accessorKey}\" returned undefined.`\n            )\n          }\n        }\n\n        return result\n      }\n    } else {\n      accessorFn = (originalRow: TData) =>\n        (originalRow as any)[resolvedColumnDef.accessorKey]\n    }\n  }\n\n  if (!id) {\n    if (process.env.NODE_ENV !== 'production') {\n      throw new Error(\n        resolvedColumnDef.accessorFn\n          ? `Columns require an id when using an accessorFn`\n          : `Columns require an id when using a non-string header`\n      )\n    }\n    throw new Error()\n  }\n\n  let column: CoreColumn<TData, any> = {\n    id: `${String(id)}`,\n    accessorFn,\n    parent: parent as any,\n    depth,\n    columnDef: resolvedColumnDef as ColumnDef<TData, any>,\n    columns: [],\n    getFlatColumns: memo(\n      () => [true],\n      () => {\n        return [\n          column as Column<TData, TValue>,\n          ...column.columns?.flatMap(d => d.getFlatColumns()),\n        ]\n      },\n      {\n        key: process.env.NODE_ENV === 'production' && 'column.getFlatColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n    getLeafColumns: memo(\n      () => [table._getOrderColumnsFn()],\n      orderColumns => {\n        if (column.columns?.length) {\n          let leafColumns = column.columns.flatMap(column =>\n            column.getLeafColumns()\n          )\n\n          return orderColumns(leafColumns)\n        }\n\n        return [column as Column<TData, TValue>]\n      },\n      {\n        key: process.env.NODE_ENV === 'production' && 'column.getLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n  }\n\n  for (const feature of table._features) {\n    feature.createColumn?.(column, table)\n  }\n\n  // Yes, we have to convert table to uknown, because we know more than the compiler here.\n  return column as Column<TData, TValue>\n}\n", "import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, HeaderGroup, Table } from '../types'\nimport { memo } from '../utils'\nimport { TableFeature } from './table'\n\nexport interface CoreHeaderGroup<TData extends RowData> {\n  depth: number\n  headers: Header<TData, unknown>[]\n  id: string\n}\n\nexport interface HeaderContext<TData, TValue> {\n  /**\n   * An instance of a column.\n   */\n  column: Column<TData, TValue>\n  /**\n   * An instance of a header.\n   */\n  header: Header<TData, TValue>\n  /**\n   * The table instance.\n   */\n  table: Table<TData>\n}\n\nexport interface CoreHeader<TData extends RowData, TValue> {\n  /**\n   * The col-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#colspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  colSpan: number\n  /**\n   * The header's associated column object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  column: Column<TData, TValue>\n  /**\n   * The depth of the header, zero-indexed based.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  depth: number\n  /**\n   * Returns the rendering context (or props) for column-based components like headers, footers and filters.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getContext: () => HeaderContext<TData, TValue>\n  /**\n   * Returns the leaf headers hierarchically nested under this header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * The header's associated header group object.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#headergroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  headerGroup: HeaderGroup<TData>\n  /**\n   * The unique identifier for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  id: string\n  /**\n   * The index for the header within the header group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  index: number\n  /**\n   * A boolean denoting if the header is a placeholder header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#isplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  isPlaceholder: boolean\n  /**\n   * If the header is a placeholder header, this will be a unique header ID that does not conflict with any other headers across the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#placeholderid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  placeholderId?: string\n  /**\n   * The row-span for the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#rowspan)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  rowSpan: number\n  /**\n   * The header's hierarchical sub/child headers. Will be empty if the header's associated column is a leaf-column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/header#subheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  subHeaders: Header<TData, TValue>[]\n}\n\nexport interface HeadersInstance<TData extends RowData> {\n  /**\n   * Returns all header groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterHeaderGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the header groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightheadergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightHeaderGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns the footer groups for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the left pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for columns that are not pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFooterGroups: () => HeaderGroup<TData>[]\n  /**\n   * If pinning, returns the footer groups for the right pinned columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightfootergroups)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFooterGroups: () => HeaderGroup<TData>[]\n\n  /**\n   * Returns headers for all columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterFlatHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned columns in the table, including parent headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightflatheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightFlatHeaders: () => Header<TData, unknown>[]\n\n  /**\n   * Returns headers for all leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all left pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getleftleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getLeftLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all columns that are not pinned, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getcenterleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getCenterLeafHeaders: () => Header<TData, unknown>[]\n  /**\n   * If pinning, returns headers for all right pinned leaf columns in the table, (not including parent headers).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/headers#getrightleafheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/headers)\n   */\n  getRightLeafHeaders: () => Header<TData, unknown>[]\n}\n\n//\n\nfunction createHeader<TData extends RowData, TValue>(\n  table: Table<TData>,\n  column: Column<TData, TValue>,\n  options: {\n    id?: string\n    isPlaceholder?: boolean\n    placeholderId?: string\n    index: number\n    depth: number\n  }\n): Header<TData, TValue> {\n  const id = options.id ?? column.id\n\n  let header: CoreHeader<TData, TValue> = {\n    id,\n    column,\n    index: options.index,\n    isPlaceholder: !!options.isPlaceholder,\n    placeholderId: options.placeholderId,\n    depth: options.depth,\n    subHeaders: [],\n    colSpan: 0,\n    rowSpan: 0,\n    headerGroup: null!,\n    getLeafHeaders: (): Header<TData, unknown>[] => {\n      const leafHeaders: Header<TData, unknown>[] = []\n\n      const recurseHeader = (h: CoreHeader<TData, any>) => {\n        if (h.subHeaders && h.subHeaders.length) {\n          h.subHeaders.map(recurseHeader)\n        }\n        leafHeaders.push(h as Header<TData, unknown>)\n      }\n\n      recurseHeader(header)\n\n      return leafHeaders\n    },\n    getContext: () => ({\n      table,\n      header: header as Header<TData, TValue>,\n      column,\n    }),\n  }\n\n  table._features.forEach(feature => {\n    feature.createHeader?.(header, table)\n  })\n\n  return header as Header<TData, TValue>\n}\n\nexport const Headers: TableFeature = {\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    // Header Groups\n\n    table.getHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        const leftColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const rightColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        const centerColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n\n        const headerGroups = buildHeaderGroups(\n          allColumns,\n          [...leftColumns, ...centerColumns, ...rightColumns],\n          table\n        )\n\n        return headerGroups\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getHeaderGroups',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getCenterHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, left, right) => {\n        leafColumns = leafColumns.filter(\n          column => !left?.includes(column.id) && !right?.includes(column.id)\n        )\n        return buildHeaderGroups(allColumns, leafColumns, table, 'center')\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getCenterHeaderGroups',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getLeftHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.left,\n      ],\n      (allColumns, leafColumns, left) => {\n        const orderedLeafColumns =\n          left\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'left')\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getLeftHeaderGroups',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getRightHeaderGroups = memo(\n      () => [\n        table.getAllColumns(),\n        table.getVisibleLeafColumns(),\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, leafColumns, right) => {\n        const orderedLeafColumns =\n          right\n            ?.map(columnId => leafColumns.find(d => d.id === columnId)!)\n            .filter(Boolean) ?? []\n\n        return buildHeaderGroups(allColumns, orderedLeafColumns, table, 'right')\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getRightHeaderGroups',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    // Footer Groups\n\n    table.getFooterGroups = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getFooterGroups',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getLeftFooterGroups = memo(\n      () => [table.getLeftHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getLeftFooterGroups',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getCenterFooterGroups = memo(\n      () => [table.getCenterHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getCenterFooterGroups',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getRightFooterGroups = memo(\n      () => [table.getRightHeaderGroups()],\n      headerGroups => {\n        return [...headerGroups].reverse()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getRightFooterGroups',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    // Flat Headers\n\n    table.getFlatHeaders = memo(\n      () => [table.getHeaderGroups()],\n      headerGroups => {\n        return headerGroups\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getFlatHeaders',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getLeftFlatHeaders = memo(\n      () => [table.getLeftHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getLeftFlatHeaders',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getCenterFlatHeaders = memo(\n      () => [table.getCenterHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getCenterFlatHeaders',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getRightFlatHeaders = memo(\n      () => [table.getRightHeaderGroups()],\n      left => {\n        return left\n          .map(headerGroup => {\n            return headerGroup.headers\n          })\n          .flat()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getRightFlatHeaders',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    // Leaf Headers\n\n    table.getCenterLeafHeaders = memo(\n      () => [table.getCenterFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getCenterLeafHeaders',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getLeftLeafHeaders = memo(\n      () => [table.getLeftFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getLeftLeafHeaders',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getRightLeafHeaders = memo(\n      () => [table.getRightFlatHeaders()],\n      flatHeaders => {\n        return flatHeaders.filter(header => !header.subHeaders?.length)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getRightLeafHeaders',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n\n    table.getLeafHeaders = memo(\n      () => [\n        table.getLeftHeaderGroups(),\n        table.getCenterHeaderGroups(),\n        table.getRightHeaderGroups(),\n      ],\n      (left, center, right) => {\n        return [\n          ...(left[0]?.headers ?? []),\n          ...(center[0]?.headers ?? []),\n          ...(right[0]?.headers ?? []),\n        ]\n          .map(header => {\n            return header.getLeafHeaders()\n          })\n          .flat()\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getLeafHeaders',\n        debug: () => table.options.debugAll ?? table.options.debugHeaders,\n      }\n    )\n  },\n}\n\nexport function buildHeaderGroups<TData extends RowData>(\n  allColumns: Column<TData, unknown>[],\n  columnsToGroup: Column<TData, unknown>[],\n  table: Table<TData>,\n  headerFamily?: 'center' | 'left' | 'right'\n) {\n  // Find the max depth of the columns:\n  // build the leaf column row\n  // build each buffer row going up\n  //    placeholder for non-existent level\n  //    real column for existing level\n\n  let maxDepth = 0\n\n  const findMaxDepth = (columns: Column<TData, unknown>[], depth = 1) => {\n    maxDepth = Math.max(maxDepth, depth)\n\n    columns\n      .filter(column => column.getIsVisible())\n      .forEach(column => {\n        if (column.columns?.length) {\n          findMaxDepth(column.columns, depth + 1)\n        }\n      }, 0)\n  }\n\n  findMaxDepth(allColumns)\n\n  let headerGroups: HeaderGroup<TData>[] = []\n\n  const createHeaderGroup = (\n    headersToGroup: Header<TData, unknown>[],\n    depth: number\n  ) => {\n    // The header group we are creating\n    const headerGroup: HeaderGroup<TData> = {\n      depth,\n      id: [headerFamily, `${depth}`].filter(Boolean).join('_'),\n      headers: [],\n    }\n\n    // The parent columns we're going to scan next\n    const pendingParentHeaders: Header<TData, unknown>[] = []\n\n    // Scan each column for parents\n    headersToGroup.forEach(headerToGroup => {\n      // What is the latest (last) parent column?\n\n      const latestPendingParentHeader = [...pendingParentHeaders].reverse()[0]\n\n      const isLeafHeader = headerToGroup.column.depth === headerGroup.depth\n\n      let column: Column<TData, unknown>\n      let isPlaceholder = false\n\n      if (isLeafHeader && headerToGroup.column.parent) {\n        // The parent header is new\n        column = headerToGroup.column.parent\n      } else {\n        // The parent header is repeated\n        column = headerToGroup.column\n        isPlaceholder = true\n      }\n\n      if (\n        latestPendingParentHeader &&\n        latestPendingParentHeader?.column === column\n      ) {\n        // This column is repeated. Add it as a sub header to the next batch\n        latestPendingParentHeader.subHeaders.push(headerToGroup)\n      } else {\n        // This is a new header. Let's create it\n        const header = createHeader(table, column, {\n          id: [headerFamily, depth, column.id, headerToGroup?.id]\n            .filter(Boolean)\n            .join('_'),\n          isPlaceholder,\n          placeholderId: isPlaceholder\n            ? `${pendingParentHeaders.filter(d => d.column === column).length}`\n            : undefined,\n          depth,\n          index: pendingParentHeaders.length,\n        })\n\n        // Add the headerToGroup as a subHeader of the new header\n        header.subHeaders.push(headerToGroup)\n        // Add the new header to the pendingParentHeaders to get grouped\n        // in the next batch\n        pendingParentHeaders.push(header)\n      }\n\n      headerGroup.headers.push(headerToGroup)\n      headerToGroup.headerGroup = headerGroup\n    })\n\n    headerGroups.push(headerGroup)\n\n    if (depth > 0) {\n      createHeaderGroup(pendingParentHeaders, depth - 1)\n    }\n  }\n\n  const bottomHeaders = columnsToGroup.map((column, index) =>\n    createHeader(table, column, {\n      depth: maxDepth,\n      index,\n    })\n  )\n\n  createHeaderGroup(bottomHeaders, maxDepth - 1)\n\n  headerGroups.reverse()\n\n  // headerGroups = headerGroups.filter(headerGroup => {\n  //   return !headerGroup.headers.every(header => header.isPlaceholder)\n  // })\n\n  const recurseHeadersForSpans = (\n    headers: Header<TData, unknown>[]\n  ): { colSpan: number; rowSpan: number }[] => {\n    const filteredHeaders = headers.filter(header =>\n      header.column.getIsVisible()\n    )\n\n    return filteredHeaders.map(header => {\n      let colSpan = 0\n      let rowSpan = 0\n      let childRowSpans = [0]\n\n      if (header.subHeaders && header.subHeaders.length) {\n        childRowSpans = []\n\n        recurseHeadersForSpans(header.subHeaders).forEach(\n          ({ colSpan: childColSpan, rowSpan: childRowSpan }) => {\n            colSpan += childColSpan\n            childRowSpans.push(childRowSpan)\n          }\n        )\n      } else {\n        colSpan = 1\n      }\n\n      const minChildRowSpan = Math.min(...childRowSpans)\n      rowSpan = rowSpan + minChildRowSpan\n\n      header.colSpan = colSpan\n      header.rowSpan = rowSpan\n\n      return { colSpan, rowSpan }\n    })\n  }\n\n  recurseHeadersForSpans(headerGroups[0]?.headers ?? [])\n\n  return headerGroups\n}\n", "import { TableFeature } from '../core/table'\nimport { <PERSON><PERSON><PERSON>, <PERSON>um<PERSON>, <PERSON><PERSON>, OnChangeFn, Table, Updater } from '../types'\nimport { makeStateUpdater } from '../utils'\nimport { ColumnPinningPosition } from './Pinning'\n\n//\n\nexport interface ColumnSizingTableState {\n  columnSizing: ColumnSizingState\n  columnSizingInfo: ColumnSizingInfoState\n}\n\nexport type ColumnSizingState = Record<string, number>\n\nexport interface ColumnSizingInfoState {\n  columnSizingStart: [string, number][]\n  deltaOffset: null | number\n  deltaPercentage: null | number\n  isResizingColumn: false | string\n  startOffset: null | number\n  startSize: null | number\n}\n\nexport type ColumnResizeMode = 'onChange' | 'onEnd'\n\nexport type ColumnResizeDirection = 'ltr' | 'rtl'\n\nexport interface ColumnSizingOptions {\n  /**\n   * Determines when the columnSizing state is updated. `onChange` updates the state when the user is dragging the resize handle. `onEnd` updates the state when the user releases the resize handle.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnresizemode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeMode?: ColumnResizeMode\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enablecolumnresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableColumnResizing?: boolean\n  /**\n   * Enables or disables right-to-left support for resizing the column. defaults to 'ltr'.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#columnResizeDirection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  columnResizeDirection?: ColumnResizeDirection\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizing` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizing` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingChange?: OnChangeFn<ColumnSizingState>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnSizingInfo` changes. This overrides the default internal state management, so you will also need to supply `state.columnSizingInfo` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#oncolumnsizinginfochange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  onColumnSizingInfoChange?: OnChangeFn<ColumnSizingInfoState>\n}\n\nexport type ColumnSizingDefaultOptions = Pick<\n  ColumnSizingOptions,\n  | 'columnResizeMode'\n  | 'onColumnSizingChange'\n  | 'onColumnSizingInfoChange'\n  | 'columnResizeDirection'\n>\n\nexport interface ColumnSizingInstance {\n  /**\n   * If pinning, returns the total size of the center portion of the table by calculating the sum of the sizes of all unpinned/center leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcentertotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCenterTotalSize: () => number\n  /**\n   * Returns the total size of the left portion of the table by calculating the sum of the sizes of all left leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getlefttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getLeftTotalSize: () => number\n  /**\n   * Returns the total size of the right portion of the table by calculating the sum of the sizes of all right leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getrighttotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getRightTotalSize: () => number\n  /**\n   * Returns the total size of the table by calculating the sum of the sizes of all leaf-columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#gettotalsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getTotalSize: () => number\n  /**\n   * Resets column sizing to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetColumnSizing: (defaultState?: boolean) => void\n  /**\n   * Resets column sizing info to its initial state. If `defaultState` is `true`, the default state for the table will be used instead of the initialValue provided to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetheadersizeinfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetHeaderSizeInfo: (defaultState?: boolean) => void\n  /**\n   * Sets the column sizing state using an updater function or a value. This will trigger the underlying `onColumnSizingChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizing: (updater: Updater<ColumnSizingState>) => void\n  /**\n   * Sets the column sizing info state using an updater function or a value. This will trigger the underlying `onColumnSizingInfoChange` function if one is passed to the table options, otherwise the state will be managed automatically by the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#setcolumnsizinginfo)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  setColumnSizingInfo: (updater: Updater<ColumnSizingInfoState>) => void\n}\n\nexport interface ColumnSizingColumnDef {\n  /**\n   * Enables or disables column resizing for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#enableresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  enableResizing?: boolean\n  /**\n   * The maximum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#maxsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  maxSize?: number\n  /**\n   * The minimum allowed size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#minsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  minSize?: number\n  /**\n   * The desired size for the column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#size)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  size?: number\n}\n\nexport interface ColumnSizingColumn {\n  /**\n   * Returns `true` if the column can be resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getcanresize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getCanResize: () => boolean\n  /**\n   * Returns `true` if the column is currently being resized.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getisresizing)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getIsResizing: () => boolean\n  /**\n   * Returns the current size of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition) => number\n  /**\n   * Resets the column to its initial size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#resetsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  resetSize: () => void\n}\n\nexport interface ColumnSizingHeader {\n  /**\n   * Returns an event handler function that can be used to resize the header. It can be used as an:\n   * - `onMouseDown` handler\n   * - `onTouchStart` handler\n   *\n   * The dragging and release events are automatically handled for you.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getresizehandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getResizeHandler: (context?: Document) => (event: unknown) => void\n  /**\n   * Returns the current size of the header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getsize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getSize: () => number\n  /**\n   * Returns the offset measurement along the row-axis (usually the x-axis for standard tables) for the header. This is effectively a sum of the offset measurements of all preceding headers.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-sizing#getstart)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-sizing)\n   */\n  getStart: (position?: ColumnPinningPosition) => number\n}\n\n//\n\nexport const defaultColumnSizing = {\n  size: 150,\n  minSize: 20,\n  maxSize: Number.MAX_SAFE_INTEGER,\n}\n\nconst getDefaultColumnSizingInfoState = (): ColumnSizingInfoState => ({\n  startOffset: null,\n  startSize: null,\n  deltaOffset: null,\n  deltaPercentage: null,\n  isResizingColumn: false,\n  columnSizingStart: [],\n})\n\nexport const ColumnSizing: TableFeature = {\n  getDefaultColumnDef: (): ColumnSizingColumnDef => {\n    return defaultColumnSizing\n  },\n  getInitialState: (state): ColumnSizingTableState => {\n    return {\n      columnSizing: {},\n      columnSizingInfo: getDefaultColumnSizingInfoState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnSizingDefaultOptions => {\n    return {\n      columnResizeMode: 'onEnd',\n      columnResizeDirection: 'ltr',\n      onColumnSizingChange: makeStateUpdater('columnSizing', table),\n      onColumnSizingInfoChange: makeStateUpdater('columnSizingInfo', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getSize = () => {\n      const columnSize = table.getState().columnSizing[column.id]\n\n      return Math.min(\n        Math.max(\n          column.columnDef.minSize ?? defaultColumnSizing.minSize,\n          columnSize ?? column.columnDef.size ?? defaultColumnSizing.size\n        ),\n        column.columnDef.maxSize ?? defaultColumnSizing.maxSize\n      )\n    }\n    column.getStart = position => {\n      const columns = !position\n        ? table.getVisibleLeafColumns()\n        : position === 'left'\n          ? table.getLeftVisibleLeafColumns()\n          : table.getRightVisibleLeafColumns()\n\n      const index = columns.findIndex(d => d.id === column.id)\n\n      if (index > 0) {\n        const prevSiblingColumn = columns[index - 1]!\n\n        return (\n          prevSiblingColumn.getStart(position) + prevSiblingColumn.getSize()\n        )\n      }\n\n      return 0\n    }\n    column.resetSize = () => {\n      table.setColumnSizing(({ [column.id]: _, ...rest }) => {\n        return rest\n      })\n    }\n    column.getCanResize = () => {\n      return (\n        (column.columnDef.enableResizing ?? true) &&\n        (table.options.enableColumnResizing ?? true)\n      )\n    }\n    column.getIsResizing = () => {\n      return table.getState().columnSizingInfo.isResizingColumn === column.id\n    }\n  },\n\n  createHeader: <TData extends RowData, TValue>(\n    header: Header<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    header.getSize = () => {\n      let sum = 0\n\n      const recurse = (header: Header<TData, TValue>) => {\n        if (header.subHeaders.length) {\n          header.subHeaders.forEach(recurse)\n        } else {\n          sum += header.column.getSize() ?? 0\n        }\n      }\n\n      recurse(header)\n\n      return sum\n    }\n    header.getStart = () => {\n      if (header.index > 0) {\n        const prevSiblingHeader = header.headerGroup.headers[header.index - 1]!\n        return prevSiblingHeader.getStart() + prevSiblingHeader.getSize()\n      }\n\n      return 0\n    }\n    header.getResizeHandler = _contextDocument => {\n      const column = table.getColumn(header.column.id)\n      const canResize = column?.getCanResize()\n\n      return (e: unknown) => {\n        if (!column || !canResize) {\n          return\n        }\n\n        ;(e as any).persist?.()\n\n        if (isTouchStartEvent(e)) {\n          // lets not respond to multiple touches (e.g. 2 or 3 fingers)\n          if (e.touches && e.touches.length > 1) {\n            return\n          }\n        }\n\n        const startSize = header.getSize()\n\n        const columnSizingStart: [string, number][] = header\n          ? header.getLeafHeaders().map(d => [d.column.id, d.column.getSize()])\n          : [[column.id, column.getSize()]]\n\n        const clientX = isTouchStartEvent(e)\n          ? Math.round(e.touches[0]!.clientX)\n          : (e as MouseEvent).clientX\n\n        const newColumnSizing: ColumnSizingState = {}\n\n        const updateOffset = (\n          eventType: 'move' | 'end',\n          clientXPos?: number\n        ) => {\n          if (typeof clientXPos !== 'number') {\n            return\n          }\n\n          table.setColumnSizingInfo(old => {\n            const deltaDirection =\n              table.options.columnResizeDirection === 'rtl' ? -1 : 1\n            const deltaOffset =\n              (clientXPos - (old?.startOffset ?? 0)) * deltaDirection\n            const deltaPercentage = Math.max(\n              deltaOffset / (old?.startSize ?? 0),\n              -0.999999\n            )\n\n            old.columnSizingStart.forEach(([columnId, headerSize]) => {\n              newColumnSizing[columnId] =\n                Math.round(\n                  Math.max(headerSize + headerSize * deltaPercentage, 0) * 100\n                ) / 100\n            })\n\n            return {\n              ...old,\n              deltaOffset,\n              deltaPercentage,\n            }\n          })\n\n          if (\n            table.options.columnResizeMode === 'onChange' ||\n            eventType === 'end'\n          ) {\n            table.setColumnSizing(old => ({\n              ...old,\n              ...newColumnSizing,\n            }))\n          }\n        }\n\n        const onMove = (clientXPos?: number) => updateOffset('move', clientXPos)\n\n        const onEnd = (clientXPos?: number) => {\n          updateOffset('end', clientXPos)\n\n          table.setColumnSizingInfo(old => ({\n            ...old,\n            isResizingColumn: false,\n            startOffset: null,\n            startSize: null,\n            deltaOffset: null,\n            deltaPercentage: null,\n            columnSizingStart: [],\n          }))\n        }\n\n        const contextDocument =\n          _contextDocument || typeof document !== 'undefined' ? document : null\n\n        const mouseEvents = {\n          moveHandler: (e: MouseEvent) => onMove(e.clientX),\n          upHandler: (e: MouseEvent) => {\n            contextDocument?.removeEventListener(\n              'mousemove',\n              mouseEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'mouseup',\n              mouseEvents.upHandler\n            )\n            onEnd(e.clientX)\n          },\n        }\n\n        const touchEvents = {\n          moveHandler: (e: TouchEvent) => {\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onMove(e.touches[0]!.clientX)\n            return false\n          },\n          upHandler: (e: TouchEvent) => {\n            contextDocument?.removeEventListener(\n              'touchmove',\n              touchEvents.moveHandler\n            )\n            contextDocument?.removeEventListener(\n              'touchend',\n              touchEvents.upHandler\n            )\n            if (e.cancelable) {\n              e.preventDefault()\n              e.stopPropagation()\n            }\n            onEnd(e.touches[0]?.clientX)\n          },\n        }\n\n        const passiveIfSupported = passiveEventSupported()\n          ? { passive: false }\n          : false\n\n        if (isTouchStartEvent(e)) {\n          contextDocument?.addEventListener(\n            'touchmove',\n            touchEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'touchend',\n            touchEvents.upHandler,\n            passiveIfSupported\n          )\n        } else {\n          contextDocument?.addEventListener(\n            'mousemove',\n            mouseEvents.moveHandler,\n            passiveIfSupported\n          )\n          contextDocument?.addEventListener(\n            'mouseup',\n            mouseEvents.upHandler,\n            passiveIfSupported\n          )\n        }\n\n        table.setColumnSizingInfo(old => ({\n          ...old,\n          startOffset: clientX,\n          startSize,\n          deltaOffset: 0,\n          deltaPercentage: 0,\n          columnSizingStart,\n          isResizingColumn: column.id,\n        }))\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnSizing = updater =>\n      table.options.onColumnSizingChange?.(updater)\n    table.setColumnSizingInfo = updater =>\n      table.options.onColumnSizingInfoChange?.(updater)\n    table.resetColumnSizing = defaultState => {\n      table.setColumnSizing(\n        defaultState ? {} : table.initialState.columnSizing ?? {}\n      )\n    }\n    table.resetHeaderSizeInfo = defaultState => {\n      table.setColumnSizingInfo(\n        defaultState\n          ? getDefaultColumnSizingInfoState()\n          : table.initialState.columnSizingInfo ??\n              getDefaultColumnSizingInfoState()\n      )\n    }\n    table.getTotalSize = () =>\n      table.getHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getLeftTotalSize = () =>\n      table.getLeftHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getCenterTotalSize = () =>\n      table.getCenterHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n    table.getRightTotalSize = () =>\n      table.getRightHeaderGroups()[0]?.headers.reduce((sum, header) => {\n        return sum + header.getSize()\n      }, 0) ?? 0\n  },\n}\n\nlet passiveSupported: boolean | null = null\nexport function passiveEventSupported() {\n  if (typeof passiveSupported === 'boolean') return passiveSupported\n\n  let supported = false\n  try {\n    const options = {\n      get passive() {\n        supported = true\n        return false\n      },\n    }\n\n    const noop = () => {}\n\n    window.addEventListener('test', noop, options)\n    window.removeEventListener('test', noop)\n  } catch (err) {\n    supported = false\n  }\n  passiveSupported = supported\n  return passiveSupported\n}\n\nfunction isTouchStartEvent(e: unknown): e is TouchEvent {\n  return (e as TouchEvent).type === 'touchstart'\n}\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, Row, Updater, RowData } from '../types'\nimport { makeStateUpdater } from '../utils'\n\nexport type ExpandedStateList = Record<string, boolean>\nexport type ExpandedState = true | Record<string, boolean>\nexport interface ExpandedTableState {\n  expanded: ExpandedState\n}\n\nexport interface ExpandedRow {\n  /**\n   * Returns whether the row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanExpand: () => boolean\n  /**\n   * Returns whether all parent rows of the row are expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallparentsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllParentsExpanded: () => boolean\n  /**\n   * Returns whether the row is expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsExpanded: () => boolean\n  /**\n   * Returns a function that can be used to toggle the expanded state of the row. This function can be used to bind to an event handler to a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleExpandedHandler: () => () => void\n  /**\n   * Toggles the expanded state (or sets it if `expanded` is provided) for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleExpanded: (expanded?: boolean) => void\n}\n\nexport interface ExpandedOptions<TData extends RowData> {\n  /**\n   * Enable this setting to automatically reset the expanded state of the table when expanding state changes.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#autoresetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  autoResetExpanded?: boolean\n  /**\n   * Enable/disable expanding for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#enableexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  enableExpanding?: boolean\n  /**\n   * This function is responsible for returning the expanded row model. If this function is not provided, the table will not expand rows. You can use the default exported `getExpandedRowModel` function to get the expanded row model or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row is currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisrowexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsRowExpanded?: (row: Row<TData>) => boolean\n  /**\n   * If provided, allows you to override the default behavior of determining whether a row can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getrowcanexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getRowCanExpand?: (row: Row<TData>) => boolean\n  /**\n   * Enables manual row expansion. If this is set to `true`, `getExpandedRowModel` will not be used to expand rows and you would be expected to perform the expansion in your own data model. This is useful if you are doing server-side expansion.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#manualexpanding)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  manualExpanding?: boolean\n  /**\n   * This function is called when the `expanded` table state changes. If a function is provided, you will be responsible for managing this state on your own. To pass the managed state back to the table, use the `tableOptions.state.expanded` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#onexpandedchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  onExpandedChange?: OnChangeFn<ExpandedState>\n  /**\n   * If `true` expanded rows will be paginated along with the rest of the table (which means expanded rows may span multiple pages). If `false` expanded rows will not be considered for pagination (which means expanded rows will always render on their parents page. This also means more rows will be rendered than the set page size)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#paginateexpandedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  paginateExpandedRows?: boolean\n}\n\nexport interface ExpandedInstance<TData extends RowData> {\n  _autoResetExpanded: () => void\n  _getExpandedRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether there are any rows that can be expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getcansomerowsexpand)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getCanSomeRowsExpand: () => boolean\n  /**\n   * Returns the maximum depth of the expanded rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandeddepth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedDepth: () => number\n  /**\n   * Returns the row model after expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether all rows are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getisallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsAllRowsExpanded: () => boolean\n  /**\n   * Returns whether there are any rows that are currently expanded.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getissomerowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getIsSomeRowsExpanded: () => boolean\n  /**\n   * Returns the row model before expansion has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#getpreexpandedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getPreExpandedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle the expanded state of all rows. This handler is meant to be used with an `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#gettoggleallrowsexpandedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  getToggleAllRowsExpandedHandler: () => (event: unknown) => void\n  /**\n   * Resets the expanded state of the table to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#resetexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  resetExpanded: (defaultState?: boolean) => void\n  /**\n   * Updates the expanded state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#setexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  setExpanded: (updater: Updater<ExpandedState>) => void\n  /**\n   * Toggles the expanded state for all rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/expanding#toggleallrowsexpanded)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/expanding)\n   */\n  toggleAllRowsExpanded: (expanded?: boolean) => void\n}\n\n//\n\nexport const Expanding: TableFeature = {\n  getInitialState: (state): ExpandedTableState => {\n    return {\n      expanded: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ExpandedOptions<TData> => {\n    return {\n      onExpandedChange: makeStateUpdater('expanded', table),\n      paginateExpandedRows: true,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetExpanded = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetExpanded ??\n        !table.options.manualExpanding\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetExpanded()\n          queued = false\n        })\n      }\n    }\n    table.setExpanded = updater => table.options.onExpandedChange?.(updater)\n    table.toggleAllRowsExpanded = expanded => {\n      if (expanded ?? !table.getIsAllRowsExpanded()) {\n        table.setExpanded(true)\n      } else {\n        table.setExpanded({})\n      }\n    }\n    table.resetExpanded = defaultState => {\n      table.setExpanded(defaultState ? {} : table.initialState?.expanded ?? {})\n    }\n    table.getCanSomeRowsExpand = () => {\n      return table\n        .getPrePaginationRowModel()\n        .flatRows.some(row => row.getCanExpand())\n    }\n    table.getToggleAllRowsExpandedHandler = () => {\n      return (e: unknown) => {\n        ;(e as any).persist?.()\n        table.toggleAllRowsExpanded()\n      }\n    }\n    table.getIsSomeRowsExpanded = () => {\n      const expanded = table.getState().expanded\n      return expanded === true || Object.values(expanded).some(Boolean)\n    }\n    table.getIsAllRowsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      // If expanded is true, save some cycles and return true\n      if (typeof expanded === 'boolean') {\n        return expanded === true\n      }\n\n      if (!Object.keys(expanded).length) {\n        return false\n      }\n\n      // If any row is not expanded, return false\n      if (table.getRowModel().flatRows.some(row => !row.getIsExpanded())) {\n        return false\n      }\n\n      // They must all be expanded :shrug:\n      return true\n    }\n    table.getExpandedDepth = () => {\n      let maxDepth = 0\n\n      const rowIds =\n        table.getState().expanded === true\n          ? Object.keys(table.getRowModel().rowsById)\n          : Object.keys(table.getState().expanded)\n\n      rowIds.forEach(id => {\n        const splitId = id.split('.')\n        maxDepth = Math.max(maxDepth, splitId.length)\n      })\n\n      return maxDepth\n    }\n    table.getPreExpandedRowModel = () => table.getSortedRowModel()\n    table.getExpandedRowModel = () => {\n      if (!table._getExpandedRowModel && table.options.getExpandedRowModel) {\n        table._getExpandedRowModel = table.options.getExpandedRowModel(table)\n      }\n\n      if (table.options.manualExpanding || !table._getExpandedRowModel) {\n        return table.getPreExpandedRowModel()\n      }\n\n      return table._getExpandedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleExpanded = expanded => {\n      table.setExpanded(old => {\n        const exists = old === true ? true : !!old?.[row.id]\n\n        let oldExpanded: ExpandedStateList = {}\n\n        if (old === true) {\n          Object.keys(table.getRowModel().rowsById).forEach(rowId => {\n            oldExpanded[rowId] = true\n          })\n        } else {\n          oldExpanded = old\n        }\n\n        expanded = expanded ?? !exists\n\n        if (!exists && expanded) {\n          return {\n            ...oldExpanded,\n            [row.id]: true,\n          }\n        }\n\n        if (exists && !expanded) {\n          const { [row.id]: _, ...rest } = oldExpanded\n          return rest\n        }\n\n        return old\n      })\n    }\n    row.getIsExpanded = () => {\n      const expanded = table.getState().expanded\n\n      return !!(\n        table.options.getIsRowExpanded?.(row) ??\n        (expanded === true || expanded?.[row.id])\n      )\n    }\n    row.getCanExpand = () => {\n      return (\n        table.options.getRowCanExpand?.(row) ??\n        ((table.options.enableExpanding ?? true) && !!row.subRows?.length)\n      )\n    }\n    row.getIsAllParentsExpanded = () => {\n      let isFullyExpanded = true\n      let currentRow = row\n\n      while (isFullyExpanded && currentRow.parentId) {\n        currentRow = table.getRow(currentRow.parentId, true)\n        isFullyExpanded = currentRow.getIsExpanded()\n      }\n\n      return isFullyExpanded\n    }\n    row.getToggleExpandedHandler = () => {\n      const canExpand = row.getCanExpand()\n\n      return () => {\n        if (!canExpand) return\n        row.toggleExpanded()\n      }\n    }\n  },\n}\n", "import { FilterFn } from './features/Filters'\n\nconst includesString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  const search = filterValue.toLowerCase()\n  return Boolean(\n    row\n      .getValue<string | null>(columnId)\n      ?.toString()\n      ?.toLowerCase()\n      ?.includes(search)\n  )\n}\n\nincludesString.autoRemove = (val: any) => testFalsey(val)\n\nconst includesStringSensitive: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return Boolean(\n    row.getValue<string | null>(columnId)?.toString()?.includes(filterValue)\n  )\n}\n\nincludesStringSensitive.autoRemove = (val: any) => testFalsey(val)\n\nconst equalsString: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: string\n) => {\n  return (\n    row.getValue<string | null>(columnId)?.toString()?.toLowerCase() ===\n    filterValue?.toLowerCase()\n  )\n}\n\nequalsString.autoRemove = (val: any) => testFalsey(val)\n\nconst arrIncludes: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue<unknown[]>(columnId)?.includes(filterValue)\n}\n\narrIncludes.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesAll: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return !filterValue.some(\n    val => !row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesAll.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst arrIncludesSome: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown[]\n) => {\n  return filterValue.some(\n    val => row.getValue<unknown[]>(columnId)?.includes(val)\n  )\n}\n\narrIncludesSome.autoRemove = (val: any) => testFalsey(val) || !val?.length\n\nconst equals: FilterFn<any> = (row, columnId: string, filterValue: unknown) => {\n  return row.getValue(columnId) === filterValue\n}\n\nequals.autoRemove = (val: any) => testFalsey(val)\n\nconst weakEquals: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: unknown\n) => {\n  return row.getValue(columnId) == filterValue\n}\n\nweakEquals.autoRemove = (val: any) => testFalsey(val)\n\nconst inNumberRange: FilterFn<any> = (\n  row,\n  columnId: string,\n  filterValue: [number, number]\n) => {\n  let [min, max] = filterValue\n\n  const rowValue = row.getValue<number>(columnId)\n  return rowValue >= min && rowValue <= max\n}\n\ninNumberRange.resolveFilterValue = (val: [any, any]) => {\n  let [unsafeMin, unsafeMax] = val\n\n  let parsedMin =\n    typeof unsafeMin !== 'number' ? parseFloat(unsafeMin as string) : unsafeMin\n  let parsedMax =\n    typeof unsafeMax !== 'number' ? parseFloat(unsafeMax as string) : unsafeMax\n\n  let min =\n    unsafeMin === null || Number.isNaN(parsedMin) ? -Infinity : parsedMin\n  let max = unsafeMax === null || Number.isNaN(parsedMax) ? Infinity : parsedMax\n\n  if (min > max) {\n    const temp = min\n    min = max\n    max = temp\n  }\n\n  return [min, max] as const\n}\n\ninNumberRange.autoRemove = (val: any) =>\n  testFalsey(val) || (testFalsey(val[0]) && testFalsey(val[1]))\n\n// Export\n\nexport const filterFns = {\n  includesString,\n  includesStringSensitive,\n  equalsString,\n  arrIncludes,\n  arrIncludesAll,\n  arrIncludesSome,\n  equals,\n  weakEquals,\n  inNumberRange,\n}\n\nexport type BuiltInFilterFn = keyof typeof filterFns\n\n// Utils\n\nfunction testFalsey(val: any) {\n  return val === undefined || val === null || val === ''\n}\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport { BuiltInFilterFn, filterFns } from '../filterFns'\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  FilterMeta,\n  FilterFns,\n} from '../types'\nimport { functionalUpdate, isFunction, makeStateUpdater } from '../utils'\n\nexport interface FiltersTableState {\n  columnFilters: ColumnFiltersState\n  globalFilter: any\n}\n\nexport type ColumnFiltersState = ColumnFilter[]\n\nexport interface ColumnFilter {\n  id: string\n  value: unknown\n}\n\nexport interface ResolvedColumnFilter<TData extends RowData> {\n  id: string\n  resolvedValue: unknown\n  filterFn: FilterFn<TData>\n}\n\nexport interface FilterFn<TData extends RowData> {\n  (\n    row: Row<TData>,\n    columnId: string,\n    filterValue: any,\n    addMeta: (meta: FilterMeta) => void\n  ): boolean\n\n  resolveFilterValue?: TransformFilterValueFn<TData>\n  autoRemove?: ColumnFilterAutoRemoveTestFn<TData>\n}\n\nexport type TransformFilterValueFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => unknown\n\nexport type ColumnFilterAutoRemoveTestFn<TData extends RowData> = (\n  value: any,\n  column?: Column<TData, unknown>\n) => boolean\n\nexport type CustomFilterFns<TData extends RowData> = Record<\n  string,\n  FilterFn<TData>\n>\n\nexport type FilterFnOption<TData extends RowData> =\n  | 'auto'\n  | BuiltInFilterFn\n  | keyof FilterFns\n  | FilterFn<TData>\n\nexport interface FiltersColumnDef<TData extends RowData> {\n  /**\n   * The filter function to use with this column. Can be the name of a built-in filter function or a custom filter function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#filterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  filterFn?: FilterFnOption<TData>\n  /**\n   * Enables/disables the **column** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#enablecolumnfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  enableColumnFilter?: boolean\n  /**\n   * Enables/disables the **global** filter for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  enableGlobalFilter?: boolean\n}\n\nexport interface FiltersColumn<TData extends RowData> {\n  _getFacetedMinMaxValues?: () => undefined | [number, number]\n  _getFacetedRowModel?: () => RowModel<TData>\n  _getFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * Returns an automatically calculated filter function for the column based off of the columns first known value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be **column** filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getcanfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getCanFilter: () => boolean\n  /**\n   * Returns whether or not the column can be **globally** filtered. Set to `false` to disable a column from being scanned during global filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getcanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getCanGlobalFilter: () => boolean\n  /**\n   * A function that **computes and returns** a min/max tuple derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedMinMaxValues` function to `options.getFacetedMinMaxValues`. A default implementation is provided via the exported `getFacetedMinMaxValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getfacetedminmaxvalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model with all other column filters applied, excluding its own filter. Useful for displaying faceted result counts.\n   * > ⚠️ Requires that you pass a valid `getFacetedRowModel` function to `options.facetedRowModel`. A default implementation is provided via the exported `getFacetedRowModel` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getFacetedRowModel: () => RowModel<TData>\n  /**\n   * A function that **computes and returns** a `Map` of unique values and their occurrences derived from `column.getFacetedRowModel`. Useful for displaying faceted result values.\n   * > ⚠️ Requires that you pass a valid `getFacetedUniqueValues` function to `options.getFacetedUniqueValues`. A default implementation is provided via the exported `getFacetedUniqueValues` function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getFacetedUniqueValues: () => Map<any, number>\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the columnId specified.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the index (including `-1`) of the column filter in the table's `state.columnFilters` array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getfilterindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getFilterIndex: () => number\n  /**\n   * Returns the current filter value for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getFilterValue: () => unknown\n  /**\n   * Returns whether or not the column is currently filtered.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getisfiltered)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getIsFiltered: () => boolean\n  /**\n   * A function that sets the current filter value for the column. You can pass it a value or an updater function for immutability-safe operations on existing values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#setfiltervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  setFilterValue: (updater: Updater<any>) => void\n}\n\nexport interface FiltersRow<TData extends RowData> {\n  /**\n   * The column filters map for the row. This object tracks whether a row is passing/failing specific filters by their column ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#columnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  columnFilters: Record<string, boolean>\n  /**\n   * The column filters meta map for the row. This object tracks any filter meta for a row as optionally provided during the filtering process.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#columnfiltersmeta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  columnFiltersMeta: Record<string, FilterMeta>\n}\n\ninterface FiltersOptionsBase<TData extends RowData> {\n  /**\n   * Enables/disables all filtering for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#enablefilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  enableFilters?: boolean\n  /**\n   * By default, filtering is done from parent rows down (so if a parent row is filtered out, all of its children will be filtered out as well). Setting this option to `true` will cause filtering to be done from leaf rows up (which means parent rows will be included so long as one of their child or grand-child rows is also included).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#filterfromleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  filterFromLeafRows?: boolean\n  /**\n   * If provided, this function is called **once** per table and should return a **new function** which will calculate and return the row model for the table when it's filtered.\n   * - For server-side filtering, this function is unnecessary and can be ignored since the server should already return the filtered row model.\n   * - For client-side filtering, this function is required. A default implementation is provided via any table adapter's `{ getFilteredRowModel }` export.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getFilteredRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Disables the `getFilteredRowModel` from being used to filter data. This may be useful if your table needs to dynamically support both client-side and server-side filtering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#manualfiltering)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  manualFiltering?: boolean\n  /**\n   * By default, filtering is done for all rows (max depth of 100), no matter if they are root level parent rows or the child leaf rows of a parent row. Setting this option to `0` will cause filtering to only be applied to the root level parent rows, with all sub-rows remaining unfiltered. Similarly, setting this option to `1` will cause filtering to only be applied to child leaf rows 1 level deep, and so on.\n\n   * This is useful for situations where you want a row's entire child hierarchy to be visible regardless of the applied filter.\n    * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#maxleafrowfilterdepth)\n    * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  maxLeafRowFilterDepth?: number\n\n  // Column\n  /**\n   * Enables/disables **column** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#enablecolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  enableColumnFilters?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnFilters` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#oncolumnfilterschange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  onColumnFiltersChange?: OnChangeFn<ColumnFiltersState>\n\n  // Global\n  /**\n   * Enables/disables **global** filtering for all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#enableglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  enableGlobalFilter?: boolean\n  /**\n   * If provided, this function will be called with the column and should return `true` or `false` to indicate whether this column should be used for global filtering.\n   *\n   * This is useful if the column can contain data that is not `string` or `number` (i.e. `undefined`).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getcolumncanglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getColumnCanGlobalFilter?: (column: Column<TData, unknown>) => boolean\n  /**\n   * The filter function to use for global filtering.\n   * - A `string` referencing a built-in filter function\n   * - A `string` that references a custom filter functions provided via the `tableOptions.filterFns` option\n   * - A custom filter function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#globalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  globalFilterFn?: FilterFnOption<TData>\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.globalFilter` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#onglobalfilterchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  onGlobalFilterChange?: OnChangeFn<any>\n\n  // Faceting\n  getFacetedRowModel?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => RowModel<TData>\n  getFacetedUniqueValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => Map<any, number>\n  getFacetedMinMaxValues?: (\n    table: Table<TData>,\n    columnId: string\n  ) => () => undefined | [number, number]\n}\n\ntype ResolvedFilterFns = keyof FilterFns extends never\n  ? {\n      filterFns?: Record<string, FilterFn<any>>\n    }\n  : {\n      filterFns: Record<keyof FilterFns, FilterFn<any>>\n    }\n\nexport interface FiltersOptions<TData extends RowData>\n  extends FiltersOptionsBase<TData>,\n    ResolvedFilterFns {}\n\nexport interface FiltersInstance<TData extends RowData> {\n  /**\n   * Sets or updates the `state.columnFilters` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#setcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  setColumnFilters: (updater: Updater<ColumnFiltersState>) => void\n  /**\n   * Resets the **columnFilters** state to `initialState.columnFilters`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#resetcolumnfilters)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  resetColumnFilters: (defaultState?: boolean) => void\n\n  // Column Filters\n  _getFilteredRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getfilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getFilteredRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any **column** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getprefilteredrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getPreFilteredRowModel: () => RowModel<TData>\n\n  // Global Filters\n  _getGlobalFacetedMinMaxValues?: () => undefined | [number, number]\n  _getGlobalFacetedRowModel?: () => RowModel<TData>\n  _getGlobalFacetedUniqueValues?: () => Map<any, number>\n  /**\n   * Currently, this function returns the built-in `includesString` filter function. In future releases, it may return more dynamic filter functions based on the nature of the data provided.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getglobalautofilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getGlobalAutoFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Returns the faceted min and max values for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getglobalfacetedminmaxvalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getGlobalFacetedMinMaxValues: () => undefined | [number, number]\n  /**\n   * Returns the row model for the table after **global** filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getglobalfacetedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getGlobalFacetedRowModel: () => RowModel<TData>\n  /**\n   * Returns the faceted unique values for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getglobalfaceteduniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getGlobalFacetedUniqueValues: () => Map<any, number>\n  /**\n   * Returns the filter function (either user-defined or automatic, depending on configuration) for the global filter.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#getglobalfilterfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  getGlobalFilterFn: () => FilterFn<TData> | undefined\n  /**\n   * Resets the **globalFilter** state to `initialState.globalFilter`, or `true` can be passed to force a default blank state reset to `undefined`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#resetglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  resetGlobalFilter: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.globalFilter` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/filters#setglobalfilter)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/filters)\n   */\n  setGlobalFilter: (updater: Updater<any>) => void\n}\n\n//\n\nexport const Filters: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): FiltersColumnDef<TData> => {\n    return {\n      filterFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): FiltersTableState => {\n    return {\n      columnFilters: [],\n      globalFilter: undefined,\n      // filtersProgress: 1,\n      // facetProgress: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): FiltersOptions<TData> => {\n    return {\n      onColumnFiltersChange: makeStateUpdater('columnFilters', table),\n      onGlobalFilterChange: makeStateUpdater('globalFilter', table),\n      filterFromLeafRows: false,\n      maxLeafRowFilterDepth: 100,\n      globalFilterFn: 'auto',\n      getColumnCanGlobalFilter: column => {\n        const value = table\n          .getCoreRowModel()\n          .flatRows[0]?._getAllCellsByColumnId()\n          [column.id]?.getValue()\n\n        return typeof value === 'string' || typeof value === 'number'\n      },\n    } as FiltersOptions<TData>\n  },\n\n  createColumn: <TData extends RowData>(\n    column: Column<TData, unknown>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoFilterFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return filterFns.includesString\n      }\n\n      if (typeof value === 'number') {\n        return filterFns.inNumberRange\n      }\n\n      if (typeof value === 'boolean') {\n        return filterFns.equals\n      }\n\n      if (value !== null && typeof value === 'object') {\n        return filterFns.equals\n      }\n\n      if (Array.isArray(value)) {\n        return filterFns.arrIncludes\n      }\n\n      return filterFns.weakEquals\n    }\n    column.getFilterFn = () => {\n      return isFunction(column.columnDef.filterFn)\n        ? column.columnDef.filterFn\n        : column.columnDef.filterFn === 'auto'\n          ? column.getAutoFilterFn()\n          : // @ts-ignore\n            table.options.filterFns?.[column.columnDef.filterFn as string] ??\n            filterFns[column.columnDef.filterFn as BuiltInFilterFn]\n    }\n    column.getCanFilter = () => {\n      return (\n        (column.columnDef.enableColumnFilter ?? true) &&\n        (table.options.enableColumnFilters ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getCanGlobalFilter = () => {\n      return (\n        (column.columnDef.enableGlobalFilter ?? true) &&\n        (table.options.enableGlobalFilter ?? true) &&\n        (table.options.enableFilters ?? true) &&\n        (table.options.getColumnCanGlobalFilter?.(column) ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsFiltered = () => column.getFilterIndex() > -1\n\n    column.getFilterValue = () =>\n      table.getState().columnFilters?.find(d => d.id === column.id)?.value\n\n    column.getFilterIndex = () =>\n      table.getState().columnFilters?.findIndex(d => d.id === column.id) ?? -1\n\n    column.setFilterValue = value => {\n      table.setColumnFilters(old => {\n        const filterFn = column.getFilterFn()\n        const previousfilter = old?.find(d => d.id === column.id)\n\n        const newFilter = functionalUpdate(\n          value,\n          previousfilter ? previousfilter.value : undefined\n        )\n\n        //\n        if (\n          shouldAutoRemoveFilter(filterFn as FilterFn<TData>, newFilter, column)\n        ) {\n          return old?.filter(d => d.id !== column.id) ?? []\n        }\n\n        const newFilterObj = { id: column.id, value: newFilter }\n\n        if (previousfilter) {\n          return (\n            old?.map(d => {\n              if (d.id === column.id) {\n                return newFilterObj\n              }\n              return d\n            }) ?? []\n          )\n        }\n\n        if (old?.length) {\n          return [...old, newFilterObj]\n        }\n\n        return [newFilterObj]\n      })\n    }\n    column._getFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, column.id)\n    column.getFacetedRowModel = () => {\n      if (!column._getFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return column._getFacetedRowModel()\n    }\n    column._getFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, column.id)\n    column.getFacetedUniqueValues = () => {\n      if (!column._getFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return column._getFacetedUniqueValues()\n    }\n    column._getFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, column.id)\n    column.getFacetedMinMaxValues = () => {\n      if (!column._getFacetedMinMaxValues) {\n        return undefined\n      }\n\n      return column._getFacetedMinMaxValues()\n    }\n    // () => [column.getFacetedRowModel()],\n    // facetedRowModel => getRowModelMinMaxValues(facetedRowModel, column.id),\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.columnFilters = {}\n    row.columnFiltersMeta = {}\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.getGlobalAutoFilterFn = () => {\n      return filterFns.includesString\n    }\n\n    table.getGlobalFilterFn = () => {\n      const { globalFilterFn: globalFilterFn } = table.options\n\n      return isFunction(globalFilterFn)\n        ? globalFilterFn\n        : globalFilterFn === 'auto'\n          ? table.getGlobalAutoFilterFn()\n          : // @ts-ignore\n            table.options.filterFns?.[globalFilterFn as string] ??\n            filterFns[globalFilterFn as BuiltInFilterFn]\n    }\n\n    table.setColumnFilters = (updater: Updater<ColumnFiltersState>) => {\n      const leafColumns = table.getAllLeafColumns()\n\n      const updateFn = (old: ColumnFiltersState) => {\n        return functionalUpdate(updater, old)?.filter(filter => {\n          const column = leafColumns.find(d => d.id === filter.id)\n\n          if (column) {\n            const filterFn = column.getFilterFn()\n\n            if (shouldAutoRemoveFilter(filterFn, filter.value, column)) {\n              return false\n            }\n          }\n\n          return true\n        })\n      }\n\n      table.options.onColumnFiltersChange?.(updateFn)\n    }\n\n    table.setGlobalFilter = updater => {\n      table.options.onGlobalFilterChange?.(updater)\n    }\n\n    table.resetGlobalFilter = defaultState => {\n      table.setGlobalFilter(\n        defaultState ? undefined : table.initialState.globalFilter\n      )\n    }\n\n    table.resetColumnFilters = defaultState => {\n      table.setColumnFilters(\n        defaultState ? [] : table.initialState?.columnFilters ?? []\n      )\n    }\n\n    table.getPreFilteredRowModel = () => table.getCoreRowModel()\n    table.getFilteredRowModel = () => {\n      if (!table._getFilteredRowModel && table.options.getFilteredRowModel) {\n        table._getFilteredRowModel = table.options.getFilteredRowModel(table)\n      }\n\n      if (table.options.manualFiltering || !table._getFilteredRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getFilteredRowModel()\n    }\n\n    table._getGlobalFacetedRowModel =\n      table.options.getFacetedRowModel &&\n      table.options.getFacetedRowModel(table, '__global__')\n\n    table.getGlobalFacetedRowModel = () => {\n      if (table.options.manualFiltering || !table._getGlobalFacetedRowModel) {\n        return table.getPreFilteredRowModel()\n      }\n\n      return table._getGlobalFacetedRowModel()\n    }\n\n    table._getGlobalFacetedUniqueValues =\n      table.options.getFacetedUniqueValues &&\n      table.options.getFacetedUniqueValues(table, '__global__')\n    table.getGlobalFacetedUniqueValues = () => {\n      if (!table._getGlobalFacetedUniqueValues) {\n        return new Map()\n      }\n\n      return table._getGlobalFacetedUniqueValues()\n    }\n\n    table._getGlobalFacetedMinMaxValues =\n      table.options.getFacetedMinMaxValues &&\n      table.options.getFacetedMinMaxValues(table, '__global__')\n    table.getGlobalFacetedMinMaxValues = () => {\n      if (!table._getGlobalFacetedMinMaxValues) {\n        return\n      }\n\n      return table._getGlobalFacetedMinMaxValues()\n    }\n  },\n}\n\nexport function shouldAutoRemoveFilter<TData extends RowData>(\n  filterFn?: FilterFn<TData>,\n  value?: any,\n  column?: Column<TData, unknown>\n) {\n  return (\n    (filterFn && filterFn.autoRemove\n      ? filterFn.autoRemove(value, column)\n      : false) ||\n    typeof value === 'undefined' ||\n    (typeof value === 'string' && !value)\n  )\n}\n", "import { AggregationFn } from './features/Grouping'\nimport { isNumberArray } from './utils'\n\nconst sum: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  // It's faster to just add the aggregations together instead of\n  // process leaf nodes individually\n  return childRows.reduce((sum, next) => {\n    const nextValue = next.getValue(columnId)\n    return sum + (typeof nextValue === 'number' ? nextValue : 0)\n  }, 0)\n}\n\nconst min: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n\n    if (\n      value != null &&\n      (min! > value || (min === undefined && value >= value))\n    ) {\n      min = value\n    }\n  })\n\n  return min\n}\n\nconst max: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (\n      value != null &&\n      (max! < value || (max === undefined && value >= value))\n    ) {\n      max = value\n    }\n  })\n\n  return max\n}\n\nconst extent: AggregationFn<any> = (columnId, _leafRows, childRows) => {\n  let min: number | undefined\n  let max: number | undefined\n\n  childRows.forEach(row => {\n    const value = row.getValue<number>(columnId)\n    if (value != null) {\n      if (min === undefined) {\n        if (value >= value) min = max = value\n      } else {\n        if (min > value) min = value\n        if (max! < value) max = value\n      }\n    }\n  })\n\n  return [min, max]\n}\n\nconst mean: AggregationFn<any> = (columnId, leafRows) => {\n  let count = 0\n  let sum = 0\n\n  leafRows.forEach(row => {\n    let value = row.getValue<number>(columnId)\n    if (value != null && (value = +value) >= value) {\n      ++count, (sum += value)\n    }\n  })\n\n  if (count) return sum / count\n\n  return\n}\n\nconst median: AggregationFn<any> = (columnId, leafRows) => {\n  if (!leafRows.length) {\n    return\n  }\n\n  const values = leafRows.map(row => row.getValue(columnId))\n  if (!isNumberArray(values)) {\n    return\n  }\n  if (values.length === 1) {\n    return values[0]\n  }\n\n  const mid = Math.floor(values.length / 2)\n  const nums = values.sort((a, b) => a - b)\n  return values.length % 2 !== 0 ? nums[mid] : (nums[mid - 1]! + nums[mid]!) / 2\n}\n\nconst unique: AggregationFn<any> = (columnId, leafRows) => {\n  return Array.from(new Set(leafRows.map(d => d.getValue(columnId))).values())\n}\n\nconst uniqueCount: AggregationFn<any> = (columnId, leafRows) => {\n  return new Set(leafRows.map(d => d.getValue(columnId))).size\n}\n\nconst count: AggregationFn<any> = (_columnId, leafRows) => {\n  return leafRows.length\n}\n\nexport const aggregationFns = {\n  sum,\n  min,\n  max,\n  extent,\n  mean,\n  median,\n  unique,\n  uniqueCount,\n  count,\n}\n\nexport type BuiltInAggregationFn = keyof typeof aggregationFns\n", "import { RowModel } from '..'\nimport { BuiltInAggregationFn, aggregationFns } from '../aggregationFns'\nimport { TableFeature } from '../core/table'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  ColumnDefTemplate,\n  RowData,\n  AggregationFns,\n} from '../types'\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type GroupingState = string[]\n\nexport interface GroupingTableState {\n  grouping: GroupingState\n}\n\nexport type AggregationFn<TData extends RowData> = (\n  columnId: string,\n  leafRows: Row<TData>[],\n  childRows: Row<TData>[]\n) => any\n\nexport type CustomAggregationFns = Record<string, AggregationFn<any>>\n\nexport type AggregationFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof AggregationFns\n  | BuiltInAggregationFn\n  | AggregationFn<TData>\n\nexport interface GroupingColumnDef<TData extends RowData, TValue> {\n  /**\n   * The cell to display each row for the column if the cell is an aggregate. If a function is passed, it will be passed a props object with the context of the cell and should return the property type for your adapter (the exact type depends on the adapter being used).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregatedcell)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregatedCell?: ColumnDefTemplate<\n    ReturnType<Cell<TData, TValue>['getContext']>\n  >\n  /**\n   * The resolved aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#aggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  aggregationFn?: AggregationFnOption<TData>\n  /**\n   * Enables/disables grouping for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Specify a value to be used for grouping rows on this column. If this option is not specified, the value derived from `accessorKey` / `accessorFn` will be used instead.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue?: (row: TData) => any\n}\n\nexport interface GroupingColumn<TData extends RowData> {\n  /**\n   * Returns the aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns the automatically inferred aggregation function for the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getautoaggregationfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getAutoAggregationFn: () => AggregationFn<TData> | undefined\n  /**\n   * Returns whether or not the column can be grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getcangroup)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getCanGroup: () => boolean\n  /**\n   * Returns the index of the column in the grouping state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedIndex: () => number\n  /**\n   * Returns whether or not the column is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns a function that toggles the grouping state of the column. This is useful for passing to the `onClick` prop of a button.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#gettogglegroupinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getToggleGroupingHandler: () => () => void\n  /**\n   * Toggles the grouping state of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#togglegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  toggleGrouping: () => void\n}\n\nexport interface GroupingRow {\n  _groupingValuesCache: Record<string, any>\n  /**\n   * Returns the grouping value for any row and column (including leaf rows).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupingValue: (columnId: string) => unknown\n  /**\n   * Returns whether or not the row is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * If this row is grouped, this is the id of the column that this row is grouped by.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingcolumnid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingColumnId?: string\n  /**\n   * If this row is grouped, this is the unique/shared value for the `groupingColumnId` for all of the rows in this group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupingvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupingValue?: unknown\n}\n\nexport interface GroupingCell {\n  /**\n   * Returns whether or not the cell is currently aggregated.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisaggregated)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsAggregated: () => boolean\n  /**\n   * Returns whether or not the cell is currently grouped.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisgrouped)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsGrouped: () => boolean\n  /**\n   * Returns whether or not the cell is currently a placeholder cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getisplaceholder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getIsPlaceholder: () => boolean\n}\n\nexport interface ColumnDefaultOptions {\n  enableGrouping: boolean\n  onGroupingChange: OnChangeFn<GroupingState>\n}\n\ninterface GroupingOptionsBase {\n  /**\n   * Enables/disables grouping for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#enablegrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  enableGrouping?: boolean\n  /**\n   * Returns the row model after grouping has taken place, but no further.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Grouping columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#groupedcolumnmode)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  groupedColumnMode?: false | 'reorder' | 'remove'\n  /**\n   * Enables manual grouping. If this option is set to `true`, the table will not automatically group rows using `getGroupedRowModel()` and instead will expect you to manually group the rows before passing them to the table. This is useful if you are doing server-side grouping and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#manualgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  manualGrouping?: boolean\n  /**\n   * If this function is provided, it will be called when the grouping state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.grouping` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#ongroupingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  onGroupingChange?: OnChangeFn<GroupingState>\n}\n\ntype ResolvedAggregationFns = keyof AggregationFns extends never\n  ? {\n      aggregationFns?: Record<string, AggregationFn<any>>\n    }\n  : {\n      aggregationFns: Record<keyof AggregationFns, AggregationFn<any>>\n    }\n\nexport interface GroupingOptions\n  extends GroupingOptionsBase,\n    ResolvedAggregationFns {}\n\nexport type GroupingColumnMode = false | 'reorder' | 'remove'\n\nexport interface GroupingInstance<TData extends RowData> {\n  _getGroupedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getgroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getGroupedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#getpregroupedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  getPreGroupedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **grouping** state to `initialState.grouping`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#resetgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  resetGrouping: (defaultState?: boolean) => void\n  /**\n   * Updates the grouping state of the table via an update function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/grouping#setgrouping)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/grouping)\n   */\n  setGrouping: (updater: Updater<GroupingState>) => void\n}\n\n//\n\nexport const Grouping: TableFeature = {\n  getDefaultColumnDef: <TData extends RowData>(): GroupingColumnDef<\n    TData,\n    unknown\n  > => {\n    return {\n      aggregatedCell: props => (props.getValue() as any)?.toString?.() ?? null,\n      aggregationFn: 'auto',\n    }\n  },\n\n  getInitialState: (state): GroupingTableState => {\n    return {\n      grouping: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): GroupingOptions => {\n    return {\n      onGroupingChange: makeStateUpdater('grouping', table),\n      groupedColumnMode: 'reorder',\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleGrouping = () => {\n      table.setGrouping(old => {\n        // Find any existing grouping for this column\n        if (old?.includes(column.id)) {\n          return old.filter(d => d !== column.id)\n        }\n\n        return [...(old ?? []), column.id]\n      })\n    }\n\n    column.getCanGroup = () => {\n      return (\n        column.columnDef.enableGrouping ??\n        true ??\n        table.options.enableGrouping ??\n        true ??\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsGrouped = () => {\n      return table.getState().grouping?.includes(column.id)\n    }\n\n    column.getGroupedIndex = () => table.getState().grouping?.indexOf(column.id)\n\n    column.getToggleGroupingHandler = () => {\n      const canGroup = column.getCanGroup()\n\n      return () => {\n        if (!canGroup) return\n        column.toggleGrouping()\n      }\n    }\n    column.getAutoAggregationFn = () => {\n      const firstRow = table.getCoreRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'number') {\n        return aggregationFns.sum\n      }\n\n      if (Object.prototype.toString.call(value) === '[object Date]') {\n        return aggregationFns.extent\n      }\n    }\n    column.getAggregationFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.aggregationFn)\n        ? column.columnDef.aggregationFn\n        : column.columnDef.aggregationFn === 'auto'\n          ? column.getAutoAggregationFn()\n          : table.options.aggregationFns?.[\n              column.columnDef.aggregationFn as string\n            ] ??\n            aggregationFns[\n              column.columnDef.aggregationFn as BuiltInAggregationFn\n            ]\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setGrouping = updater => table.options.onGroupingChange?.(updater)\n\n    table.resetGrouping = defaultState => {\n      table.setGrouping(defaultState ? [] : table.initialState?.grouping ?? [])\n    }\n\n    table.getPreGroupedRowModel = () => table.getFilteredRowModel()\n    table.getGroupedRowModel = () => {\n      if (!table._getGroupedRowModel && table.options.getGroupedRowModel) {\n        table._getGroupedRowModel = table.options.getGroupedRowModel(table)\n      }\n\n      if (table.options.manualGrouping || !table._getGroupedRowModel) {\n        return table.getPreGroupedRowModel()\n      }\n\n      return table._getGroupedRowModel()\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.getIsGrouped = () => !!row.groupingColumnId\n    row.getGroupingValue = columnId => {\n      if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n        return row._groupingValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.columnDef.getGroupingValue) {\n        return row.getValue(columnId)\n      }\n\n      row._groupingValuesCache[columnId] = column.columnDef.getGroupingValue(\n        row.original\n      )\n\n      return row._groupingValuesCache[columnId]\n    }\n    row._groupingValuesCache = {}\n  },\n\n  createCell: <TData extends RowData, TValue>(\n    cell: Cell<TData, TValue>,\n    column: Column<TData, TValue>,\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    const getRenderValue = () =>\n      cell.getValue() ?? table.options.renderFallbackValue\n\n    cell.getIsGrouped = () =>\n      column.getIsGrouped() && column.id === row.groupingColumnId\n    cell.getIsPlaceholder = () => !cell.getIsGrouped() && column.getIsGrouped()\n    cell.getIsAggregated = () =>\n      !cell.getIsGrouped() && !cell.getIsPlaceholder() && !!row.subRows?.length\n  },\n}\n\nexport function orderColumns<TData extends RowData>(\n  leafColumns: Column<TData, unknown>[],\n  grouping: string[],\n  groupedColumnMode?: GroupingColumnMode\n) {\n  if (!grouping?.length || !groupedColumnMode) {\n    return leafColumns\n  }\n\n  const nonGroupingColumns = leafColumns.filter(\n    col => !grouping.includes(col.id)\n  )\n\n  if (groupedColumnMode === 'remove') {\n    return nonGroupingColumns\n  }\n\n  const groupingColumns = grouping\n    .map(g => leafColumns.find(col => col.id === g)!)\n    .filter(Boolean)\n\n  return [...groupingColumns, ...nonGroupingColumns]\n}\n", "import { makeStateUpdater, memo } from '../utils'\n\nimport { Table, OnChangeFn, Updater, Column, RowData } from '../types'\n\nimport { orderColumns } from './Grouping'\nimport { TableFeature } from '../core/table'\n\nexport interface ColumnOrderTableState {\n  columnOrder: ColumnOrderState\n}\n\nexport type ColumnOrderState = string[]\n\nexport interface ColumnOrderOptions {\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnOrder` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#oncolumnorderchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  onColumnOrderChange?: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderDefaultOptions {\n  onColumnOrderChange: OnChangeFn<ColumnOrderState>\n}\n\nexport interface ColumnOrderInstance<TData extends RowData> {\n  _getOrderColumnsFn: () => (\n    columns: Column<TData, unknown>[]\n  ) => Column<TData, unknown>[]\n  /**\n   * Resets the **columnOrder** state to `initialState.columnOrder`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#resetcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  resetColumnOrder: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnOrder` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-ordering#setcolumnorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-ordering)\n   */\n  setColumnOrder: (updater: Updater<ColumnOrderState>) => void\n}\n\n//\n\nexport const Ordering: TableFeature = {\n  getInitialState: (state): ColumnOrderTableState => {\n    return {\n      columnOrder: [],\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnOrderDefaultOptions => {\n    return {\n      onColumnOrderChange: makeStateUpdater('columnOrder', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnOrder = updater =>\n      table.options.onColumnOrderChange?.(updater)\n    table.resetColumnOrder = defaultState => {\n      table.setColumnOrder(\n        defaultState ? [] : table.initialState.columnOrder ?? []\n      )\n    }\n    table._getOrderColumnsFn = memo(\n      () => [\n        table.getState().columnOrder,\n        table.getState().grouping,\n        table.options.groupedColumnMode,\n      ],\n      (columnOrder, grouping, groupedColumnMode) => columns => {\n        // Sort grouped columns to the start of the column list\n        // before the headers are built\n        let orderedColumns: Column<TData, unknown>[] = []\n\n        // If there is no order, return the normal columns\n        if (!columnOrder?.length) {\n          orderedColumns = columns\n        } else {\n          const columnOrderCopy = [...columnOrder]\n\n          // If there is an order, make a copy of the columns\n          const columnsCopy = [...columns]\n\n          // And make a new ordered array of the columns\n\n          // Loop over the columns and place them in order into the new array\n          while (columnsCopy.length && columnOrderCopy.length) {\n            const targetColumnId = columnOrderCopy.shift()\n            const foundIndex = columnsCopy.findIndex(\n              d => d.id === targetColumnId\n            )\n            if (foundIndex > -1) {\n              orderedColumns.push(columnsCopy.splice(foundIndex, 1)[0]!)\n            }\n          }\n\n          // If there are any columns left, add them to the end\n          orderedColumns = [...orderedColumns, ...columnsCopy]\n        }\n\n        return orderColumns(orderedColumns, grouping, groupedColumnMode)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getOrderColumnsFn',\n        // debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, RowModel, Updater, RowData } from '../types'\nimport { functionalUpdate, makeStateUpdater, memo } from '../utils'\n\nexport interface PaginationState {\n  pageIndex: number\n  pageSize: number\n}\n\nexport interface PaginationTableState {\n  pagination: PaginationState\n}\n\nexport interface PaginationInitialTableState {\n  pagination?: Partial<PaginationState>\n}\n\nexport interface PaginationOptions {\n  /**\n   * If set to `true`, pagination will be reset to the first page when page-altering state changes eg. `data` is updated, filters change, grouping changes, etc.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#autoresetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  autoResetPageIndex?: boolean\n  /**\n   * Returns the row model after pagination has taken place, but no further.\n   *\n   * Pagination columns are automatically reordered by default to the start of the columns list. If you would rather remove them or leave them as-is, set the appropriate mode here.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Enables manual pagination. If this option is set to `true`, the table will not automatically paginate rows using `getPaginationRowModel()` and instead will expect you to manually paginate the rows before passing them to the table. This is useful if you are doing server-side pagination and aggregation.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#manualpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  manualPagination?: boolean\n  /**\n   * If this function is provided, it will be called when the pagination state changes and you will be expected to manage the state yourself. You can pass the managed state back to the table via the `tableOptions.state.pagination` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#onpaginationchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  onPaginationChange?: OnChangeFn<PaginationState>\n  /**\n   * When manually controlling pagination, you should supply a total `pageCount` value to the table if you know it. If you do not know how many pages there are, you can set this to `-1`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#pagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  pageCount?: number\n}\n\nexport interface PaginationDefaultOptions {\n  onPaginationChange: OnChangeFn<PaginationState>\n}\n\nexport interface PaginationInstance<TData extends RowData> {\n  _autoResetPageIndex: () => void\n  _getPaginationRowModel?: () => RowModel<TData>\n  /**\n   * Returns whether the table can go to the next page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcannextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanNextPage: () => boolean\n  /**\n   * Returns whether the table can go to the previous page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getcanpreviouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getCanPreviousPage: () => boolean\n  /**\n   * Returns the page count. If manually paginating or controlling the pagination state, this will come directly from the `options.pageCount` table option, otherwise it will be calculated from the table data using the total row count and current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageCount: () => number\n  /**\n   * Returns an array of page options (zero-index-based) for the current page size.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpageoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPageOptions: () => number[]\n  /**\n   * Returns the row model for the table after pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getpaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPaginationRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any pagination has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#getprepaginationrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  getPrePaginationRowModel: () => RowModel<TData>\n  /**\n   * Increments the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#nextpage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  nextPage: () => void\n  /**\n   * Decrements the page index by one, if possible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#previouspage)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  previousPage: () => void\n  /**\n   * Resets the page index to its initial state. If `defaultState` is `true`, the page index will be reset to `0` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageIndex: (defaultState?: boolean) => void\n  /**\n   * Resets the page size to its initial state. If `defaultState` is `true`, the page size will be reset to `10` regardless of initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPageSize: (defaultState?: boolean) => void\n  /**\n   * Resets the **pagination** state to `initialState.pagination`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#resetpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  resetPagination: (defaultState?: boolean) => void\n  /**\n   * Updates the page count using the provided function or value.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagecount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageCount: (updater: Updater<number>) => void\n  /**\n   * Updates the page index using the provided function or value in the `state.pagination.pageIndex` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpageindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageIndex: (updater: Updater<number>) => void\n  /**\n   * Updates the page size using the provided function or value in the `state.pagination.pageSize` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagesize)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPageSize: (updater: Updater<number>) => void\n  /**\n   * Sets or updates the `state.pagination` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pagination#setpagination)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pagination)\n   */\n  setPagination: (updater: Updater<PaginationState>) => void\n}\n\n//\n\nconst defaultPageIndex = 0\nconst defaultPageSize = 10\n\nconst getDefaultPaginationState = (): PaginationState => ({\n  pageIndex: defaultPageIndex,\n  pageSize: defaultPageSize,\n})\n\nexport const Pagination: TableFeature = {\n  getInitialState: (state): PaginationTableState => {\n    return {\n      ...state,\n      pagination: {\n        ...getDefaultPaginationState(),\n        ...state?.pagination,\n      },\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): PaginationDefaultOptions => {\n    return {\n      onPaginationChange: makeStateUpdater('pagination', table),\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    let registered = false\n    let queued = false\n\n    table._autoResetPageIndex = () => {\n      if (!registered) {\n        table._queue(() => {\n          registered = true\n        })\n        return\n      }\n\n      if (\n        table.options.autoResetAll ??\n        table.options.autoResetPageIndex ??\n        !table.options.manualPagination\n      ) {\n        if (queued) return\n        queued = true\n        table._queue(() => {\n          table.resetPageIndex()\n          queued = false\n        })\n      }\n    }\n    table.setPagination = updater => {\n      const safeUpdater: Updater<PaginationState> = old => {\n        let newState = functionalUpdate(updater, old)\n\n        return newState\n      }\n\n      return table.options.onPaginationChange?.(safeUpdater)\n    }\n    table.resetPagination = defaultState => {\n      table.setPagination(\n        defaultState\n          ? getDefaultPaginationState()\n          : table.initialState.pagination ?? getDefaultPaginationState()\n      )\n    }\n    table.setPageIndex = updater => {\n      table.setPagination(old => {\n        let pageIndex = functionalUpdate(updater, old.pageIndex)\n\n        const maxPageIndex =\n          typeof table.options.pageCount === 'undefined' ||\n          table.options.pageCount === -1\n            ? Number.MAX_SAFE_INTEGER\n            : table.options.pageCount - 1\n\n        pageIndex = Math.max(0, Math.min(pageIndex, maxPageIndex))\n\n        return {\n          ...old,\n          pageIndex,\n        }\n      })\n    }\n    table.resetPageIndex = defaultState => {\n      table.setPageIndex(\n        defaultState\n          ? defaultPageIndex\n          : table.initialState?.pagination?.pageIndex ?? defaultPageIndex\n      )\n    }\n    table.resetPageSize = defaultState => {\n      table.setPageSize(\n        defaultState\n          ? defaultPageSize\n          : table.initialState?.pagination?.pageSize ?? defaultPageSize\n      )\n    }\n    table.setPageSize = updater => {\n      table.setPagination(old => {\n        const pageSize = Math.max(1, functionalUpdate(updater, old.pageSize))\n        const topRowIndex = old.pageSize * old.pageIndex!\n        const pageIndex = Math.floor(topRowIndex / pageSize)\n\n        return {\n          ...old,\n          pageIndex,\n          pageSize,\n        }\n      })\n    }\n    table.setPageCount = updater =>\n      table.setPagination(old => {\n        let newPageCount = functionalUpdate(\n          updater,\n          table.options.pageCount ?? -1\n        )\n\n        if (typeof newPageCount === 'number') {\n          newPageCount = Math.max(-1, newPageCount)\n        }\n\n        return {\n          ...old,\n          pageCount: newPageCount,\n        }\n      })\n\n    table.getPageOptions = memo(\n      () => [table.getPageCount()],\n      pageCount => {\n        let pageOptions: number[] = []\n        if (pageCount && pageCount > 0) {\n          pageOptions = [...new Array(pageCount)].fill(null).map((_, i) => i)\n        }\n        return pageOptions\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getPageOptions',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n\n    table.getCanPreviousPage = () => table.getState().pagination.pageIndex > 0\n\n    table.getCanNextPage = () => {\n      const { pageIndex } = table.getState().pagination\n\n      const pageCount = table.getPageCount()\n\n      if (pageCount === -1) {\n        return true\n      }\n\n      if (pageCount === 0) {\n        return false\n      }\n\n      return pageIndex < pageCount - 1\n    }\n\n    table.previousPage = () => {\n      return table.setPageIndex(old => old - 1)\n    }\n\n    table.nextPage = () => {\n      return table.setPageIndex(old => {\n        return old + 1\n      })\n    }\n\n    table.getPrePaginationRowModel = () => table.getExpandedRowModel()\n    table.getPaginationRowModel = () => {\n      if (\n        !table._getPaginationRowModel &&\n        table.options.getPaginationRowModel\n      ) {\n        table._getPaginationRowModel =\n          table.options.getPaginationRowModel(table)\n      }\n\n      if (table.options.manualPagination || !table._getPaginationRowModel) {\n        return table.getPrePaginationRowModel()\n      }\n\n      return table._getPaginationRowModel()\n    }\n\n    table.getPageCount = () => {\n      return (\n        table.options.pageCount ??\n        Math.ceil(\n          table.getPrePaginationRowModel().rows.length /\n            table.getState().pagination.pageSize\n        )\n      )\n    }\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport {\n  OnChangeFn,\n  Updater,\n  Table,\n  Column,\n  Row,\n  Cell,\n  RowData,\n} from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type ColumnPinningPosition = false | 'left' | 'right'\nexport type RowPinningPosition = false | 'top' | 'bottom'\n\nexport interface ColumnPinningState {\n  left?: string[]\n  right?: string[]\n}\n\nexport interface RowPinningState {\n  bottom?: string[]\n  top?: string[]\n}\n\nexport interface ColumnPinningTableState {\n  columnPinning: ColumnPinningState\n}\n\nexport interface RowPinningTableState {\n  rowPinning: RowPinningState\n}\n\nexport interface ColumnPinningOptions {\n  /**\n   * Enables/disables column pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#enablecolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  enableColumnPinning?: boolean\n  /**\n   * Enables/disables all pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#enablepinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  enablePinning?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnPinning` changes. This overrides the default internal state management, so you will also need to supply `state.columnPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#oncolumnpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/oncolumnpinningchange)\n   */\n  onColumnPinningChange?: OnChangeFn<ColumnPinningState>\n}\n\nexport interface RowPinningOptions<TData extends RowData> {\n  /**\n   * Enables/disables row pinning for the table. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#enablerowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  enableRowPinning?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * When `false`, pinned rows will not be visible if they are filtered or paginated out of the table. When `true`, pinned rows will always be visible regardless of filtering or pagination. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#keeppinnedrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  keepPinnedRows?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowPinning` changes. This overrides the default internal state management, so you will also need to supply `state.rowPinning` from your own managed state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#onrowpinningchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/onrowpinningchange)\n   */\n  onRowPinningChange?: OnChangeFn<RowPinningState>\n}\n\nexport interface ColumnPinningDefaultOptions {\n  onColumnPinningChange: OnChangeFn<ColumnPinningState>\n}\n\nexport interface RowPinningDefaultOptions {\n  onRowPinningChange: OnChangeFn<RowPinningState>\n}\n\nexport interface ColumnPinningColumnDef {\n  /**\n   * Enables/disables column pinning for this column. Defaults to `true`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#enablepinning-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  enablePinning?: boolean\n}\n\nexport interface ColumnPinningColumn {\n  /**\n   * Returns whether or not the column can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getcanpin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the column. (`'left'`, `'right'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getispinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getIsPinned: () => ColumnPinningPosition\n  /**\n   * Returns the numeric pinned index of the column within a pinned column group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getpinnedindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a column to the `'left'` or `'right'`, or unpins the column to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#pin)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  pin: (position: ColumnPinningPosition) => void\n}\n\nexport interface ColumnPinningRow<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getcentervisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getCenterVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all left pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getleftvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getLeftVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf cells in the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getrightvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getRightVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface RowPinningRow {\n  /**\n   * Returns whether or not the row can be pinned.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getcanpin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getCanPin: () => boolean\n  /**\n   * Returns the pinned position of the row. (`'top'`, `'bottom'` or `false`)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getispinned-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getIsPinned: () => RowPinningPosition\n  /**\n   * Returns the numeric pinned index of the row within a pinned row group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getpinnedindex-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getPinnedIndex: () => number\n  /**\n   * Pins a row to the `'top'` or `'bottom'`, or unpins the row to the center if `false` is passed.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#pin-1)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  pin: (\n    position: RowPinningPosition,\n    includeLeafRows?: boolean,\n    includeParentRows?: boolean\n  ) => void\n}\n\nexport interface ColumnPinningInstance<TData extends RowData> {\n  /**\n   * Returns all center pinned (unpinned) leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getcenterleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getCenterLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether or not any columns are pinned. Optionally specify to only check for pinned columns in either the `left` or `right` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getissomecolumnspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getIsSomeColumnsPinned: (position?: ColumnPinningPosition) => boolean\n  /**\n   * Returns all left pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getleftleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getLeftLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all right pinned leaf columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getrightleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getRightLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the **columnPinning** state to `initialState.columnPinning`, or `true` can be passed to force a default blank state reset to `{ left: [], right: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#resetcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  resetColumnPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#setcolumnpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  setColumnPinning: (updater: Updater<ColumnPinningState>) => void\n}\n\nexport interface RowPinningInstance<TData extends RowData> {\n  _getPinnedRows: (position: 'top' | 'bottom') => Row<TData>[]\n  /**\n   * Returns all bottom pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getbottomrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getBottomRows: () => Row<TData>[]\n  /**\n   * Returns all rows that are not pinned to the top or bottom.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getcenterrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getCenterRows: () => Row<TData>[]\n  /**\n   * Returns whether or not any rows are pinned. Optionally specify to only check for pinned rows in either the `top` or `bottom` position.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#getissomerowspinned)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getIsSomeRowsPinned: (position?: RowPinningPosition) => boolean\n  /**\n   * Returns all top pinned rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#gettoprows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  getTopRows: () => Row<TData>[]\n  /**\n   * Resets the **rowPinning** state to `initialState.rowPinning`, or `true` can be passed to force a default blank state reset to `{ top: [], bottom: [], }`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#resetrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  resetRowPinning: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowPinning` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/pinning#setrowpinning)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/pinning)\n   */\n  setRowPinning: (updater: Updater<RowPinningState>) => void\n}\n\n//\n\nconst getDefaultColumnPinningState = (): ColumnPinningState => ({\n  left: [],\n  right: [],\n})\n\nconst getDefaultRowPinningState = (): RowPinningState => ({\n  top: [],\n  bottom: [],\n})\n\nexport const Pinning: TableFeature = {\n  getInitialState: (state): ColumnPinningTableState & RowPinningState => {\n    return {\n      columnPinning: getDefaultColumnPinningState(),\n      rowPinning: getDefaultRowPinningState(),\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): ColumnPinningDefaultOptions & RowPinningDefaultOptions => {\n    return {\n      onColumnPinningChange: makeStateUpdater('columnPinning', table),\n      onRowPinningChange: makeStateUpdater('rowPinning', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.pin = position => {\n      const columnIds = column\n        .getLeafColumns()\n        .map(d => d.id)\n        .filter(Boolean) as string[]\n\n      table.setColumnPinning(old => {\n        if (position === 'right') {\n          return {\n            left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n            right: [\n              ...(old?.right ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n          }\n        }\n\n        if (position === 'left') {\n          return {\n            left: [\n              ...(old?.left ?? []).filter(d => !columnIds?.includes(d)),\n              ...columnIds,\n            ],\n            right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n          }\n        }\n\n        return {\n          left: (old?.left ?? []).filter(d => !columnIds?.includes(d)),\n          right: (old?.right ?? []).filter(d => !columnIds?.includes(d)),\n        }\n      })\n    }\n\n    column.getCanPin = () => {\n      const leafColumns = column.getLeafColumns()\n\n      return leafColumns.some(\n        d =>\n          (d.columnDef.enablePinning ?? true) &&\n          (table.options.enableColumnPinning ??\n            table.options.enablePinning ??\n            true)\n      )\n    }\n\n    column.getIsPinned = () => {\n      const leafColumnIds = column.getLeafColumns().map(d => d.id)\n\n      const { left, right } = table.getState().columnPinning\n\n      const isLeft = leafColumnIds.some(d => left?.includes(d))\n      const isRight = leafColumnIds.some(d => right?.includes(d))\n\n      return isLeft ? 'left' : isRight ? 'right' : false\n    }\n\n    column.getPinnedIndex = () => {\n      const position = column.getIsPinned()\n\n      return position\n        ? table.getState().columnPinning?.[position]?.indexOf(column.id) ?? -1\n        : 0\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.pin = (position, includeLeafRows, includeParentRows) => {\n      const leafRowIds = includeLeafRows\n        ? row.getLeafRows().map(({ id }) => id)\n        : []\n      const parentRowIds = includeParentRows\n        ? row.getParentRows().map(({ id }) => id)\n        : []\n      const rowIds = new Set([...parentRowIds, row.id, ...leafRowIds])\n\n      table.setRowPinning(old => {\n        if (position === 'bottom') {\n          return {\n            top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n            bottom: [\n              ...(old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n          }\n        }\n\n        if (position === 'top') {\n          return {\n            top: [\n              ...(old?.top ?? []).filter(d => !rowIds?.has(d)),\n              ...Array.from(rowIds),\n            ],\n            bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n          }\n        }\n\n        return {\n          top: (old?.top ?? []).filter(d => !rowIds?.has(d)),\n          bottom: (old?.bottom ?? []).filter(d => !rowIds?.has(d)),\n        }\n      })\n    }\n    row.getCanPin = () => {\n      const { enableRowPinning, enablePinning } = table.options\n      if (typeof enableRowPinning === 'function') {\n        return enableRowPinning(row)\n      }\n      return enableRowPinning ?? enablePinning ?? true\n    }\n    row.getIsPinned = () => {\n      const rowIds = [row.id]\n\n      const { top, bottom } = table.getState().rowPinning\n\n      const isTop = rowIds.some(d => top?.includes(d))\n      const isBottom = rowIds.some(d => bottom?.includes(d))\n\n      return isTop ? 'top' : isBottom ? 'bottom' : false\n    }\n    row.getPinnedIndex = () => {\n      const position = row.getIsPinned()\n      if (!position) return -1\n\n      const visiblePinnedRowIds = table\n        ._getPinnedRows(position)\n        ?.map(({ id }) => id)\n\n      return visiblePinnedRowIds?.indexOf(row.id) ?? -1\n    }\n    row.getCenterVisibleCells = memo(\n      () => [\n        row._getAllVisibleCells(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allCells, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allCells.filter(d => !leftAndRight.includes(d.column.id))\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' && 'row.getCenterVisibleCells',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    )\n    row.getLeftVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.left, ,],\n      (allCells, left) => {\n        const cells = (left ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'left' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' && 'row.getLeftVisibleCells',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    )\n    row.getRightVisibleCells = memo(\n      () => [row._getAllVisibleCells(), table.getState().columnPinning.right],\n      (allCells, right) => {\n        const cells = (right ?? [])\n          .map(columnId => allCells.find(cell => cell.column.id === columnId)!)\n          .filter(Boolean)\n          .map(d => ({ ...d, position: 'right' }) as Cell<TData, unknown>)\n\n        return cells\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' && 'row.getRightVisibleCells',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setColumnPinning = updater =>\n      table.options.onColumnPinningChange?.(updater)\n\n    table.resetColumnPinning = defaultState =>\n      table.setColumnPinning(\n        defaultState\n          ? getDefaultColumnPinningState()\n          : table.initialState?.columnPinning ?? getDefaultColumnPinningState()\n      )\n\n    table.getIsSomeColumnsPinned = position => {\n      const pinningState = table.getState().columnPinning\n\n      if (!position) {\n        return Boolean(pinningState.left?.length || pinningState.right?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table.getLeftLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.left],\n      (allColumns, left) => {\n        return (left ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getLeftLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    )\n\n    table.getRightLeafColumns = memo(\n      () => [table.getAllLeafColumns(), table.getState().columnPinning.right],\n      (allColumns, right) => {\n        return (right ?? [])\n          .map(columnId => allColumns.find(column => column.id === columnId)!)\n          .filter(Boolean)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getRightLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    )\n\n    table.getCenterLeafColumns = memo(\n      () => [\n        table.getAllLeafColumns(),\n        table.getState().columnPinning.left,\n        table.getState().columnPinning.right,\n      ],\n      (allColumns, left, right) => {\n        const leftAndRight: string[] = [...(left ?? []), ...(right ?? [])]\n\n        return allColumns.filter(d => !leftAndRight.includes(d.id))\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getCenterLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    )\n\n    table.setRowPinning = updater => table.options.onRowPinningChange?.(updater)\n\n    table.resetRowPinning = defaultState =>\n      table.setRowPinning(\n        defaultState\n          ? getDefaultRowPinningState()\n          : table.initialState?.rowPinning ?? getDefaultRowPinningState()\n      )\n\n    table.getIsSomeRowsPinned = position => {\n      const pinningState = table.getState().rowPinning\n\n      if (!position) {\n        return Boolean(pinningState.top?.length || pinningState.bottom?.length)\n      }\n      return Boolean(pinningState[position]?.length)\n    }\n\n    table._getPinnedRows = (position: 'top' | 'bottom') =>\n      memo(\n        () => [table.getRowModel().rows, table.getState().rowPinning[position]],\n        (visibleRows, pinnedRowIds) => {\n          const rows =\n            table.options.keepPinnedRows ?? true\n              ? //get all rows that are pinned even if they would not be otherwise visible\n                //account for expanded parent rows, but not pagination or filtering\n                (pinnedRowIds ?? []).map(rowId => {\n                  const row = table.getRow(rowId, true)\n                  return row.getIsAllParentsExpanded() ? row : null\n                })\n              : //else get only visible rows that are pinned\n                (pinnedRowIds ?? []).map(\n                  rowId => visibleRows.find(row => row.id === rowId)!\n                )\n\n          return rows\n            .filter(Boolean)\n            .map(d => ({ ...d, position })) as Row<TData>[]\n        },\n        {\n          key:\n            process.env.NODE_ENV === 'development' &&\n            `row.get${position === 'top' ? 'Top' : 'Bottom'}Rows`,\n          debug: () => table.options.debugAll ?? table.options.debugRows,\n        }\n      )()\n\n    table.getTopRows = () => table._getPinnedRows('top')\n\n    table.getBottomRows = () => table._getPinnedRows('bottom')\n\n    table.getCenterRows = memo(\n      () => [\n        table.getRowModel().rows,\n        table.getState().rowPinning.top,\n        table.getState().rowPinning.bottom,\n      ],\n      (allRows, top, bottom) => {\n        const topAndBottom = new Set([...(top ?? []), ...(bottom ?? [])])\n        return allRows.filter(d => !topAndBottom.has(d.id))\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'row.getCenterRows',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    )\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport { OnChangeFn, Table, Row, RowModel, Updater, RowData } from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type RowSelectionState = Record<string, boolean>\n\nexport interface RowSelectionTableState {\n  rowSelection: RowSelectionState\n}\n\nexport interface RowSelectionOptions<TData extends RowData> {\n  /**\n   * - Enables/disables multiple row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable multiple row selection for that row's children/grandchildren\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablemultirowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableMultiRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * - Enables/disables row selection for all rows in the table OR\n   * - A function that given a row, returns whether to enable/disable row selection for that row\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablerowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * Enables/disables automatic sub-row selection when a parent row is selected, or a function that enables/disables automatic sub-row selection for each row.\n   * (Use in combination with expanding or grouping features)\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#enablesubrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  enableSubRowSelection?: boolean | ((row: Row<TData>) => boolean)\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.rowSelection` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#onrowselectionchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  onRowSelectionChange?: OnChangeFn<RowSelectionState>\n  // enableGroupingRowSelection?:\n  //   | boolean\n  //   | ((\n  //       row: Row<TData>\n  //     ) => boolean)\n  // isAdditiveSelectEvent?: (e: unknown) => boolean\n  // isInclusiveSelectEvent?: (e: unknown) => boolean\n  // selectRowsFn?: (\n  //   table: Table<TData>,\n  //   rowModel: RowModel<TData>\n  // ) => RowModel<TData>\n}\n\nexport interface RowSelectionRow {\n  /**\n   * Returns whether or not the row can multi-select.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanmultiselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanMultiSelect: () => boolean\n  /**\n   * Returns whether or not the row can be selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselect)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelect: () => boolean\n  /**\n   * Returns whether or not the row can select sub rows automatically when the parent row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getcanselectsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getCanSelectSubRows: () => boolean\n  /**\n   * Returns whether or not all of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallsubrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllSubRowsSelected: () => boolean\n  /**\n   * Returns whether or not the row is selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSelected: () => boolean\n  /**\n   * Returns whether or not some of the row's sub rows are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomeselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeSelected: () => boolean\n  /**\n   * Returns a handler that can be used to toggle the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleSelectedHandler: () => (event: unknown) => void\n  /**\n   * Selects/deselects the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleSelected: (value?: boolean, opts?: { selectChildren?: boolean }) => void\n}\n\nexport interface RowSelectionInstance<TData extends RowData> {\n  /**\n   * Returns the row model of all rows that are selected after filtering has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getfilteredselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getFilteredSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected after grouping has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getgroupedselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getGroupedSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns whether or not all rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllPageRowsSelected: () => boolean\n  /**\n   * Returns whether or not all rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getisallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsAllRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows on the current page are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomepagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomePageRowsSelected: () => boolean\n  /**\n   * Returns whether or not any rows in the table are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getissomerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getIsSomeRowsSelected: () => boolean\n  /**\n   * Returns the core row model of all rows before row selection has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getpreselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getPreSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model of all rows that are selected.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#getselectedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getSelectedRowModel: () => RowModel<TData>\n  /**\n   * Returns a handler that can be used to toggle all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallpagerowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllPageRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Returns a handler that can be used to toggle all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#gettoggleallrowsselectedhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  getToggleAllRowsSelectedHandler: () => (event: unknown) => void\n  /**\n   * Resets the **rowSelection** state to the `initialState.rowSelection`, or `true` can be passed to force a default blank state reset to `{}`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#resetrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  resetRowSelection: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.rowSelection` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#setrowselection)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  setRowSelection: (updater: Updater<RowSelectionState>) => void\n  /**\n   * Selects/deselects all rows on the current page.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallpagerowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllPageRowsSelected: (value?: boolean) => void\n  /**\n   * Selects/deselects all rows in the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/row-selection#toggleallrowsselected)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/row-selection)\n   */\n  toggleAllRowsSelected: (value?: boolean) => void\n}\n\n//\n\nexport const RowSelection: TableFeature = {\n  getInitialState: (state): RowSelectionTableState => {\n    return {\n      rowSelection: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): RowSelectionOptions<TData> => {\n    return {\n      onRowSelectionChange: makeStateUpdater('rowSelection', table),\n      enableRowSelection: true,\n      enableMultiRowSelection: true,\n      enableSubRowSelection: true,\n      // enableGroupingRowSelection: false,\n      // isAdditiveSelectEvent: (e: unknown) => !!e.metaKey,\n      // isInclusiveSelectEvent: (e: unknown) => !!e.shiftKey,\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setRowSelection = updater =>\n      table.options.onRowSelectionChange?.(updater)\n    table.resetRowSelection = defaultState =>\n      table.setRowSelection(\n        defaultState ? {} : table.initialState.rowSelection ?? {}\n      )\n    table.toggleAllRowsSelected = value => {\n      table.setRowSelection(old => {\n        value =\n          typeof value !== 'undefined' ? value : !table.getIsAllRowsSelected()\n\n        const rowSelection = { ...old }\n\n        const preGroupedFlatRows = table.getPreGroupedRowModel().flatRows\n\n        // We don't use `mutateRowIsSelected` here for performance reasons.\n        // All of the rows are flat already, so it wouldn't be worth it\n        if (value) {\n          preGroupedFlatRows.forEach(row => {\n            if (!row.getCanSelect()) {\n              return\n            }\n            rowSelection[row.id] = true\n          })\n        } else {\n          preGroupedFlatRows.forEach(row => {\n            delete rowSelection[row.id]\n          })\n        }\n\n        return rowSelection\n      })\n    }\n    table.toggleAllPageRowsSelected = value =>\n      table.setRowSelection(old => {\n        const resolvedValue =\n          typeof value !== 'undefined'\n            ? value\n            : !table.getIsAllPageRowsSelected()\n\n        const rowSelection: RowSelectionState = { ...old }\n\n        table.getRowModel().rows.forEach(row => {\n          mutateRowIsSelected(rowSelection, row.id, resolvedValue, true, table)\n        })\n\n        return rowSelection\n      })\n\n    // addRowSelectionRange: rowId => {\n    //   const {\n    //     rows,\n    //     rowsById,\n    //     options: { selectGroupingRows, selectSubRows },\n    //   } = table\n\n    //   const findSelectedRow = (rows: Row[]) => {\n    //     let found\n    //     rows.find(d => {\n    //       if (d.getIsSelected()) {\n    //         found = d\n    //         return true\n    //       }\n    //       const subFound = findSelectedRow(d.subRows || [])\n    //       if (subFound) {\n    //         found = subFound\n    //         return true\n    //       }\n    //       return false\n    //     })\n    //     return found\n    //   }\n\n    //   const firstRow = findSelectedRow(rows) || rows[0]\n    //   const lastRow = rowsById[rowId]\n\n    //   let include = false\n    //   const selectedRowIds = {}\n\n    //   const addRow = (row: Row) => {\n    //     mutateRowIsSelected(selectedRowIds, row.id, true, {\n    //       rowsById,\n    //       selectGroupingRows: selectGroupingRows!,\n    //       selectSubRows: selectSubRows!,\n    //     })\n    //   }\n\n    //   table.rows.forEach(row => {\n    //     const isFirstRow = row.id === firstRow.id\n    //     const isLastRow = row.id === lastRow.id\n\n    //     if (isFirstRow || isLastRow) {\n    //       if (!include) {\n    //         include = true\n    //       } else if (include) {\n    //         addRow(row)\n    //         include = false\n    //       }\n    //     }\n\n    //     if (include) {\n    //       addRow(row)\n    //     }\n    //   })\n\n    //   table.setRowSelection(selectedRowIds)\n    // },\n    table.getPreSelectedRowModel = () => table.getCoreRowModel()\n    table.getSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getCoreRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getSelectedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n\n    table.getFilteredSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getFilteredRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'production' &&\n          'getFilteredSelectedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n\n    table.getGroupedSelectedRowModel = memo(\n      () => [table.getState().rowSelection, table.getSortedRowModel()],\n      (rowSelection, rowModel) => {\n        if (!Object.keys(rowSelection).length) {\n          return {\n            rows: [],\n            flatRows: [],\n            rowsById: {},\n          }\n        }\n\n        return selectRowsFn(table, rowModel)\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'production' && 'getGroupedSelectedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n\n    ///\n\n    // getGroupingRowCanSelect: rowId => {\n    //   const row = table.getRow(rowId)\n\n    //   if (!row) {\n    //     throw new Error()\n    //   }\n\n    //   if (typeof table.options.enableGroupingRowSelection === 'function') {\n    //     return table.options.enableGroupingRowSelection(row)\n    //   }\n\n    //   return table.options.enableGroupingRowSelection ?? false\n    // },\n\n    table.getIsAllRowsSelected = () => {\n      const preGroupedFlatRows = table.getFilteredRowModel().flatRows\n      const { rowSelection } = table.getState()\n\n      let isAllRowsSelected = Boolean(\n        preGroupedFlatRows.length && Object.keys(rowSelection).length\n      )\n\n      if (isAllRowsSelected) {\n        if (\n          preGroupedFlatRows.some(\n            row => row.getCanSelect() && !rowSelection[row.id]\n          )\n        ) {\n          isAllRowsSelected = false\n        }\n      }\n\n      return isAllRowsSelected\n    }\n\n    table.getIsAllPageRowsSelected = () => {\n      const paginationFlatRows = table\n        .getPaginationRowModel()\n        .flatRows.filter(row => row.getCanSelect())\n      const { rowSelection } = table.getState()\n\n      let isAllPageRowsSelected = !!paginationFlatRows.length\n\n      if (\n        isAllPageRowsSelected &&\n        paginationFlatRows.some(row => !rowSelection[row.id])\n      ) {\n        isAllPageRowsSelected = false\n      }\n\n      return isAllPageRowsSelected\n    }\n\n    table.getIsSomeRowsSelected = () => {\n      const totalSelected = Object.keys(\n        table.getState().rowSelection ?? {}\n      ).length\n      return (\n        totalSelected > 0 &&\n        totalSelected < table.getFilteredRowModel().flatRows.length\n      )\n    }\n\n    table.getIsSomePageRowsSelected = () => {\n      const paginationFlatRows = table.getPaginationRowModel().flatRows\n      return table.getIsAllPageRowsSelected()\n        ? false\n        : paginationFlatRows\n            .filter(row => row.getCanSelect())\n            .some(d => d.getIsSelected() || d.getIsSomeSelected())\n    }\n\n    table.getToggleAllRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n\n    table.getToggleAllPageRowsSelectedHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllPageRowsSelected(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row.toggleSelected = (value, opts) => {\n      const isSelected = row.getIsSelected()\n\n      table.setRowSelection(old => {\n        value = typeof value !== 'undefined' ? value : !isSelected\n\n        if (row.getCanSelect() && isSelected === value) {\n          return old\n        }\n\n        const selectedRowIds = { ...old }\n\n        mutateRowIsSelected(\n          selectedRowIds,\n          row.id,\n          value,\n          opts?.selectChildren ?? true,\n          table\n        )\n\n        return selectedRowIds\n      })\n    }\n    row.getIsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isRowSelected(row, rowSelection)\n    }\n\n    row.getIsSomeSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'some'\n    }\n\n    row.getIsAllSubRowsSelected = () => {\n      const { rowSelection } = table.getState()\n      return isSubRowSelected(row, rowSelection, table) === 'all'\n    }\n\n    row.getCanSelect = () => {\n      if (typeof table.options.enableRowSelection === 'function') {\n        return table.options.enableRowSelection(row)\n      }\n\n      return table.options.enableRowSelection ?? true\n    }\n\n    row.getCanSelectSubRows = () => {\n      if (typeof table.options.enableSubRowSelection === 'function') {\n        return table.options.enableSubRowSelection(row)\n      }\n\n      return table.options.enableSubRowSelection ?? true\n    }\n\n    row.getCanMultiSelect = () => {\n      if (typeof table.options.enableMultiRowSelection === 'function') {\n        return table.options.enableMultiRowSelection(row)\n      }\n\n      return table.options.enableMultiRowSelection ?? true\n    }\n    row.getToggleSelectedHandler = () => {\n      const canSelect = row.getCanSelect()\n\n      return (e: unknown) => {\n        if (!canSelect) return\n        row.toggleSelected(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n\nconst mutateRowIsSelected = <TData extends RowData>(\n  selectedRowIds: Record<string, boolean>,\n  id: string,\n  value: boolean,\n  includeChildren: boolean,\n  table: Table<TData>\n) => {\n  const row = table.getRow(id, true)\n\n  // const isGrouped = row.getIsGrouped()\n\n  // if ( // TODO: enforce grouping row selection rules\n  //   !isGrouped ||\n  //   (isGrouped && table.options.enableGroupingRowSelection)\n  // ) {\n  if (value) {\n    if (!row.getCanMultiSelect()) {\n      Object.keys(selectedRowIds).forEach(key => delete selectedRowIds[key])\n    }\n    if (row.getCanSelect()) {\n      selectedRowIds[id] = true\n    }\n  } else {\n    delete selectedRowIds[id]\n  }\n  // }\n\n  if (includeChildren && row.subRows?.length && row.getCanSelectSubRows()) {\n    row.subRows.forEach(row =>\n      mutateRowIsSelected(selectedRowIds, row.id, value, includeChildren, table)\n    )\n  }\n}\n\nexport function selectRowsFn<TData extends RowData>(\n  table: Table<TData>,\n  rowModel: RowModel<TData>\n): RowModel<TData> {\n  const rowSelection = table.getState().rowSelection\n\n  const newSelectedFlatRows: Row<TData>[] = []\n  const newSelectedRowsById: Record<string, Row<TData>> = {}\n\n  // Filters top level and nested rows\n  const recurseRows = (rows: Row<TData>[], depth = 0): Row<TData>[] => {\n    return rows\n      .map(row => {\n        const isSelected = isRowSelected(row, rowSelection)\n\n        if (isSelected) {\n          newSelectedFlatRows.push(row)\n          newSelectedRowsById[row.id] = row\n        }\n\n        if (row.subRows?.length) {\n          row = {\n            ...row,\n            subRows: recurseRows(row.subRows, depth + 1),\n          }\n        }\n\n        if (isSelected) {\n          return row\n        }\n      })\n      .filter(Boolean) as Row<TData>[]\n  }\n\n  return {\n    rows: recurseRows(rowModel.rows),\n    flatRows: newSelectedFlatRows,\n    rowsById: newSelectedRowsById,\n  }\n}\n\nexport function isRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>\n): boolean {\n  return selection[row.id] ?? false\n}\n\nexport function isSubRowSelected<TData extends RowData>(\n  row: Row<TData>,\n  selection: Record<string, boolean>,\n  table: Table<TData>\n): boolean | 'some' | 'all' {\n  if (!row.subRows?.length) return false\n\n  let allChildrenSelected = true\n  let someSelected = false\n\n  row.subRows.forEach(subRow => {\n    // Bail out early if we know both of these\n    if (someSelected && !allChildrenSelected) {\n      return\n    }\n\n    if (subRow.getCanSelect()) {\n      if (isRowSelected(subRow, selection)) {\n        someSelected = true\n      } else {\n        allChildrenSelected = false\n      }\n    }\n\n    // Check row selection of nested subrows\n    if (subRow.subRows && subRow.subRows.length) {\n      const subRowChildrenSelected = isSubRowSelected(subRow, selection, table)\n      if (subRowChildrenSelected === 'all') {\n        someSelected = true\n      } else if (subRowChildrenSelected === 'some') {\n        someSelected = true\n        allChildrenSelected = false\n      } else {\n        allChildrenSelected = false\n      }\n    }\n  })\n\n  return allChildrenSelected ? 'all' : someSelected ? 'some' : false\n}\n", "import { SortingFn } from './features/Sorting'\n\nexport const reSplitAlphaNumeric = /([0-9]+)/gm\n\nconst alphanumeric: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\nconst alphanumericCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareAlphanumeric(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst text: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)).toLowerCase(),\n    toString(rowB.getValue(columnId)).toLowerCase()\n  )\n}\n\n// The text filter is more basic (less numeric support)\n// but is much faster\nconst textCaseSensitive: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(\n    toString(rowA.getValue(columnId)),\n    toString(rowB.getValue(columnId))\n  )\n}\n\nconst datetime: SortingFn<any> = (rowA, rowB, columnId) => {\n  const a = rowA.getValue<Date>(columnId)\n  const b = rowB.getValue<Date>(columnId)\n\n  // Can handle nullish values\n  // Use > and < because == (and ===) doesn't work with\n  // Date objects (would require calling getTime()).\n  return a > b ? 1 : a < b ? -1 : 0\n}\n\nconst basic: SortingFn<any> = (rowA, rowB, columnId) => {\n  return compareBasic(rowA.getValue(columnId), rowB.getValue(columnId))\n}\n\n// Utils\n\nfunction compareBasic(a: any, b: any) {\n  return a === b ? 0 : a > b ? 1 : -1\n}\n\nfunction toString(a: any) {\n  if (typeof a === 'number') {\n    if (isNaN(a) || a === Infinity || a === -Infinity) {\n      return ''\n    }\n    return String(a)\n  }\n  if (typeof a === 'string') {\n    return a\n  }\n  return ''\n}\n\n// Mixed sorting is slow, but very inclusive of many edge cases.\n// It handles numbers, mixed alphanumeric combinations, and even\n// null, undefined, and Infinity\nfunction compareAlphanumeric(aStr: string, bStr: string) {\n  // Split on number groups, but keep the delimiter\n  // Then remove falsey split values\n  const a = aStr.split(reSplitAlphaNumeric).filter(Boolean)\n  const b = bStr.split(reSplitAlphaNumeric).filter(Boolean)\n\n  // While\n  while (a.length && b.length) {\n    const aa = a.shift()!\n    const bb = b.shift()!\n\n    const an = parseInt(aa, 10)\n    const bn = parseInt(bb, 10)\n\n    const combo = [an, bn].sort()\n\n    // Both are string\n    if (isNaN(combo[0]!)) {\n      if (aa > bb) {\n        return 1\n      }\n      if (bb > aa) {\n        return -1\n      }\n      continue\n    }\n\n    // One is a string, one is a number\n    if (isNaN(combo[1]!)) {\n      return isNaN(an) ? -1 : 1\n    }\n\n    // Both are numbers\n    if (an > bn) {\n      return 1\n    }\n    if (bn > an) {\n      return -1\n    }\n  }\n\n  return a.length - b.length\n}\n\n// Exports\n\nexport const sortingFns = {\n  alphanumeric,\n  alphanumericCaseSensitive,\n  text,\n  textCaseSensitive,\n  datetime,\n  basic,\n}\n\nexport type BuiltInSortingFn = keyof typeof sortingFns\n", "import { RowModel } from '..'\nimport { TableFeature } from '../core/table'\nimport {\n  BuiltInSortingFn,\n  reSplitAlphaNumeric,\n  sortingFns,\n} from '../sortingFns'\n\nimport {\n  Column,\n  OnChangeFn,\n  Table,\n  Row,\n  Updater,\n  RowData,\n  SortingFns,\n} from '../types'\n\nimport { isFunction, makeStateUpdater } from '../utils'\n\nexport type SortDirection = 'asc' | 'desc'\n\nexport interface ColumnSort {\n  desc: boolean\n  id: string\n}\n\nexport type SortingState = ColumnSort[]\n\nexport interface SortingTableState {\n  sorting: SortingState\n}\n\nexport interface SortingFn<TData extends RowData> {\n  (rowA: Row<TData>, rowB: Row<TData>, columnId: string): number\n}\n\nexport type CustomSortingFns<TData extends RowData> = Record<\n  string,\n  SortingFn<TData>\n>\n\nexport type SortingFnOption<TData extends RowData> =\n  | 'auto'\n  | keyof SortingFns\n  | BuiltInSortingFn\n  | SortingFn<TData>\n\nexport interface SortingColumnDef<TData extends RowData> {\n  /**\n   * Enables/Disables multi-sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Inverts the order of the sorting for this column. This is useful for values that have an inverted best/worst scale where lower numbers are better, eg. a ranking (1st, 2nd, 3rd) or golf-like scoring\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#invertsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  invertSorting?: boolean\n  /**\n   * Set to `true` for sorting toggles on this column to start in the descending direction.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n  /**\n   * The sorting function to use with this column.\n   * - A `string` referencing a built-in sorting function\n   * - A custom sorting function\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortingFn?: SortingFnOption<TData>\n  /**\n   * - `false`\n   *   - Undefined values will be considered tied and need to be sorted by the next column filter or original index (whichever applies)\n   * - `-1`\n   *   - Undefined values will be sorted with higher priority (ascending) (if ascending, undefined will appear on the beginning of the list)\n   * - `1`\n   *   - Undefined values will be sorted with lower priority (descending) (if ascending, undefined will appear on the end of the list)\n   */\n  sortUndefined?: false | -1 | 1\n}\n\nexport interface SortingColumn<TData extends RowData> {\n  /**\n   * Removes this column from the table's sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#clearsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  clearSorting: () => void\n  /**\n   * Returns a sort direction automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortDir: () => SortDirection\n  /**\n   * Returns a sorting function automatically inferred based on the columns values.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getautosortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getAutoSortingFn: () => SortingFn<TData>\n  /**\n   * Returns whether this column can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcanmultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanMultiSort: () => boolean\n  /**\n   * Returns whether this column can be sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getcansort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getCanSort: () => boolean\n  /**\n   * Returns the first direction that should be used when sorting this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getfirstsortdir)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getFirstSortDir: () => SortDirection\n  /**\n   * Returns the current sort direction of this column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getissorted)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getIsSorted: () => false | SortDirection\n  /**\n   * Returns the next sorting order.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getnextsortingorder)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getNextSortingOrder: () => SortDirection | false\n  /**\n   * Returns the index position of this column's sorting within the sorting state\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortindex)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortIndex: () => number\n  /**\n   * Returns the resolved sorting function to be used for this column\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortingfn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortingFn: () => SortingFn<TData>\n  /**\n   * Returns a function that can be used to toggle this column's sorting state. This is useful for attaching a click handler to the column header.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#gettogglesortinghandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getToggleSortingHandler: () => undefined | ((event: unknown) => void)\n  /**\n   * Toggles this columns sorting state. If `desc` is provided, it will force the sort direction to that value. If `isMulti` is provided, it will additivity multi-sort the column (or toggle it if it is already sorted).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#togglesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  toggleSorting: (desc?: boolean, isMulti?: boolean) => void\n}\n\ninterface SortingOptionsBase {\n  /**\n   * Enables/disables the ability to remove multi-sorts\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultiremove)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiRemove?: boolean\n  /**\n   * Enables/Disables multi-sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablemultisort)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableMultiSort?: boolean\n  /**\n   * Enables/Disables sorting for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSorting?: boolean\n  /**\n   * Enables/Disables the ability to remove sorting for the table.\n   * - If `true` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'none' -> ...\n   * - If `false` then changing sort order will circle like: 'none' -> 'desc' -> 'asc' -> 'desc' -> 'asc' -> ...\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#enablesortingremoval)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  enableSortingRemoval?: boolean\n  /**\n   * This function is used to retrieve the sorted row model. If using server-side sorting, this function is not required. To use client-side sorting, pass the exported `getSortedRowModel()` from your adapter to your table or implement your own.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel?: (table: Table<any>) => () => RowModel<any>\n  /**\n   * Pass a custom function that will be used to determine if a multi-sort event should be triggered. It is passed the event from the sort toggle handler and should return `true` if the event should trigger a multi-sort.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#ismultisortevent)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  isMultiSortEvent?: (e: unknown) => boolean\n  /**\n   * Enables manual sorting for the table. If this is `true`, you will be expected to sort your data before it is passed to the table. This is useful if you are doing server-side sorting.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#manualsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  manualSorting?: boolean\n  /**\n   * Set a maximum number of columns that can be multi-sorted.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#maxmultisortcolcount)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  maxMultiSortColCount?: number\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.sorting` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#onsortingchange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  onSortingChange?: OnChangeFn<SortingState>\n  /**\n   * If `true`, all sorts will default to descending as their first toggle state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#sortdescfirst)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  sortDescFirst?: boolean\n}\n\ntype ResolvedSortingFns = keyof SortingFns extends never\n  ? {\n      sortingFns?: Record<string, SortingFn<any>>\n    }\n  : {\n      sortingFns: Record<keyof SortingFns, SortingFn<any>>\n    }\n\nexport interface SortingOptions<TData extends RowData>\n  extends SortingOptionsBase,\n    ResolvedSortingFns {}\n\nexport interface SortingInstance<TData extends RowData> {\n  _getSortedRowModel?: () => RowModel<TData>\n  /**\n   * Returns the row model for the table before any sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getpresortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getPreSortedRowModel: () => RowModel<TData>\n  /**\n   * Returns the row model for the table after sorting has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#getsortedrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  getSortedRowModel: () => RowModel<TData>\n  /**\n   * Resets the **sorting** state to `initialState.sorting`, or `true` can be passed to force a default blank state reset to `[]`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#resetsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  resetSorting: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.sorting` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/sorting#setsorting)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/sorting)\n   */\n  setSorting: (updater: Updater<SortingState>) => void\n}\n\n//\n\nexport const Sorting: TableFeature = {\n  getInitialState: (state): SortingTableState => {\n    return {\n      sorting: [],\n      ...state,\n    }\n  },\n\n  getDefaultColumnDef: <TData extends RowData>(): SortingColumnDef<TData> => {\n    return {\n      sortingFn: 'auto',\n      sortUndefined: 1,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): SortingOptions<TData> => {\n    return {\n      onSortingChange: makeStateUpdater('sorting', table),\n      isMultiSortEvent: (e: unknown) => {\n        return (e as MouseEvent).shiftKey\n      },\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.getAutoSortingFn = () => {\n      const firstRows = table.getFilteredRowModel().flatRows.slice(10)\n\n      let isString = false\n\n      for (const row of firstRows) {\n        const value = row?.getValue(column.id)\n\n        if (Object.prototype.toString.call(value) === '[object Date]') {\n          return sortingFns.datetime\n        }\n\n        if (typeof value === 'string') {\n          isString = true\n\n          if (value.split(reSplitAlphaNumeric).length > 1) {\n            return sortingFns.alphanumeric\n          }\n        }\n      }\n\n      if (isString) {\n        return sortingFns.text\n      }\n\n      return sortingFns.basic\n    }\n    column.getAutoSortDir = () => {\n      const firstRow = table.getFilteredRowModel().flatRows[0]\n\n      const value = firstRow?.getValue(column.id)\n\n      if (typeof value === 'string') {\n        return 'asc'\n      }\n\n      return 'desc'\n    }\n    column.getSortingFn = () => {\n      if (!column) {\n        throw new Error()\n      }\n\n      return isFunction(column.columnDef.sortingFn)\n        ? column.columnDef.sortingFn\n        : column.columnDef.sortingFn === 'auto'\n          ? column.getAutoSortingFn()\n          : table.options.sortingFns?.[column.columnDef.sortingFn as string] ??\n            sortingFns[column.columnDef.sortingFn as BuiltInSortingFn]\n    }\n    column.toggleSorting = (desc, multi) => {\n      // if (column.columns.length) {\n      //   column.columns.forEach((c, i) => {\n      //     if (c.id) {\n      //       table.toggleColumnSorting(c.id, undefined, multi || !!i)\n      //     }\n      //   })\n      //   return\n      // }\n\n      // this needs to be outside of table.setSorting to be in sync with rerender\n      const nextSortingOrder = column.getNextSortingOrder()\n      const hasManualValue = typeof desc !== 'undefined' && desc !== null\n\n      table.setSorting(old => {\n        // Find any existing sorting for this column\n        const existingSorting = old?.find(d => d.id === column.id)\n        const existingIndex = old?.findIndex(d => d.id === column.id)\n\n        let newSorting: SortingState = []\n\n        // What should we do with this sort action?\n        let sortAction: 'add' | 'remove' | 'toggle' | 'replace'\n        let nextDesc = hasManualValue ? desc : nextSortingOrder === 'desc'\n\n        // Multi-mode\n        if (old?.length && column.getCanMultiSort() && multi) {\n          if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'add'\n          }\n        } else {\n          // Normal mode\n          if (old?.length && existingIndex !== old.length - 1) {\n            sortAction = 'replace'\n          } else if (existingSorting) {\n            sortAction = 'toggle'\n          } else {\n            sortAction = 'replace'\n          }\n        }\n\n        // Handle toggle states that will remove the sorting\n        if (sortAction === 'toggle') {\n          // If we are \"actually\" toggling (not a manual set value), should we remove the sorting?\n          if (!hasManualValue) {\n            // Is our intention to remove?\n            if (!nextSortingOrder) {\n              sortAction = 'remove'\n            }\n          }\n        }\n\n        if (sortAction === 'add') {\n          newSorting = [\n            ...old,\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n          // Take latest n columns\n          newSorting.splice(\n            0,\n            newSorting.length -\n              (table.options.maxMultiSortColCount ?? Number.MAX_SAFE_INTEGER)\n          )\n        } else if (sortAction === 'toggle') {\n          // This flips (or sets) the\n          newSorting = old.map(d => {\n            if (d.id === column.id) {\n              return {\n                ...d,\n                desc: nextDesc,\n              }\n            }\n            return d\n          })\n        } else if (sortAction === 'remove') {\n          newSorting = old.filter(d => d.id !== column.id)\n        } else {\n          newSorting = [\n            {\n              id: column.id,\n              desc: nextDesc,\n            },\n          ]\n        }\n\n        return newSorting\n      })\n    }\n\n    column.getFirstSortDir = () => {\n      const sortDescFirst =\n        column.columnDef.sortDescFirst ??\n        table.options.sortDescFirst ??\n        column.getAutoSortDir() === 'desc'\n      return sortDescFirst ? 'desc' : 'asc'\n    }\n\n    column.getNextSortingOrder = (multi?: boolean) => {\n      const firstSortDirection = column.getFirstSortDir()\n      const isSorted = column.getIsSorted()\n\n      if (!isSorted) {\n        return firstSortDirection\n      }\n\n      if (\n        isSorted !== firstSortDirection &&\n        (table.options.enableSortingRemoval ?? true) && // If enableSortRemove, enable in general\n        (multi ? table.options.enableMultiRemove ?? true : true) // If multi, don't allow if enableMultiRemove))\n      ) {\n        return false\n      }\n      return isSorted === 'desc' ? 'asc' : 'desc'\n    }\n\n    column.getCanSort = () => {\n      return (\n        (column.columnDef.enableSorting ?? true) &&\n        (table.options.enableSorting ?? true) &&\n        !!column.accessorFn\n      )\n    }\n\n    column.getCanMultiSort = () => {\n      return (\n        column.columnDef.enableMultiSort ??\n        table.options.enableMultiSort ??\n        !!column.accessorFn\n      )\n    }\n\n    column.getIsSorted = () => {\n      const columnSort = table.getState().sorting?.find(d => d.id === column.id)\n\n      return !columnSort ? false : columnSort.desc ? 'desc' : 'asc'\n    }\n\n    column.getSortIndex = () =>\n      table.getState().sorting?.findIndex(d => d.id === column.id) ?? -1\n\n    column.clearSorting = () => {\n      //clear sorting for just 1 column\n      table.setSorting(old =>\n        old?.length ? old.filter(d => d.id !== column.id) : []\n      )\n    }\n\n    column.getToggleSortingHandler = () => {\n      const canSort = column.getCanSort()\n\n      return (e: unknown) => {\n        if (!canSort) return\n        ;(e as any).persist?.()\n        column.toggleSorting?.(\n          undefined,\n          column.getCanMultiSort() ? table.options.isMultiSortEvent?.(e) : false\n        )\n      }\n    }\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    table.setSorting = updater => table.options.onSortingChange?.(updater)\n    table.resetSorting = defaultState => {\n      table.setSorting(defaultState ? [] : table.initialState?.sorting ?? [])\n    }\n    table.getPreSortedRowModel = () => table.getGroupedRowModel()\n    table.getSortedRowModel = () => {\n      if (!table._getSortedRowModel && table.options.getSortedRowModel) {\n        table._getSortedRowModel = table.options.getSortedRowModel(table)\n      }\n\n      if (table.options.manualSorting || !table._getSortedRowModel) {\n        return table.getPreSortedRowModel()\n      }\n\n      return table._getSortedRowModel()\n    }\n  },\n}\n", "import { TableFeature } from '../core/table'\nimport {\n  Cell,\n  Column,\n  OnChangeFn,\n  Table,\n  Updater,\n  Row,\n  RowData,\n} from '../types'\nimport { makeStateUpdater, memo } from '../utils'\n\nexport type VisibilityState = Record<string, boolean>\n\nexport interface VisibilityTableState {\n  columnVisibility: VisibilityState\n}\n\nexport interface VisibilityOptions {\n  enableHiding?: boolean\n  /**\n   * If provided, this function will be called with an `updaterFn` when `state.columnVisibility` changes. This overrides the default internal state management, so you will need to persist the state change either fully or partially outside of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#oncolumnvisibilitychange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  onColumnVisibilityChange?: OnChangeFn<VisibilityState>\n}\n\nexport type VisibilityDefaultOptions = Pick<\n  VisibilityOptions,\n  'onColumnVisibilityChange'\n>\n\nexport interface VisibilityInstance<TData extends RowData> {\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the unpinned/center portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcentervisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCenterVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns whether all columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsAllColumnsVisible: () => boolean\n  /**\n   * Returns whether any columns are visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getissomecolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsSomeColumnsVisible: () => boolean\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the left portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getleftvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getLeftVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * If column pinning, returns a flat array of leaf-node columns that are visible in the right portion of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getrightvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getRightVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a handler for toggling the visibility of all columns, meant to be bound to a `input[type=checkbox]` element.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettoggleallcolumnsvisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleAllColumnsVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Returns a flat array of columns that are visible, including parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a flat array of leaf-node columns that are visible.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisibleleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Resets the column visibility state to the initial state. If `defaultState` is provided, the state will be reset to `{}`\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#resetcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  resetColumnVisibility: (defaultState?: boolean) => void\n  /**\n   * Sets or updates the `state.columnVisibility` state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#setcolumnvisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  setColumnVisibility: (updater: Updater<VisibilityState>) => void\n  /**\n   * Toggles the visibility of all columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#toggleallcolumnsvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleAllColumnsVisible: (value?: boolean) => void\n}\n\nexport interface VisibilityColumnDef {\n  enableHiding?: boolean\n}\n\nexport interface VisibilityRow<TData extends RowData> {\n  _getAllVisibleCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns an array of cells that account for column visibility for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getvisiblecells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getVisibleCells: () => Cell<TData, unknown>[]\n}\n\nexport interface VisibilityColumn {\n  /**\n   * Returns whether the column can be hidden\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getcanhide)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getCanHide: () => boolean\n  /**\n   * Returns whether the column is visible\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#getisvisible)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getIsVisible: () => boolean\n  /**\n   * Returns a function that can be used to toggle the column visibility. This function can be used to bind to an event handler to a checkbox.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#gettogglevisibilityhandler)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  getToggleVisibilityHandler: () => (event: unknown) => void\n  /**\n   * Toggles the visibility of the column.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/features/column-visibility#togglevisibility)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/column-visibility)\n   */\n  toggleVisibility: (value?: boolean) => void\n}\n\n//\n\nexport const Visibility: TableFeature = {\n  getInitialState: (state): VisibilityTableState => {\n    return {\n      columnVisibility: {},\n      ...state,\n    }\n  },\n\n  getDefaultOptions: <TData extends RowData>(\n    table: Table<TData>\n  ): VisibilityDefaultOptions => {\n    return {\n      onColumnVisibilityChange: makeStateUpdater('columnVisibility', table),\n    }\n  },\n\n  createColumn: <TData extends RowData, TValue>(\n    column: Column<TData, TValue>,\n    table: Table<TData>\n  ): void => {\n    column.toggleVisibility = value => {\n      if (column.getCanHide()) {\n        table.setColumnVisibility(old => ({\n          ...old,\n          [column.id]: value ?? !column.getIsVisible(),\n        }))\n      }\n    }\n    column.getIsVisible = () => {\n      return table.getState().columnVisibility?.[column.id] ?? true\n    }\n\n    column.getCanHide = () => {\n      return (\n        (column.columnDef.enableHiding ?? true) &&\n        (table.options.enableHiding ?? true)\n      )\n    }\n    column.getToggleVisibilityHandler = () => {\n      return (e: unknown) => {\n        column.toggleVisibility?.(\n          ((e as MouseEvent).target as HTMLInputElement).checked\n        )\n      }\n    }\n  },\n\n  createRow: <TData extends RowData>(\n    row: Row<TData>,\n    table: Table<TData>\n  ): void => {\n    row._getAllVisibleCells = memo(\n      () => [row.getAllCells(), table.getState().columnVisibility],\n      cells => {\n        return cells.filter(cell => cell.column.getIsVisible())\n      },\n      {\n        key: process.env.NODE_ENV === 'production' && 'row._getAllVisibleCells',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    )\n    row.getVisibleCells = memo(\n      () => [\n        row.getLeftVisibleCells(),\n        row.getCenterVisibleCells(),\n        row.getRightVisibleCells(),\n      ],\n      (left, center, right) => [...left, ...center, ...right],\n      {\n        key: process.env.NODE_ENV === 'development' && 'row.getVisibleCells',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    )\n  },\n\n  createTable: <TData extends RowData>(table: Table<TData>): void => {\n    const makeVisibleColumnsMethod = (\n      key: string,\n      getColumns: () => Column<TData, unknown>[]\n    ): (() => Column<TData, unknown>[]) => {\n      return memo(\n        () => [\n          getColumns(),\n          getColumns()\n            .filter(d => d.getIsVisible())\n            .map(d => d.id)\n            .join('_'),\n        ],\n        columns => {\n          return columns.filter(d => d.getIsVisible?.())\n        },\n        {\n          key,\n          debug: () => table.options.debugAll ?? table.options.debugColumns,\n        }\n      )\n    }\n\n    table.getVisibleFlatColumns = makeVisibleColumnsMethod(\n      'getVisibleFlatColumns',\n      () => table.getAllFlatColumns()\n    )\n    table.getVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getVisibleLeafColumns',\n      () => table.getAllLeafColumns()\n    )\n    table.getLeftVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getLeftVisibleLeafColumns',\n      () => table.getLeftLeafColumns()\n    )\n    table.getRightVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getRightVisibleLeafColumns',\n      () => table.getRightLeafColumns()\n    )\n    table.getCenterVisibleLeafColumns = makeVisibleColumnsMethod(\n      'getCenterVisibleLeafColumns',\n      () => table.getCenterLeafColumns()\n    )\n\n    table.setColumnVisibility = updater =>\n      table.options.onColumnVisibilityChange?.(updater)\n\n    table.resetColumnVisibility = defaultState => {\n      table.setColumnVisibility(\n        defaultState ? {} : table.initialState.columnVisibility ?? {}\n      )\n    }\n\n    table.toggleAllColumnsVisible = value => {\n      value = value ?? !table.getIsAllColumnsVisible()\n\n      table.setColumnVisibility(\n        table.getAllLeafColumns().reduce(\n          (obj, column) => ({\n            ...obj,\n            [column.id]: !value ? !column.getCanHide?.() : value,\n          }),\n          {}\n        )\n      )\n    }\n\n    table.getIsAllColumnsVisible = () =>\n      !table.getAllLeafColumns().some(column => !column.getIsVisible?.())\n\n    table.getIsSomeColumnsVisible = () =>\n      table.getAllLeafColumns().some(column => column.getIsVisible?.())\n\n    table.getToggleAllColumnsVisibilityHandler = () => {\n      return (e: unknown) => {\n        table.toggleAllColumnsVisible(\n          ((e as MouseEvent).target as HTMLInputElement)?.checked\n        )\n      }\n    }\n  },\n}\n", "import { functionalUpdate, memo, RequiredKeys } from '../utils'\n\nimport {\n  Updater,\n  TableOptionsResolved,\n  TableState,\n  Table,\n  InitialTableState,\n  Row,\n  Column,\n  RowModel,\n  ColumnDef,\n  TableOptions,\n  RowData,\n  TableMeta,\n  ColumnDefResolved,\n  GroupColumnDef,\n} from '../types'\n\n//\nimport { createColumn } from './column'\nimport { Headers } from './headers'\n//\n\nimport { ColumnSizing } from '../features/ColumnSizing'\nimport { Expanding } from '../features/Expanding'\nimport { Filters } from '../features/Filters'\nimport { Grouping } from '../features/Grouping'\nimport { Ordering } from '../features/Ordering'\nimport { Pagination } from '../features/Pagination'\nimport { Pinning } from '../features/Pinning'\nimport { RowSelection } from '../features/RowSelection'\nimport { Sorting } from '../features/Sorting'\nimport { Visibility } from '../features/Visibility'\n\nexport interface TableFeature {\n  createCell?: (cell: any, column: any, row: any, table: any) => any\n  createColumn?: (column: any, table: any) => any\n  createHeader?: (column: any, table: any) => any\n  createRow?: (row: any, table: any) => any\n  createTable?: (table: any) => any\n  getDefaultColumnDef?: () => any\n  getDefaultOptions?: (table: any) => any\n  getInitialState?: (initialState?: InitialTableState) => any\n}\n\nconst features = [\n  Headers,\n  Visibility,\n  Ordering,\n  Pinning,\n  Filters,\n  Sorting,\n  Grouping,\n  Expanding,\n  Pagination,\n  RowSelection,\n  ColumnSizing,\n] as const\n\n//\n\nexport interface CoreTableState {}\n\nexport interface CoreOptions<TData extends RowData> {\n  /**\n   * Set this option to override any of the `autoReset...` feature options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#autoresetall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  autoResetAll?: boolean\n  /**\n   * The array of column defs to use for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#columns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  columns: ColumnDef<TData, any>[]\n  /**\n   * The data for the table to display. This array should match the type you provided to `table.setRowType<...>`. Columns can access this data via string/index or a functional accessor. When the `data` option changes reference, the table will reprocess the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#data)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  data: TData[]\n  /**\n   * Set this option to `true` to output all debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugall)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugAll?: boolean\n  /**\n   * Set this option to `true` to output column debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugColumns?: boolean\n  /**\n   * Set this option to `true` to output header debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugheaders)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugHeaders?: boolean\n  /**\n   * Set this option to `true` to output row debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugRows?: boolean\n  /**\n   * Set this option to `true` to output table debugging information to the console.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#debugtable)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  debugTable?: boolean\n  /**\n   * Default column options to use for all column defs supplied to the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#defaultcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  defaultColumn?: Partial<ColumnDef<TData, unknown>>\n  /**\n   * This required option is a factory for a function that computes and returns the core row model for the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: (table: Table<any>) => () => RowModel<any>\n  /**\n   * This optional function is used to derive a unique ID for any given row. If not provided the rows index is used (nested rows join together with `.` using their grandparents' index eg. `index.index.index`). If you need to identify individual rows that are originating from any server-side operations, it's suggested you use this function to return an ID that makes sense regardless of network IO/ambiguity eg. a userId, taskId, database ID field, etc.\n   * @example getRowId: row => row.userId\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowId?: (originalRow: TData, index: number, parent?: Row<TData>) => string\n  /**\n   * This optional function is used to access the sub rows for any given row. If you are using nested rows, you will need to use this function to return the sub rows object (or undefined) from the row.\n   * @example getSubRows: row => row.subRows\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getSubRows?: (originalRow: TData, index: number) => undefined | TData[]\n  /**\n   * Use this option to optionally pass initial state to the table. This state will be used when resetting various table states either automatically by the table (eg. `options.autoResetPageIndex`) or via functions like `table.resetRowSelection()`. Most reset function allow you optionally pass a flag to reset to a blank/default state instead of the initial state.\n   *\n   * Table state will not be reset when this object changes, which also means that the initial state object does not need to be stable.\n   *\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState?: InitialTableState\n  /**\n   * This option is used to optionally implement the merging of table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#mergeoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  mergeOptions?: (\n    defaultOptions: TableOptions<TData>,\n    options: Partial<TableOptions<TData>>\n  ) => TableOptions<TData>\n  /**\n   * You can pass any object to `options.meta` and access it anywhere the `table` is available via `table.options.meta`.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#meta)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  meta?: TableMeta<TData>\n  /**\n   * The `onStateChange` option can be used to optionally listen to state changes within the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#onstatechange)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  onStateChange: (updater: Updater<TableState>) => void\n  /**\n   * Value used when the desired value is not found in the data.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#renderfallbackvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  renderFallbackValue: any\n  /**\n   * The `state` option can be used to optionally _control_ part or all of the table state. The state you pass here will merge with and overwrite the internal automatically-managed state to produce the final state for the table. You can also listen to state changes via the `onStateChange` option.\n   * > Note: Any state passed in here will override both the internal state and any other `initialState` you provide.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#state)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  state: Partial<TableState>\n}\n\nexport interface CoreInstance<TData extends RowData> {\n  _features: readonly TableFeature[]\n  _getAllFlatColumnsById: () => Record<string, Column<TData, unknown>>\n  _getColumnDefs: () => ColumnDef<TData, unknown>[]\n  _getCoreRowModel?: () => RowModel<TData>\n  _getDefaultColumnDef: () => Partial<ColumnDef<TData, unknown>>\n  _getRowId: (_: TData, index: number, parent?: Row<TData>) => string\n  _queue: (cb: () => void) => void\n  /**\n   * Returns all columns in the table in their normalized and nested hierarchy.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all columns in the table flattened to a single level.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallflatcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllFlatColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns all leaf-node columns in the table flattened to a single level. This does not include parent columns.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getallleafcolumns)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getAllLeafColumns: () => Column<TData, unknown>[]\n  /**\n   * Returns a single column by its ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcolumn)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getColumn: (columnId: string) => Column<TData, unknown> | undefined\n  /**\n   * Returns the core row model before any processing has been applied.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getcorerowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getCoreRowModel: () => RowModel<TData>\n  /**\n   * Returns the row with the given ID.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRow: (id: string, searchAll?: boolean) => Row<TData>\n  /**\n   * Returns the final model after all processing from other used features has been applied. This is the row model that is most commonly used for rendering.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getrowmodel)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getRowModel: () => RowModel<TData>\n  /**\n   * Call this function to get the table's current state. It's recommended to use this function and its state, especially when managing the table state manually. It is the exact same state used internally by the table for every feature and function it provides.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#getstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  getState: () => TableState\n  /**\n   * This is the resolved initial state of the table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#initialstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  initialState: TableState\n  /**\n   * A read-only reference to the table's current options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#options)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  options: RequiredKeys<TableOptionsResolved<TData>, 'state'>\n  /**\n   * Call this function to reset the table state to the initial state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#reset)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  reset: () => void\n  /**\n   * This function can be used to update the table options.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setoptions)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setOptions: (newOptions: Updater<TableOptionsResolved<TData>>) => void\n  /**\n   * Call this function to update the table state.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/table#setstate)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/tables)\n   */\n  setState: (updater: Updater<TableState>) => void\n}\n\nexport function createTable<TData extends RowData>(\n  options: TableOptionsResolved<TData>\n): Table<TData> {\n  if (options.debugAll || options.debugTable) {\n    console.info('Creating Table Instance...')\n  }\n\n  let table = { _features: features } as unknown as Table<TData>\n\n  const defaultOptions = table._features.reduce((obj, feature) => {\n    return Object.assign(obj, feature.getDefaultOptions?.(table))\n  }, {}) as TableOptionsResolved<TData>\n\n  const mergeOptions = (options: TableOptionsResolved<TData>) => {\n    if (table.options.mergeOptions) {\n      return table.options.mergeOptions(defaultOptions, options)\n    }\n\n    return {\n      ...defaultOptions,\n      ...options,\n    }\n  }\n\n  const coreInitialState: CoreTableState = {}\n\n  let initialState = {\n    ...coreInitialState,\n    ...(options.initialState ?? {}),\n  } as TableState\n\n  table._features.forEach(feature => {\n    initialState = feature.getInitialState?.(initialState) ?? initialState\n  })\n\n  const queued: (() => void)[] = []\n  let queuedTimeout = false\n\n  const coreInstance: CoreInstance<TData> = {\n    _features: features,\n    options: {\n      ...defaultOptions,\n      ...options,\n    },\n    initialState,\n    _queue: cb => {\n      queued.push(cb)\n\n      if (!queuedTimeout) {\n        queuedTimeout = true\n\n        // Schedule a microtask to run the queued callbacks after\n        // the current call stack (render, etc) has finished.\n        Promise.resolve()\n          .then(() => {\n            while (queued.length) {\n              queued.shift()!()\n            }\n            queuedTimeout = false\n          })\n          .catch(error =>\n            setTimeout(() => {\n              throw error\n            })\n          )\n      }\n    },\n    reset: () => {\n      table.setState(table.initialState)\n    },\n    setOptions: updater => {\n      const newOptions = functionalUpdate(updater, table.options)\n      table.options = mergeOptions(newOptions) as RequiredKeys<\n        TableOptionsResolved<TData>,\n        'state'\n      >\n    },\n\n    getState: () => {\n      return table.options.state as TableState\n    },\n\n    setState: (updater: Updater<TableState>) => {\n      table.options.onStateChange?.(updater)\n    },\n\n    _getRowId: (row: TData, index: number, parent?: Row<TData>) =>\n      table.options.getRowId?.(row, index, parent) ??\n      `${parent ? [parent.id, index].join('.') : index}`,\n\n    getCoreRowModel: () => {\n      if (!table._getCoreRowModel) {\n        table._getCoreRowModel = table.options.getCoreRowModel(table)\n      }\n\n      return table._getCoreRowModel!()\n    },\n\n    // The final calls start at the bottom of the model,\n    // expanded rows, which then work their way up\n\n    getRowModel: () => {\n      return table.getPaginationRowModel()\n    },\n    //in next version, we should just pass in the row model as the optional 2nd arg\n    getRow: (id: string, searchAll?: boolean) => {\n      let row = (\n        searchAll ? table.getPrePaginationRowModel() : table.getRowModel()\n      ).rowsById[id]\n\n      if (!row) {\n        row = table.getCoreRowModel().rowsById[id]\n        if (!row) {\n          if (process.env.NODE_ENV !== 'production') {\n            throw new Error(`getRow could not find row with ID: ${id}`)\n          }\n          throw new Error()\n        }\n      }\n\n      return row\n    },\n    _getDefaultColumnDef: memo(\n      () => [table.options.defaultColumn],\n      defaultColumn => {\n        defaultColumn = (defaultColumn ?? {}) as Partial<\n          ColumnDef<TData, unknown>\n        >\n\n        return {\n          header: props => {\n            const resolvedColumnDef = props.header.column\n              .columnDef as ColumnDefResolved<TData>\n\n            if (resolvedColumnDef.accessorKey) {\n              return resolvedColumnDef.accessorKey\n            }\n\n            if (resolvedColumnDef.accessorFn) {\n              return resolvedColumnDef.id\n            }\n\n            return null\n          },\n          // footer: props => props.header.column.id,\n          cell: props => props.renderValue<any>()?.toString?.() ?? null,\n          ...table._features.reduce((obj, feature) => {\n            return Object.assign(obj, feature.getDefaultColumnDef?.())\n          }, {}),\n          ...defaultColumn,\n        } as Partial<ColumnDef<TData, unknown>>\n      },\n      {\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n        key: process.env.NODE_ENV === 'development' && 'getDefaultColumnDef',\n      }\n    ),\n\n    _getColumnDefs: () => table.options.columns,\n\n    getAllColumns: memo(\n      () => [table._getColumnDefs()],\n      columnDefs => {\n        const recurseColumns = (\n          columnDefs: ColumnDef<TData, unknown>[],\n          parent?: Column<TData, unknown>,\n          depth = 0\n        ): Column<TData, unknown>[] => {\n          return columnDefs.map(columnDef => {\n            const column = createColumn(table, columnDef, depth, parent)\n\n            const groupingColumnDef = columnDef as GroupColumnDef<\n              TData,\n              unknown\n            >\n\n            column.columns = groupingColumnDef.columns\n              ? recurseColumns(groupingColumnDef.columns, column, depth + 1)\n              : []\n\n            return column\n          })\n        }\n\n        return recurseColumns(columnDefs)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getAllFlatColumns: memo(\n      () => [table.getAllColumns()],\n      allColumns => {\n        return allColumns.flatMap(column => {\n          return column.getFlatColumns()\n        })\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllFlatColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    _getAllFlatColumnsById: memo(\n      () => [table.getAllFlatColumns()],\n      flatColumns => {\n        return flatColumns.reduce(\n          (acc, column) => {\n            acc[column.id] = column\n            return acc\n          },\n          {} as Record<string, Column<TData, unknown>>\n        )\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllFlatColumnsById',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getAllLeafColumns: memo(\n      () => [table.getAllColumns(), table._getOrderColumnsFn()],\n      (allColumns, orderColumns) => {\n        let leafColumns = allColumns.flatMap(column => column.getLeafColumns())\n        return orderColumns(leafColumns)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getAllLeafColumns',\n        debug: () => table.options.debugAll ?? table.options.debugColumns,\n      }\n    ),\n\n    getColumn: columnId => {\n      const column = table._getAllFlatColumnsById()[columnId]\n\n      if (process.env.NODE_ENV !== 'production' && !column) {\n        console.error(`[Table] Column with id '${columnId}' does not exist.`)\n      }\n\n      return column\n    },\n  }\n\n  Object.assign(table, coreInstance)\n\n  for (let index = 0; index < table._features.length; index++) {\n    const feature = table._features[index]\n    feature?.createTable?.(table)\n  }\n\n  return table\n}\n", "import { RowData, Cell, Column, Row, Table } from '../types'\nimport { Getter, memo } from '../utils'\n\nexport interface CellContext<TData extends RowData, TValue> {\n  cell: Cell<TData, TValue>\n  column: Column<TData, TValue>\n  getValue: Getter<TValue>\n  renderValue: Getter<TValue | null>\n  row: Row<TData>\n  table: Table<TData>\n}\n\nexport interface CoreCell<TData extends RowData, TValue> {\n  /**\n   * The associated Column object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#column)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  column: Column<TData, TValue>\n  /**\n   * Returns the rendering context (or props) for cell-based components like cells and aggregated cells. Use these props with your framework's `flexRender` utility to render these using the template of your choice:\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getcontext)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getContext: () => CellContext<TData, TValue>\n  /**\n   * Returns the value for the cell, accessed via the associated column's accessor key or accessor function.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  getValue: CellContext<TData, TValue>['getValue']\n  /**\n   * The unique ID for the cell across the entire table.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  id: string\n  /**\n   * Renders the value for a cell the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  renderValue: CellContext<TData, TValue>['renderValue']\n  /**\n   * The associated Row object for the cell.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/cell#row)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/cells)\n   */\n  row: Row<TData>\n}\n\nexport function createCell<TData extends RowData, TValue>(\n  table: Table<TData>,\n  row: Row<TData>,\n  column: Column<TData, TValue>,\n  columnId: string\n): Cell<TData, TValue> {\n  const getRenderValue = () =>\n    cell.getValue() ?? table.options.renderFallbackValue\n\n  const cell: CoreCell<TData, TValue> = {\n    id: `${row.id}_${column.id}`,\n    row,\n    column,\n    getValue: () => row.getValue(columnId),\n    renderValue: getRenderValue,\n    getContext: memo(\n      () => [table, column, row, cell],\n      (table, column, row, cell) => ({\n        table,\n        column,\n        row,\n        cell: cell as Cell<TData, TValue>,\n        getValue: cell.getValue,\n        renderValue: cell.renderValue,\n      }),\n      {\n        key: process.env.NODE_ENV === 'development' && 'cell.getContext',\n        debug: () => table.options.debugAll,\n      }\n    ),\n  }\n\n  table._features.forEach(feature => {\n    feature.createCell?.(\n      cell as Cell<TData, TValue>,\n      column,\n      row as Row<TData>,\n      table\n    )\n  }, {})\n\n  return cell as Cell<TData, TValue>\n}\n", "import { RowData, Cell, Row, Table } from '../types'\nimport { flattenBy, memo } from '../utils'\nimport { createCell } from './cell'\n\nexport interface CoreRow<TData extends RowData> {\n  _getAllCellsByColumnId: () => Record<string, Cell<TData, unknown>>\n  _uniqueValuesCache: Record<string, unknown>\n  _valuesCache: Record<string, unknown>\n  /**\n   * The depth of the row (if nested or grouped) relative to the root row array.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#depth)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  depth: number\n  /**\n   * Returns all of the cells for the row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getallcells)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getAllCells: () => Cell<TData, unknown>[]\n  /**\n   * Returns the leaf rows for the row, not including any parent rows.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getleafrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getLeafRows: () => Row<TData>[]\n  /**\n   * Returns the parent row for the row, if it exists.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrow)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRow: () => Row<TData> | undefined\n  /**\n   * Returns the parent rows for the row, all the way up to a root row.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getparentrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getParentRows: () => Row<TData>[]\n  /**\n   * Returns a unique array of values from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getuniquevalues)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getUniqueValues: <TValue>(columnId: string) => TValue[]\n  /**\n   * Returns the value from the row for a given columnId.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#getvalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  getValue: <TValue>(columnId: string) => TValue\n  /**\n   * The resolved unique identifier for the row resolved via the `options.getRowId` option. Defaults to the row's index (or relative index if it is a subRow).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#id)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  id: string\n  /**\n   * The index of the row within its parent array (or the root data array).\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#index)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  index: number\n  /**\n   * The original row object provided to the table. If the row is a grouped row, the original row object will be the first original in the group.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#original)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  original: TData\n  /**\n   * An array of the original subRows as returned by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#originalsubrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  originalSubRows?: TData[]\n  /**\n   * If nested, this row's parent row id.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#parentid)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  parentId?: string\n  /**\n   * Renders the value for the row in a given columnId the same as `getValue`, but will return the `renderFallbackValue` if no value is found.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#rendervalue)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  renderValue: <TValue>(columnId: string) => TValue\n  /**\n   * An array of subRows for the row as returned and created by the `options.getSubRows` option.\n   * @link [API Docs](https://tanstack.com/table/v8/docs/api/core/row#subrows)\n   * @link [Guide](https://tanstack.com/table/v8/docs/guide/rows)\n   */\n  subRows: Row<TData>[]\n}\n\nexport const createRow = <TData extends RowData>(\n  table: Table<TData>,\n  id: string,\n  original: TData,\n  rowIndex: number,\n  depth: number,\n  subRows?: Row<TData>[],\n  parentId?: string\n): Row<TData> => {\n  let row: CoreRow<TData> = {\n    id,\n    index: rowIndex,\n    original,\n    depth,\n    parentId,\n    _valuesCache: {},\n    _uniqueValuesCache: {},\n    getValue: columnId => {\n      if (row._valuesCache.hasOwnProperty(columnId)) {\n        return row._valuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      row._valuesCache[columnId] = column.accessorFn(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._valuesCache[columnId] as any\n    },\n    getUniqueValues: columnId => {\n      if (row._uniqueValuesCache.hasOwnProperty(columnId)) {\n        return row._uniqueValuesCache[columnId]\n      }\n\n      const column = table.getColumn(columnId)\n\n      if (!column?.accessorFn) {\n        return undefined\n      }\n\n      if (!column.columnDef.getUniqueValues) {\n        row._uniqueValuesCache[columnId] = [row.getValue(columnId)]\n        return row._uniqueValuesCache[columnId]\n      }\n\n      row._uniqueValuesCache[columnId] = column.columnDef.getUniqueValues(\n        row.original as TData,\n        rowIndex\n      )\n\n      return row._uniqueValuesCache[columnId] as any\n    },\n    renderValue: columnId =>\n      row.getValue(columnId) ?? table.options.renderFallbackValue,\n    subRows: subRows ?? [],\n    getLeafRows: () => flattenBy(row.subRows, d => d.subRows),\n    getParentRow: () =>\n      row.parentId ? table.getRow(row.parentId, true) : undefined,\n    getParentRows: () => {\n      let parentRows: Row<TData>[] = []\n      let currentRow = row\n      while (true) {\n        const parentRow = currentRow.getParentRow()\n        if (!parentRow) break\n        parentRows.push(parentRow)\n        currentRow = parentRow\n      }\n      return parentRows.reverse()\n    },\n    getAllCells: memo(\n      () => [table.getAllLeafColumns()],\n      leafColumns => {\n        return leafColumns.map(column => {\n          return createCell(table, row as Row<TData>, column, column.id)\n        })\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'row.getAllCells',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    ),\n\n    _getAllCellsByColumnId: memo(\n      () => [row.getAllCells()],\n      allCells => {\n        return allCells.reduce(\n          (acc, cell) => {\n            acc[cell.column.id] = cell\n            return acc\n          },\n          {} as Record<string, Cell<TData, unknown>>\n        )\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'production' && 'row.getAllCellsByColumnId',\n        debug: () => table.options.debugAll ?? table.options.debugRows,\n      }\n    ),\n  }\n\n  for (let i = 0; i < table._features.length; i++) {\n    const feature = table._features[i]\n    feature?.createRow?.(row, table)\n  }\n\n  return row as Row<TData>\n}\n", "import {\n  AccessorFn,\n  ColumnDef,\n  DisplayColumnDef,\n  GroupColumnDef,\n  IdentifiedColumnDef,\n  RowData,\n} from './types'\nimport { DeepKeys, DeepValue } from './utils'\n\n// type Person = {\n//   firstName: string\n//   lastName: string\n//   age: number\n//   visits: number\n//   status: string\n//   progress: number\n//   createdAt: Date\n//   nested: {\n//     foo: [\n//       {\n//         bar: 'bar'\n//       }\n//     ]\n//     bar: { subBar: boolean }[]\n//     baz: {\n//       foo: 'foo'\n//       bar: {\n//         baz: 'baz'\n//       }\n//     }\n//   }\n// }\n\n// const test: DeepKeys<Person> = 'nested.foo.0.bar'\n// const test2: DeepKeys<Person> = 'nested.bar'\n\n// const helper = createColumnHelper<Person>()\n\n// helper.accessor('nested.foo', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.foo.0.bar', {\n//   cell: info => info.getValue(),\n// })\n\n// helper.accessor('nested.bar', {\n//   cell: info => info.getValue(),\n// })\n\nexport type ColumnHelper<TData extends RowData> = {\n  accessor: <\n    TAccessor extends AccessorFn<TData> | DeepKeys<TData>,\n    TValue extends TAccessor extends AccessorFn<TData, infer TReturn>\n      ? TReturn\n      : TAccessor extends DeepKeys<TData>\n        ? DeepValue<TData, TAccessor>\n        : never,\n  >(\n    accessor: TAccessor,\n    column: TAccessor extends AccessorFn<TData>\n      ? DisplayColumnDef<TData, TValue>\n      : IdentifiedColumnDef<TData, TValue>\n  ) => ColumnDef<TData, TValue>\n  display: (column: DisplayColumnDef<TData>) => ColumnDef<TData, unknown>\n  group: (column: GroupColumnDef<TData>) => ColumnDef<TData, unknown>\n}\n\nexport function createColumnHelper<\n  TData extends RowData,\n>(): ColumnHelper<TData> {\n  return {\n    accessor: (accessor, column) => {\n      return typeof accessor === 'function'\n        ? ({\n            ...column,\n            accessorFn: accessor,\n          } as any)\n        : {\n            ...column,\n            accessorKey: accessor,\n          }\n    },\n    display: column => column as ColumnDef<TData, unknown>,\n    group: column => column as ColumnDef<TData, unknown>,\n  }\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getCoreRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.options.data],\n      (\n        data\n      ): {\n        rows: Row<TData>[]\n        flatRows: Row<TData>[]\n        rowsById: Record<string, Row<TData>>\n      } => {\n        const rowModel: RowModel<TData> = {\n          rows: [],\n          flatRows: [],\n          rowsById: {},\n        }\n\n        const accessRows = (\n          originalRows: TData[],\n          depth = 0,\n          parentRow?: Row<TData>\n        ): Row<TData>[] => {\n          const rows = [] as Row<TData>[]\n\n          for (let i = 0; i < originalRows.length; i++) {\n            // This could be an expensive check at scale, so we should move it somewhere else, but where?\n            // if (!id) {\n            //   if (process.env.NODE_ENV !== 'production') {\n            //     throw new Error(`getRowId expected an ID, but got ${id}`)\n            //   }\n            // }\n\n            // Make the row\n            const row = createRow(\n              table,\n              table._getRowId(originalRows[i]!, i, parentRow),\n              originalRows[i]!,\n              i,\n              depth,\n              undefined,\n              parentRow?.id\n            )\n\n            // Keep track of every row in a flat array\n            rowModel.flatRows.push(row)\n            // Also keep track of every row by its ID\n            rowModel.rowsById[row.id] = row\n            // Push table row into parent\n            rows.push(row)\n\n            // Get the original subrows\n            if (table.options.getSubRows) {\n              row.originalSubRows = table.options.getSubRows(\n                originalRows[i]!,\n                i\n              )\n\n              // Then recursively access them\n              if (row.originalSubRows?.length) {\n                row.subRows = accessRows(row.originalSubRows, depth + 1, row)\n              }\n            }\n          }\n\n          return rows\n        }\n\n        rowModel.rows = accessRows(data)\n\n        return rowModel\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._autoResetPageIndex()\n        },\n      }\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Row, RowModel, Table, RowData } from '../types'\n\nexport function filterRows<TData extends RowData>(\n  rows: Row<TData>[],\n  filterRowImpl: (row: Row<TData>) => any,\n  table: Table<TData>\n) {\n  if (table.options.filterFromLeafRows) {\n    return filterRowModelFromLeafs(rows, filterRowImpl, table)\n  }\n\n  return filterRowModelFromRoot(rows, filterRowImpl, table)\n}\n\nexport function filterRowModelFromLeafs<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => Row<TData>[],\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    const rows: Row<TData>[] = []\n\n    // Filter from children up first\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const newRow = createRow(\n        table,\n        row.id,\n        row.original,\n        row.index,\n        row.depth,\n        undefined,\n        row.parentId\n      )\n      newRow.columnFilters = row.columnFilters\n\n      if (row.subRows?.length && depth < maxDepth) {\n        newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n        row = newRow\n\n        if (filterRow(row) && !newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n\n        if (filterRow(row) || newRow.subRows.length) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n          continue\n        }\n      } else {\n        row = newRow\n        if (filterRow(row)) {\n          rows.push(row)\n          newFilteredRowsById[row.id] = row\n          newFilteredFlatRows.push(row)\n        }\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n\nexport function filterRowModelFromRoot<TData extends RowData>(\n  rowsToFilter: Row<TData>[],\n  filterRow: (row: Row<TData>) => any,\n  table: Table<TData>\n): RowModel<TData> {\n  const newFilteredFlatRows: Row<TData>[] = []\n  const newFilteredRowsById: Record<string, Row<TData>> = {}\n  const maxDepth = table.options.maxLeafRowFilterDepth ?? 100\n\n  // Filters top level and nested rows\n  const recurseFilterRows = (rowsToFilter: Row<TData>[], depth = 0) => {\n    // Filter from parents downward first\n\n    const rows: Row<TData>[] = []\n\n    // Apply the filter to any subRows\n    for (let i = 0; i < rowsToFilter.length; i++) {\n      let row = rowsToFilter[i]!\n\n      const pass = filterRow(row)\n\n      if (pass) {\n        if (row.subRows?.length && depth < maxDepth) {\n          const newRow = createRow(\n            table,\n            row.id,\n            row.original,\n            row.index,\n            row.depth,\n            undefined,\n            row.parentId\n          )\n          newRow.subRows = recurseFilterRows(row.subRows, depth + 1)\n          row = newRow\n        }\n\n        rows.push(row)\n        newFilteredFlatRows.push(row)\n        newFilteredRowsById[row.id] = row\n      }\n    }\n\n    return rows\n  }\n\n  return {\n    rows: recurseFilterRows(rowsToFilter),\n    flatRows: newFilteredFlatRows,\n    rowsById: newFilteredRowsById,\n  }\n}\n", "import { ResolvedColumnFilter } from '../features/Filters'\nimport { Table, RowModel, Row, RowData } from '../types'\nimport { memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFilteredRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n      ],\n      (rowModel, columnFilters, globalFilter) => {\n        if (\n          !rowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          for (let i = 0; i < rowModel.flatRows.length; i++) {\n            rowModel.flatRows[i]!.columnFilters = {}\n            rowModel.flatRows[i]!.columnFiltersMeta = {}\n          }\n          return rowModel\n        }\n\n        const resolvedColumnFilters: ResolvedColumnFilter<TData>[] = []\n        const resolvedGlobalFilters: ResolvedColumnFilter<TData>[] = []\n\n        ;(columnFilters ?? []).forEach(d => {\n          const column = table.getColumn(d.id)\n\n          if (!column) {\n            return\n          }\n\n          const filterFn = column.getFilterFn()\n\n          if (!filterFn) {\n            if (process.env.NODE_ENV !== 'production') {\n              console.warn(\n                `Could not find a valid 'column.filterFn' for column with the ID: ${column.id}.`\n              )\n            }\n            return\n          }\n\n          resolvedColumnFilters.push({\n            id: d.id,\n            filterFn,\n            resolvedValue: filterFn.resolveFilterValue?.(d.value) ?? d.value,\n          })\n        })\n\n        const filterableIds = columnFilters.map(d => d.id)\n\n        const globalFilterFn = table.getGlobalFilterFn()\n\n        const globallyFilterableColumns = table\n          .getAllLeafColumns()\n          .filter(column => column.getCanGlobalFilter())\n\n        if (\n          globalFilter &&\n          globalFilterFn &&\n          globallyFilterableColumns.length\n        ) {\n          filterableIds.push('__global__')\n\n          globallyFilterableColumns.forEach(column => {\n            resolvedGlobalFilters.push({\n              id: column.id,\n              filterFn: globalFilterFn,\n              resolvedValue:\n                globalFilterFn.resolveFilterValue?.(globalFilter) ??\n                globalFilter,\n            })\n          })\n        }\n\n        let currentColumnFilter\n        let currentGlobalFilter\n\n        // Flag the prefiltered row model with each filter state\n        for (let j = 0; j < rowModel.flatRows.length; j++) {\n          const row = rowModel.flatRows[j]!\n\n          row.columnFilters = {}\n\n          if (resolvedColumnFilters.length) {\n            for (let i = 0; i < resolvedColumnFilters.length; i++) {\n              currentColumnFilter = resolvedColumnFilters[i]!\n              const id = currentColumnFilter.id\n\n              // Tag the row with the column filter state\n              row.columnFilters[id] = currentColumnFilter.filterFn(\n                row,\n                id,\n                currentColumnFilter.resolvedValue,\n                filterMeta => {\n                  row.columnFiltersMeta[id] = filterMeta\n                }\n              )\n            }\n          }\n\n          if (resolvedGlobalFilters.length) {\n            for (let i = 0; i < resolvedGlobalFilters.length; i++) {\n              currentGlobalFilter = resolvedGlobalFilters[i]!\n              const id = currentGlobalFilter.id\n              // Tag the row with the first truthy global filter state\n              if (\n                currentGlobalFilter.filterFn(\n                  row,\n                  id,\n                  currentGlobalFilter.resolvedValue,\n                  filterMeta => {\n                    row.columnFiltersMeta[id] = filterMeta\n                  }\n                )\n              ) {\n                row.columnFilters.__global__ = true\n                break\n              }\n            }\n\n            if (row.columnFilters.__global__ !== true) {\n              row.columnFilters.__global__ = false\n            }\n          }\n        }\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        // Filter final rows using all of the active filters\n        return filterRows(rowModel.rows, filterRowsImpl, table)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getFilteredRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._autoResetPageIndex()\n        },\n      }\n    )\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { memo } from '../utils'\nimport { filterRows } from './filterRowsUtils'\n\nexport function getFacetedRowModel<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => RowModel<TData> {\n  return (table, columnId) =>\n    memo(\n      () => [\n        table.getPreFilteredRowModel(),\n        table.getState().columnFilters,\n        table.getState().globalFilter,\n        table.getFilteredRowModel(),\n      ],\n      (preRowModel, columnFilters, globalFilter) => {\n        if (\n          !preRowModel.rows.length ||\n          (!columnFilters?.length && !globalFilter)\n        ) {\n          return preRowModel\n        }\n\n        const filterableIds = [\n          ...columnFilters.map(d => d.id).filter(d => d !== columnId),\n          globalFilter ? '__global__' : undefined,\n        ].filter(Boolean) as string[]\n\n        const filterRowsImpl = (row: Row<TData>) => {\n          // Horizontally filter rows through each column\n          for (let i = 0; i < filterableIds.length; i++) {\n            if (row.columnFilters[filterableIds[i]!] === false) {\n              return false\n            }\n          }\n          return true\n        }\n\n        return filterRows(preRowModel.rows, filterRowsImpl, table)\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' &&\n          'getFacetedRowModel_' + columnId,\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {},\n      }\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getFacetedUniqueValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => Map<any, number> {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return new Map()\n\n        let facetedUniqueValues = new Map<any, number>()\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (facetedUniqueValues.has(value)) {\n              facetedUniqueValues.set(\n                value,\n                (facetedUniqueValues.get(value) ?? 0) + 1\n              )\n            } else {\n              facetedUniqueValues.set(value, 1)\n            }\n          }\n        }\n\n        return facetedUniqueValues\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' &&\n          'getFacetedUniqueValues_' + columnId,\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {},\n      }\n    )\n}\n", "import { Table, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getFacetedMinMaxValues<TData extends RowData>(): (\n  table: Table<TData>,\n  columnId: string\n) => () => undefined | [number, number] {\n  return (table, columnId) =>\n    memo(\n      () => [table.getColumn(columnId)?.getFacetedRowModel()],\n      facetedRowModel => {\n        if (!facetedRowModel) return undefined\n\n        const firstValue =\n          facetedRowModel.flatRows[0]?.getUniqueValues(columnId)\n\n        if (typeof firstValue === 'undefined') {\n          return undefined\n        }\n\n        let facetedMinMaxValues: [any, any] = [firstValue, firstValue]\n\n        for (let i = 0; i < facetedRowModel.flatRows.length; i++) {\n          const values =\n            facetedRowModel.flatRows[i]!.getUniqueValues<number>(columnId)\n\n          for (let j = 0; j < values.length; j++) {\n            const value = values[j]!\n\n            if (value < facetedMinMaxValues[0]) {\n              facetedMinMaxValues[0] = value\n            } else if (value > facetedMinMaxValues[1]) {\n              facetedMinMaxValues[1] = value\n            }\n          }\n        }\n\n        return facetedMinMaxValues\n      },\n      {\n        key:\n          process.env.NODE_ENV === 'development' &&\n          'getFacetedMinMaxValues_' + columnId,\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {},\n      }\n    )\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { SortingFn } from '../features/Sorting'\nimport { memo } from '../utils'\n\nexport function getSortedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().sorting, table.getPreSortedRowModel()],\n      (sorting, rowModel) => {\n        if (!rowModel.rows.length || !sorting?.length) {\n          return rowModel\n        }\n\n        const sortingState = table.getState().sorting\n\n        const sortedFlatRows: Row<TData>[] = []\n\n        // Filter out sortings that correspond to non existing columns\n        const availableSorting = sortingState.filter(\n          sort => table.getColumn(sort.id)?.getCanSort()\n        )\n\n        const columnInfoById: Record<\n          string,\n          {\n            sortUndefined?: false | -1 | 1\n            invertSorting?: boolean\n            sortingFn: SortingFn<TData>\n          }\n        > = {}\n\n        availableSorting.forEach(sortEntry => {\n          const column = table.getColumn(sortEntry.id)\n          if (!column) return\n\n          columnInfoById[sortEntry.id] = {\n            sortUndefined: column.columnDef.sortUndefined,\n            invertSorting: column.columnDef.invertSorting,\n            sortingFn: column.getSortingFn(),\n          }\n        })\n\n        const sortData = (rows: Row<TData>[]) => {\n          // This will also perform a stable sorting using the row index\n          // if needed.\n          const sortedData = rows.map(row => ({ ...row }))\n\n          sortedData.sort((rowA, rowB) => {\n            for (let i = 0; i < availableSorting.length; i += 1) {\n              const sortEntry = availableSorting[i]!\n              const columnInfo = columnInfoById[sortEntry.id]!\n              const isDesc = sortEntry?.desc ?? false\n\n              let sortInt = 0\n\n              // All sorting ints should always return in ascending order\n              if (columnInfo.sortUndefined) {\n                const aValue = rowA.getValue(sortEntry.id)\n                const bValue = rowB.getValue(sortEntry.id)\n\n                const aUndefined = aValue === undefined\n                const bUndefined = bValue === undefined\n\n                if (aUndefined || bUndefined) {\n                  sortInt =\n                    aUndefined && bUndefined\n                      ? 0\n                      : aUndefined\n                        ? columnInfo.sortUndefined\n                        : -columnInfo.sortUndefined\n                }\n              }\n\n              if (sortInt === 0) {\n                sortInt = columnInfo.sortingFn(rowA, rowB, sortEntry.id)\n              }\n\n              // If sorting is non-zero, take care of desc and inversion\n              if (sortInt !== 0) {\n                if (isDesc) {\n                  sortInt *= -1\n                }\n\n                if (columnInfo.invertSorting) {\n                  sortInt *= -1\n                }\n\n                return sortInt\n              }\n            }\n\n            return rowA.index - rowB.index\n          })\n\n          // If there are sub-rows, sort them\n          sortedData.forEach(row => {\n            sortedFlatRows.push(row)\n            if (row.subRows?.length) {\n              row.subRows = sortData(row.subRows)\n            }\n          })\n\n          return sortedData\n        }\n\n        return {\n          rows: sortData(rowModel.rows),\n          flatRows: sortedFlatRows,\n          rowsById: rowModel.rowsById,\n        }\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getSortedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._autoResetPageIndex()\n        },\n      }\n    )\n}\n", "import { createRow } from '../core/row'\nimport { Table, Row, RowModel, RowData } from '../types'\nimport { flattenBy, memo } from '../utils'\n\nexport function getGroupedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [table.getState().grouping, table.getPreGroupedRowModel()],\n      (grouping, rowModel) => {\n        if (!rowModel.rows.length || !grouping.length) {\n          return rowModel\n        }\n\n        // Filter the grouping list down to columns that exist\n        const existingGrouping = grouping.filter(columnId =>\n          table.getColumn(columnId)\n        )\n\n        const groupedFlatRows: Row<TData>[] = []\n        const groupedRowsById: Record<string, Row<TData>> = {}\n        // const onlyGroupedFlatRows: Row[] = [];\n        // const onlyGroupedRowsById: Record<RowId, Row> = {};\n        // const nonGroupedFlatRows: Row[] = [];\n        // const nonGroupedRowsById: Record<RowId, Row> = {};\n\n        // Recursively group the data\n        const groupUpRecursively = (\n          rows: Row<TData>[],\n          depth = 0,\n          parentId?: string\n        ) => {\n          // Grouping depth has been been met\n          // Stop grouping and simply rewrite thd depth and row relationships\n          if (depth >= existingGrouping.length) {\n            return rows.map(row => {\n              row.depth = depth\n\n              groupedFlatRows.push(row)\n              groupedRowsById[row.id] = row\n\n              if (row.subRows) {\n                row.subRows = groupUpRecursively(row.subRows, depth + 1, row.id)\n              }\n\n              return row\n            })\n          }\n\n          const columnId: string = existingGrouping[depth]!\n\n          // Group the rows together for this level\n          const rowGroupsMap = groupBy(rows, columnId)\n\n          // Peform aggregations for each group\n          const aggregatedGroupedRows = Array.from(rowGroupsMap.entries()).map(\n            ([groupingValue, groupedRows], index) => {\n              let id = `${columnId}:${groupingValue}`\n              id = parentId ? `${parentId}>${id}` : id\n\n              // First, Recurse to group sub rows before aggregation\n              const subRows = groupUpRecursively(groupedRows, depth + 1, id)\n\n              // Flatten the leaf rows of the rows in this group\n              const leafRows = depth\n                ? flattenBy(groupedRows, row => row.subRows)\n                : groupedRows\n\n              const row = createRow(\n                table,\n                id,\n                leafRows[0]!.original,\n                index,\n                depth,\n                undefined,\n                parentId\n              )\n\n              Object.assign(row, {\n                groupingColumnId: columnId,\n                groupingValue,\n                subRows,\n                leafRows,\n                getValue: (columnId: string) => {\n                  // Don't aggregate columns that are in the grouping\n                  if (existingGrouping.includes(columnId)) {\n                    if (row._valuesCache.hasOwnProperty(columnId)) {\n                      return row._valuesCache[columnId]\n                    }\n\n                    if (groupedRows[0]) {\n                      row._valuesCache[columnId] =\n                        groupedRows[0].getValue(columnId) ?? undefined\n                    }\n\n                    return row._valuesCache[columnId]\n                  }\n\n                  if (row._groupingValuesCache.hasOwnProperty(columnId)) {\n                    return row._groupingValuesCache[columnId]\n                  }\n\n                  // Aggregate the values\n                  const column = table.getColumn(columnId)\n                  const aggregateFn = column?.getAggregationFn()\n\n                  if (aggregateFn) {\n                    row._groupingValuesCache[columnId] = aggregateFn(\n                      columnId,\n                      leafRows,\n                      groupedRows\n                    )\n\n                    return row._groupingValuesCache[columnId]\n                  }\n                },\n              })\n\n              subRows.forEach(subRow => {\n                groupedFlatRows.push(subRow)\n                groupedRowsById[subRow.id] = subRow\n                // if (subRow.getIsGrouped?.()) {\n                //   onlyGroupedFlatRows.push(subRow);\n                //   onlyGroupedRowsById[subRow.id] = subRow;\n                // } else {\n                //   nonGroupedFlatRows.push(subRow);\n                //   nonGroupedRowsById[subRow.id] = subRow;\n                // }\n              })\n\n              return row\n            }\n          )\n\n          return aggregatedGroupedRows\n        }\n\n        const groupedRows = groupUpRecursively(rowModel.rows, 0)\n\n        groupedRows.forEach(subRow => {\n          groupedFlatRows.push(subRow)\n          groupedRowsById[subRow.id] = subRow\n          // if (subRow.getIsGrouped?.()) {\n          //   onlyGroupedFlatRows.push(subRow);\n          //   onlyGroupedRowsById[subRow.id] = subRow;\n          // } else {\n          //   nonGroupedFlatRows.push(subRow);\n          //   nonGroupedRowsById[subRow.id] = subRow;\n          // }\n        })\n\n        return {\n          rows: groupedRows,\n          flatRows: groupedFlatRows,\n          rowsById: groupedRowsById,\n        }\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getGroupedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n        onChange: () => {\n          table._queue(() => {\n            table._autoResetExpanded()\n            table._autoResetPageIndex()\n          })\n        },\n      }\n    )\n}\n\nfunction groupBy<TData extends RowData>(rows: Row<TData>[], columnId: string) {\n  const groupMap = new Map<any, Row<TData>[]>()\n\n  return rows.reduce((map, row) => {\n    const resKey = `${row.getGroupingValue(columnId)}`\n    const previous = map.get(resKey)\n    if (!previous) {\n      map.set(resKey, [row])\n    } else {\n      previous.push(row)\n    }\n    return map\n  }, groupMap)\n}\n", "import { Table, Row, RowModel, RowData } from '../types'\nimport { memo } from '../utils'\n\nexport function getExpandedRowModel<TData extends RowData>(): (\n  table: Table<TData>\n) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().expanded,\n        table.getPreExpandedRowModel(),\n        table.options.paginateExpandedRows,\n      ],\n      (expanded, rowModel, paginateExpandedRows) => {\n        if (\n          !rowModel.rows.length ||\n          (expanded !== true && !Object.keys(expanded ?? {}).length)\n        ) {\n          return rowModel\n        }\n\n        if (!paginateExpandedRows) {\n          // Only expand rows at this point if they are being paginated\n          return rowModel\n        }\n\n        return expandRows(rowModel)\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getExpandedRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n}\n\nexport function expandRows<TData extends RowData>(rowModel: RowModel<TData>) {\n  const expandedRows: Row<TData>[] = []\n\n  const handleRow = (row: Row<TData>) => {\n    expandedRows.push(row)\n\n    if (row.subRows?.length && row.getIsExpanded()) {\n      row.subRows.forEach(handleRow)\n    }\n  }\n\n  rowModel.rows.forEach(handleRow)\n\n  return {\n    rows: expandedRows,\n    flatRows: rowModel.flatRows,\n    rowsById: rowModel.rowsById,\n  }\n}\n", "import { Table, RowModel, Row, RowData } from '../types'\nimport { memo } from '../utils'\nimport { expandRows } from './getExpandedRowModel'\n\nexport function getPaginationRowModel<TData extends RowData>(opts?: {\n  initialSync: boolean\n}): (table: Table<TData>) => () => RowModel<TData> {\n  return table =>\n    memo(\n      () => [\n        table.getState().pagination,\n        table.getPrePaginationRowModel(),\n        table.options.paginateExpandedRows\n          ? undefined\n          : table.getState().expanded,\n      ],\n      (pagination, rowModel) => {\n        if (!rowModel.rows.length) {\n          return rowModel\n        }\n\n        const { pageSize, pageIndex } = pagination\n        let { rows, flatRows, rowsById } = rowModel\n        const pageStart = pageSize * pageIndex\n        const pageEnd = pageStart + pageSize\n\n        rows = rows.slice(pageStart, pageEnd)\n\n        let paginatedRowModel: RowModel<TData>\n\n        if (!table.options.paginateExpandedRows) {\n          paginatedRowModel = expandRows({\n            rows,\n            flatRows,\n            rowsById,\n          })\n        } else {\n          paginatedRowModel = {\n            rows,\n            flatRows,\n            rowsById,\n          }\n        }\n\n        paginatedRowModel.flatRows = []\n\n        const handleRow = (row: Row<TData>) => {\n          paginatedRowModel.flatRows.push(row)\n          if (row.subRows.length) {\n            row.subRows.forEach(handleRow)\n          }\n        }\n\n        paginatedRowModel.rows.forEach(handleRow)\n\n        return paginatedRowModel\n      },\n      {\n        key: process.env.NODE_ENV === 'development' && 'getPaginationRowModel',\n        debug: () => table.options.debugAll ?? table.options.debugTable,\n      }\n    )\n}\n", "import * as React from 'react'\nexport * from '@tanstack/table-core'\n\nimport {\n  TableOptions,\n  TableOptionsResolved,\n  RowData,\n  createTable,\n} from '@tanstack/table-core'\n\nexport type Renderable<TProps> = React.ReactNode | React.ComponentType<TProps>\n\n//\n\n/**\n * If rendering headers, cells, or footers with custom markup, use flexRender instead of `cell.getValue()` or `cell.renderValue()`.\n */\nexport function flexRender<TProps extends object>(\n  Comp: Renderable<TProps>,\n  props: TProps\n): React.ReactNode | JSX.Element {\n  return !Comp ? null : isReactComponent<TProps>(Comp) ? (\n    <Comp {...props} />\n  ) : (\n    Comp\n  )\n}\n\nfunction isReactComponent<TProps>(\n  component: unknown\n): component is React.ComponentType<TProps> {\n  return (\n    isClassComponent(component) ||\n    typeof component === 'function' ||\n    isExoticComponent(component)\n  )\n}\n\nfunction isClassComponent(component: any) {\n  return (\n    typeof component === 'function' &&\n    (() => {\n      const proto = Object.getPrototypeOf(component)\n      return proto.prototype && proto.prototype.isReactComponent\n    })()\n  )\n}\n\nfunction isExoticComponent(component: any) {\n  return (\n    typeof component === 'object' &&\n    typeof component.$$typeof === 'symbol' &&\n    ['react.memo', 'react.forward_ref'].includes(component.$$typeof.description)\n  )\n}\n\nexport function useReactTable<TData extends RowData>(\n  options: TableOptions<TData>\n) {\n  // Compose in the generic options to the user options\n  const resolvedOptions: TableOptionsResolved<TData> = {\n    state: {}, // Dummy state\n    onStateChange: () => {}, // noop\n    renderFallbackValue: null,\n    ...options,\n  }\n\n  // Create a new table and store it in state\n  const [tableRef] = React.useState(() => ({\n    current: createTable<TData>(resolvedOptions),\n  }))\n\n  // By default, manage table state here using the table's initial state\n  const [state, setState] = React.useState(() => tableRef.current.initialState)\n\n  // Compose the default state above with any user state. This will allow the user\n  // to only control a subset of the state if desired.\n  tableRef.current.setOptions(prev => ({\n    ...prev,\n    ...options,\n    state: {\n      ...state,\n      ...options.state,\n    },\n    // Similarly, we'll maintain both our internal state and any user-provided\n    // state.\n    onStateChange: updater => {\n      setState(updater)\n      options.onStateChange?.(updater)\n    },\n  }))\n\n  return tableRef.current\n}\n"], "mappings": ";;;;;;;;;;;AAiFO,SAASA,iBAAoBC,SAAqBC,OAAa;AACpE,SAAO,OAAOD,YAAY,aACrBA,QAA4BC,KAAK,IAClCD;AACN;AAEO,SAASE,OAAO;AACrB;AAGK,SAASC,iBACdC,KACAC,UACA;AACA,SAAQL,aAAoC;AACxCK,aAAiBC,SAAuBC,SAAqB;AAC7D,aAAO;QACL,GAAGA;QACH,CAACH,GAAG,GAAGL,iBAAiBC,SAAUO,IAAYH,GAAG,CAAC;;IAEtD,CAAC;;AAEL;AAIO,SAASI,WAAkCC,GAAgB;AAChE,SAAOA,aAAaC;AACtB;AAEO,SAASC,cAAcF,GAAuB;AACnD,SAAOG,MAAMC,QAAQJ,CAAC,KAAKA,EAAEK,MAAMC,SAAO,OAAOA,QAAQ,QAAQ;AACnE;AAEO,SAASC,UACdC,KACAC,aACA;AACA,QAAMC,OAAgB,CAAA;AAEtB,QAAMC,UAAWC,YAAoB;AACnCA,WAAOC,QAAQC,UAAQ;AACrBJ,WAAKK,KAAKD,IAAI;AACd,YAAME,WAAWP,YAAYK,IAAI;AACjC,UAAIE,YAAQ,QAARA,SAAUC,QAAQ;AACpBN,gBAAQK,QAAQ;MAClB;IACF,CAAC;;AAGHL,UAAQH,GAAG;AAEX,SAAOE;AACT;AAEO,SAASQ,KACdC,SACAC,IACAC,MAKe;AACf,MAAIC,OAAc,CAAA;AAClB,MAAIC;AAEJ,SAAO,MAAM;AACX,QAAIC;AACJ,QAAIH,KAAK1B,OAAO0B,KAAKI;AAAOD,gBAAUE,KAAKC,IAAG;AAE9C,UAAMC,UAAUT,QAAO;AAEvB,UAAMU,cACJD,QAAQX,WAAWK,KAAKL,UACxBW,QAAQE,KAAK,CAACC,KAAUC,UAAkBV,KAAKU,KAAK,MAAMD,GAAG;AAE/D,QAAI,CAACF,aAAa;AAChB,aAAON;IACT;AAEAD,WAAOM;AAEP,QAAIK;AACJ,QAAIZ,KAAK1B,OAAO0B,KAAKI;AAAOQ,mBAAaP,KAAKC,IAAG;AAEjDJ,aAASH,GAAG,GAAGQ,OAAO;AACtBP,YAAI,QAAJA,KAAMa,YAAQ,QAAdb,KAAMa,SAAWX,MAAM;AAEvB,QAAIF,KAAK1B,OAAO0B,KAAKI,OAAO;AAC1B,UAAIJ,QAAAA,QAAAA,KAAMI,MAAK,GAAI;AACjB,cAAMU,aAAaC,KAAKC,OAAOX,KAAKC,IAAG,IAAKH,WAAY,GAAG,IAAI;AAC/D,cAAMc,gBAAgBF,KAAKC,OAAOX,KAAKC,IAAG,IAAKM,cAAe,GAAG,IAAI;AACrE,cAAMM,sBAAsBD,gBAAgB;AAE5C,cAAME,MAAMA,CAACC,KAAsBC,QAAgB;AACjDD,gBAAME,OAAOF,GAAG;AAChB,iBAAOA,IAAIxB,SAASyB,KAAK;AACvBD,kBAAM,MAAMA;UACd;AACA,iBAAOA;;AAGTG,gBAAQC,KACL,OAAML,IAAIF,eAAe,CAAC,CAAE,KAAIE,IAAIL,YAAY,CAAC,CAAE,OACnD;;;yBAGcC,KAAKU,IAChB,GACAV,KAAKW,IAAI,MAAM,MAAMR,qBAAqB,GAAG,CAC/C,CAAE,kBACJlB,QAAAA,OAAAA,SAAAA,KAAM1B,GACR;MACF;IACF;AAEA,WAAO4B;;AAEX;ACxIO,SAASyB,aACdC,OACAC,WACAC,OACAC,QACuB;AAAA,MAAAC,MAAAC;AACvB,QAAMC,gBAAgBN,MAAMO,qBAAoB;AAEhD,QAAMC,oBAAoB;IACxB,GAAGF;IACH,GAAGL;;AAGL,QAAMQ,cAAcD,kBAAkBC;AAEtC,MAAIC,MAAEN,QAAAC,wBACJG,kBAAkBE,OAAE,OAAAL,wBACnBI,cAAcA,YAAYE,QAAQ,KAAK,GAAG,IAAIC,WAASR,OAAAA,OACvD,OAAOI,kBAAkBK,WAAW,WACjCL,kBAAkBK,SAClBD;AAEN,MAAIE;AAEJ,MAAIN,kBAAkBM,YAAY;AAChCA,iBAAaN,kBAAkBM;aACtBL,aAAa;AAEtB,QAAIA,YAAYM,SAAS,GAAG,GAAG;AAC7BD,mBAAcE,iBAAuB;AACnC,YAAI1C,SAAS0C;AAEb,mBAAWtE,OAAO+D,YAAYQ,MAAM,GAAG,GAAG;AAAA,cAAAC;AACxC5C,oBAAM4C,UAAG5C,WAAM,OAAA,SAAN4C,QAASxE,GAAG;AACrB,cAA6C4B,WAAWsC,QAAW;AACjEjB,oBAAQwB,KACL,IAAGzE,GAAI,2BAA0B+D,WAAY,uBAChD;UACF;QACF;AAEA,eAAOnC;;IAEX,OAAO;AACLwC,mBAAcE,iBACXA,YAAoBR,kBAAkBC,WAAW;IACtD;EACF;AAEA,MAAI,CAACC,IAAI;AACP,QAAIU,MAAuC;AACzC,YAAM,IAAIC,MACRb,kBAAkBM,aACb,mDACA,sDACP;IACF;AACA,UAAM,IAAIO,MAAK;EACjB;AAEA,MAAIC,SAAiC;IACnCZ,IAAK,GAAEhB,OAAOgB,EAAE,CAAE;IAClBI;IACAX;IACAD;IACAD,WAAWO;IACXe,SAAS,CAAA;IACTC,gBAAgBvD,KACd,MAAM,CAAC,IAAI,GACX,MAAM;AAAA,UAAAwD;AACJ,aAAO,CACLH,QACA,IAAAG,kBAAGH,OAAOC,YAAPE,OAAAA,SAAAA,gBAAgBC,QAAQ3E,OAAKA,EAAEyE,eAAc,CAAE,CAAC;IAEvD,GACA;MACE9E,KAAK0E;MACL5C,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQE;MAAY;IACnE,CACF;IACAC,gBAAgB9D,KACd,MAAM,CAAC+B,MAAMgC,mBAAkB,CAAE,GACjCC,CAAAA,kBAAgB;AAAA,UAAAC;AACd,WAAAA,mBAAIZ,OAAOC,YAAPW,QAAAA,iBAAgBlE,QAAQ;AAC1B,YAAImE,cAAcb,OAAOC,QAAQG,QAAQJ,CAAAA,YACvCA,QAAOS,eAAc,CACvB;AAEA,eAAOE,cAAaE,WAAW;MACjC;AAEA,aAAO,CAACb,MAAM;IAChB,GACA;MACE5E,KAAK0E;MACL5C,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQE;MAAY;KAErE;;AAGF,aAAWO,WAAWrC,MAAMsC,WAAW;AACrCD,YAAQtC,gBAAY,QAApBsC,QAAQtC,aAAeuB,QAAQtB,KAAK;EACtC;AAGA,SAAOsB;AACT;ACkCA,SAASiB,aACPvC,OACAsB,QACAM,SAOuB;AAAA,MAAAY;AACvB,QAAM9B,MAAE8B,cAAGZ,QAAQlB,OAAE,OAAA8B,cAAIlB,OAAOZ;AAEhC,MAAIG,SAAoC;IACtCH;IACAY;IACAvC,OAAO6C,QAAQ7C;IACf0D,eAAe,CAAC,CAACb,QAAQa;IACzBC,eAAed,QAAQc;IACvBxC,OAAO0B,QAAQ1B;IACfyC,YAAY,CAAA;IACZC,SAAS;IACTC,SAAS;IACTC,aAAa;IACbC,gBAAgBA,MAAgC;AAC9C,YAAMC,cAAwC,CAAA;AAE9C,YAAMC,gBAAiBC,OAA8B;AACnD,YAAIA,EAAEP,cAAcO,EAAEP,WAAW3E,QAAQ;AACvCkF,YAAEP,WAAWQ,IAAIF,aAAa;QAChC;AACAD,oBAAYlF,KAAKoF,CAA2B;;AAG9CD,oBAAcpC,MAAM;AAEpB,aAAOmC;;IAETI,YAAYA,OAAO;MACjBpD;MACAa;MACAS;;;AAIJtB,QAAMsC,UAAU1E,QAAQyE,aAAW;AACjCA,YAAQE,gBAAY,QAApBF,QAAQE,aAAe1B,QAAQb,KAAK;EACtC,CAAC;AAED,SAAOa;AACT;AAEO,IAAMwC,UAAwB;EACnCC,aAAqCtD,WAA8B;AAGjEA,UAAMuD,kBAAkBtF,KACtB,MAAM,CACJ+B,MAAMwD,cAAa,GACnBxD,MAAMyD,sBAAqB,GAC3BzD,MAAM0D,SAAQ,EAAGC,cAAcC,MAC/B5D,MAAM0D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAY3B,aAAayB,MAAMC,UAAU;AAAA,UAAAE,kBAAAC;AACxC,YAAMC,eAAWF,mBACfH,QAAAA,OAAAA,SAAAA,KACIT,IAAIe,cAAY/B,YAAYgC,KAAKpH,OAAKA,EAAE2D,OAAOwD,QAAQ,CAAE,EAC1DE,OAAOC,OAAO,MAACN,OAAAA,mBAAI,CAAA;AAExB,YAAMO,gBAAYN,oBAChBH,SAAAA,OAAAA,SAAAA,MACIV,IAAIe,cAAY/B,YAAYgC,KAAKpH,OAAKA,EAAE2D,OAAOwD,QAAQ,CAAE,EAC1DE,OAAOC,OAAO,MAACL,OAAAA,oBAAI,CAAA;AAExB,YAAMO,gBAAgBpC,YAAYiC,OAChC9C,YAAU,EAACsC,QAAI,QAAJA,KAAM7C,SAASO,OAAOZ,EAAE,MAAK,EAACmD,SAAK,QAALA,MAAO9C,SAASO,OAAOZ,EAAE,EACpE;AAEA,YAAM8D,eAAeC,kBACnBX,YACA,CAAC,GAAGG,aAAa,GAAGM,eAAe,GAAGD,YAAY,GAClDtE,KACF;AAEA,aAAOwE;IACT,GACA;MACE9H,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAM2E,wBAAwB1G,KAC5B,MAAM,CACJ+B,MAAMwD,cAAa,GACnBxD,MAAMyD,sBAAqB,GAC3BzD,MAAM0D,SAAQ,EAAGC,cAAcC,MAC/B5D,MAAM0D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAY3B,aAAayB,MAAMC,UAAU;AACxC1B,oBAAcA,YAAYiC,OACxB9C,YAAU,EAACsC,QAAI,QAAJA,KAAM7C,SAASO,OAAOZ,EAAE,MAAK,EAACmD,SAAK,QAALA,MAAO9C,SAASO,OAAOZ,EAAE,EACpE;AACA,aAAO+D,kBAAkBX,YAAY3B,aAAanC,OAAO,QAAQ;IACnE,GACA;MACEtD,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAM4E,sBAAsB3G,KAC1B,MAAM,CACJ+B,MAAMwD,cAAa,GACnBxD,MAAMyD,sBAAqB,GAC3BzD,MAAM0D,SAAQ,EAAGC,cAAcC,IAAI,GAErC,CAACE,YAAY3B,aAAayB,SAAS;AAAA,UAAAiB;AACjC,YAAMC,sBAAkBD,oBACtBjB,QAAAA,OAAAA,SAAAA,KACIT,IAAIe,cAAY/B,YAAYgC,KAAKpH,OAAKA,EAAE2D,OAAOwD,QAAQ,CAAE,EAC1DE,OAAOC,OAAO,MAACQ,OAAAA,oBAAI,CAAA;AAExB,aAAOJ,kBAAkBX,YAAYgB,oBAAoB9E,OAAO,MAAM;IACxE,GACA;MACEtD,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAuG;AAAA,gBAAAA,yBAAM/E,MAAM4B,QAAQC,aAAQkD,OAAAA,yBAAI/E,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAMgF,uBAAuB/G,KAC3B,MAAM,CACJ+B,MAAMwD,cAAa,GACnBxD,MAAMyD,sBAAqB,GAC3BzD,MAAM0D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAY3B,aAAa0B,UAAU;AAAA,UAAAoB;AAClC,YAAMH,sBAAkBG,qBACtBpB,SAAAA,OAAAA,SAAAA,MACIV,IAAIe,cAAY/B,YAAYgC,KAAKpH,OAAKA,EAAE2D,OAAOwD,QAAQ,CAAE,EAC1DE,OAAOC,OAAO,MAACY,OAAAA,qBAAI,CAAA;AAExB,aAAOR,kBAAkBX,YAAYgB,oBAAoB9E,OAAO,OAAO;IACzE,GACA;MACEtD,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA0G;AAAA,gBAAAA,yBAAMlF,MAAM4B,QAAQC,aAAQqD,OAAAA,yBAAIlF,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAIA1E,UAAMmF,kBAAkBlH,KACtB,MAAM,CAAC+B,MAAMuD,gBAAe,CAAE,GAC9BiB,kBAAgB;AACd,aAAO,CAAC,GAAGA,YAAY,EAAEY,QAAO;IAClC,GACA;MACE1I,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA6G;AAAA,gBAAAA,yBAAMrF,MAAM4B,QAAQC,aAAQwD,OAAAA,yBAAIrF,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAMsF,sBAAsBrH,KAC1B,MAAM,CAAC+B,MAAM4E,oBAAmB,CAAE,GAClCJ,kBAAgB;AACd,aAAO,CAAC,GAAGA,YAAY,EAAEY,QAAO;IAClC,GACA;MACE1I,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA+G;AAAA,gBAAAA,yBAAMvF,MAAM4B,QAAQC,aAAQ0D,OAAAA,yBAAIvF,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAMwF,wBAAwBvH,KAC5B,MAAM,CAAC+B,MAAM2E,sBAAqB,CAAE,GACpCH,kBAAgB;AACd,aAAO,CAAC,GAAGA,YAAY,EAAEY,QAAO;IAClC,GACA;MACE1I,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAiH;AAAA,gBAAAA,yBAAMzF,MAAM4B,QAAQC,aAAQ4D,OAAAA,yBAAIzF,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAM0F,uBAAuBzH,KAC3B,MAAM,CAAC+B,MAAMgF,qBAAoB,CAAE,GACnCR,kBAAgB;AACd,aAAO,CAAC,GAAGA,YAAY,EAAEY,QAAO;IAClC,GACA;MACE1I,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAmH;AAAA,gBAAAA,yBAAM3F,MAAM4B,QAAQC,aAAQ8D,OAAAA,yBAAI3F,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAIA1E,UAAM4F,iBAAiB3H,KACrB,MAAM,CAAC+B,MAAMuD,gBAAe,CAAE,GAC9BiB,kBAAgB;AACd,aAAOA,aACJrB,IAAIL,iBAAe;AAClB,eAAOA,YAAY+C;MACrB,CAAC,EACApI,KAAI;IACT,GACA;MACEf,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAsH;AAAA,gBAAAA,yBAAM9F,MAAM4B,QAAQC,aAAQiE,OAAAA,yBAAI9F,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAM+F,qBAAqB9H,KACzB,MAAM,CAAC+B,MAAM4E,oBAAmB,CAAE,GAClChB,UAAQ;AACN,aAAOA,KACJT,IAAIL,iBAAe;AAClB,eAAOA,YAAY+C;MACrB,CAAC,EACApI,KAAI;IACT,GACA;MACEf,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAwH;AAAA,gBAAAA,0BAAMhG,MAAM4B,QAAQC,aAAQmE,OAAAA,0BAAIhG,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAMiG,uBAAuBhI,KAC3B,MAAM,CAAC+B,MAAM2E,sBAAqB,CAAE,GACpCf,UAAQ;AACN,aAAOA,KACJT,IAAIL,iBAAe;AAClB,eAAOA,YAAY+C;MACrB,CAAC,EACApI,KAAI;IACT,GACA;MACEf,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA0H;AAAA,gBAAAA,0BAAMlG,MAAM4B,QAAQC,aAAQqE,OAAAA,0BAAIlG,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAMmG,sBAAsBlI,KAC1B,MAAM,CAAC+B,MAAMgF,qBAAoB,CAAE,GACnCpB,UAAQ;AACN,aAAOA,KACJT,IAAIL,iBAAe;AAClB,eAAOA,YAAY+C;MACrB,CAAC,EACApI,KAAI;IACT,GACA;MACEf,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA4H;AAAA,gBAAAA,0BAAMpG,MAAM4B,QAAQC,aAAQuE,OAAAA,0BAAIpG,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAIA1E,UAAMqG,uBAAuBpI,KAC3B,MAAM,CAAC+B,MAAMiG,qBAAoB,CAAE,GACnCK,iBAAe;AACb,aAAOA,YAAYlC,OAAOvD,YAAM;AAAA,YAAA0F;AAAA,eAAI,GAAAA,qBAAC1F,OAAO8B,eAAU,QAAjB4D,mBAAmBvI;OAAO;IACjE,GACA;MACEtB,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAgI;AAAA,gBAAAA,0BAAMxG,MAAM4B,QAAQC,aAAQ2E,OAAAA,0BAAIxG,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAMyG,qBAAqBxI,KACzB,MAAM,CAAC+B,MAAM+F,mBAAkB,CAAE,GACjCO,iBAAe;AACb,aAAOA,YAAYlC,OAAOvD,YAAM;AAAA,YAAA6F;AAAA,eAAI,GAAAA,sBAAC7F,OAAO8B,eAAU,QAAjB+D,oBAAmB1I;OAAO;IACjE,GACA;MACEtB,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAmI;AAAA,gBAAAA,0BAAM3G,MAAM4B,QAAQC,aAAQ8E,OAAAA,0BAAI3G,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAM4G,sBAAsB3I,KAC1B,MAAM,CAAC+B,MAAMmG,oBAAmB,CAAE,GAClCG,iBAAe;AACb,aAAOA,YAAYlC,OAAOvD,YAAM;AAAA,YAAAgG;AAAA,eAAI,GAAAA,sBAAChG,OAAO8B,eAAU,QAAjBkE,oBAAmB7I;OAAO;IACjE,GACA;MACEtB,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAsI;AAAA,gBAAAA,0BAAM9G,MAAM4B,QAAQC,aAAQiF,OAAAA,0BAAI9G,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;AAEA1E,UAAM+C,iBAAiB9E,KACrB,MAAM,CACJ+B,MAAM4E,oBAAmB,GACzB5E,MAAM2E,sBAAqB,GAC3B3E,MAAMgF,qBAAoB,CAAE,GAE9B,CAACpB,MAAMmD,QAAQlD,UAAU;AAAA,UAAAmD,iBAAAC,QAAAC,mBAAAC,UAAAC,kBAAAC;AACvB,aAAO,CACL,IAAAL,mBAAAC,SAAIrD,KAAK,CAAC,MAANqD,OAAAA,SAAAA,OAASpB,YAAOmB,OAAAA,kBAAI,CAAA,GACxB,IAAAE,qBAAAC,WAAIJ,OAAO,CAAC,MAARI,OAAAA,SAAAA,SAAWtB,YAAOqB,OAAAA,oBAAI,CAAA,GAC1B,IAAAE,oBAAAC,UAAIxD,MAAM,CAAC,MAAC,OAAA,SAARwD,QAAUxB,YAAO,OAAAuB,mBAAI,CAAA,CAAE,EAE1BjE,IAAItC,YAAU;AACb,eAAOA,OAAOkC,eAAc;MAC9B,CAAC,EACAtF,KAAI;IACT,GACA;MACEf,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA8I;AAAA,gBAAAA,0BAAMtH,MAAM4B,QAAQC,aAAQyF,OAAAA,0BAAItH,MAAM4B,QAAQ8C;MAAY;IACnE,CACF;EACF;AACF;AAEO,SAASD,kBACdX,YACAyD,gBACAvH,OACAwH,cACA;AAAA,MAAAC,uBAAAC;AAOA,MAAIC,WAAW;AAEf,QAAMC,eAAe,SAACrG,SAAmCrB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAChEyH,eAAWxI,KAAKU,IAAI8H,UAAUzH,KAAK;AAEnCqB,YACG6C,OAAO9C,YAAUA,OAAOuG,aAAY,CAAE,EACtCjK,QAAQ0D,YAAU;AAAA,UAAAG;AACjB,WAAAA,kBAAIH,OAAOC,YAAPE,QAAAA,gBAAgBzD,QAAQ;AAC1B4J,qBAAatG,OAAOC,SAASrB,QAAQ,CAAC;MACxC;OACC,CAAC;;AAGR0H,eAAa9D,UAAU;AAEvB,MAAIU,eAAqC,CAAA;AAEzC,QAAMsD,oBAAoBA,CACxBC,gBACA7H,UACG;AAEH,UAAM4C,cAAkC;MACtC5C;MACAQ,IAAI,CAAC8G,cAAe,GAAEtH,KAAM,EAAC,EAAEkE,OAAOC,OAAO,EAAE2D,KAAK,GAAG;MACvDnC,SAAS,CAAA;;AAIX,UAAMoC,uBAAiD,CAAA;AAGvDF,mBAAenK,QAAQsK,mBAAiB;AAGtC,YAAMC,4BAA4B,CAAC,GAAGF,oBAAoB,EAAE7C,QAAO,EAAG,CAAC;AAEvE,YAAMgD,eAAeF,cAAc5G,OAAOpB,UAAU4C,YAAY5C;AAEhE,UAAIoB;AACJ,UAAImB,gBAAgB;AAEpB,UAAI2F,gBAAgBF,cAAc5G,OAAOnB,QAAQ;AAE/CmB,iBAAS4G,cAAc5G,OAAOnB;MAChC,OAAO;AAELmB,iBAAS4G,cAAc5G;AACvBmB,wBAAgB;MAClB;AAEA,UACE0F,8BACAA,6BAAyB,OAAA,SAAzBA,0BAA2B7G,YAAWA,QACtC;AAEA6G,kCAA0BxF,WAAW7E,KAAKoK,aAAa;MACzD,OAAO;AAEL,cAAMrH,SAAS0B,aAAavC,OAAOsB,QAAQ;UACzCZ,IAAI,CAAC8G,cAActH,OAAOoB,OAAOZ,IAAIwH,iBAAa,OAAA,SAAbA,cAAexH,EAAE,EACnD0D,OAAOC,OAAO,EACd2D,KAAK,GAAG;UACXvF;UACAC,eAAeD,gBACV,GAAEwF,qBAAqB7D,OAAOrH,OAAKA,EAAEuE,WAAWA,MAAM,EAAEtD,MAAO,KAChE4C;UACJV;UACAnB,OAAOkJ,qBAAqBjK;QAC9B,CAAC;AAGD6C,eAAO8B,WAAW7E,KAAKoK,aAAa;AAGpCD,6BAAqBnK,KAAK+C,MAAM;MAClC;AAEAiC,kBAAY+C,QAAQ/H,KAAKoK,aAAa;AACtCA,oBAAcpF,cAAcA;IAC9B,CAAC;AAED0B,iBAAa1G,KAAKgF,WAAW;AAE7B,QAAI5C,QAAQ,GAAG;AACb4H,wBAAkBG,sBAAsB/H,QAAQ,CAAC;IACnD;;AAGF,QAAMmI,gBAAgBd,eAAepE,IAAI,CAAC7B,QAAQvC,UAChDwD,aAAavC,OAAOsB,QAAQ;IAC1BpB,OAAOyH;IACP5I;EACF,CAAC,CACH;AAEA+I,oBAAkBO,eAAeV,WAAW,CAAC;AAE7CnD,eAAaY,QAAO;AAMpB,QAAMkD,yBACJzC,aAC2C;AAC3C,UAAM0C,kBAAkB1C,QAAQzB,OAAOvD,YACrCA,OAAOS,OAAOuG,aAAY,CAC5B;AAEA,WAAOU,gBAAgBpF,IAAItC,YAAU;AACnC,UAAI+B,UAAU;AACd,UAAIC,UAAU;AACd,UAAI2F,gBAAgB,CAAC,CAAC;AAEtB,UAAI3H,OAAO8B,cAAc9B,OAAO8B,WAAW3E,QAAQ;AACjDwK,wBAAgB,CAAA;AAEhBF,+BAAuBzH,OAAO8B,UAAU,EAAE/E,QACxCwC,UAAsD;AAAA,cAArD;YAAEwC,SAAS6F;YAAc5F,SAAS6F;UAAa,IAACtI;AAC/CwC,qBAAW6F;AACXD,wBAAc1K,KAAK4K,YAAY;QACjC,CACF;MACF,OAAO;AACL9F,kBAAU;MACZ;AAEA,YAAM+F,kBAAkBxJ,KAAKW,IAAI,GAAG0I,aAAa;AACjD3F,gBAAUA,UAAU8F;AAEpB9H,aAAO+B,UAAUA;AACjB/B,aAAOgC,UAAUA;AAEjB,aAAO;QAAED;QAASC;;IACpB,CAAC;;AAGHyF,0BAAsBb,yBAAAC,iBAAClD,aAAa,CAAC,MAAC,OAAA,SAAfkD,eAAiB7B,YAAO,OAAA4B,wBAAI,CAAA,CAAE;AAErD,SAAOjD;AACT;ACxdO,IAAMoE,sBAAsB;EACjCC,MAAM;EACNC,SAAS;EACTC,SAASC,OAAOC;AAClB;AAEA,IAAMC,kCAAkCA,OAA8B;EACpEC,aAAa;EACbC,WAAW;EACXC,aAAa;EACbC,iBAAiB;EACjBC,kBAAkB;EAClBC,mBAAmB,CAAA;AACrB;AAEO,IAAMC,eAA6B;EACxCC,qBAAqBA,MAA6B;AAChD,WAAOd;;EAETe,iBAAkBC,WAAkC;AAClD,WAAO;MACLC,cAAc,CAAA;MACdC,kBAAkBZ,gCAA+B;MACjD,GAAGU;;;EAIPG,mBACE/J,WAC+B;AAC/B,WAAO;MACLgK,kBAAkB;MAClBC,uBAAuB;MACvBC,sBAAsBzN,iBAAiB,gBAAgBuD,KAAK;MAC5DmK,0BAA0B1N,iBAAiB,oBAAoBuD,KAAK;;;EAIxED,cAAcA,CACZuB,QACAtB,UACS;AACTsB,WAAO8I,UAAU,MAAM;AAAA,UAAAC,uBAAAjK,MAAAkK;AACrB,YAAMC,aAAavK,MAAM0D,SAAQ,EAAGmG,aAAavI,OAAOZ,EAAE;AAE1D,aAAOvB,KAAKW,IACVX,KAAKU,KAAGwK,wBACN/I,OAAOrB,UAAU6I,YAAOuB,OAAAA,wBAAIzB,oBAAoBE,UAAO1I,OACvDmK,cAAAA,OAAAA,aAAcjJ,OAAOrB,UAAU4I,SAAI,OAAAzI,OAAIwI,oBAAoBC,IAC7D,IAACyB,wBACDhJ,OAAOrB,UAAU8I,YAAOuB,OAAAA,wBAAI1B,oBAAoBG,OAClD;;AAEFzH,WAAOkJ,WAAWC,cAAY;AAC5B,YAAMlJ,UAAU,CAACkJ,WACbzK,MAAMyD,sBAAqB,IAC3BgH,aAAa,SACXzK,MAAM0K,0BAAyB,IAC/B1K,MAAM2K,2BAA0B;AAEtC,YAAM5L,QAAQwC,QAAQqJ,UAAU7N,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AAEvD,UAAI3B,QAAQ,GAAG;AACb,cAAM8L,oBAAoBtJ,QAAQxC,QAAQ,CAAC;AAE3C,eACE8L,kBAAkBL,SAASC,QAAQ,IAAII,kBAAkBT,QAAO;MAEpE;AAEA,aAAO;;AAET9I,WAAOwJ,YAAY,MAAM;AACvB9K,YAAM+K,gBAAgBC,WAAiC;AAAA,YAAhC;UAAE,CAAC1J,OAAOZ,EAAE,GAAGuK;UAAG,GAAGC;QAAK,IAACF;AAChD,eAAOE;MACT,CAAC;;AAEH5J,WAAO6J,eAAe,MAAM;AAAA,UAAAC,uBAAAC;AAC1B,eACED,wBAAC9J,OAAOrB,UAAUqL,mBAAcF,OAAAA,wBAAI,WAAIC,wBACvCrL,MAAM4B,QAAQ2J,yBAAoBF,OAAAA,wBAAI;;AAG3C/J,WAAOkK,gBAAgB,MAAM;AAC3B,aAAOxL,MAAM0D,SAAQ,EAAGoG,iBAAiBP,qBAAqBjI,OAAOZ;;;EAIzE6B,cAAcA,CACZ1B,QACAb,UACS;AACTa,WAAOuJ,UAAU,MAAM;AACrB,UAAIqB,OAAM;AAEV,YAAM/N,UAAWmD,CAAAA,YAAkC;AACjD,YAAIA,QAAO8B,WAAW3E,QAAQ;AAC5B6C,UAAAA,QAAO8B,WAAW/E,QAAQF,OAAO;QACnC,OAAO;AAAA,cAAAgO;AACLD,UAAAA,SAAGC,wBAAI7K,QAAOS,OAAO8I,QAAO,MAAEsB,OAAAA,wBAAI;QACpC;;AAGFhO,cAAQmD,MAAM;AAEd,aAAO4K;;AAET5K,WAAO2J,WAAW,MAAM;AACtB,UAAI3J,OAAO9B,QAAQ,GAAG;AACpB,cAAM4M,oBAAoB9K,OAAOiC,YAAY+C,QAAQhF,OAAO9B,QAAQ,CAAC;AACrE,eAAO4M,kBAAkBnB,SAAQ,IAAKmB,kBAAkBvB,QAAO;MACjE;AAEA,aAAO;;AAETvJ,WAAO+K,mBAAmBC,sBAAoB;AAC5C,YAAMvK,SAAStB,MAAM8L,UAAUjL,OAAOS,OAAOZ,EAAE;AAC/C,YAAMqL,YAAYzK,UAAM,OAAA,SAANA,OAAQ6J,aAAY;AAEtC,aAAQa,OAAe;AACrB,YAAI,CAAC1K,UAAU,CAACyK,WAAW;AACzB;QACF;AAEEC,UAAUC,WAAO,QAAjBD,EAAUC,QAAO;AAEnB,YAAIC,kBAAkBF,CAAC,GAAG;AAExB,cAAIA,EAAEG,WAAWH,EAAEG,QAAQnO,SAAS,GAAG;AACrC;UACF;QACF;AAEA,cAAMoL,YAAYvI,OAAOuJ,QAAO;AAEhC,cAAMZ,oBAAwC3I,SAC1CA,OAAOkC,eAAc,EAAGI,IAAIpG,OAAK,CAACA,EAAEuE,OAAOZ,IAAI3D,EAAEuE,OAAO8I,QAAO,CAAE,CAAC,IAClE,CAAC,CAAC9I,OAAOZ,IAAIY,OAAO8I,QAAO,CAAE,CAAC;AAElC,cAAMgC,UAAUF,kBAAkBF,CAAC,IAC/B7M,KAAKC,MAAM4M,EAAEG,QAAQ,CAAC,EAAGC,OAAO,IAC/BJ,EAAiBI;AAEtB,cAAMC,kBAAqC,CAAA;AAE3C,cAAMC,eAAeA,CACnBC,WACAC,eACG;AACH,cAAI,OAAOA,eAAe,UAAU;AAClC;UACF;AAEAxM,gBAAMyM,oBAAoB5P,SAAO;AAAA,gBAAA6P,kBAAAC;AAC/B,kBAAMC,iBACJ5M,MAAM4B,QAAQqI,0BAA0B,QAAQ,KAAK;AACvD,kBAAMZ,eACHmD,eAAUE,mBAAI7P,OAAG,OAAA,SAAHA,IAAKsM,gBAAWuD,OAAAA,mBAAI,MAAME;AAC3C,kBAAMtD,kBAAkBnK,KAAKU,IAC3BwJ,gBAAWsD,iBAAI9P,OAAG,OAAA,SAAHA,IAAKuM,cAAS,OAAAuD,iBAAI,IACjC,SACF;AAEA9P,gBAAI2M,kBAAkB5L,QAAQiP,WAA4B;AAAA,kBAA3B,CAAC3I,UAAU4I,UAAU,IAACD;AACnDR,8BAAgBnI,QAAQ,IACtB/E,KAAKC,MACHD,KAAKU,IAAIiN,aAAaA,aAAaxD,iBAAiB,CAAC,IAAI,GAC3D,IAAI;YACR,CAAC;AAED,mBAAO;cACL,GAAGzM;cACHwM;cACAC;;UAEJ,CAAC;AAED,cACEtJ,MAAM4B,QAAQoI,qBAAqB,cACnCuC,cAAc,OACd;AACAvM,kBAAM+K,gBAAgBlO,UAAQ;cAC5B,GAAGA;cACH,GAAGwP;YACL,EAAE;UACJ;;AAGF,cAAMU,SAAUP,gBAAwBF,aAAa,QAAQE,UAAU;AAEvE,cAAMQ,QAASR,gBAAwB;AACrCF,uBAAa,OAAOE,UAAU;AAE9BxM,gBAAMyM,oBAAoB5P,UAAQ;YAChC,GAAGA;YACH0M,kBAAkB;YAClBJ,aAAa;YACbC,WAAW;YACXC,aAAa;YACbC,iBAAiB;YACjBE,mBAAmB,CAAA;UACrB,EAAE;;AAGJ,cAAMyD,kBACJpB,oBAAoB,OAAOqB,aAAa,cAAcA,WAAW;AAEnE,cAAMC,cAAc;UAClBC,aAAcpB,CAAAA,OAAkBe,OAAOf,GAAEI,OAAO;UAChDiB,WAAYrB,CAAAA,OAAkB;AAC5BiB,+BAAe,QAAfA,gBAAiBK,oBACf,aACAH,YAAYC,WACd;AACAH,+BAAe,QAAfA,gBAAiBK,oBACf,WACAH,YAAYE,SACd;AACAL,kBAAMhB,GAAEI,OAAO;UACjB;;AAGF,cAAMmB,cAAc;UAClBH,aAAcpB,CAAAA,OAAkB;AAC9B,gBAAIA,GAAEwB,YAAY;AAChBxB,cAAAA,GAAEyB,eAAc;AAChBzB,cAAAA,GAAE0B,gBAAe;YACnB;AACAX,mBAAOf,GAAEG,QAAQ,CAAC,EAAGC,OAAO;AAC5B,mBAAO;;UAETiB,WAAYrB,CAAAA,OAAkB;AAAA,gBAAA2B;AAC5BV,+BAAe,QAAfA,gBAAiBK,oBACf,aACAC,YAAYH,WACd;AACAH,+BAAe,QAAfA,gBAAiBK,oBACf,YACAC,YAAYF,SACd;AACA,gBAAIrB,GAAEwB,YAAY;AAChBxB,cAAAA,GAAEyB,eAAc;AAChBzB,cAAAA,GAAE0B,gBAAe;YACnB;AACAV,mBAAKW,cAAC3B,GAAEG,QAAQ,CAAC,MAAC,OAAA,SAAZwB,YAAcvB,OAAO;UAC7B;;AAGF,cAAMwB,qBAAqBC,sBAAqB,IAC5C;UAAEC,SAAS;QAAM,IACjB;AAEJ,YAAI5B,kBAAkBF,CAAC,GAAG;AACxBiB,6BAAAA,QAAAA,gBAAiBc,iBACf,aACAR,YAAYH,aACZQ,kBACF;AACAX,6BAAAA,QAAAA,gBAAiBc,iBACf,YACAR,YAAYF,WACZO,kBACF;QACF,OAAO;AACLX,6BAAAA,QAAAA,gBAAiBc,iBACf,aACAZ,YAAYC,aACZQ,kBACF;AACAX,6BAAAA,QAAAA,gBAAiBc,iBACf,WACAZ,YAAYE,WACZO,kBACF;QACF;AAEA5N,cAAMyM,oBAAoB5P,UAAQ;UAChC,GAAGA;UACHsM,aAAaiD;UACbhD;UACAC,aAAa;UACbC,iBAAiB;UACjBE;UACAD,kBAAkBjI,OAAOZ;QAC3B,EAAE;;;;EAKR4C,aAAqCtD,WAA8B;AACjEA,UAAM+K,kBAAkBzO,aACtB0D,MAAM4B,QAAQsI,wBAAoB,OAAA,SAAlClK,MAAM4B,QAAQsI,qBAAuB5N,OAAO;AAC9C0D,UAAMyM,sBAAsBnQ,aAC1B0D,MAAM4B,QAAQuI,4BAAwB,OAAA,SAAtCnK,MAAM4B,QAAQuI,yBAA2B7N,OAAO;AAClD0D,UAAMgO,oBAAoBC,kBAAgB;AAAA,UAAAC;AACxClO,YAAM+K,gBACJkD,eAAe,CAAA,KAAEC,wBAAGlO,MAAMmO,aAAatE,iBAAY,OAAAqE,wBAAI,CAAA,CACzD;;AAEFlO,UAAMoO,sBAAsBH,kBAAgB;AAAA,UAAAI;AAC1CrO,YAAMyM,oBACJwB,eACI/E,gCAA+B,KAAEmF,yBACjCrO,MAAMmO,aAAarE,qBAAgB,OAAAuE,yBACjCnF,gCAA+B,CACvC;;AAEFlJ,UAAMsO,eAAe,MAAA;AAAA,UAAAC,uBAAAC;AAAA,cAAAD,yBAAAC,yBACnBxO,MAAMuD,gBAAe,EAAG,CAAC,MAAzBiL,OAAAA,SAAAA,uBAA4B3I,QAAQ4I,OAAO,CAAChD,MAAK5K,WAAW;AAC1D,eAAO4K,OAAM5K,OAAOuJ,QAAO;MAC7B,GAAG,CAAC,MAACmE,OAAAA,wBAAI;IAAC;AACZvO,UAAM0O,mBAAmB,MAAA;AAAA,UAAAC,uBAAAC;AAAA,cAAAD,yBAAAC,yBACvB5O,MAAM4E,oBAAmB,EAAG,CAAC,MAA7BgK,OAAAA,SAAAA,uBAAgC/I,QAAQ4I,OAAO,CAAChD,MAAK5K,WAAW;AAC9D,eAAO4K,OAAM5K,OAAOuJ,QAAO;MAC7B,GAAG,CAAC,MAACuE,OAAAA,wBAAI;IAAC;AACZ3O,UAAM6O,qBAAqB,MAAA;AAAA,UAAAC,uBAAAC;AAAA,cAAAD,yBAAAC,yBACzB/O,MAAM2E,sBAAqB,EAAG,CAAC,MAA/BoK,OAAAA,SAAAA,uBAAkClJ,QAAQ4I,OAAO,CAAChD,MAAK5K,WAAW;AAChE,eAAO4K,OAAM5K,OAAOuJ,QAAO;MAC7B,GAAG,CAAC,MAAC0E,OAAAA,wBAAI;IAAC;AACZ9O,UAAMgP,oBAAoB,MAAA;AAAA,UAAAC,uBAAAC;AAAA,cAAAD,yBAAAC,yBACxBlP,MAAMgF,qBAAoB,EAAG,CAAC,MAA9BkK,OAAAA,SAAAA,uBAAiCrJ,QAAQ4I,OAAO,CAAChD,MAAK5K,WAAW;AAC/D,eAAO4K,OAAM5K,OAAOuJ,QAAO;MAC7B,GAAG,CAAC,MAAC6E,OAAAA,wBAAI;IAAC;EACd;AACF;AAEA,IAAIE,mBAAmC;AAChC,SAAStB,wBAAwB;AACtC,MAAI,OAAOsB,qBAAqB;AAAW,WAAOA;AAElD,MAAIC,YAAY;AAChB,MAAI;AACF,UAAMxN,UAAU;MACd,IAAIkM,UAAU;AACZsB,oBAAY;AACZ,eAAO;MACT;;AAGF,UAAM5S,QAAOA,MAAM;IAAA;AAEnB6S,WAAOtB,iBAAiB,QAAQvR,OAAMoF,OAAO;AAC7CyN,WAAO/B,oBAAoB,QAAQ9Q,KAAI;WAChC8S,KAAK;AACZF,gBAAY;EACd;AACAD,qBAAmBC;AACnB,SAAOD;AACT;AAEA,SAASjD,kBAAkBF,GAA6B;AACtD,SAAQA,EAAiBuD,SAAS;AACpC;AC5YO,IAAMC,YAA0B;EACrC7F,iBAAkBC,WAA8B;AAC9C,WAAO;MACL6F,UAAU,CAAA;MACV,GAAG7F;;;EAIPG,mBACE/J,WAC2B;AAC3B,WAAO;MACL0P,kBAAkBjT,iBAAiB,YAAYuD,KAAK;MACpD2P,sBAAsB;;;EAI1BrM,aAAqCtD,WAA8B;AACjE,QAAI4P,aAAa;AACjB,QAAIC,SAAS;AAEb7P,UAAM8P,qBAAqB,MAAM;AAAA,UAAA1P,MAAA2P;AAC/B,UAAI,CAACH,YAAY;AACf5P,cAAMgQ,OAAO,MAAM;AACjBJ,uBAAa;QACf,CAAC;AACD;MACF;AAEA,WAAAxP,QAAA2P,wBACE/P,MAAM4B,QAAQqO,iBAAYF,OAAAA,wBAC1B/P,MAAM4B,QAAQsO,sBAAiB,OAAA9P,OAC/B,CAACJ,MAAM4B,QAAQuO,iBACf;AACA,YAAIN;AAAQ;AACZA,iBAAS;AACT7P,cAAMgQ,OAAO,MAAM;AACjBhQ,gBAAMoQ,cAAa;AACnBP,mBAAS;QACX,CAAC;MACH;;AAEF7P,UAAMqQ,cAAc/T,aAAW0D,MAAM4B,QAAQ8N,oBAAgB,OAAA,SAA9B1P,MAAM4B,QAAQ8N,iBAAmBpT,OAAO;AACvE0D,UAAMsQ,wBAAwBb,cAAY;AACxC,UAAIA,YAAAA,OAAAA,WAAY,CAACzP,MAAMuQ,qBAAoB,GAAI;AAC7CvQ,cAAMqQ,YAAY,IAAI;MACxB,OAAO;AACLrQ,cAAMqQ,YAAY,CAAA,CAAE;MACtB;;AAEFrQ,UAAMoQ,gBAAgBnC,kBAAgB;AAAA,UAAAuC,uBAAAC;AACpCzQ,YAAMqQ,YAAYpC,eAAe,CAAA,KAAEuC,yBAAAC,sBAAGzQ,MAAMmO,iBAANsC,OAAAA,SAAAA,oBAAoBhB,aAAQ,OAAAe,wBAAI,CAAA,CAAE;;AAE1ExQ,UAAM0Q,uBAAuB,MAAM;AACjC,aAAO1Q,MACJ2Q,yBAAwB,EACxBC,SAAS/R,KAAKgS,SAAOA,IAAIC,aAAY,CAAE;;AAE5C9Q,UAAM+Q,kCAAkC,MAAM;AAC5C,aAAQ/E,OAAe;AACnBA,UAAUC,WAAO,QAAjBD,EAAUC,QAAO;AACnBjM,cAAMsQ,sBAAqB;;;AAG/BtQ,UAAMgR,wBAAwB,MAAM;AAClC,YAAMvB,WAAWzP,MAAM0D,SAAQ,EAAG+L;AAClC,aAAOA,aAAa,QAAQwB,OAAOC,OAAOzB,QAAQ,EAAE5Q,KAAKwF,OAAO;;AAElErE,UAAMuQ,uBAAuB,MAAM;AACjC,YAAMd,WAAWzP,MAAM0D,SAAQ,EAAG+L;AAGlC,UAAI,OAAOA,aAAa,WAAW;AACjC,eAAOA,aAAa;MACtB;AAEA,UAAI,CAACwB,OAAOE,KAAK1B,QAAQ,EAAEzR,QAAQ;AACjC,eAAO;MACT;AAGA,UAAIgC,MAAMoR,YAAW,EAAGR,SAAS/R,KAAKgS,SAAO,CAACA,IAAIQ,cAAa,CAAE,GAAG;AAClE,eAAO;MACT;AAGA,aAAO;;AAETrR,UAAMsR,mBAAmB,MAAM;AAC7B,UAAI3J,WAAW;AAEf,YAAM4J,SACJvR,MAAM0D,SAAQ,EAAG+L,aAAa,OAC1BwB,OAAOE,KAAKnR,MAAMoR,YAAW,EAAGI,QAAQ,IACxCP,OAAOE,KAAKnR,MAAM0D,SAAQ,EAAG+L,QAAQ;AAE3C8B,aAAO3T,QAAQ8C,QAAM;AACnB,cAAM+Q,UAAU/Q,GAAGO,MAAM,GAAG;AAC5B0G,mBAAWxI,KAAKU,IAAI8H,UAAU8J,QAAQzT,MAAM;MAC9C,CAAC;AAED,aAAO2J;;AAET3H,UAAM0R,yBAAyB,MAAM1R,MAAM2R,kBAAiB;AAC5D3R,UAAM4R,sBAAsB,MAAM;AAChC,UAAI,CAAC5R,MAAM6R,wBAAwB7R,MAAM4B,QAAQgQ,qBAAqB;AACpE5R,cAAM6R,uBAAuB7R,MAAM4B,QAAQgQ,oBAAoB5R,KAAK;MACtE;AAEA,UAAIA,MAAM4B,QAAQuO,mBAAmB,CAACnQ,MAAM6R,sBAAsB;AAChE,eAAO7R,MAAM0R,uBAAsB;MACrC;AAEA,aAAO1R,MAAM6R,qBAAoB;;;EAIrCC,WAAWA,CACTjB,KACA7Q,UACS;AACT6Q,QAAIkB,iBAAiBtC,cAAY;AAC/BzP,YAAMqQ,YAAYxT,SAAO;AAAA,YAAAmV;AACvB,cAAMC,SAASpV,QAAQ,OAAO,OAAO,CAAC,EAACA,OAAAA,QAAAA,IAAMgU,IAAInQ,EAAE;AAEnD,YAAIwR,cAAiC,CAAA;AAErC,YAAIrV,QAAQ,MAAM;AAChBoU,iBAAOE,KAAKnR,MAAMoR,YAAW,EAAGI,QAAQ,EAAE5T,QAAQuU,WAAS;AACzDD,wBAAYC,KAAK,IAAI;UACvB,CAAC;QACH,OAAO;AACLD,wBAAcrV;QAChB;AAEA4S,oBAAQuC,YAAGvC,aAAQ,OAAAuC,YAAI,CAACC;AAExB,YAAI,CAACA,UAAUxC,UAAU;AACvB,iBAAO;YACL,GAAGyC;YACH,CAACrB,IAAInQ,EAAE,GAAG;;QAEd;AAEA,YAAIuR,UAAU,CAACxC,UAAU;AACvB,gBAAM;YAAE,CAACoB,IAAInQ,EAAE,GAAGuK;YAAG,GAAGC;UAAK,IAAIgH;AACjC,iBAAOhH;QACT;AAEA,eAAOrO;MACT,CAAC;;AAEHgU,QAAIQ,gBAAgB,MAAM;AAAA,UAAAe;AACxB,YAAM3C,WAAWzP,MAAM0D,SAAQ,EAAG+L;AAElC,aAAO,CAAC,GAAA2C,wBACNpS,MAAM4B,QAAQyQ,oBAAdrS,OAAAA,SAAAA,MAAM4B,QAAQyQ,iBAAmBxB,GAAG,MAACuB,OAAAA,wBACpC3C,aAAa,SAAQA,YAAQ,OAAA,SAARA,SAAWoB,IAAInQ,EAAE;;AAG3CmQ,QAAIC,eAAe,MAAM;AAAA,UAAAwB,uBAAAjH,uBAAAkH;AACvB,cAAAD,wBACEtS,MAAM4B,QAAQ4Q,mBAAdxS,OAAAA,SAAAA,MAAM4B,QAAQ4Q,gBAAkB3B,GAAG,MAACyB,OAAAA,0BACnCjH,wBAACrL,MAAM4B,QAAQ6Q,oBAAepH,OAAAA,wBAAI,SAAS,CAAC,GAAAkH,eAAC1B,IAAI6B,YAAJH,QAAAA,aAAavU;;AAG/D6S,QAAI8B,0BAA0B,MAAM;AAClC,UAAIC,kBAAkB;AACtB,UAAIC,aAAahC;AAEjB,aAAO+B,mBAAmBC,WAAWC,UAAU;AAC7CD,qBAAa7S,MAAM+S,OAAOF,WAAWC,UAAU,IAAI;AACnDF,0BAAkBC,WAAWxB,cAAa;MAC5C;AAEA,aAAOuB;;AAET/B,QAAImC,2BAA2B,MAAM;AACnC,YAAMC,YAAYpC,IAAIC,aAAY;AAElC,aAAO,MAAM;AACX,YAAI,CAACmC;AAAW;AAChBpC,YAAIkB,eAAc;;;EAGxB;AACF;AC1VA,IAAMmB,iBAAgCA,CACpCrC,KACA3M,UACAiP,gBACG;AAAA,MAAAC;AACH,QAAMC,SAASF,YAAYG,YAAW;AACtC,SAAOjP,SAAO+O,gBACZvC,IACG0C,SAAwBrP,QAAQ,MAAC,SAAAkP,gBADpCA,cAEII,SAAQ,MAAEJ,SAAAA,gBAFdA,cAGIE,YAAW,MAAE,OAAA,SAHjBF,cAIIrS,SAASsS,MAAM,CACrB;AACF;AAEAH,eAAeO,aAAcpW,SAAaqW,WAAWrW,GAAG;AAExD,IAAMsW,0BAAyCA,CAC7C9C,KACA3M,UACAiP,gBACG;AAAA,MAAAS;AACH,SAAOvP,SAAOuP,iBACZ/C,IAAI0C,SAAwBrP,QAAQ,MAAC0P,SAAAA,iBAArCA,eAAuCJ,SAAQ,MAAE,OAAA,SAAjDI,eAAmD7S,SAASoS,WAAW,CACzE;AACF;AAEAQ,wBAAwBF,aAAcpW,SAAaqW,WAAWrW,GAAG;AAEjE,IAAMwW,eAA8BA,CAClChD,KACA3M,UACAiP,gBACG;AAAA,MAAAW;AACH,WACEA,iBAAAjD,IAAI0C,SAAwBrP,QAAQ,MAAC,SAAA4P,iBAArCA,eAAuCN,SAAQ,MAA/CM,OAAAA,SAAAA,eAAmDR,YAAW,QAC9DH,eAAAA,OAAAA,SAAAA,YAAaG,YAAW;AAE5B;AAEAO,aAAaJ,aAAcpW,SAAaqW,WAAWrW,GAAG;AAEtD,IAAM0W,cAA6BA,CACjClD,KACA3M,UACAiP,gBACG;AAAA,MAAAa;AACH,UAAAA,iBAAOnD,IAAI0C,SAAoBrP,QAAQ,MAAC,OAAA,SAAjC8P,eAAmCjT,SAASoS,WAAW;AAChE;AAEAY,YAAYN,aAAcpW,SAAaqW,WAAWrW,GAAG,KAAK,EAACA,OAAAA,QAAAA,IAAKW;AAEhE,IAAMiW,iBAAgCA,CACpCpD,KACA3M,UACAiP,gBACG;AACH,SAAO,CAACA,YAAYtU,KAClBxB,SAAG;AAAA,QAAA6W;AAAA,WAAI,GAAAA,iBAACrD,IAAI0C,SAAoBrP,QAAQ,MAAC,QAAjCgQ,eAAmCnT,SAAS1D,GAAG;EAAC,CAC1D;AACF;AAEA4W,eAAeR,aAAcpW,SAAaqW,WAAWrW,GAAG,KAAK,EAACA,OAAAA,QAAAA,IAAKW;AAEnE,IAAMmW,kBAAiCA,CACrCtD,KACA3M,UACAiP,gBACG;AACH,SAAOA,YAAYtU,KACjBxB,SAAG;AAAA,QAAA+W;AAAA,YAAAA,iBAAIvD,IAAI0C,SAAoBrP,QAAQ,MAAC,OAAA,SAAjCkQ,eAAmCrT,SAAS1D,GAAG;EAAC,CACzD;AACF;AAEA8W,gBAAgBV,aAAcpW,SAAaqW,WAAWrW,GAAG,KAAK,EAACA,OAAAA,QAAAA,IAAKW;AAEpE,IAAMqW,SAAwBA,CAACxD,KAAK3M,UAAkBiP,gBAAyB;AAC7E,SAAOtC,IAAI0C,SAASrP,QAAQ,MAAMiP;AACpC;AAEAkB,OAAOZ,aAAcpW,SAAaqW,WAAWrW,GAAG;AAEhD,IAAMiX,aAA4BA,CAChCzD,KACA3M,UACAiP,gBACG;AACH,SAAOtC,IAAI0C,SAASrP,QAAQ,KAAKiP;AACnC;AAEAmB,WAAWb,aAAcpW,SAAaqW,WAAWrW,GAAG;AAEpD,IAAMkX,gBAA+BA,CACnC1D,KACA3M,UACAiP,gBACG;AACH,MAAI,CAACrT,MAAKD,IAAG,IAAIsT;AAEjB,QAAMqB,WAAW3D,IAAI0C,SAAiBrP,QAAQ;AAC9C,SAAOsQ,YAAY1U,QAAO0U,YAAY3U;AACxC;AAEA0U,cAAcE,qBAAsBpX,SAAoB;AACtD,MAAI,CAACqX,WAAWC,SAAS,IAAItX;AAE7B,MAAIuX,YACF,OAAOF,cAAc,WAAWG,WAAWH,SAAmB,IAAIA;AACpE,MAAII,YACF,OAAOH,cAAc,WAAWE,WAAWF,SAAmB,IAAIA;AAEpE,MAAI7U,OACF4U,cAAc,QAAQ1L,OAAO+L,MAAMH,SAAS,IAAI,YAAYA;AAC9D,MAAI/U,OAAM8U,cAAc,QAAQ3L,OAAO+L,MAAMD,SAAS,IAAIE,WAAWF;AAErE,MAAIhV,OAAMD,MAAK;AACb,UAAMoV,OAAOnV;AACbA,IAAAA,OAAMD;AACNA,IAAAA,OAAMoV;EACR;AAEA,SAAO,CAACnV,MAAKD,IAAG;AAClB;AAEA0U,cAAcd,aAAcpW,SAC1BqW,WAAWrW,GAAG,KAAMqW,WAAWrW,IAAI,CAAC,CAAC,KAAKqW,WAAWrW,IAAI,CAAC,CAAC;AAItD,IAAM6X,YAAY;EACvBhC;EACAS;EACAE;EACAE;EACAE;EACAE;EACAE;EACAC;EACAC;AACF;AAMA,SAASb,WAAWrW,KAAU;AAC5B,SAAOA,QAAQuD,UAAavD,QAAQ,QAAQA,QAAQ;AACtD;ACuNO,IAAM8X,UAAwB;EACnCzL,qBAAqBA,MAAsD;AACzE,WAAO;MACL0L,UAAU;;;EAIdzL,iBAAkBC,WAA6B;AAC7C,WAAO;MACLyL,eAAe,CAAA;MACfC,cAAc1U;;;MAGd,GAAGgJ;;;EAIPG,mBACE/J,WAC0B;AAC1B,WAAO;MACLuV,uBAAuB9Y,iBAAiB,iBAAiBuD,KAAK;MAC9DwV,sBAAsB/Y,iBAAiB,gBAAgBuD,KAAK;MAC5DyV,oBAAoB;MACpBC,uBAAuB;MACvBC,gBAAgB;MAChBC,0BAA0BtU,YAAU;AAAA,YAAAuU;AAClC,cAAMC,SAAKD,wBAAG7V,MACX+V,gBAAe,EACfnF,SAAS,CAAC,MAACiF,SAAAA,wBAFAA,sBAEEG,uBAAsB,EACnC1U,OAAOZ,EAAE,MAHEmV,OAAAA,SAAAA,sBAGCtC,SAAQ;AAEvB,eAAO,OAAOuC,UAAU,YAAY,OAAOA,UAAU;MACvD;;;EAIJ/V,cAAcA,CACZuB,QACAtB,UACS;AACTsB,WAAO2U,kBAAkB,MAAM;AAC7B,YAAMC,WAAWlW,MAAM+V,gBAAe,EAAGnF,SAAS,CAAC;AAEnD,YAAMkF,QAAQI,YAAAA,OAAAA,SAAAA,SAAU3C,SAASjS,OAAOZ,EAAE;AAE1C,UAAI,OAAOoV,UAAU,UAAU;AAC7B,eAAOZ,UAAUhC;MACnB;AAEA,UAAI,OAAO4C,UAAU,UAAU;AAC7B,eAAOZ,UAAUX;MACnB;AAEA,UAAI,OAAOuB,UAAU,WAAW;AAC9B,eAAOZ,UAAUb;MACnB;AAEA,UAAIyB,UAAU,QAAQ,OAAOA,UAAU,UAAU;AAC/C,eAAOZ,UAAUb;MACnB;AAEA,UAAInX,MAAMC,QAAQ2Y,KAAK,GAAG;AACxB,eAAOZ,UAAUnB;MACnB;AAEA,aAAOmB,UAAUZ;;AAEnBhT,WAAO6U,cAAc,MAAM;AAAA,UAAAC,uBAAAC;AACzB,aAAOvZ,WAAWwE,OAAOrB,UAAUmV,QAAQ,IACvC9T,OAAOrB,UAAUmV,WACjB9T,OAAOrB,UAAUmV,aAAa,SAC5B9T,OAAO2U,gBAAe;;SACtBG,yBAAAC,yBACArW,MAAM4B,QAAQsT,cAAS,OAAA,SAAvBmB,uBAA0B/U,OAAOrB,UAAUmV,QAAQ,MAAWgB,OAAAA,wBAC9DlB,UAAU5T,OAAOrB,UAAUmV,QAAQ;;;AAE3C9T,WAAOgV,eAAe,MAAM;AAAA,UAAAlL,uBAAAC,uBAAAkL;AAC1B,eACEnL,wBAAC9J,OAAOrB,UAAUuW,uBAAkB,OAAApL,wBAAI,WAAIC,wBAC3CrL,MAAM4B,QAAQ6U,wBAAmB,OAAApL,wBAAI,WAAKkL,yBAC1CvW,MAAM4B,QAAQ8U,kBAAa,OAAAH,yBAAI,SAChC,CAAC,CAACjV,OAAOR;;AAIbQ,WAAOqV,qBAAqB,MAAM;AAAA,UAAAC,wBAAAC,wBAAAC,wBAAAC;AAChC,eACEH,yBAACtV,OAAOrB,UAAU+W,uBAAkBJ,OAAAA,yBAAI,WAAIC,yBAC3C7W,MAAM4B,QAAQoV,uBAAkB,OAAAH,yBAAI,WAAKC,yBACzC9W,MAAM4B,QAAQ8U,kBAAa,OAAAI,yBAAI,WAAKC,wBACpC/W,MAAM4B,QAAQgU,4BAAwB,OAAA,SAAtC5V,MAAM4B,QAAQgU,yBAA2BtU,MAAM,MAAC,OAAAyV,wBAAI,SACrD,CAAC,CAACzV,OAAOR;;AAIbQ,WAAO2V,gBAAgB,MAAM3V,OAAO4V,eAAc,IAAK;AAEvD5V,WAAO6V,iBAAiB,MAAA;AAAA,UAAAC;AAAA,cAAAA,wBACtBpX,MAAM0D,SAAQ,EAAG2R,kBAAa,SAAA+B,wBAA9BA,sBAAgCjT,KAAKpH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,MAA5D0W,OAAAA,SAAAA,sBAA+DtB;IAAK;AAEtExU,WAAO4V,iBAAiB,MAAA;AAAA,UAAAG,wBAAAC;AAAA,cAAAD,0BAAAC,yBACtBtX,MAAM0D,SAAQ,EAAG2R,kBAAa,OAAA,SAA9BiC,uBAAgC1M,UAAU7N,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,MAAC,OAAA2W,yBAAI;IAAE;AAE1E/V,WAAOiW,iBAAiBzB,WAAS;AAC/B9V,YAAMwX,iBAAiB3a,SAAO;AAC5B,cAAMuY,WAAW9T,OAAO6U,YAAW;AACnC,cAAMsB,iBAAiB5a,OAAAA,OAAAA,SAAAA,IAAKsH,KAAKpH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AAExD,cAAMgX,YAAYrb,iBAChByZ,OACA2B,iBAAiBA,eAAe3B,QAAQlV,MAC1C;AAGA,YACE+W,uBAAuBvC,UAA6BsC,WAAWpW,MAAM,GACrE;AAAA,cAAAsW;AACA,kBAAAA,cAAO/a,OAAG,OAAA,SAAHA,IAAKuH,OAAOrH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,MAAC,OAAAkX,cAAI,CAAA;QACjD;AAEA,cAAMC,eAAe;UAAEnX,IAAIY,OAAOZ;UAAIoV,OAAO4B;;AAE7C,YAAID,gBAAgB;AAAA,cAAAK;AAClB,kBAAAA,WACEjb,OAAG,OAAA,SAAHA,IAAKsG,IAAIpG,OAAK;AACZ,gBAAIA,EAAE2D,OAAOY,OAAOZ,IAAI;AACtB,qBAAOmX;YACT;AACA,mBAAO9a;UACT,CAAC,MAAC,OAAA+a,WAAI,CAAA;QAEV;AAEA,YAAIjb,OAAG,QAAHA,IAAKmB,QAAQ;AACf,iBAAO,CAAC,GAAGnB,KAAKgb,YAAY;QAC9B;AAEA,eAAO,CAACA,YAAY;MACtB,CAAC;;AAEHvW,WAAOyW,sBACL/X,MAAM4B,QAAQoW,sBACdhY,MAAM4B,QAAQoW,mBAAmBhY,OAAOsB,OAAOZ,EAAE;AACnDY,WAAO0W,qBAAqB,MAAM;AAChC,UAAI,CAAC1W,OAAOyW,qBAAqB;AAC/B,eAAO/X,MAAMiY,uBAAsB;MACrC;AAEA,aAAO3W,OAAOyW,oBAAmB;;AAEnCzW,WAAO4W,0BACLlY,MAAM4B,QAAQuW,0BACdnY,MAAM4B,QAAQuW,uBAAuBnY,OAAOsB,OAAOZ,EAAE;AACvDY,WAAO6W,yBAAyB,MAAM;AACpC,UAAI,CAAC7W,OAAO4W,yBAAyB;AACnC,eAAO,oBAAIE,IAAG;MAChB;AAEA,aAAO9W,OAAO4W,wBAAuB;;AAEvC5W,WAAO+W,0BACLrY,MAAM4B,QAAQ0W,0BACdtY,MAAM4B,QAAQ0W,uBAAuBtY,OAAOsB,OAAOZ,EAAE;AACvDY,WAAOgX,yBAAyB,MAAM;AACpC,UAAI,CAAChX,OAAO+W,yBAAyB;AACnC,eAAOzX;MACT;AAEA,aAAOU,OAAO+W,wBAAuB;;;EAMzCvG,WAAWA,CACTjB,KACA7Q,UACS;AACT6Q,QAAIwE,gBAAgB,CAAA;AACpBxE,QAAI0H,oBAAoB,CAAA;;EAG1BjV,aAAqCtD,WAA8B;AACjEA,UAAMwY,wBAAwB,MAAM;AAClC,aAAOtD,UAAUhC;;AAGnBlT,UAAMyY,oBAAoB,MAAM;AAAA,UAAAC,wBAAAC;AAC9B,YAAM;QAAEhD;UAAmC3V,MAAM4B;AAEjD,aAAO9E,WAAW6Y,cAAc,IAC5BA,iBACAA,mBAAmB,SACjB3V,MAAMwY,sBAAqB;;SAC3BE,0BAAAC,yBACA3Y,MAAM4B,QAAQsT,cAAS,OAAA,SAAvByD,uBAA0BhD,cAAc,MAAW+C,OAAAA,yBACnDxD,UAAUS,cAAc;;;AAGhC3V,UAAMwX,mBAAoBlb,aAAyC;AACjE,YAAM6F,cAAcnC,MAAM4Y,kBAAiB;AAE3C,YAAMC,WAAYhc,SAA4B;AAAA,YAAAic;AAC5C,gBAAAA,oBAAOzc,iBAAiBC,SAASO,GAAG,MAAC,OAAA,SAA9Bic,kBAAgC1U,OAAOA,YAAU;AACtD,gBAAM9C,SAASa,YAAYgC,KAAKpH,OAAKA,EAAE2D,OAAO0D,OAAO1D,EAAE;AAEvD,cAAIY,QAAQ;AACV,kBAAM8T,WAAW9T,OAAO6U,YAAW;AAEnC,gBAAIwB,uBAAuBvC,UAAUhR,OAAO0R,OAAOxU,MAAM,GAAG;AAC1D,qBAAO;YACT;UACF;AAEA,iBAAO;QACT,CAAC;;AAGHtB,YAAM4B,QAAQ2T,yBAAdvV,QAAAA,MAAM4B,QAAQ2T,sBAAwBsD,QAAQ;;AAGhD7Y,UAAM+Y,kBAAkBzc,aAAW;AACjC0D,YAAM4B,QAAQ4T,wBAAdxV,QAAAA,MAAM4B,QAAQ4T,qBAAuBlZ,OAAO;;AAG9C0D,UAAMgZ,oBAAoB/K,kBAAgB;AACxCjO,YAAM+Y,gBACJ9K,eAAerN,SAAYZ,MAAMmO,aAAamH,YAChD;;AAGFtV,UAAMiZ,qBAAqBhL,kBAAgB;AAAA,UAAAC,uBAAAuC;AACzCzQ,YAAMwX,iBACJvJ,eAAe,CAAA,KAAEC,yBAAAuC,sBAAGzQ,MAAMmO,iBAAY,OAAA,SAAlBsC,oBAAoB4E,kBAAanH,OAAAA,wBAAI,CAAA,CAC3D;;AAGFlO,UAAMiY,yBAAyB,MAAMjY,MAAM+V,gBAAe;AAC1D/V,UAAMkZ,sBAAsB,MAAM;AAChC,UAAI,CAAClZ,MAAMmZ,wBAAwBnZ,MAAM4B,QAAQsX,qBAAqB;AACpElZ,cAAMmZ,uBAAuBnZ,MAAM4B,QAAQsX,oBAAoBlZ,KAAK;MACtE;AAEA,UAAIA,MAAM4B,QAAQwX,mBAAmB,CAACpZ,MAAMmZ,sBAAsB;AAChE,eAAOnZ,MAAMiY,uBAAsB;MACrC;AAEA,aAAOjY,MAAMmZ,qBAAoB;;AAGnCnZ,UAAMqZ,4BACJrZ,MAAM4B,QAAQoW,sBACdhY,MAAM4B,QAAQoW,mBAAmBhY,OAAO,YAAY;AAEtDA,UAAMsZ,2BAA2B,MAAM;AACrC,UAAItZ,MAAM4B,QAAQwX,mBAAmB,CAACpZ,MAAMqZ,2BAA2B;AACrE,eAAOrZ,MAAMiY,uBAAsB;MACrC;AAEA,aAAOjY,MAAMqZ,0BAAyB;;AAGxCrZ,UAAMuZ,gCACJvZ,MAAM4B,QAAQuW,0BACdnY,MAAM4B,QAAQuW,uBAAuBnY,OAAO,YAAY;AAC1DA,UAAMwZ,+BAA+B,MAAM;AACzC,UAAI,CAACxZ,MAAMuZ,+BAA+B;AACxC,eAAO,oBAAInB,IAAG;MAChB;AAEA,aAAOpY,MAAMuZ,8BAA6B;;AAG5CvZ,UAAMyZ,gCACJzZ,MAAM4B,QAAQ0W,0BACdtY,MAAM4B,QAAQ0W,uBAAuBtY,OAAO,YAAY;AAC1DA,UAAM0Z,+BAA+B,MAAM;AACzC,UAAI,CAAC1Z,MAAMyZ,+BAA+B;AACxC;MACF;AAEA,aAAOzZ,MAAMyZ,8BAA6B;;EAE9C;AACF;AAEO,SAAS9B,uBACdvC,UACAU,OACAxU,QACA;AACA,UACG8T,YAAYA,SAAS3B,aAClB2B,SAAS3B,WAAWqC,OAAOxU,MAAM,IACjC,UACJ,OAAOwU,UAAU,eAChB,OAAOA,UAAU,YAAY,CAACA;AAEnC;ACppBA,IAAMrK,MAA0BA,CAACvH,UAAUyV,WAAWC,cAAc;AAGlE,SAAOA,UAAUnL,OAAO,CAAChD,MAAKoO,SAAS;AACrC,UAAMC,YAAYD,KAAKtG,SAASrP,QAAQ;AACxC,WAAOuH,QAAO,OAAOqO,cAAc,WAAWA,YAAY;KACzD,CAAC;AACN;AAEA,IAAMha,MAA0BA,CAACoE,UAAUyV,WAAWC,cAAc;AAClE,MAAI9Z;AAEJ8Z,YAAUhc,QAAQiT,SAAO;AACvB,UAAMiF,QAAQjF,IAAI0C,SAAiBrP,QAAQ;AAE3C,QACE4R,SAAS,SACRhW,OAAOgW,SAAUhW,SAAQc,UAAakV,SAASA,QAChD;AACAhW,MAAAA,OAAMgW;IACR;EACF,CAAC;AAED,SAAOhW;AACT;AAEA,IAAMD,MAA0BA,CAACqE,UAAUyV,WAAWC,cAAc;AAClE,MAAI/Z;AAEJ+Z,YAAUhc,QAAQiT,SAAO;AACvB,UAAMiF,QAAQjF,IAAI0C,SAAiBrP,QAAQ;AAC3C,QACE4R,SAAS,SACRjW,OAAOiW,SAAUjW,SAAQe,UAAakV,SAASA,QAChD;AACAjW,MAAAA,OAAMiW;IACR;EACF,CAAC;AAED,SAAOjW;AACT;AAEA,IAAMka,SAA6BA,CAAC7V,UAAUyV,WAAWC,cAAc;AACrE,MAAI9Z;AACJ,MAAID;AAEJ+Z,YAAUhc,QAAQiT,SAAO;AACvB,UAAMiF,QAAQjF,IAAI0C,SAAiBrP,QAAQ;AAC3C,QAAI4R,SAAS,MAAM;AACjB,UAAIhW,SAAQc,QAAW;AACrB,YAAIkV,SAASA;AAAOhW,UAAAA,OAAMD,OAAMiW;MAClC,OAAO;AACL,YAAIhW,OAAMgW;AAAOhW,UAAAA,OAAMgW;AACvB,YAAIjW,OAAOiW;AAAOjW,UAAAA,OAAMiW;MAC1B;IACF;EACF,CAAC;AAED,SAAO,CAAChW,MAAKD,IAAG;AAClB;AAEA,IAAMma,OAA2BA,CAAC9V,UAAU+V,aAAa;AACvD,MAAIC,SAAQ;AACZ,MAAIzO,OAAM;AAEVwO,WAASrc,QAAQiT,SAAO;AACtB,QAAIiF,QAAQjF,IAAI0C,SAAiBrP,QAAQ;AACzC,QAAI4R,SAAS,SAASA,QAAQ,CAACA,UAAUA,OAAO;AAC9C,QAAEoE,QAAQzO,QAAOqK;IACnB;EACF,CAAC;AAED,MAAIoE;AAAO,WAAOzO,OAAMyO;AAExB;AACF;AAEA,IAAMC,SAA6BA,CAACjW,UAAU+V,aAAa;AACzD,MAAI,CAACA,SAASjc,QAAQ;AACpB;EACF;AAEA,QAAMkT,SAAS+I,SAAS9W,IAAI0N,SAAOA,IAAI0C,SAASrP,QAAQ,CAAC;AACzD,MAAI,CAACjH,cAAciU,MAAM,GAAG;AAC1B;EACF;AACA,MAAIA,OAAOlT,WAAW,GAAG;AACvB,WAAOkT,OAAO,CAAC;EACjB;AAEA,QAAMkJ,MAAMjb,KAAKkb,MAAMnJ,OAAOlT,SAAS,CAAC;AACxC,QAAMsc,OAAOpJ,OAAOqJ,KAAK,CAACC,GAAGC,MAAMD,IAAIC,CAAC;AACxC,SAAOvJ,OAAOlT,SAAS,MAAM,IAAIsc,KAAKF,GAAG,KAAKE,KAAKF,MAAM,CAAC,IAAKE,KAAKF,GAAG,KAAM;AAC/E;AAEA,IAAMM,SAA6BA,CAACxW,UAAU+V,aAAa;AACzD,SAAO/c,MAAMyd,KAAK,IAAIC,IAAIX,SAAS9W,IAAIpG,OAAKA,EAAEwW,SAASrP,QAAQ,CAAC,CAAC,EAAEgN,OAAM,CAAE;AAC7E;AAEA,IAAM2J,cAAkCA,CAAC3W,UAAU+V,aAAa;AAC9D,SAAO,IAAIW,IAAIX,SAAS9W,IAAIpG,OAAKA,EAAEwW,SAASrP,QAAQ,CAAC,CAAC,EAAE2E;AAC1D;AAEA,IAAMqR,QAA4BA,CAACY,WAAWb,aAAa;AACzD,SAAOA,SAASjc;AAClB;AAEO,IAAM+c,iBAAiB;EAC5BtP;EACA3L;EACAD;EACAka;EACAC;EACAG;EACAO;EACAG;EACAX;AACF;ACyHO,IAAMc,WAAyB;EACpCtR,qBAAqBA,MAGhB;AACH,WAAO;MACLuR,gBAAgBC,WAAK;AAAA,YAAAC,WAAAC;AAAA,gBAAAD,aAAAC,kBAAKF,MAAM3H,SAAQ,MAAf6H,QAAAA,gBAA2B5H,YAAQ,OAAA,SAAnC4H,gBAA2B5H,SAAQ,MAAI,OAAA2H,YAAI;MAAI;MACxEE,eAAe;;;EAInB1R,iBAAkBC,WAA8B;AAC9C,WAAO;MACL0R,UAAU,CAAA;MACV,GAAG1R;;;EAIPG,mBACE/J,WACoB;AACpB,WAAO;MACLub,kBAAkB9e,iBAAiB,YAAYuD,KAAK;MACpDwb,mBAAmB;;;EAIvBzb,cAAcA,CACZuB,QACAtB,UACS;AACTsB,WAAOma,iBAAiB,MAAM;AAC5Bzb,YAAM0b,YAAY7e,SAAO;AAEvB,YAAIA,OAAAA,QAAAA,IAAKkE,SAASO,OAAOZ,EAAE,GAAG;AAC5B,iBAAO7D,IAAIuH,OAAOrH,OAAKA,MAAMuE,OAAOZ,EAAE;QACxC;AAEA,eAAO,CAAC,GAAI7D,OAAG,OAAHA,MAAO,CAAA,GAAKyE,OAAOZ,EAAE;MACnC,CAAC;;AAGHY,WAAOqa,cAAc,MAAM;AAAA,UAAAvb,MAAA4K,OAAA6B,OAAAzB;AACzB,cAAAhL,QAAA4K,SAAA6B,SAAAzB,wBACE9J,OAAOrB,UAAU2b,mBAAcxQ,OAAAA,wBAC/B,SAAI,OAAAyB,QACJ7M,MAAM4B,QAAQga,mBAAc,OAAA5Q,QAC5B,SAAI,OAAA5K,OACJ,CAAC,CAACkB,OAAOR;;AAIbQ,WAAOua,eAAe,MAAM;AAAA,UAAAC;AAC1B,cAAAA,wBAAO9b,MAAM0D,SAAQ,EAAG4X,aAAQ,OAAA,SAAzBQ,sBAA2B/a,SAASO,OAAOZ,EAAE;;AAGtDY,WAAOya,kBAAkB,MAAA;AAAA,UAAAC;AAAA,cAAAA,yBAAMhc,MAAM0D,SAAQ,EAAG4X,aAAQ,OAAA,SAAzBU,uBAA2BC,QAAQ3a,OAAOZ,EAAE;IAAC;AAE5EY,WAAO4a,2BAA2B,MAAM;AACtC,YAAMC,WAAW7a,OAAOqa,YAAW;AAEnC,aAAO,MAAM;AACX,YAAI,CAACQ;AAAU;AACf7a,eAAOma,eAAc;;;AAGzBna,WAAO8a,uBAAuB,MAAM;AAClC,YAAMlG,WAAWlW,MAAM+V,gBAAe,EAAGnF,SAAS,CAAC;AAEnD,YAAMkF,QAAQI,YAAAA,OAAAA,SAAAA,SAAU3C,SAASjS,OAAOZ,EAAE;AAE1C,UAAI,OAAOoV,UAAU,UAAU;AAC7B,eAAOiF,eAAetP;MACxB;AAEA,UAAIwF,OAAOoL,UAAU7I,SAAS8I,KAAKxG,KAAK,MAAM,iBAAiB;AAC7D,eAAOiF,eAAehB;MACxB;;AAEFzY,WAAOib,mBAAmB,MAAM;AAAA,UAAAC,uBAAAC;AAC9B,UAAI,CAACnb,QAAQ;AACX,cAAM,IAAID,MAAK;MACjB;AAEA,aAAOvE,WAAWwE,OAAOrB,UAAUob,aAAa,IAC5C/Z,OAAOrB,UAAUob,gBACjB/Z,OAAOrB,UAAUob,kBAAkB,SACjC/Z,OAAO8a,qBAAoB,KAAEI,yBAAAC,yBAC7Bzc,MAAM4B,QAAQmZ,mBAAc,OAAA,SAA5B0B,uBACEnb,OAAOrB,UAAUob,aAAa,MAC/BmB,OAAAA,wBACDzB,eACEzZ,OAAOrB,UAAUob,aAAa;;;EAK1C/X,aAAqCtD,WAA8B;AACjEA,UAAM0b,cAAcpf,aAAW0D,MAAM4B,QAAQ2Z,oBAAgB,OAAA,SAA9Bvb,MAAM4B,QAAQ2Z,iBAAmBjf,OAAO;AAEvE0D,UAAM0c,gBAAgBzO,kBAAgB;AAAA,UAAA0O,uBAAAlM;AACpCzQ,YAAM0b,YAAYzN,eAAe,CAAA,KAAE0O,yBAAAlM,sBAAGzQ,MAAMmO,iBAAY,OAAA,SAAlBsC,oBAAoB6K,aAAQqB,OAAAA,wBAAI,CAAA,CAAE;;AAG1E3c,UAAM4c,wBAAwB,MAAM5c,MAAMkZ,oBAAmB;AAC7DlZ,UAAM6c,qBAAqB,MAAM;AAC/B,UAAI,CAAC7c,MAAM8c,uBAAuB9c,MAAM4B,QAAQib,oBAAoB;AAClE7c,cAAM8c,sBAAsB9c,MAAM4B,QAAQib,mBAAmB7c,KAAK;MACpE;AAEA,UAAIA,MAAM4B,QAAQmb,kBAAkB,CAAC/c,MAAM8c,qBAAqB;AAC9D,eAAO9c,MAAM4c,sBAAqB;MACpC;AAEA,aAAO5c,MAAM8c,oBAAmB;;;EAIpChL,WAAWA,CACTjB,KACA7Q,UACS;AACT6Q,QAAIgL,eAAe,MAAM,CAAC,CAAChL,IAAImM;AAC/BnM,QAAIoM,mBAAmB/Y,cAAY;AACjC,UAAI2M,IAAIqM,qBAAqBC,eAAejZ,QAAQ,GAAG;AACrD,eAAO2M,IAAIqM,qBAAqBhZ,QAAQ;MAC1C;AAEA,YAAM5C,SAAStB,MAAM8L,UAAU5H,QAAQ;AAEvC,UAAI,EAAC5C,UAAAA,QAAAA,OAAQrB,UAAUgd,mBAAkB;AACvC,eAAOpM,IAAI0C,SAASrP,QAAQ;MAC9B;AAEA2M,UAAIqM,qBAAqBhZ,QAAQ,IAAI5C,OAAOrB,UAAUgd,iBACpDpM,IAAIuM,QACN;AAEA,aAAOvM,IAAIqM,qBAAqBhZ,QAAQ;;AAE1C2M,QAAIqM,uBAAuB,CAAA;;EAG7BG,YAAYA,CACVC,MACAhc,QACAuP,KACA7Q,UACS;AAITsd,SAAKzB,eAAe,MAClBva,OAAOua,aAAY,KAAMva,OAAOZ,OAAOmQ,IAAImM;AAC7CM,SAAKC,mBAAmB,MAAM,CAACD,KAAKzB,aAAY,KAAMva,OAAOua,aAAY;AACzEyB,SAAKE,kBAAkB,MAAA;AAAA,UAAAjL;AAAA,aACrB,CAAC+K,KAAKzB,aAAY,KAAM,CAACyB,KAAKC,iBAAgB,KAAM,CAAC,GAAAhL,eAAC1B,IAAI6B,YAAO,QAAXH,aAAavU;IAAM;EAC7E;AACF;AAEO,SAASiE,aACdE,aACAmZ,UACAE,mBACA;AACA,MAAI,EAACF,YAAAA,QAAAA,SAAUtd,WAAU,CAACwd,mBAAmB;AAC3C,WAAOrZ;EACT;AAEA,QAAMsb,qBAAqBtb,YAAYiC,OACrCsZ,SAAO,CAACpC,SAASva,SAAS2c,IAAIhd,EAAE,CAClC;AAEA,MAAI8a,sBAAsB,UAAU;AAClC,WAAOiC;EACT;AAEA,QAAME,kBAAkBrC,SACrBnY,IAAIya,OAAKzb,YAAYgC,KAAKuZ,SAAOA,IAAIhd,OAAOkd,CAAC,CAAE,EAC/CxZ,OAAOC,OAAO;AAEjB,SAAO,CAAC,GAAGsZ,iBAAiB,GAAGF,kBAAkB;AACnD;ACzXO,IAAMI,WAAyB;EACpClU,iBAAkBC,WAAiC;AACjD,WAAO;MACLkU,aAAa,CAAA;MACb,GAAGlU;;;EAIPG,mBACE/J,WAC8B;AAC9B,WAAO;MACL+d,qBAAqBthB,iBAAiB,eAAeuD,KAAK;;;EAI9DsD,aAAqCtD,WAA8B;AACjEA,UAAMge,iBAAiB1hB,aACrB0D,MAAM4B,QAAQmc,uBAAmB,OAAA,SAAjC/d,MAAM4B,QAAQmc,oBAAsBzhB,OAAO;AAC7C0D,UAAMie,mBAAmBhQ,kBAAgB;AAAA,UAAAC;AACvClO,YAAMge,eACJ/P,eAAe,CAAA,KAAEC,wBAAGlO,MAAMmO,aAAa2P,gBAAW,OAAA5P,wBAAI,CAAA,CACxD;;AAEFlO,UAAMgC,qBAAqB/D,KACzB,MAAM,CACJ+B,MAAM0D,SAAQ,EAAGoa,aACjB9d,MAAM0D,SAAQ,EAAG4X,UACjBtb,MAAM4B,QAAQ4Z,iBAAiB,GAEjC,CAACsC,aAAaxC,UAAUE,sBAAsBja,aAAW;AAGvD,UAAI2c,iBAA2C,CAAA;AAG/C,UAAI,EAACJ,eAAW,QAAXA,YAAa9f,SAAQ;AACxBkgB,yBAAiB3c;MACnB,OAAO;AACL,cAAM4c,kBAAkB,CAAC,GAAGL,WAAW;AAGvC,cAAMM,cAAc,CAAC,GAAG7c,OAAO;AAK/B,eAAO6c,YAAYpgB,UAAUmgB,gBAAgBngB,QAAQ;AACnD,gBAAMqgB,iBAAiBF,gBAAgBG,MAAK;AAC5C,gBAAMC,aAAaH,YAAYxT,UAC7B7N,OAAKA,EAAE2D,OAAO2d,cAChB;AACA,cAAIE,aAAa,IAAI;AACnBL,2BAAepgB,KAAKsgB,YAAYI,OAAOD,YAAY,CAAC,EAAE,CAAC,CAAE;UAC3D;QACF;AAGAL,yBAAiB,CAAC,GAAGA,gBAAgB,GAAGE,WAAW;MACrD;AAEA,aAAOnc,aAAaic,gBAAgB5C,UAAUE,iBAAiB;IACjE,GACA;MACE9e,KAA+C;;IAEjD,CACF;EACF;AACF;ACsCA,IAAM+hB,mBAAmB;AACzB,IAAMC,kBAAkB;AAExB,IAAMC,4BAA4BA,OAAwB;EACxDC,WAAWH;EACXI,UAAUH;AACZ;AAEO,IAAMI,aAA2B;EACtCnV,iBAAkBC,WAAgC;AAChD,WAAO;MACL,GAAGA;MACHmV,YAAY;QACV,GAAGJ,0BAAyB;QAC5B,GAAG/U,SAAK,OAAA,SAALA,MAAOmV;MACZ;;;EAIJhV,mBACE/J,WAC6B;AAC7B,WAAO;MACLgf,oBAAoBviB,iBAAiB,cAAcuD,KAAK;;;EAI5DsD,aAAqCtD,WAA8B;AACjE,QAAI4P,aAAa;AACjB,QAAIC,SAAS;AAEb7P,UAAMif,sBAAsB,MAAM;AAAA,UAAA7e,MAAA2P;AAChC,UAAI,CAACH,YAAY;AACf5P,cAAMgQ,OAAO,MAAM;AACjBJ,uBAAa;QACf,CAAC;AACD;MACF;AAEA,WAAAxP,QAAA2P,wBACE/P,MAAM4B,QAAQqO,iBAAYF,OAAAA,wBAC1B/P,MAAM4B,QAAQsd,uBAAkB,OAAA9e,OAChC,CAACJ,MAAM4B,QAAQud,kBACf;AACA,YAAItP;AAAQ;AACZA,iBAAS;AACT7P,cAAMgQ,OAAO,MAAM;AACjBhQ,gBAAMof,eAAc;AACpBvP,mBAAS;QACX,CAAC;MACH;;AAEF7P,UAAMqf,gBAAgB/iB,aAAW;AAC/B,YAAMgjB,cAAwCziB,SAAO;AACnD,YAAI0iB,WAAWljB,iBAAiBC,SAASO,GAAG;AAE5C,eAAO0iB;;AAGT,aAAOvf,MAAM4B,QAAQod,sBAAkB,OAAA,SAAhChf,MAAM4B,QAAQod,mBAAqBM,WAAW;;AAEvDtf,UAAMwf,kBAAkBvR,kBAAgB;AAAA,UAAAwR;AACtCzf,YAAMqf,cACJpR,eACI0Q,0BAAyB,KAAEc,wBAC3Bzf,MAAMmO,aAAa4Q,eAAU,OAAAU,wBAAId,0BAAyB,CAChE;;AAEF3e,UAAM0f,eAAepjB,aAAW;AAC9B0D,YAAMqf,cAAcxiB,SAAO;AACzB,YAAI+hB,YAAYviB,iBAAiBC,SAASO,IAAI+hB,SAAS;AAEvD,cAAMe,eACJ,OAAO3f,MAAM4B,QAAQge,cAAc,eACnC5f,MAAM4B,QAAQge,cAAc,KACxB5W,OAAOC,mBACPjJ,MAAM4B,QAAQge,YAAY;AAEhChB,oBAAYzf,KAAKU,IAAI,GAAGV,KAAKW,IAAI8e,WAAWe,YAAY,CAAC;AAEzD,eAAO;UACL,GAAG9iB;UACH+hB;;MAEJ,CAAC;;AAEH5e,UAAMof,iBAAiBnR,kBAAgB;AAAA,UAAA4R,wBAAApP;AACrCzQ,YAAM0f,aACJzR,eACIwQ,oBAAgBoB,0BAAApP,sBAChBzQ,MAAMmO,iBAAYsC,SAAAA,sBAAlBA,oBAAoBsO,eAApBtO,OAAAA,SAAAA,oBAAgCmO,cAASiB,OAAAA,yBAAIpB,gBACnD;;AAEFze,UAAM8f,gBAAgB7R,kBAAgB;AAAA,UAAA8R,wBAAAC;AACpChgB,YAAMigB,YACJhS,eACIyQ,mBAAeqB,0BAAAC,uBACfhgB,MAAMmO,iBAAY6R,SAAAA,uBAAlBA,qBAAoBjB,eAApBiB,OAAAA,SAAAA,qBAAgCnB,aAAQkB,OAAAA,yBAAIrB,eAClD;;AAEF1e,UAAMigB,cAAc3jB,aAAW;AAC7B0D,YAAMqf,cAAcxiB,SAAO;AACzB,cAAMgiB,WAAW1f,KAAKU,IAAI,GAAGxD,iBAAiBC,SAASO,IAAIgiB,QAAQ,CAAC;AACpE,cAAMqB,cAAcrjB,IAAIgiB,WAAWhiB,IAAI+hB;AACvC,cAAMA,YAAYzf,KAAKkb,MAAM6F,cAAcrB,QAAQ;AAEnD,eAAO;UACL,GAAGhiB;UACH+hB;UACAC;;MAEJ,CAAC;;AAEH7e,UAAMmgB,eAAe7jB,aACnB0D,MAAMqf,cAAcxiB,SAAO;AAAA,UAAAujB;AACzB,UAAIC,eAAehkB,iBACjBC,UAAO8jB,wBACPpgB,MAAM4B,QAAQge,cAAS,OAAAQ,wBAAI,EAC7B;AAEA,UAAI,OAAOC,iBAAiB,UAAU;AACpCA,uBAAelhB,KAAKU,IAAI,IAAIwgB,YAAY;MAC1C;AAEA,aAAO;QACL,GAAGxjB;QACH+iB,WAAWS;;IAEf,CAAC;AAEHrgB,UAAMsgB,iBAAiBriB,KACrB,MAAM,CAAC+B,MAAMugB,aAAY,CAAE,GAC3BX,eAAa;AACX,UAAIY,cAAwB,CAAA;AAC5B,UAAIZ,aAAaA,YAAY,GAAG;AAC9BY,sBAAc,CAAC,GAAG,IAAItjB,MAAM0iB,SAAS,CAAC,EAAEa,KAAK,IAAI,EAAEtd,IAAI,CAAC8H,GAAGyV,MAAMA,CAAC;MACpE;AACA,aAAOF;IACT,GACA;MACE9jB,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;MAAU;IACjE,CACF;AAEA3gB,UAAM4gB,qBAAqB,MAAM5gB,MAAM0D,SAAQ,EAAGqb,WAAWH,YAAY;AAEzE5e,UAAM6gB,iBAAiB,MAAM;AAC3B,YAAM;QAAEjC;MAAU,IAAI5e,MAAM0D,SAAQ,EAAGqb;AAEvC,YAAMa,YAAY5f,MAAMugB,aAAY;AAEpC,UAAIX,cAAc,IAAI;AACpB,eAAO;MACT;AAEA,UAAIA,cAAc,GAAG;AACnB,eAAO;MACT;AAEA,aAAOhB,YAAYgB,YAAY;;AAGjC5f,UAAM8gB,eAAe,MAAM;AACzB,aAAO9gB,MAAM0f,aAAa7iB,SAAOA,MAAM,CAAC;;AAG1CmD,UAAM+gB,WAAW,MAAM;AACrB,aAAO/gB,MAAM0f,aAAa7iB,SAAO;AAC/B,eAAOA,MAAM;MACf,CAAC;;AAGHmD,UAAM2Q,2BAA2B,MAAM3Q,MAAM4R,oBAAmB;AAChE5R,UAAMghB,wBAAwB,MAAM;AAClC,UACE,CAAChhB,MAAMihB,0BACPjhB,MAAM4B,QAAQof,uBACd;AACAhhB,cAAMihB,yBACJjhB,MAAM4B,QAAQof,sBAAsBhhB,KAAK;MAC7C;AAEA,UAAIA,MAAM4B,QAAQud,oBAAoB,CAACnf,MAAMihB,wBAAwB;AACnE,eAAOjhB,MAAM2Q,yBAAwB;MACvC;AAEA,aAAO3Q,MAAMihB,uBAAsB;;AAGrCjhB,UAAMugB,eAAe,MAAM;AAAA,UAAAW;AACzB,cAAAA,yBACElhB,MAAM4B,QAAQge,cAASsB,OAAAA,yBACvB/hB,KAAKgiB,KACHnhB,MAAM2Q,yBAAwB,EAAGyQ,KAAKpjB,SACpCgC,MAAM0D,SAAQ,EAAGqb,WAAWF,QAChC;;EAGN;AACF;ACrGA,IAAMwC,+BAA+BA,OAA2B;EAC9Dzd,MAAM,CAAA;EACNC,OAAO,CAAA;AACT;AAEA,IAAMyd,4BAA4BA,OAAwB;EACxDC,KAAK,CAAA;EACLC,QAAQ,CAAA;AACV;AAEO,IAAMC,UAAwB;EACnC9X,iBAAkBC,WAAqD;AACrE,WAAO;MACLjG,eAAe0d,6BAA4B;MAC3CK,YAAYJ,0BAAyB;MACrC,GAAG1X;;;EAIPG,mBACE/J,WAC2D;AAC3D,WAAO;MACL2hB,uBAAuBllB,iBAAiB,iBAAiBuD,KAAK;MAC9D4hB,oBAAoBnlB,iBAAiB,cAAcuD,KAAK;;;EAI5DD,cAAcA,CACZuB,QACAtB,UACS;AACTsB,WAAOugB,MAAMpX,cAAY;AACvB,YAAMqX,YAAYxgB,OACfS,eAAc,EACdoB,IAAIpG,OAAKA,EAAE2D,EAAE,EACb0D,OAAOC,OAAO;AAEjBrE,YAAM+hB,iBAAiBllB,SAAO;AAAA,YAAAmlB,YAAAC;AAC5B,YAAIxX,aAAa,SAAS;AAAA,cAAAyX,WAAAC;AACxB,iBAAO;YACLve,QAAMse,YAACrlB,OAAAA,OAAAA,SAAAA,IAAK+G,SAAIse,OAAAA,YAAI,CAAA,GAAI9d,OAAOrH,OAAK,EAAC+kB,aAAS,QAATA,UAAW/gB,SAAShE,CAAC,EAAE;YAC5D8G,OAAO,CACL,KAAGse,aAACtlB,OAAAA,OAAAA,SAAAA,IAAKgH,UAAKse,OAAAA,aAAI,CAAA,GAAI/d,OAAOrH,OAAK,EAAC+kB,aAAS,QAATA,UAAW/gB,SAAShE,CAAC,EAAE,GAC1D,GAAG+kB,SAAS;;QAGlB;AAEA,YAAIrX,aAAa,QAAQ;AAAA,cAAA2X,YAAAC;AACvB,iBAAO;YACLze,MAAM,CACJ,KAAGwe,aAACvlB,OAAAA,OAAAA,SAAAA,IAAK+G,SAAIwe,OAAAA,aAAI,CAAA,GAAIhe,OAAOrH,OAAK,EAAC+kB,aAAS,QAATA,UAAW/gB,SAAShE,CAAC,EAAC,GACxD,GAAG+kB,SAAS;YAEdje,SAAOwe,cAACxlB,OAAAA,OAAAA,SAAAA,IAAKgH,UAAKwe,OAAAA,cAAI,CAAA,GAAIje,OAAOrH,OAAK,EAAC+kB,aAAAA,QAAAA,UAAW/gB,SAAShE,CAAC,EAAC;;QAEjE;AAEA,eAAO;UACL6G,QAAMoe,aAACnlB,OAAAA,OAAAA,SAAAA,IAAK+G,SAAIoe,OAAAA,aAAI,CAAA,GAAI5d,OAAOrH,OAAK,EAAC+kB,aAAS,QAATA,UAAW/gB,SAAShE,CAAC,EAAE;UAC5D8G,SAAOoe,cAACplB,OAAAA,OAAAA,SAAAA,IAAKgH,UAAKoe,OAAAA,cAAI,CAAA,GAAI7d,OAAOrH,OAAK,EAAC+kB,aAAAA,QAAAA,UAAW/gB,SAAShE,CAAC,EAAC;;MAEjE,CAAC;;AAGHuE,WAAOghB,YAAY,MAAM;AACvB,YAAMngB,cAAcb,OAAOS,eAAc;AAEzC,aAAOI,YAAYtD,KACjB9B,OAAC;AAAA,YAAAwlB,uBAAAniB,MAAAiL;AAAA,iBACCkX,wBAACxlB,EAAEkD,UAAUuiB,kBAAa,OAAAD,wBAAI,WAAIniB,QAAAiL,wBACjCrL,MAAM4B,QAAQ6gB,wBAAmB,OAAApX,wBAChCrL,MAAM4B,QAAQ4gB,kBAAa,OAAApiB,OAC3B;MAAK,CACX;;AAGFkB,WAAOohB,cAAc,MAAM;AACzB,YAAMC,gBAAgBrhB,OAAOS,eAAc,EAAGoB,IAAIpG,OAAKA,EAAE2D,EAAE;AAE3D,YAAM;QAAEkD;QAAMC;MAAM,IAAI7D,MAAM0D,SAAQ,EAAGC;AAEzC,YAAMif,SAASD,cAAc9jB,KAAK9B,OAAK6G,QAAI,OAAA,SAAJA,KAAM7C,SAAShE,CAAC,CAAC;AACxD,YAAM8lB,UAAUF,cAAc9jB,KAAK9B,OAAK8G,SAAK,OAAA,SAALA,MAAO9C,SAAShE,CAAC,CAAC;AAE1D,aAAO6lB,SAAS,SAASC,UAAU,UAAU;;AAG/CvhB,WAAOwhB,iBAAiB,MAAM;AAAA,UAAA1L,uBAAAC;AAC5B,YAAM5M,WAAWnJ,OAAOohB,YAAW;AAEnC,aAAOjY,YAAQ2M,yBAAAC,yBACXrX,MAAM0D,SAAQ,EAAGC,kBAAa,SAAA0T,yBAA9BA,uBAAiC5M,QAAQ,MAAzC4M,OAAAA,SAAAA,uBAA4C4E,QAAQ3a,OAAOZ,EAAE,MAAC,OAAA0W,wBAAI,KAClE;;;EAIRtF,WAAWA,CACTjB,KACA7Q,UACS;AACT6Q,QAAIgR,MAAM,CAACpX,UAAUsY,iBAAiBC,sBAAsB;AAC1D,YAAMC,aAAaF,kBACflS,IAAIqS,YAAW,EAAG/f,IAAI6H,WAAA;AAAA,YAAC;UAAEtK;QAAG,IAACsK;AAAA,eAAKtK;MAAE,CAAA,IACpC,CAAA;AACJ,YAAMyiB,eAAeH,oBACjBnS,IAAIuS,cAAa,EAAGjgB,IAAI0J,WAAA;AAAA,YAAC;UAAEnM;QAAG,IAACmM;AAAA,eAAKnM;MAAE,CAAA,IACtC,CAAA;AACJ,YAAM6Q,SAAS,oBAAIqJ,IAAI,CAAC,GAAGuI,cAActS,IAAInQ,IAAI,GAAGuiB,UAAU,CAAC;AAE/DjjB,YAAMqjB,cAAcxmB,SAAO;AAAA,YAAAymB,WAAAC;AACzB,YAAI9Y,aAAa,UAAU;AAAA,cAAA+Y,UAAAC;AACzB,iBAAO;YACLlC,OAAKiC,WAAC3mB,OAAAA,OAAAA,SAAAA,IAAK0kB,QAAGiC,OAAAA,WAAI,CAAA,GAAIpf,OAAOrH,OAAK,EAACwU,UAAM,QAANA,OAAQmS,IAAI3mB,CAAC,EAAE;YAClDykB,QAAQ,CACN,KAAGiC,cAAC5mB,OAAG,OAAA,SAAHA,IAAK2kB,WAAMiC,OAAAA,cAAI,CAAA,GAAIrf,OAAOrH,OAAK,EAACwU,UAAM,QAANA,OAAQmS,IAAI3mB,CAAC,EAAC,GAClD,GAAGG,MAAMyd,KAAKpJ,MAAM,CAAC;;QAG3B;AAEA,YAAI9G,aAAa,OAAO;AAAA,cAAAkZ,WAAAC;AACtB,iBAAO;YACLrC,KAAK,CACH,KAAGoC,YAAC9mB,OAAG,OAAA,SAAHA,IAAK0kB,QAAGoC,OAAAA,YAAI,CAAA,GAAIvf,OAAOrH,OAAK,EAACwU,UAAAA,QAAAA,OAAQmS,IAAI3mB,CAAC,EAAE,GAChD,GAAGG,MAAMyd,KAAKpJ,MAAM,CAAC;YAEvBiQ,UAAQoC,eAAC/mB,OAAAA,OAAAA,SAAAA,IAAK2kB,WAAMoC,OAAAA,eAAI,CAAA,GAAIxf,OAAOrH,OAAK,EAACwU,UAAAA,QAAAA,OAAQmS,IAAI3mB,CAAC,EAAC;;QAE3D;AAEA,eAAO;UACLwkB,OAAK+B,YAACzmB,OAAAA,OAAAA,SAAAA,IAAK0kB,QAAG+B,OAAAA,YAAI,CAAA,GAAIlf,OAAOrH,OAAK,EAACwU,UAAM,QAANA,OAAQmS,IAAI3mB,CAAC,EAAE;UAClDykB,UAAQ+B,eAAC1mB,OAAAA,OAAAA,SAAAA,IAAK2kB,WAAM+B,OAAAA,eAAI,CAAA,GAAInf,OAAOrH,OAAK,EAACwU,UAAAA,QAAAA,OAAQmS,IAAI3mB,CAAC,EAAC;;MAE3D,CAAC;;AAEH8T,QAAIyR,YAAY,MAAM;AAAA,UAAAuB;AACpB,YAAM;QAAEC;QAAkBtB;UAAkBxiB,MAAM4B;AAClD,UAAI,OAAOkiB,qBAAqB,YAAY;AAC1C,eAAOA,iBAAiBjT,GAAG;MAC7B;AACA,cAAAgT,QAAOC,oBAAAA,OAAAA,mBAAoBtB,kBAAa,OAAAqB,QAAI;;AAE9ChT,QAAI6R,cAAc,MAAM;AACtB,YAAMnR,SAAS,CAACV,IAAInQ,EAAE;AAEtB,YAAM;QAAE6gB;QAAKC;MAAO,IAAIxhB,MAAM0D,SAAQ,EAAGge;AAEzC,YAAMqC,QAAQxS,OAAO1S,KAAK9B,OAAKwkB,OAAG,OAAA,SAAHA,IAAKxgB,SAAShE,CAAC,CAAC;AAC/C,YAAMinB,WAAWzS,OAAO1S,KAAK9B,OAAKykB,UAAM,OAAA,SAANA,OAAQzgB,SAAShE,CAAC,CAAC;AAErD,aAAOgnB,QAAQ,QAAQC,WAAW,WAAW;;AAE/CnT,QAAIiS,iBAAiB,MAAM;AAAA,UAAAmB,uBAAAC;AACzB,YAAMzZ,WAAWoG,IAAI6R,YAAW;AAChC,UAAI,CAACjY;AAAU,eAAO;AAEtB,YAAM0Z,uBAAmBF,wBAAGjkB,MACzBokB,eAAe3Z,QAAQ,MADEwZ,OAAAA,SAAAA,sBAExB9gB,IAAIkhB,WAAA;AAAA,YAAC;UAAE3jB;QAAG,IAAC2jB;AAAA,eAAK3jB;OAAG;AAEvB,cAAAwjB,wBAAOC,uBAAAA,OAAAA,SAAAA,oBAAqBlI,QAAQpL,IAAInQ,EAAE,MAAC,OAAAwjB,wBAAI;;AAEjDrT,QAAIyT,wBAAwBrmB,KAC1B,MAAM,CACJ4S,IAAI0T,oBAAmB,GACvBvkB,MAAM0D,SAAQ,EAAGC,cAAcC,MAC/B5D,MAAM0D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAAC2gB,UAAU5gB,MAAMC,UAAU;AACzB,YAAM4gB,eAAyB,CAAC,GAAI7gB,QAAI,OAAJA,OAAQ,CAAA,GAAK,GAAIC,SAAK,OAALA,QAAS,CAAA,CAAE;AAEhE,aAAO2gB,SAASpgB,OAAOrH,OAAK,CAAC0nB,aAAa1jB,SAAShE,EAAEuE,OAAOZ,EAAE,CAAC;IACjE,GACA;MACEhE,KAC4C;MAC5C8B,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ8iB;MAAS;IAChE,CACF;AACA7T,QAAI8T,sBAAsB1mB,KACxB,MAAM,CAAC4S,IAAI0T,oBAAmB,GAAIvkB,MAAM0D,SAAQ,EAAGC,cAAcC,MAAI,CAAA,GACrE,CAAC4gB,UAAU5gB,SAAS;AAClB,YAAMghB,SAAShhB,QAAI,OAAJA,OAAQ,CAAA,GACpBT,IAAIe,cAAYsgB,SAASrgB,KAAKmZ,UAAQA,KAAKhc,OAAOZ,OAAOwD,QAAQ,CAAE,EACnEE,OAAOC,OAAO,EACdlB,IAAIpG,QAAM;QAAE,GAAGA;QAAG0N,UAAU;MAAO,EAA0B;AAEhE,aAAOma;IACT,GACA;MACEloB,KAC4C;MAC5C8B,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQ8iB;MAAS;IAChE,CACF;AACA7T,QAAIgU,uBAAuB5mB,KACzB,MAAM,CAAC4S,IAAI0T,oBAAmB,GAAIvkB,MAAM0D,SAAQ,EAAGC,cAAcE,KAAK,GACtE,CAAC2gB,UAAU3gB,UAAU;AACnB,YAAM+gB,SAAS/gB,SAAK,OAALA,QAAS,CAAA,GACrBV,IAAIe,cAAYsgB,SAASrgB,KAAKmZ,UAAQA,KAAKhc,OAAOZ,OAAOwD,QAAQ,CAAE,EACnEE,OAAOC,OAAO,EACdlB,IAAIpG,QAAM;QAAE,GAAGA;QAAG0N,UAAU;MAAQ,EAA0B;AAEjE,aAAOma;IACT,GACA;MACEloB,KAC4C;MAC5C8B,OAAOA,MAAA;AAAA,YAAAuG;AAAA,gBAAAA,yBAAM/E,MAAM4B,QAAQC,aAAQkD,OAAAA,yBAAI/E,MAAM4B,QAAQ8iB;MAAS;IAChE,CACF;;EAGFphB,aAAqCtD,WAA8B;AACjEA,UAAM+hB,mBAAmBzlB,aACvB0D,MAAM4B,QAAQ+f,yBAAqB,OAAA,SAAnC3hB,MAAM4B,QAAQ+f,sBAAwBrlB,OAAO;AAE/C0D,UAAM8kB,qBAAqB7W,kBAAY;AAAA,UAAAC,uBAAAuC;AAAA,aACrCzQ,MAAM+hB,iBACJ9T,eACIoT,6BAA4B,KAAEnT,yBAAAuC,sBAC9BzQ,MAAMmO,iBAANsC,OAAAA,SAAAA,oBAAoB9M,kBAAauK,OAAAA,wBAAImT,6BAA4B,CACvE;IAAC;AAEHrhB,UAAM+kB,yBAAyBta,cAAY;AAAA,UAAAua;AACzC,YAAMC,eAAejlB,MAAM0D,SAAQ,EAAGC;AAEtC,UAAI,CAAC8G,UAAU;AAAA,YAAAya,oBAAAC;AACb,eAAO9gB,UAAQ6gB,qBAAAD,aAAarhB,SAAI,OAAA,SAAjBshB,mBAAmBlnB,aAAMmnB,sBAAIF,aAAaphB,UAAbshB,OAAAA,SAAAA,oBAAoBnnB,OAAO;MACzE;AACA,aAAOqG,SAAO2gB,wBAACC,aAAaxa,QAAQ,MAArBua,OAAAA,SAAAA,sBAAwBhnB,MAAM;;AAG/CgC,UAAMolB,qBAAqBnnB,KACzB,MAAM,CAAC+B,MAAM4Y,kBAAiB,GAAI5Y,MAAM0D,SAAQ,EAAGC,cAAcC,IAAI,GACrE,CAACE,YAAYF,SAAS;AACpB,cAAQA,QAAAA,OAAAA,OAAQ,CAAA,GACbT,IAAIe,cAAYJ,WAAWK,KAAK7C,YAAUA,OAAOZ,OAAOwD,QAAQ,CAAE,EAClEE,OAAOC,OAAO;IACnB,GACA;MACE3H,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA0G;AAAA,gBAAAA,yBAAMlF,MAAM4B,QAAQC,aAAQqD,OAAAA,yBAAIlF,MAAM4B,QAAQE;MAAY;IACnE,CACF;AAEA9B,UAAMqlB,sBAAsBpnB,KAC1B,MAAM,CAAC+B,MAAM4Y,kBAAiB,GAAI5Y,MAAM0D,SAAQ,EAAGC,cAAcE,KAAK,GACtE,CAACC,YAAYD,UAAU;AACrB,cAAQA,SAAAA,OAAAA,QAAS,CAAA,GACdV,IAAIe,cAAYJ,WAAWK,KAAK7C,YAAUA,OAAOZ,OAAOwD,QAAQ,CAAE,EAClEE,OAAOC,OAAO;IACnB,GACA;MACE3H,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA6G;AAAA,gBAAAA,yBAAMrF,MAAM4B,QAAQC,aAAQwD,OAAAA,yBAAIrF,MAAM4B,QAAQE;MAAY;IACnE,CACF;AAEA9B,UAAMslB,uBAAuBrnB,KAC3B,MAAM,CACJ+B,MAAM4Y,kBAAiB,GACvB5Y,MAAM0D,SAAQ,EAAGC,cAAcC,MAC/B5D,MAAM0D,SAAQ,EAAGC,cAAcE,KAAK,GAEtC,CAACC,YAAYF,MAAMC,UAAU;AAC3B,YAAM4gB,eAAyB,CAAC,GAAI7gB,QAAI,OAAJA,OAAQ,CAAA,GAAK,GAAIC,SAAK,OAALA,QAAS,CAAA,CAAE;AAEhE,aAAOC,WAAWM,OAAOrH,OAAK,CAAC0nB,aAAa1jB,SAAShE,EAAE2D,EAAE,CAAC;IAC5D,GACA;MACEhE,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA+G;AAAA,gBAAAA,yBAAMvF,MAAM4B,QAAQC,aAAQ0D,OAAAA,yBAAIvF,MAAM4B,QAAQE;MAAY;IACnE,CACF;AAEA9B,UAAMqjB,gBAAgB/mB,aAAW0D,MAAM4B,QAAQggB,sBAAkB,OAAA,SAAhC5hB,MAAM4B,QAAQggB,mBAAqBtlB,OAAO;AAE3E0D,UAAMulB,kBAAkBtX,kBAAY;AAAA,UAAAuX,uBAAAxF;AAAA,aAClChgB,MAAMqjB,cACJpV,eACIqT,0BAAyB,KAAEkE,yBAAAxF,uBAC3BhgB,MAAMmO,iBAAN6R,OAAAA,SAAAA,qBAAoB0B,eAAU8D,OAAAA,wBAAIlE,0BAAyB,CACjE;IAAC;AAEHthB,UAAMylB,sBAAsBhb,cAAY;AAAA,UAAAib;AACtC,YAAMT,eAAejlB,MAAM0D,SAAQ,EAAGge;AAEtC,UAAI,CAACjX,UAAU;AAAA,YAAAkb,mBAAAC;AACb,eAAOvhB,UAAQshB,oBAAAV,aAAa1D,QAAG,OAAA,SAAhBoE,kBAAkB3nB,aAAM4nB,uBAAIX,aAAazD,WAAboE,OAAAA,SAAAA,qBAAqB5nB,OAAO;MACzE;AACA,aAAOqG,SAAOqhB,yBAACT,aAAaxa,QAAQ,MAArBib,OAAAA,SAAAA,uBAAwB1nB,MAAM;;AAG/CgC,UAAMokB,iBAAkB3Z,cACtBxM,KACE,MAAM,CAAC+B,MAAMoR,YAAW,EAAGgQ,MAAMphB,MAAM0D,SAAQ,EAAGge,WAAWjX,QAAQ,CAAC,GACtE,CAACob,aAAaC,iBAAiB;AAAA,UAAAC;AAC7B,YAAM3E,SACJ2E,wBAAA/lB,MAAM4B,QAAQokB,mBAAcD,OAAAA,wBAAI;;;SAG3BD,gBAAY,OAAZA,eAAgB,CAAA,GAAI3iB,IAAIgP,WAAS;AAChC,gBAAMtB,MAAM7Q,MAAM+S,OAAOZ,OAAO,IAAI;AACpC,iBAAOtB,IAAI8B,wBAAuB,IAAK9B,MAAM;QAC/C,CAAC;;;SAEAiV,gBAAY,OAAZA,eAAgB,CAAA,GAAI3iB,IACnBgP,WAAS0T,YAAY1hB,KAAK0M,SAAOA,IAAInQ,OAAOyR,KAAK,CACnD;;AAEN,aAAOiP,KACJhd,OAAOC,OAAO,EACdlB,IAAIpG,QAAM;QAAE,GAAGA;QAAG0N;MAAS,EAAE;IAClC,GACA;MACE/N,KAEG,UAAS+N,aAAa,QAAQ,QAAQ,QAAS;MAClDjM,OAAOA,MAAA;AAAA,YAAAiH;AAAA,gBAAAA,yBAAMzF,MAAM4B,QAAQC,aAAQ4D,OAAAA,yBAAIzF,MAAM4B,QAAQ8iB;MAAS;KAElE,EAAC;AAEH1kB,UAAMimB,aAAa,MAAMjmB,MAAMokB,eAAe,KAAK;AAEnDpkB,UAAMkmB,gBAAgB,MAAMlmB,MAAMokB,eAAe,QAAQ;AAEzDpkB,UAAMmmB,gBAAgBloB,KACpB,MAAM,CACJ+B,MAAMoR,YAAW,EAAGgQ,MACpBphB,MAAM0D,SAAQ,EAAGge,WAAWH,KAC5BvhB,MAAM0D,SAAQ,EAAGge,WAAWF,MAAM,GAEpC,CAAC4E,SAAS7E,KAAKC,WAAW;AACxB,YAAM6E,eAAe,oBAAIzL,IAAI,CAAC,GAAI2G,OAAG,OAAHA,MAAO,CAAA,GAAK,GAAIC,UAAM,OAANA,SAAU,CAAA,CAAE,CAAE;AAChE,aAAO4E,QAAQhiB,OAAOrH,OAAK,CAACspB,aAAa3C,IAAI3mB,EAAE2D,EAAE,CAAC;IACpD,GACA;MACEhE,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAmH;AAAA,gBAAAA,yBAAM3F,MAAM4B,QAAQC,aAAQ8D,OAAAA,yBAAI3F,MAAM4B,QAAQ8iB;MAAS;IAChE,CACF;EACF;AACF;ACvZO,IAAM4B,eAA6B;EACxC3c,iBAAkBC,WAAkC;AAClD,WAAO;MACL2c,cAAc,CAAA;MACd,GAAG3c;;;EAIPG,mBACE/J,WAC+B;AAC/B,WAAO;MACLwmB,sBAAsB/pB,iBAAiB,gBAAgBuD,KAAK;MAC5DymB,oBAAoB;MACpBC,yBAAyB;MACzBC,uBAAuB;;;;;;EAO3BrjB,aAAqCtD,WAA8B;AACjEA,UAAM4mB,kBAAkBtqB,aACtB0D,MAAM4B,QAAQ4kB,wBAAoB,OAAA,SAAlCxmB,MAAM4B,QAAQ4kB,qBAAuBlqB,OAAO;AAC9C0D,UAAM6mB,oBAAoB5Y,kBAAY;AAAA,UAAAuX;AAAA,aACpCxlB,MAAM4mB,gBACJ3Y,eAAe,CAAA,KAAEuX,wBAAGxlB,MAAMmO,aAAaoY,iBAAY,OAAAf,wBAAI,CAAA,CACzD;IAAC;AACHxlB,UAAM8mB,wBAAwBhR,WAAS;AACrC9V,YAAM4mB,gBAAgB/pB,SAAO;AAC3BiZ,gBACE,OAAOA,UAAU,cAAcA,QAAQ,CAAC9V,MAAM+mB,qBAAoB;AAEpE,cAAMR,eAAe;UAAE,GAAG1pB;;AAE1B,cAAMmqB,qBAAqBhnB,MAAM4c,sBAAqB,EAAGhM;AAIzD,YAAIkF,OAAO;AACTkR,6BAAmBppB,QAAQiT,SAAO;AAChC,gBAAI,CAACA,IAAIoW,aAAY,GAAI;AACvB;YACF;AACAV,yBAAa1V,IAAInQ,EAAE,IAAI;UACzB,CAAC;QACH,OAAO;AACLsmB,6BAAmBppB,QAAQiT,SAAO;AAChC,mBAAO0V,aAAa1V,IAAInQ,EAAE;UAC5B,CAAC;QACH;AAEA,eAAO6lB;MACT,CAAC;;AAEHvmB,UAAMknB,4BAA4BpR,WAChC9V,MAAM4mB,gBAAgB/pB,SAAO;AAC3B,YAAMsqB,gBACJ,OAAOrR,UAAU,cACbA,QACA,CAAC9V,MAAMonB,yBAAwB;AAErC,YAAMb,eAAkC;QAAE,GAAG1pB;;AAE7CmD,YAAMoR,YAAW,EAAGgQ,KAAKxjB,QAAQiT,SAAO;AACtCwW,4BAAoBd,cAAc1V,IAAInQ,IAAIymB,eAAe,MAAMnnB,KAAK;MACtE,CAAC;AAED,aAAOumB;IACT,CAAC;AA4DHvmB,UAAMsnB,yBAAyB,MAAMtnB,MAAM+V,gBAAe;AAC1D/V,UAAMunB,sBAAsBtpB,KAC1B,MAAM,CAAC+B,MAAM0D,SAAQ,EAAG6iB,cAAcvmB,MAAM+V,gBAAe,CAAE,GAC7D,CAACwQ,cAAciB,aAAa;AAC1B,UAAI,CAACvW,OAAOE,KAAKoV,YAAY,EAAEvoB,QAAQ;AACrC,eAAO;UACLojB,MAAM,CAAA;UACNxQ,UAAU,CAAA;UACVY,UAAU,CAAA;;MAEd;AAEA,aAAOiW,aAAaznB,OAAOwnB,QAAQ;IACrC,GACA;MACE9qB,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;MAAU;IACjE,CACF;AAEA3gB,UAAM0nB,8BAA8BzpB,KAClC,MAAM,CAAC+B,MAAM0D,SAAQ,EAAG6iB,cAAcvmB,MAAMkZ,oBAAmB,CAAE,GACjE,CAACqN,cAAciB,aAAa;AAC1B,UAAI,CAACvW,OAAOE,KAAKoV,YAAY,EAAEvoB,QAAQ;AACrC,eAAO;UACLojB,MAAM,CAAA;UACNxQ,UAAU,CAAA;UACVY,UAAU,CAAA;;MAEd;AAEA,aAAOiW,aAAaznB,OAAOwnB,QAAQ;IACrC,GACA;MACE9qB,KACE0E;MAEF5C,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQ+e;MAAU;IACjE,CACF;AAEA3gB,UAAM2nB,6BAA6B1pB,KACjC,MAAM,CAAC+B,MAAM0D,SAAQ,EAAG6iB,cAAcvmB,MAAM2R,kBAAiB,CAAE,GAC/D,CAAC4U,cAAciB,aAAa;AAC1B,UAAI,CAACvW,OAAOE,KAAKoV,YAAY,EAAEvoB,QAAQ;AACrC,eAAO;UACLojB,MAAM,CAAA;UACNxQ,UAAU,CAAA;UACVY,UAAU,CAAA;;MAEd;AAEA,aAAOiW,aAAaznB,OAAOwnB,QAAQ;IACrC,GACA;MACE9qB,KACE0E;MACF5C,OAAOA,MAAA;AAAA,YAAAuG;AAAA,gBAAAA,yBAAM/E,MAAM4B,QAAQC,aAAQkD,OAAAA,yBAAI/E,MAAM4B,QAAQ+e;MAAU;IACjE,CACF;AAkBA3gB,UAAM+mB,uBAAuB,MAAM;AACjC,YAAMC,qBAAqBhnB,MAAMkZ,oBAAmB,EAAGtI;AACvD,YAAM;QAAE2V;MAAa,IAAIvmB,MAAM0D,SAAQ;AAEvC,UAAIkkB,oBAAoBvjB,QACtB2iB,mBAAmBhpB,UAAUiT,OAAOE,KAAKoV,YAAY,EAAEvoB,MACzD;AAEA,UAAI4pB,mBAAmB;AACrB,YACEZ,mBAAmBnoB,KACjBgS,SAAOA,IAAIoW,aAAY,KAAM,CAACV,aAAa1V,IAAInQ,EAAE,CACnD,GACA;AACAknB,8BAAoB;QACtB;MACF;AAEA,aAAOA;;AAGT5nB,UAAMonB,2BAA2B,MAAM;AACrC,YAAMS,qBAAqB7nB,MACxBghB,sBAAqB,EACrBpQ,SAASxM,OAAOyM,SAAOA,IAAIoW,aAAY,CAAE;AAC5C,YAAM;QAAEV;MAAa,IAAIvmB,MAAM0D,SAAQ;AAEvC,UAAIokB,wBAAwB,CAAC,CAACD,mBAAmB7pB;AAEjD,UACE8pB,yBACAD,mBAAmBhpB,KAAKgS,SAAO,CAAC0V,aAAa1V,IAAInQ,EAAE,CAAC,GACpD;AACAonB,gCAAwB;MAC1B;AAEA,aAAOA;;AAGT9nB,UAAM+nB,wBAAwB,MAAM;AAAA,UAAAC;AAClC,YAAMC,gBAAgBhX,OAAOE,MAAI6W,wBAC/BhoB,MAAM0D,SAAQ,EAAG6iB,iBAAYyB,OAAAA,wBAAI,CAAA,CACnC,EAAEhqB;AACF,aACEiqB,gBAAgB,KAChBA,gBAAgBjoB,MAAMkZ,oBAAmB,EAAGtI,SAAS5S;;AAIzDgC,UAAMkoB,4BAA4B,MAAM;AACtC,YAAML,qBAAqB7nB,MAAMghB,sBAAqB,EAAGpQ;AACzD,aAAO5Q,MAAMonB,yBAAwB,IACjC,QACAS,mBACGzjB,OAAOyM,SAAOA,IAAIoW,aAAY,CAAE,EAChCpoB,KAAK9B,OAAKA,EAAEorB,cAAa,KAAMprB,EAAEqrB,kBAAiB,CAAE;;AAG7DpoB,UAAMqoB,kCAAkC,MAAM;AAC5C,aAAQrc,OAAe;AACrBhM,cAAM8mB,sBACF9a,EAAiBsc,OAA4BC,OACjD;;;AAIJvoB,UAAMwoB,sCAAsC,MAAM;AAChD,aAAQxc,OAAe;AACrBhM,cAAMknB,0BACFlb,EAAiBsc,OAA4BC,OACjD;;;;EAKNzW,WAAWA,CACTjB,KACA7Q,UACS;AACT6Q,QAAI4X,iBAAiB,CAAC3S,OAAO1X,SAAS;AACpC,YAAMsqB,aAAa7X,IAAIsX,cAAa;AAEpCnoB,YAAM4mB,gBAAgB/pB,SAAO;AAAA,YAAA8rB;AAC3B7S,gBAAQ,OAAOA,UAAU,cAAcA,QAAQ,CAAC4S;AAEhD,YAAI7X,IAAIoW,aAAY,KAAMyB,eAAe5S,OAAO;AAC9C,iBAAOjZ;QACT;AAEA,cAAM+rB,iBAAiB;UAAE,GAAG/rB;;AAE5BwqB,4BACEuB,gBACA/X,IAAInQ,IACJoV,QAAK6S,uBACLvqB,QAAAA,OAAAA,SAAAA,KAAMyqB,mBAAc,OAAAF,uBAAI,MACxB3oB,KACF;AAEA,eAAO4oB;MACT,CAAC;;AAEH/X,QAAIsX,gBAAgB,MAAM;AACxB,YAAM;QAAE5B;MAAa,IAAIvmB,MAAM0D,SAAQ;AACvC,aAAOolB,cAAcjY,KAAK0V,YAAY;;AAGxC1V,QAAIuX,oBAAoB,MAAM;AAC5B,YAAM;QAAE7B;MAAa,IAAIvmB,MAAM0D,SAAQ;AACvC,aAAOqlB,iBAAiBlY,KAAK0V,YAAmB,MAAM;;AAGxD1V,QAAImY,0BAA0B,MAAM;AAClC,YAAM;QAAEzC;MAAa,IAAIvmB,MAAM0D,SAAQ;AACvC,aAAOqlB,iBAAiBlY,KAAK0V,YAAmB,MAAM;;AAGxD1V,QAAIoW,eAAe,MAAM;AAAA,UAAA5b;AACvB,UAAI,OAAOrL,MAAM4B,QAAQ6kB,uBAAuB,YAAY;AAC1D,eAAOzmB,MAAM4B,QAAQ6kB,mBAAmB5V,GAAG;MAC7C;AAEA,cAAAxF,wBAAOrL,MAAM4B,QAAQ6kB,uBAAkB,OAAApb,wBAAI;;AAG7CwF,QAAIoY,sBAAsB,MAAM;AAAA,UAAA1S;AAC9B,UAAI,OAAOvW,MAAM4B,QAAQ+kB,0BAA0B,YAAY;AAC7D,eAAO3mB,MAAM4B,QAAQ+kB,sBAAsB9V,GAAG;MAChD;AAEA,cAAA0F,yBAAOvW,MAAM4B,QAAQ+kB,0BAAqB,OAAApQ,yBAAI;;AAGhD1F,QAAIqY,oBAAoB,MAAM;AAAA,UAAArS;AAC5B,UAAI,OAAO7W,MAAM4B,QAAQ8kB,4BAA4B,YAAY;AAC/D,eAAO1mB,MAAM4B,QAAQ8kB,wBAAwB7V,GAAG;MAClD;AAEA,cAAAgG,yBAAO7W,MAAM4B,QAAQ8kB,4BAAuB,OAAA7P,yBAAI;;AAElDhG,QAAIsY,2BAA2B,MAAM;AACnC,YAAMC,YAAYvY,IAAIoW,aAAY;AAElC,aAAQjb,OAAe;AAAA,YAAAqd;AACrB,YAAI,CAACD;AAAW;AAChBvY,YAAI4X,gBAAcY,UACdrd,EAAiBsc,WAAnBe,OAAAA,SAAAA,QAAgDd,OAClD;;;EAGN;AACF;AAEA,IAAMlB,sBAAsBA,CAC1BuB,gBACAloB,IACAoV,OACAwT,iBACAtpB,UACG;AAAA,MAAAuS;AACH,QAAM1B,MAAM7Q,MAAM+S,OAAOrS,IAAI,IAAI;AAQjC,MAAIoV,OAAO;AACT,QAAI,CAACjF,IAAIqY,kBAAiB,GAAI;AAC5BjY,aAAOE,KAAKyX,cAAc,EAAEhrB,QAAQlB,SAAO,OAAOksB,eAAelsB,GAAG,CAAC;IACvE;AACA,QAAImU,IAAIoW,aAAY,GAAI;AACtB2B,qBAAeloB,EAAE,IAAI;IACvB;EACF,OAAO;AACL,WAAOkoB,eAAeloB,EAAE;EAC1B;AAGA,MAAI4oB,oBAAe/W,eAAI1B,IAAI6B,YAAO,QAAXH,aAAavU,UAAU6S,IAAIoY,oBAAmB,GAAI;AACvEpY,QAAI6B,QAAQ9U,QAAQiT,CAAAA,SAClBwW,oBAAoBuB,gBAAgB/X,KAAInQ,IAAIoV,OAAOwT,iBAAiBtpB,KAAK,CAC3E;EACF;AACF;AAEO,SAASynB,aACdznB,OACAwnB,UACiB;AACjB,QAAMjB,eAAevmB,MAAM0D,SAAQ,EAAG6iB;AAEtC,QAAMgD,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AAGxD,QAAMC,cAAc,SAACrI,MAAoBlhB,OAA4B;AACnE,WAAOkhB,KACJje,IAAI0N,SAAO;AAAA,UAAA6Y;AACV,YAAMhB,aAAaI,cAAcjY,KAAK0V,YAAY;AAElD,UAAImC,YAAY;AACda,4BAAoBzrB,KAAK+S,GAAG;AAC5B2Y,4BAAoB3Y,IAAInQ,EAAE,IAAImQ;MAChC;AAEA,WAAA6Y,gBAAI7Y,IAAI6B,YAAJgX,QAAAA,cAAa1rB,QAAQ;AACvB6S,cAAM;UACJ,GAAGA;UACH6B,SAAS+W,YAAY5Y,IAAI6B,OAAkB;;MAE/C;AAEA,UAAIgW,YAAY;AACd,eAAO7X;MACT;IACF,CAAC,EACAzM,OAAOC,OAAO;;AAGnB,SAAO;IACL+c,MAAMqI,YAAYjC,SAASpG,IAAI;IAC/BxQ,UAAU2Y;IACV/X,UAAUgY;;AAEd;AAEO,SAASV,cACdjY,KACA8Y,WACS;AAAA,MAAAC;AACT,UAAAA,oBAAOD,UAAU9Y,IAAInQ,EAAE,MAAC,OAAAkpB,oBAAI;AAC9B;AAEO,SAASb,iBACdlY,KACA8Y,WACA3pB,OAC0B;AAAA,MAAA6pB;AAC1B,MAAI,GAAAA,gBAAChZ,IAAI6B,YAAJmX,QAAAA,cAAa7rB;AAAQ,WAAO;AAEjC,MAAI8rB,sBAAsB;AAC1B,MAAIC,eAAe;AAEnBlZ,MAAI6B,QAAQ9U,QAAQosB,YAAU;AAE5B,QAAID,gBAAgB,CAACD,qBAAqB;AACxC;IACF;AAEA,QAAIE,OAAO/C,aAAY,GAAI;AACzB,UAAI6B,cAAckB,QAAQL,SAAS,GAAG;AACpCI,uBAAe;MACjB,OAAO;AACLD,8BAAsB;MACxB;IACF;AAGA,QAAIE,OAAOtX,WAAWsX,OAAOtX,QAAQ1U,QAAQ;AAC3C,YAAMisB,yBAAyBlB,iBAAiBiB,QAAQL,SAAgB;AACxE,UAAIM,2BAA2B,OAAO;AACpCF,uBAAe;MACjB,WAAWE,2BAA2B,QAAQ;AAC5CF,uBAAe;AACfD,8BAAsB;MACxB,OAAO;AACLA,8BAAsB;MACxB;IACF;EACF,CAAC;AAED,SAAOA,sBAAsB,QAAQC,eAAe,SAAS;AAC/D;AC9pBO,IAAMG,sBAAsB;AAEnC,IAAMC,eAA+BA,CAACC,MAAMC,MAAMnmB,aAAa;AAC7D,SAAOomB,oBACL9W,SAAS4W,KAAK7W,SAASrP,QAAQ,CAAC,EAAEoP,YAAW,GAC7CE,SAAS6W,KAAK9W,SAASrP,QAAQ,CAAC,EAAEoP,YAAW,CAC/C;AACF;AAEA,IAAMiX,4BAA4CA,CAACH,MAAMC,MAAMnmB,aAAa;AAC1E,SAAOomB,oBACL9W,SAAS4W,KAAK7W,SAASrP,QAAQ,CAAC,GAChCsP,SAAS6W,KAAK9W,SAASrP,QAAQ,CAAC,CAClC;AACF;AAIA,IAAMsmB,OAAuBA,CAACJ,MAAMC,MAAMnmB,aAAa;AACrD,SAAOumB,aACLjX,SAAS4W,KAAK7W,SAASrP,QAAQ,CAAC,EAAEoP,YAAW,GAC7CE,SAAS6W,KAAK9W,SAASrP,QAAQ,CAAC,EAAEoP,YAAW,CAC/C;AACF;AAIA,IAAMoX,oBAAoCA,CAACN,MAAMC,MAAMnmB,aAAa;AAClE,SAAOumB,aACLjX,SAAS4W,KAAK7W,SAASrP,QAAQ,CAAC,GAChCsP,SAAS6W,KAAK9W,SAASrP,QAAQ,CAAC,CAClC;AACF;AAEA,IAAMymB,WAA2BA,CAACP,MAAMC,MAAMnmB,aAAa;AACzD,QAAMsW,IAAI4P,KAAK7W,SAAerP,QAAQ;AACtC,QAAMuW,IAAI4P,KAAK9W,SAAerP,QAAQ;AAKtC,SAAOsW,IAAIC,IAAI,IAAID,IAAIC,IAAI,KAAK;AAClC;AAEA,IAAMmQ,QAAwBA,CAACR,MAAMC,MAAMnmB,aAAa;AACtD,SAAOumB,aAAaL,KAAK7W,SAASrP,QAAQ,GAAGmmB,KAAK9W,SAASrP,QAAQ,CAAC;AACtE;AAIA,SAASumB,aAAajQ,GAAQC,GAAQ;AACpC,SAAOD,MAAMC,IAAI,IAAID,IAAIC,IAAI,IAAI;AACnC;AAEA,SAASjH,SAASgH,GAAQ;AACxB,MAAI,OAAOA,MAAM,UAAU;AACzB,QAAIzF,MAAMyF,CAAC,KAAKA,MAAMxF,YAAYwF,MAAM,WAAW;AACjD,aAAO;IACT;AACA,WAAO9a,OAAO8a,CAAC;EACjB;AACA,MAAI,OAAOA,MAAM,UAAU;AACzB,WAAOA;EACT;AACA,SAAO;AACT;AAKA,SAAS8P,oBAAoBO,MAAcC,MAAc;AAGvD,QAAMtQ,IAAIqQ,KAAK5pB,MAAMipB,mBAAmB,EAAE9lB,OAAOC,OAAO;AACxD,QAAMoW,IAAIqQ,KAAK7pB,MAAMipB,mBAAmB,EAAE9lB,OAAOC,OAAO;AAGxD,SAAOmW,EAAExc,UAAUyc,EAAEzc,QAAQ;AAC3B,UAAM+sB,KAAKvQ,EAAE8D,MAAK;AAClB,UAAM0M,KAAKvQ,EAAE6D,MAAK;AAElB,UAAM2M,KAAKC,SAASH,IAAI,EAAE;AAC1B,UAAMI,KAAKD,SAASF,IAAI,EAAE;AAE1B,UAAMI,QAAQ,CAACH,IAAIE,EAAE,EAAE5Q,KAAI;AAG3B,QAAIxF,MAAMqW,MAAM,CAAC,CAAE,GAAG;AACpB,UAAIL,KAAKC,IAAI;AACX,eAAO;MACT;AACA,UAAIA,KAAKD,IAAI;AACX,eAAO;MACT;AACA;IACF;AAGA,QAAIhW,MAAMqW,MAAM,CAAC,CAAE,GAAG;AACpB,aAAOrW,MAAMkW,EAAE,IAAI,KAAK;IAC1B;AAGA,QAAIA,KAAKE,IAAI;AACX,aAAO;IACT;AACA,QAAIA,KAAKF,IAAI;AACX,aAAO;IACT;EACF;AAEA,SAAOzQ,EAAExc,SAASyc,EAAEzc;AACtB;AAIO,IAAMqtB,aAAa;EACxBlB;EACAI;EACAC;EACAE;EACAC;EACAC;AACF;ACqJO,IAAMU,UAAwB;EACnC3hB,iBAAkBC,WAA6B;AAC7C,WAAO;MACL2hB,SAAS,CAAA;MACT,GAAG3hB;;;EAIPF,qBAAqBA,MAAsD;AACzE,WAAO;MACL8hB,WAAW;MACXC,eAAe;;;EAInB1hB,mBACE/J,WAC0B;AAC1B,WAAO;MACL0rB,iBAAiBjvB,iBAAiB,WAAWuD,KAAK;MAClD2rB,kBAAmB3f,OAAe;AAChC,eAAQA,EAAiB4f;MAC3B;;;EAIJ7rB,cAAcA,CACZuB,QACAtB,UACS;AACTsB,WAAOuqB,mBAAmB,MAAM;AAC9B,YAAMC,YAAY9rB,MAAMkZ,oBAAmB,EAAGtI,SAASmb,MAAM,EAAE;AAE/D,UAAIC,WAAW;AAEf,iBAAWnb,OAAOib,WAAW;AAC3B,cAAMhW,QAAQjF,OAAAA,OAAAA,SAAAA,IAAK0C,SAASjS,OAAOZ,EAAE;AAErC,YAAIuQ,OAAOoL,UAAU7I,SAAS8I,KAAKxG,KAAK,MAAM,iBAAiB;AAC7D,iBAAOuV,WAAWV;QACpB;AAEA,YAAI,OAAO7U,UAAU,UAAU;AAC7BkW,qBAAW;AAEX,cAAIlW,MAAM7U,MAAMipB,mBAAmB,EAAElsB,SAAS,GAAG;AAC/C,mBAAOqtB,WAAWlB;UACpB;QACF;MACF;AAEA,UAAI6B,UAAU;AACZ,eAAOX,WAAWb;MACpB;AAEA,aAAOa,WAAWT;;AAEpBtpB,WAAO2qB,iBAAiB,MAAM;AAC5B,YAAM/V,WAAWlW,MAAMkZ,oBAAmB,EAAGtI,SAAS,CAAC;AAEvD,YAAMkF,QAAQI,YAAAA,OAAAA,SAAAA,SAAU3C,SAASjS,OAAOZ,EAAE;AAE1C,UAAI,OAAOoV,UAAU,UAAU;AAC7B,eAAO;MACT;AAEA,aAAO;;AAETxU,WAAO4qB,eAAe,MAAM;AAAA,UAAAC,uBAAAC;AAC1B,UAAI,CAAC9qB,QAAQ;AACX,cAAM,IAAID,MAAK;MACjB;AAEA,aAAOvE,WAAWwE,OAAOrB,UAAUurB,SAAS,IACxClqB,OAAOrB,UAAUurB,YACjBlqB,OAAOrB,UAAUurB,cAAc,SAC7BlqB,OAAOuqB,iBAAgB,KAAEM,yBAAAC,yBACzBpsB,MAAM4B,QAAQypB,eAAU,OAAA,SAAxBe,uBAA2B9qB,OAAOrB,UAAUurB,SAAS,MAAWW,OAAAA,wBAChEd,WAAW/pB,OAAOrB,UAAUurB,SAAS;;AAE7ClqB,WAAO+qB,gBAAgB,CAACC,MAAMC,UAAU;AAWtC,YAAMC,mBAAmBlrB,OAAOmrB,oBAAmB;AACnD,YAAMC,iBAAiB,OAAOJ,SAAS,eAAeA,SAAS;AAE/DtsB,YAAM2sB,WAAW9vB,SAAO;AAEtB,cAAM+vB,kBAAkB/vB,OAAAA,OAAAA,SAAAA,IAAKsH,KAAKpH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AACzD,cAAMmsB,gBAAgBhwB,OAAAA,OAAAA,SAAAA,IAAK+N,UAAU7N,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AAE5D,YAAIosB,aAA2B,CAAA;AAG/B,YAAIC;AACJ,YAAIC,WAAWN,iBAAiBJ,OAAOE,qBAAqB;AAG5D,YAAI3vB,OAAG,QAAHA,IAAKmB,UAAUsD,OAAO2rB,gBAAe,KAAMV,OAAO;AACpD,cAAIK,iBAAiB;AACnBG,yBAAa;UACf,OAAO;AACLA,yBAAa;UACf;QACF,OAAO;AAEL,cAAIlwB,OAAG,QAAHA,IAAKmB,UAAU6uB,kBAAkBhwB,IAAImB,SAAS,GAAG;AACnD+uB,yBAAa;qBACJH,iBAAiB;AAC1BG,yBAAa;UACf,OAAO;AACLA,yBAAa;UACf;QACF;AAGA,YAAIA,eAAe,UAAU;AAE3B,cAAI,CAACL,gBAAgB;AAEnB,gBAAI,CAACF,kBAAkB;AACrBO,2BAAa;YACf;UACF;QACF;AAEA,YAAIA,eAAe,OAAO;AAAA,cAAAG;AACxBJ,uBAAa,CACX,GAAGjwB,KACH;YACE6D,IAAIY,OAAOZ;YACX4rB,MAAMU;UACR,CAAC;AAGHF,qBAAWtO,OACT,GACAsO,WAAW9uB,WAAMkvB,wBACdltB,MAAM4B,QAAQurB,yBAAoBD,OAAAA,wBAAIlkB,OAAOC,iBAClD;QACF,WAAW8jB,eAAe,UAAU;AAElCD,uBAAajwB,IAAIsG,IAAIpG,OAAK;AACxB,gBAAIA,EAAE2D,OAAOY,OAAOZ,IAAI;AACtB,qBAAO;gBACL,GAAG3D;gBACHuvB,MAAMU;;YAEV;AACA,mBAAOjwB;UACT,CAAC;QACH,WAAWgwB,eAAe,UAAU;AAClCD,uBAAajwB,IAAIuH,OAAOrH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;QACjD,OAAO;AACLosB,uBAAa,CACX;YACEpsB,IAAIY,OAAOZ;YACX4rB,MAAMU;UACR,CAAC;QAEL;AAEA,eAAOF;MACT,CAAC;;AAGHxrB,WAAO8rB,kBAAkB,MAAM;AAAA,UAAAhtB,MAAAitB;AAC7B,YAAMC,iBAAaltB,QAAAitB,wBACjB/rB,OAAOrB,UAAUqtB,kBAAa,OAAAD,wBAC9BrtB,MAAM4B,QAAQ0rB,kBAAa,OAAAltB,OAC3BkB,OAAO2qB,eAAc,MAAO;AAC9B,aAAOqB,gBAAgB,SAAS;;AAGlChsB,WAAOmrB,sBAAuBF,WAAoB;AAAA,UAAAlhB,uBAAAkL;AAChD,YAAMgX,qBAAqBjsB,OAAO8rB,gBAAe;AACjD,YAAMI,WAAWlsB,OAAOmsB,YAAW;AAEnC,UAAI,CAACD,UAAU;AACb,eAAOD;MACT;AAEA,UACEC,aAAaD,wBAAkBliB,wBAC9BrL,MAAM4B,QAAQ8rB,yBAAoB,OAAAriB,wBAAI;OACtCkhB,SAAKhW,yBAAGvW,MAAM4B,QAAQ+rB,sBAAiB,OAAApX,yBAAI,OAAO,OACnD;AACA,eAAO;MACT;AACA,aAAOiX,aAAa,SAAS,QAAQ;;AAGvClsB,WAAOssB,aAAa,MAAM;AAAA,UAAAxiB,uBAAAyL;AACxB,eACEzL,wBAAC9J,OAAOrB,UAAU4tB,kBAAaziB,OAAAA,wBAAI,WAAIyL,yBACtC7W,MAAM4B,QAAQisB,kBAAa,OAAAhX,yBAAI,SAChC,CAAC,CAACvV,OAAOR;;AAIbQ,WAAO2rB,kBAAkB,MAAM;AAAA,UAAAjiB,OAAA4L;AAC7B,cAAA5L,SAAA4L,yBACEtV,OAAOrB,UAAU6tB,oBAAe,OAAAlX,yBAChC5W,MAAM4B,QAAQksB,oBAAe9iB,OAAAA,QAC7B,CAAC,CAAC1J,OAAOR;;AAIbQ,WAAOmsB,cAAc,MAAM;AAAA,UAAAM;AACzB,YAAMC,cAAUD,wBAAG/tB,MAAM0D,SAAQ,EAAG6nB,YAAO,OAAA,SAAxBwC,sBAA0B5pB,KAAKpH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE;AAEzE,aAAO,CAACstB,aAAa,QAAQA,WAAW1B,OAAO,SAAS;;AAG1DhrB,WAAO2sB,eAAe,MAAA;AAAA,UAAAC,wBAAAC;AAAA,cAAAD,0BAAAC,yBACpBnuB,MAAM0D,SAAQ,EAAG6nB,YAAO,OAAA,SAAxB4C,uBAA0BvjB,UAAU7N,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,MAAC,OAAAwtB,yBAAI;IAAE;AAEpE5sB,WAAO8sB,eAAe,MAAM;AAE1BpuB,YAAM2sB,WAAW9vB,SACfA,OAAG,QAAHA,IAAKmB,SAASnB,IAAIuH,OAAOrH,OAAKA,EAAE2D,OAAOY,OAAOZ,EAAE,IAAI,CAAA,CACtD;;AAGFY,WAAO+sB,0BAA0B,MAAM;AACrC,YAAMC,UAAUhtB,OAAOssB,WAAU;AAEjC,aAAQ5hB,OAAe;AACrB,YAAI,CAACsiB;AAAS;AACZtiB,UAAUC,WAAO,QAAjBD,EAAUC,QAAO;AACnB3K,eAAO+qB,iBAAP/qB,QAAAA,OAAO+qB,cACLzrB,QACAU,OAAO2rB,gBAAe,IAAKjtB,MAAM4B,QAAQ+pB,oBAAgB,OAAA,SAA9B3rB,MAAM4B,QAAQ+pB,iBAAmB3f,CAAC,IAAI,KACnE;;;;EAKN1I,aAAqCtD,WAA8B;AACjEA,UAAM2sB,aAAarwB,aAAW0D,MAAM4B,QAAQ8pB,mBAAe,OAAA,SAA7B1rB,MAAM4B,QAAQ8pB,gBAAkBpvB,OAAO;AACrE0D,UAAMuuB,eAAetgB,kBAAgB;AAAA,UAAAugB,uBAAA/d;AACnCzQ,YAAM2sB,WAAW1e,eAAe,CAAA,KAAEugB,yBAAA/d,sBAAGzQ,MAAMmO,iBAAY,OAAA,SAAlBsC,oBAAoB8a,YAAOiD,OAAAA,wBAAI,CAAA,CAAE;;AAExExuB,UAAMyuB,uBAAuB,MAAMzuB,MAAM6c,mBAAkB;AAC3D7c,UAAM2R,oBAAoB,MAAM;AAC9B,UAAI,CAAC3R,MAAM0uB,sBAAsB1uB,MAAM4B,QAAQ+P,mBAAmB;AAChE3R,cAAM0uB,qBAAqB1uB,MAAM4B,QAAQ+P,kBAAkB3R,KAAK;MAClE;AAEA,UAAIA,MAAM4B,QAAQ+sB,iBAAiB,CAAC3uB,MAAM0uB,oBAAoB;AAC5D,eAAO1uB,MAAMyuB,qBAAoB;MACnC;AAEA,aAAOzuB,MAAM0uB,mBAAkB;;EAEnC;AACF;ACzYO,IAAME,aAA2B;EACtCjlB,iBAAkBC,WAAgC;AAChD,WAAO;MACLilB,kBAAkB,CAAA;MAClB,GAAGjlB;;;EAIPG,mBACE/J,WAC6B;AAC7B,WAAO;MACL8uB,0BAA0BryB,iBAAiB,oBAAoBuD,KAAK;;;EAIxED,cAAcA,CACZuB,QACAtB,UACS;AACTsB,WAAOytB,mBAAmBjZ,WAAS;AACjC,UAAIxU,OAAO0tB,WAAU,GAAI;AACvBhvB,cAAMivB,oBAAoBpyB,UAAQ;UAChC,GAAGA;UACH,CAACyE,OAAOZ,EAAE,GAAGoV,SAAK,OAALA,QAAS,CAACxU,OAAOuG,aAAY;QAC5C,EAAE;MACJ;;AAEFvG,WAAOuG,eAAe,MAAM;AAAA,UAAAuP,uBAAAC;AAC1B,cAAAD,yBAAAC,yBAAOrX,MAAM0D,SAAQ,EAAGmrB,qBAAjBxX,OAAAA,SAAAA,uBAAoC/V,OAAOZ,EAAE,MAAC,OAAA0W,wBAAI;;AAG3D9V,WAAO0tB,aAAa,MAAM;AAAA,UAAA5jB,uBAAAC;AACxB,eACED,wBAAC9J,OAAOrB,UAAUivB,iBAAY9jB,OAAAA,wBAAI,WAAIC,wBACrCrL,MAAM4B,QAAQstB,iBAAY7jB,OAAAA,wBAAI;;AAGnC/J,WAAO6tB,6BAA6B,MAAM;AACxC,aAAQnjB,OAAe;AACrB1K,eAAOytB,oBAAPztB,QAAAA,OAAOytB,iBACH/iB,EAAiBsc,OAA4BC,OACjD;;;;EAKNzW,WAAWA,CACTjB,KACA7Q,UACS;AACT6Q,QAAI0T,sBAAsBtmB,KACxB,MAAM,CAAC4S,IAAIue,YAAW,GAAIpvB,MAAM0D,SAAQ,EAAGmrB,gBAAgB,GAC3DjK,WAAS;AACP,aAAOA,MAAMxgB,OAAOkZ,UAAQA,KAAKhc,OAAOuG,aAAY,CAAE;IACxD,GACA;MACEnL,KAAK0E;MACL5C,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ8iB;MAAS;IAChE,CACF;AACA7T,QAAIwe,kBAAkBpxB,KACpB,MAAM,CACJ4S,IAAI8T,oBAAmB,GACvB9T,IAAIyT,sBAAqB,GACzBzT,IAAIgU,qBAAoB,CAAE,GAE5B,CAACjhB,MAAMmD,QAAQlD,UAAU,CAAC,GAAGD,MAAM,GAAGmD,QAAQ,GAAGlD,KAAK,GACtD;MACEnH,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQ8iB;MAAS;IAChE,CACF;;EAGFphB,aAAqCtD,WAA8B;AACjE,UAAMsvB,2BAA2BA,CAC/B5yB,KACA6yB,eACqC;AACrC,aAAOtxB,KACL,MAAM,CACJsxB,WAAU,GACVA,WAAU,EACPnrB,OAAOrH,OAAKA,EAAE8K,aAAY,CAAE,EAC5B1E,IAAIpG,OAAKA,EAAE2D,EAAE,EACbsH,KAAK,GAAG,CAAC,GAEdzG,aAAW;AACT,eAAOA,QAAQ6C,OAAOrH,OAAKA,EAAE8K,gBAAY,OAAA,SAAd9K,EAAE8K,aAAY,CAAI;MAC/C,GACA;QACEnL;QACA8B,OAAOA,MAAA;AAAA,cAAAuG;AAAA,kBAAAA,yBAAM/E,MAAM4B,QAAQC,aAAQkD,OAAAA,yBAAI/E,MAAM4B,QAAQE;QAAY;MACnE,CACF;;AAGF9B,UAAMwvB,wBAAwBF,yBAC5B,yBACA,MAAMtvB,MAAMyvB,kBAAiB,CAC/B;AACAzvB,UAAMyD,wBAAwB6rB,yBAC5B,yBACA,MAAMtvB,MAAM4Y,kBAAiB,CAC/B;AACA5Y,UAAM0K,4BAA4B4kB,yBAChC,6BACA,MAAMtvB,MAAMolB,mBAAkB,CAChC;AACAplB,UAAM2K,6BAA6B2kB,yBACjC,8BACA,MAAMtvB,MAAMqlB,oBAAmB,CACjC;AACArlB,UAAM0vB,8BAA8BJ,yBAClC,+BACA,MAAMtvB,MAAMslB,qBAAoB,CAClC;AAEAtlB,UAAMivB,sBAAsB3yB,aAC1B0D,MAAM4B,QAAQktB,4BAAwB,OAAA,SAAtC9uB,MAAM4B,QAAQktB,yBAA2BxyB,OAAO;AAElD0D,UAAM2vB,wBAAwB1hB,kBAAgB;AAAA,UAAAC;AAC5ClO,YAAMivB,oBACJhhB,eAAe,CAAA,KAAEC,wBAAGlO,MAAMmO,aAAa0gB,qBAAgB,OAAA3gB,wBAAI,CAAA,CAC7D;;AAGFlO,UAAM4vB,0BAA0B9Z,WAAS;AAAA,UAAA+Z;AACvC/Z,eAAK+Z,SAAG/Z,UAAK+Z,OAAAA,SAAI,CAAC7vB,MAAM8vB,uBAAsB;AAE9C9vB,YAAMivB,oBACJjvB,MAAM4Y,kBAAiB,EAAGnK,OACxB,CAACshB,KAAKzuB,YAAY;QAChB,GAAGyuB;QACH,CAACzuB,OAAOZ,EAAE,GAAG,CAACoV,QAAQ,EAACxU,OAAO0tB,cAAP1tB,QAAAA,OAAO0tB,WAAU,KAAOlZ;MACjD,IACA,CAAA,CACF,CACF;;AAGF9V,UAAM8vB,yBAAyB,MAC7B,CAAC9vB,MAAM4Y,kBAAiB,EAAG/Z,KAAKyC,YAAU,EAACA,OAAOuG,gBAAPvG,QAAAA,OAAOuG,aAAY,EAAK;AAErE7H,UAAMgwB,0BAA0B,MAC9BhwB,MAAM4Y,kBAAiB,EAAG/Z,KAAKyC,YAAUA,OAAOuG,gBAAY,OAAA,SAAnBvG,OAAOuG,aAAY,CAAI;AAElE7H,UAAMiwB,uCAAuC,MAAM;AACjD,aAAQjkB,OAAe;AAAA,YAAAqd;AACrBrpB,cAAM4vB,yBAAuBvG,UACzBrd,EAAiBsc,WAAnBe,OAAAA,SAAAA,QAAgDd,OAClD;;;EAGN;AACF;AC/PA,IAAM2H,WAAW,CACf7sB,SACAurB,YACA/Q,UACA4D,SACAtM,SACAmW,SACAtQ,UACAxL,WACAsP,YACAwH,cACA7c,YAAY;AAuNP,SAASnG,YACd1B,SACc;AAAA,MAAAuuB;AACd,MAAIvuB,QAAQC,YAAYD,QAAQ+e,YAAY;AAC1ChhB,YAAQC,KAAK,4BAA4B;EAC3C;AAEA,MAAII,QAAQ;IAAEsC,WAAW4tB;;AAEzB,QAAME,iBAAiBpwB,MAAMsC,UAAUmM,OAAO,CAACshB,KAAK1tB,YAAY;AAC9D,WAAO4O,OAAOof,OAAON,KAAK1tB,QAAQ0H,qBAAiB,OAAA,SAAzB1H,QAAQ0H,kBAAoB/J,KAAK,CAAC;KAC3D,CAAA,CAAE;AAEL,QAAMswB,eAAgB1uB,CAAAA,aAAyC;AAC7D,QAAI5B,MAAM4B,QAAQ0uB,cAAc;AAC9B,aAAOtwB,MAAM4B,QAAQ0uB,aAAaF,gBAAgBxuB,QAAO;IAC3D;AAEA,WAAO;MACL,GAAGwuB;MACH,GAAGxuB;;;AAIP,QAAM2uB,mBAAmC,CAAA;AAEzC,MAAIpiB,eAAe;IACjB,GAAGoiB;IACH,IAAAJ,wBAAIvuB,QAAQuM,iBAAYgiB,OAAAA,wBAAI,CAAA;;AAG9BnwB,QAAMsC,UAAU1E,QAAQyE,aAAW;AAAA,QAAAmuB;AACjCriB,oBAAYqiB,wBAAGnuB,QAAQsH,mBAARtH,OAAAA,SAAAA,QAAQsH,gBAAkBwE,YAAY,MAACqiB,OAAAA,wBAAIriB;EAC5D,CAAC;AAED,QAAM0B,SAAyB,CAAA;AAC/B,MAAI4gB,gBAAgB;AAEpB,QAAMC,eAAoC;IACxCpuB,WAAW4tB;IACXtuB,SAAS;MACP,GAAGwuB;MACH,GAAGxuB;;IAELuM;IACA6B,QAAQ2gB,QAAM;AACZ9gB,aAAO/R,KAAK6yB,EAAE;AAEd,UAAI,CAACF,eAAe;AAClBA,wBAAgB;AAIhBG,gBAAQC,QAAO,EACZC,KAAK,MAAM;AACV,iBAAOjhB,OAAO7R,QAAQ;AACpB6R,mBAAOyO,MAAK,EAAE;UAChB;AACAmS,0BAAgB;SACjB,EACAM,MAAMC,WACLC,WAAW,MAAM;AACf,gBAAMD;QACR,CAAC,CACH;MACJ;;IAEFE,OAAOA,MAAM;AACXlxB,YAAMpD,SAASoD,MAAMmO,YAAY;;IAEnCgjB,YAAY70B,aAAW;AACrB,YAAM80B,aAAa/0B,iBAAiBC,SAAS0D,MAAM4B,OAAO;AAC1D5B,YAAM4B,UAAU0uB,aAAac,UAAU;;IAMzC1tB,UAAUA,MAAM;AACd,aAAO1D,MAAM4B,QAAQgI;;IAGvBhN,UAAWN,aAAiC;AAC1C0D,YAAM4B,QAAQyvB,iBAAdrxB,QAAAA,MAAM4B,QAAQyvB,cAAgB/0B,OAAO;;IAGvCg1B,WAAWA,CAACzgB,KAAY9R,OAAeoB,WAAmB;AAAA,UAAAmS;AAAA,cAAAA,wBACxDtS,MAAM4B,QAAQ2vB,YAAdvxB,OAAAA,SAAAA,MAAM4B,QAAQ2vB,SAAW1gB,KAAK9R,OAAOoB,MAAM,MAACmS,OAAAA,wBAC3C,GAAEnS,SAAS,CAACA,OAAOO,IAAI3B,KAAK,EAAEiJ,KAAK,GAAG,IAAIjJ,KAAM;IAAC;IAEpDgX,iBAAiBA,MAAM;AACrB,UAAI,CAAC/V,MAAMwxB,kBAAkB;AAC3BxxB,cAAMwxB,mBAAmBxxB,MAAM4B,QAAQmU,gBAAgB/V,KAAK;MAC9D;AAEA,aAAOA,MAAMwxB,iBAAgB;;;;IAM/BpgB,aAAaA,MAAM;AACjB,aAAOpR,MAAMghB,sBAAqB;;;IAGpCjO,QAAQA,CAACrS,IAAY+wB,cAAwB;AAC3C,UAAI5gB,OACF4gB,YAAYzxB,MAAM2Q,yBAAwB,IAAK3Q,MAAMoR,YAAW,GAChEI,SAAS9Q,EAAE;AAEb,UAAI,CAACmQ,KAAK;AACRA,cAAM7Q,MAAM+V,gBAAe,EAAGvE,SAAS9Q,EAAE;AACzC,YAAI,CAACmQ,KAAK;AACR,cAAIzP,MAAuC;AACzC,kBAAM,IAAIC,MAAO,sCAAqCX,EAAG,EAAC;UAC5D;AACA,gBAAM,IAAIW,MAAK;QACjB;MACF;AAEA,aAAOwP;;IAETtQ,sBAAsBtC,KACpB,MAAM,CAAC+B,MAAM4B,QAAQtB,aAAa,GAClCA,mBAAiB;AAAA,UAAAoxB;AACfpxB,uBAAaoxB,iBAAIpxB,kBAAa,OAAAoxB,iBAAI,CAAA;AAIlC,aAAO;QACL7wB,QAAQqa,WAAS;AACf,gBAAM1a,oBAAoB0a,MAAMra,OAAOS,OACpCrB;AAEH,cAAIO,kBAAkBC,aAAa;AACjC,mBAAOD,kBAAkBC;UAC3B;AAEA,cAAID,kBAAkBM,YAAY;AAChC,mBAAON,kBAAkBE;UAC3B;AAEA,iBAAO;;;QAGT4c,MAAMpC,WAAK;AAAA,cAAAyW,uBAAAC;AAAA,kBAAAD,yBAAAC,qBAAI1W,MAAM2W,YAAW,MAAjBD,QAAAA,mBAA0Bpe,YAAQ,OAAA,SAAlCoe,mBAA0Bpe,SAAQ,MAAI,OAAAme,wBAAI;QAAI;QAC7D,GAAG3xB,MAAMsC,UAAUmM,OAAO,CAACshB,KAAK1tB,YAAY;AAC1C,iBAAO4O,OAAOof,OAAON,KAAK1tB,QAAQqH,uBAAmB,OAAA,SAA3BrH,QAAQqH,oBAAmB,CAAI;WACxD,CAAA,CAAE;QACL,GAAGpJ;;IAEP,GACA;MACE9B,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQE;MAAY;MACjEpF,KAA+C;IACjD,CACF;IAEAo1B,gBAAgBA,MAAM9xB,MAAM4B,QAAQL;IAEpCiC,eAAevF,KACb,MAAM,CAAC+B,MAAM8xB,eAAc,CAAE,GAC7BC,gBAAc;AACZ,YAAMC,iBAAiB,SACrBD,aACA5xB,QACAD,OAC6B;AAAA,YAD7BA,UAAK,QAAA;AAALA,kBAAQ;QAAC;AAET,eAAO6xB,YAAW5uB,IAAIlD,eAAa;AACjC,gBAAMqB,SAASvB,aAAaC,OAAOC,WAAWC,OAAOC,MAAM;AAE3D,gBAAM8xB,oBAAoBhyB;AAK1BqB,iBAAOC,UAAU0wB,kBAAkB1wB,UAC/BywB,eAAeC,kBAAkB1wB,SAASD,QAAQpB,QAAQ,CAAC,IAC3D,CAAA;AAEJ,iBAAOoB;QACT,CAAC;;AAGH,aAAO0wB,eAAeD,UAAU;IAClC,GACA;MACEr1B,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQE;MAAY;IACnE,CACF;IAEA2tB,mBAAmBxxB,KACjB,MAAM,CAAC+B,MAAMwD,cAAa,CAAE,GAC5BM,gBAAc;AACZ,aAAOA,WAAWpC,QAAQJ,YAAU;AAClC,eAAOA,OAAOE,eAAc;MAC9B,CAAC;IACH,GACA;MACE9E,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAuG;AAAA,gBAAAA,yBAAM/E,MAAM4B,QAAQC,aAAQkD,OAAAA,yBAAI/E,MAAM4B,QAAQE;MAAY;IACnE,CACF;IAEAowB,wBAAwBj0B,KACtB,MAAM,CAAC+B,MAAMyvB,kBAAiB,CAAE,GAChC0C,iBAAe;AACb,aAAOA,YAAY1jB,OACjB,CAAC2jB,KAAK9wB,WAAW;AACf8wB,YAAI9wB,OAAOZ,EAAE,IAAIY;AACjB,eAAO8wB;SAET,CAAA,CACF;IACF,GACA;MACE11B,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA0G;AAAA,gBAAAA,yBAAMlF,MAAM4B,QAAQC,aAAQqD,OAAAA,yBAAIlF,MAAM4B,QAAQE;MAAY;IACnE,CACF;IAEA8W,mBAAmB3a,KACjB,MAAM,CAAC+B,MAAMwD,cAAa,GAAIxD,MAAMgC,mBAAkB,CAAE,GACxD,CAAC8B,YAAY7B,kBAAiB;AAC5B,UAAIE,cAAc2B,WAAWpC,QAAQJ,YAAUA,OAAOS,eAAc,CAAE;AACtE,aAAOE,cAAaE,WAAW;IACjC,GACA;MACEzF,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAA6G;AAAA,gBAAAA,yBAAMrF,MAAM4B,QAAQC,aAAQwD,OAAAA,yBAAIrF,MAAM4B,QAAQE;MAAY;IACnE,CACF;IAEAgK,WAAW5H,cAAY;AACrB,YAAM5C,SAAStB,MAAMkyB,uBAAsB,EAAGhuB,QAAQ;AAEtD,UAA6C,CAAC5C,QAAQ;AACpD3B,gBAAQqxB,MAAO,2BAA0B9sB,QAAS,mBAAkB;MACtE;AAEA,aAAO5C;IACT;;AAGF2P,SAAOof,OAAOrwB,OAAO0wB,YAAY;AAEjC,WAAS3xB,QAAQ,GAAGA,QAAQiB,MAAMsC,UAAUtE,QAAQe,SAAS;AAC3D,UAAMsD,UAAUrC,MAAMsC,UAAUvD,KAAK;AACrCsD,eAAO,QAAPA,QAASiB,eAAW,QAApBjB,QAASiB,YAActD,KAAK;EAC9B;AAEA,SAAOA;AACT;AC1dO,SAASqd,WACdrd,OACA6Q,KACAvP,QACA4C,UACqB;AACrB,QAAMmuB,iBAAiBA,MAAA;AAAA,QAAAC;AAAA,YAAAA,iBACrBhV,KAAK/J,SAAQ,MAAE+e,OAAAA,iBAAItyB,MAAM4B,QAAQ2wB;EAAmB;AAEtD,QAAMjV,OAAgC;IACpC5c,IAAK,GAAEmQ,IAAInQ,EAAG,IAAGY,OAAOZ,EAAG;IAC3BmQ;IACAvP;IACAiS,UAAUA,MAAM1C,IAAI0C,SAASrP,QAAQ;IACrC2tB,aAAaQ;IACbjvB,YAAYnF,KACV,MAAM,CAAC+B,OAAOsB,QAAQuP,KAAKyM,IAAI,GAC/B,CAACtd,QAAOsB,SAAQuP,MAAKyM,WAAU;MAC7Btd,OAAAA;MACAsB,QAAAA;MACAuP,KAAAA;MACAyM,MAAMA;MACN/J,UAAU+J,MAAK/J;MACfse,aAAavU,MAAKuU;IACpB,IACA;MACEn1B,KAA+C;MAC/C8B,OAAOA,MAAMwB,MAAM4B,QAAQC;KAE/B;;AAGF7B,QAAMsC,UAAU1E,QAAQyE,aAAW;AACjCA,YAAQgb,cAARhb,QAAAA,QAAQgb,WACNC,MACAhc,QACAuP,KACA7Q,KACF;KACC,CAAA,CAAE;AAEL,SAAOsd;AACT;ICCaxL,YAAYA,CACvB9R,OACAU,IACA0c,UACAoV,UACAtyB,OACAwS,SACAI,aACe;AACf,MAAIjC,MAAsB;IACxBnQ;IACA3B,OAAOyzB;IACPpV;IACAld;IACA4S;IACA2f,cAAc,CAAA;IACdC,oBAAoB,CAAA;IACpBnf,UAAUrP,cAAY;AACpB,UAAI2M,IAAI4hB,aAAatV,eAAejZ,QAAQ,GAAG;AAC7C,eAAO2M,IAAI4hB,aAAavuB,QAAQ;MAClC;AAEA,YAAM5C,SAAStB,MAAM8L,UAAU5H,QAAQ;AAEvC,UAAI,EAAC5C,UAAM,QAANA,OAAQR,aAAY;AACvB,eAAOF;MACT;AAEAiQ,UAAI4hB,aAAavuB,QAAQ,IAAI5C,OAAOR,WAClC+P,IAAIuM,UACJoV,QACF;AAEA,aAAO3hB,IAAI4hB,aAAavuB,QAAQ;;IAElCyuB,iBAAiBzuB,cAAY;AAC3B,UAAI2M,IAAI6hB,mBAAmBvV,eAAejZ,QAAQ,GAAG;AACnD,eAAO2M,IAAI6hB,mBAAmBxuB,QAAQ;MACxC;AAEA,YAAM5C,SAAStB,MAAM8L,UAAU5H,QAAQ;AAEvC,UAAI,EAAC5C,UAAM,QAANA,OAAQR,aAAY;AACvB,eAAOF;MACT;AAEA,UAAI,CAACU,OAAOrB,UAAU0yB,iBAAiB;AACrC9hB,YAAI6hB,mBAAmBxuB,QAAQ,IAAI,CAAC2M,IAAI0C,SAASrP,QAAQ,CAAC;AAC1D,eAAO2M,IAAI6hB,mBAAmBxuB,QAAQ;MACxC;AAEA2M,UAAI6hB,mBAAmBxuB,QAAQ,IAAI5C,OAAOrB,UAAU0yB,gBAClD9hB,IAAIuM,UACJoV,QACF;AAEA,aAAO3hB,IAAI6hB,mBAAmBxuB,QAAQ;;IAExC2tB,aAAa3tB,cAAQ;AAAA,UAAAkP;AAAA,cAAAA,gBACnBvC,IAAI0C,SAASrP,QAAQ,MAAC,OAAAkP,gBAAIpT,MAAM4B,QAAQ2wB;IAAmB;IAC7D7f,SAASA,WAAAA,OAAAA,UAAW,CAAA;IACpBwQ,aAAaA,MAAM5lB,UAAUuT,IAAI6B,SAAS3V,OAAKA,EAAE2V,OAAO;IACxDkgB,cAAcA,MACZ/hB,IAAIiC,WAAW9S,MAAM+S,OAAOlC,IAAIiC,UAAU,IAAI,IAAIlS;IACpDwiB,eAAeA,MAAM;AACnB,UAAIyP,aAA2B,CAAA;AAC/B,UAAIhgB,aAAahC;AACjB,aAAO,MAAM;AACX,cAAMiiB,YAAYjgB,WAAW+f,aAAY;AACzC,YAAI,CAACE;AAAW;AAChBD,mBAAW/0B,KAAKg1B,SAAS;AACzBjgB,qBAAaigB;MACf;AACA,aAAOD,WAAWztB,QAAO;;IAE3BgqB,aAAanxB,KACX,MAAM,CAAC+B,MAAM4Y,kBAAiB,CAAE,GAChCzW,iBAAe;AACb,aAAOA,YAAYgB,IAAI7B,YAAU;AAC/B,eAAO+b,WAAWrd,OAAO6Q,KAAmBvP,QAAQA,OAAOZ,EAAE;MAC/D,CAAC;IACH,GACA;MACEhE,KAA+C;MAC/C8B,OAAOA,MAAA;AAAA,YAAAmD;AAAA,gBAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ8iB;MAAS;IAChE,CACF;IAEA1O,wBAAwB/X,KACtB,MAAM,CAAC4S,IAAIue,YAAW,CAAE,GACxB5K,cAAY;AACV,aAAOA,SAAS/V,OACd,CAAC2jB,KAAK9U,SAAS;AACb8U,YAAI9U,KAAKhc,OAAOZ,EAAE,IAAI4c;AACtB,eAAO8U;SAET,CAAA,CACF;IACF,GACA;MACE11B,KACE0E;MACF5C,OAAOA,MAAA;AAAA,YAAA4D;AAAA,gBAAAA,yBAAMpC,MAAM4B,QAAQC,aAAQO,OAAAA,yBAAIpC,MAAM4B,QAAQ8iB;MAAS;KAElE;;AAGF,WAAShE,IAAI,GAAGA,IAAI1gB,MAAMsC,UAAUtE,QAAQ0iB,KAAK;AAC/C,UAAMre,UAAUrC,MAAMsC,UAAUoe,CAAC;AACjCre,eAAAA,QAAAA,QAASyP,aAATzP,QAAAA,QAASyP,UAAYjB,KAAK7Q,KAAK;EACjC;AAEA,SAAO6Q;AACT;AC1IO,SAASkiB,qBAES;AACvB,SAAO;IACLC,UAAUA,CAACA,UAAU1xB,WAAW;AAC9B,aAAO,OAAO0xB,aAAa,aACtB;QACC,GAAG1xB;QACHR,YAAYkyB;MACd,IACA;QACE,GAAG1xB;QACHb,aAAauyB;;;IAGrBC,SAAS3xB,YAAUA;IACnB4xB,OAAO5xB,YAAUA;;AAErB;ACnFO,SAASyU,kBAEW;AACzB,SAAO/V,WACL/B,KACE,MAAM,CAAC+B,MAAM4B,QAAQuxB,IAAI,GAEvBA,UAKG;AACH,UAAM3L,WAA4B;MAChCpG,MAAM,CAAA;MACNxQ,UAAU,CAAA;MACVY,UAAU,CAAA;;AAGZ,UAAM4hB,aAAa,SACjBC,cACAnzB,OACA4yB,WACiB;AAAA,UAFjB5yB,UAAK,QAAA;AAALA,gBAAQ;MAAC;AAGT,YAAMkhB,OAAO,CAAA;AAEb,eAASV,IAAI,GAAGA,IAAI2S,aAAar1B,QAAQ0iB,KAAK;AAS5C,cAAM7P,MAAMiB,UACV9R,OACAA,MAAMsxB,UAAU+B,aAAa3S,CAAC,GAAIA,GAAGoS,SAAS,GAC9CO,aAAa3S,CAAC,GACdA,GACAxgB,OACAU,QACAkyB,aAAS,OAAA,SAATA,UAAWpyB,EACb;AAGA8mB,iBAAS5W,SAAS9S,KAAK+S,GAAG;AAE1B2W,iBAAShW,SAASX,IAAInQ,EAAE,IAAImQ;AAE5BuQ,aAAKtjB,KAAK+S,GAAG;AAGb,YAAI7Q,MAAM4B,QAAQ0xB,YAAY;AAAA,cAAAC;AAC5B1iB,cAAI2iB,kBAAkBxzB,MAAM4B,QAAQ0xB,WAClCD,aAAa3S,CAAC,GACdA,CACF;AAGA,eAAA6S,uBAAI1iB,IAAI2iB,oBAAJD,QAAAA,qBAAqBv1B,QAAQ;AAC/B6S,gBAAI6B,UAAU0gB,WAAWviB,IAAI2iB,iBAAiBtzB,QAAQ,GAAG2Q,GAAG;UAC9D;QACF;MACF;AAEA,aAAOuQ;;AAGToG,aAASpG,OAAOgS,WAAWD,IAAI;AAE/B,WAAO3L;EACT,GACA;IACE9qB,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;IAC/D1hB,UAAUA,MAAM;AACde,YAAMif,oBAAmB;IAC3B;EACF,CACF;AACJ;AClFO,SAASwU,WACdrS,MACAsS,eACA1zB,OACA;AACA,MAAIA,MAAM4B,QAAQ6T,oBAAoB;AACpC,WAAOke,wBAAwBvS,MAAMsS,eAAe1zB,KAAK;EAC3D;AAEA,SAAO4zB,uBAAuBxS,MAAMsS,eAAe1zB,KAAK;AAC1D;AAEO,SAAS2zB,wBACdE,cACAC,WACA9zB,OACiB;AAAA,MAAA+zB;AACjB,QAAMC,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AACxD,QAAMtsB,YAAQosB,wBAAG/zB,MAAM4B,QAAQ8T,0BAAqB,OAAAqe,wBAAI;AAExD,QAAMG,oBAAoB,SAACL,eAA4B3zB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAC9D,UAAMkhB,OAAqB,CAAA;AAG3B,aAASV,IAAI,GAAGA,IAAImT,cAAa71B,QAAQ0iB,KAAK;AAAA,UAAAnO;AAC5C,UAAI1B,MAAMgjB,cAAanT,CAAC;AAExB,YAAMyT,SAASriB,UACb9R,OACA6Q,IAAInQ,IACJmQ,IAAIuM,UACJvM,IAAI9R,OACJ8R,IAAI3Q,OACJU,QACAiQ,IAAIiC,QACN;AACAqhB,aAAO9e,gBAAgBxE,IAAIwE;AAE3B,WAAI9C,eAAA1B,IAAI6B,YAAO,QAAXH,aAAavU,UAAUkC,QAAQyH,UAAU;AAC3CwsB,eAAOzhB,UAAUwhB,kBAAkBrjB,IAAI6B,SAASxS,QAAQ,CAAC;AACzD2Q,cAAMsjB;AAEN,YAAIL,UAAUjjB,GAAG,KAAK,CAACsjB,OAAOzhB,QAAQ1U,QAAQ;AAC5CojB,eAAKtjB,KAAK+S,GAAG;AACbojB,8BAAoBpjB,IAAInQ,EAAE,IAAImQ;AAC9BmjB,8BAAoBl2B,KAAK+S,GAAG;AAC5B;QACF;AAEA,YAAIijB,UAAUjjB,GAAG,KAAKsjB,OAAOzhB,QAAQ1U,QAAQ;AAC3CojB,eAAKtjB,KAAK+S,GAAG;AACbojB,8BAAoBpjB,IAAInQ,EAAE,IAAImQ;AAC9BmjB,8BAAoBl2B,KAAK+S,GAAG;AAC5B;QACF;MACF,OAAO;AACLA,cAAMsjB;AACN,YAAIL,UAAUjjB,GAAG,GAAG;AAClBuQ,eAAKtjB,KAAK+S,GAAG;AACbojB,8BAAoBpjB,IAAInQ,EAAE,IAAImQ;AAC9BmjB,8BAAoBl2B,KAAK+S,GAAG;QAC9B;MACF;IACF;AAEA,WAAOuQ;;AAGT,SAAO;IACLA,MAAM8S,kBAAkBL,YAAY;IACpCjjB,UAAUojB;IACVxiB,UAAUyiB;;AAEd;AAEO,SAASL,uBACdC,cACAC,WACA9zB,OACiB;AAAA,MAAAo0B;AACjB,QAAMJ,sBAAoC,CAAA;AAC1C,QAAMC,sBAAkD,CAAA;AACxD,QAAMtsB,YAAQysB,yBAAGp0B,MAAM4B,QAAQ8T,0BAAqB,OAAA0e,yBAAI;AAGxD,QAAMF,oBAAoB,SAACL,eAA4B3zB,OAAc;AAAA,QAAdA,UAAK,QAAA;AAALA,cAAQ;IAAC;AAG9D,UAAMkhB,OAAqB,CAAA;AAG3B,aAASV,IAAI,GAAGA,IAAImT,cAAa71B,QAAQ0iB,KAAK;AAC5C,UAAI7P,MAAMgjB,cAAanT,CAAC;AAExB,YAAM2T,OAAOP,UAAUjjB,GAAG;AAE1B,UAAIwjB,MAAM;AAAA,YAAA3K;AACR,aAAIA,gBAAA7Y,IAAI6B,YAAO,QAAXgX,cAAa1rB,UAAUkC,QAAQyH,UAAU;AAC3C,gBAAMwsB,SAASriB,UACb9R,OACA6Q,IAAInQ,IACJmQ,IAAIuM,UACJvM,IAAI9R,OACJ8R,IAAI3Q,OACJU,QACAiQ,IAAIiC,QACN;AACAqhB,iBAAOzhB,UAAUwhB,kBAAkBrjB,IAAI6B,SAASxS,QAAQ,CAAC;AACzD2Q,gBAAMsjB;QACR;AAEA/S,aAAKtjB,KAAK+S,GAAG;AACbmjB,4BAAoBl2B,KAAK+S,GAAG;AAC5BojB,4BAAoBpjB,IAAInQ,EAAE,IAAImQ;MAChC;IACF;AAEA,WAAOuQ;;AAGT,SAAO;IACLA,MAAM8S,kBAAkBL,YAAY;IACpCjjB,UAAUojB;IACVxiB,UAAUyiB;;AAEd;AC5HO,SAAS/a,sBAEW;AACzB,SAAOlZ,WACL/B,KACE,MAAM,CACJ+B,MAAMiY,uBAAsB,GAC5BjY,MAAM0D,SAAQ,EAAG2R,eACjBrV,MAAM0D,SAAQ,EAAG4R,YAAY,GAE/B,CAACkS,UAAUnS,eAAeC,iBAAiB;AACzC,QACE,CAACkS,SAASpG,KAAKpjB,UACd,EAACqX,iBAAa,QAAbA,cAAerX,WAAU,CAACsX,cAC5B;AACA,eAASoL,IAAI,GAAGA,IAAI8G,SAAS5W,SAAS5S,QAAQ0iB,KAAK;AACjD8G,iBAAS5W,SAAS8P,CAAC,EAAGrL,gBAAgB,CAAA;AACtCmS,iBAAS5W,SAAS8P,CAAC,EAAGnI,oBAAoB,CAAA;MAC5C;AACA,aAAOiP;IACT;AAEA,UAAM8M,wBAAuD,CAAA;AAC7D,UAAMC,wBAAuD,CAAA;AAE5D,KAAClf,iBAAa,OAAbA,gBAAiB,CAAA,GAAIzX,QAAQb,OAAK;AAAA,UAAAy3B;AAClC,YAAMlzB,SAAStB,MAAM8L,UAAU/O,EAAE2D,EAAE;AAEnC,UAAI,CAACY,QAAQ;AACX;MACF;AAEA,YAAM8T,WAAW9T,OAAO6U,YAAW;AAEnC,UAAI,CAACf,UAAU;AACb,YAAIhU,MAAuC;AACzCzB,kBAAQwB,KACL,oEAAmEG,OAAOZ,EAAG,GAChF;QACF;AACA;MACF;AAEA4zB,4BAAsBx2B,KAAK;QACzB4C,IAAI3D,EAAE2D;QACN0U;QACA+R,gBAAaqN,wBAAEpf,SAASX,sBAAkB,OAAA,SAA3BW,SAASX,mBAAqB1X,EAAE+Y,KAAK,MAAC,OAAA0e,wBAAIz3B,EAAE+Y;MAC7D,CAAC;IACH,CAAC;AAED,UAAM2e,gBAAgBpf,cAAclS,IAAIpG,OAAKA,EAAE2D,EAAE;AAEjD,UAAMiV,iBAAiB3V,MAAMyY,kBAAiB;AAE9C,UAAMic,4BAA4B10B,MAC/B4Y,kBAAiB,EACjBxU,OAAO9C,YAAUA,OAAOqV,mBAAkB,CAAE;AAE/C,QACErB,gBACAK,kBACA+e,0BAA0B12B,QAC1B;AACAy2B,oBAAc32B,KAAK,YAAY;AAE/B42B,gCAA0B92B,QAAQ0D,YAAU;AAAA,YAAAqzB;AAC1CJ,8BAAsBz2B,KAAK;UACzB4C,IAAIY,OAAOZ;UACX0U,UAAUO;UACVwR,gBAAawN,wBACXhf,eAAelB,sBAAkB,OAAA,SAAjCkB,eAAelB,mBAAqBa,YAAY,MAAC,OAAAqf,wBACjDrf;QACJ,CAAC;MACH,CAAC;IACH;AAEA,QAAIsf;AACJ,QAAIC;AAGJ,aAASC,IAAI,GAAGA,IAAItN,SAAS5W,SAAS5S,QAAQ82B,KAAK;AACjD,YAAMjkB,MAAM2W,SAAS5W,SAASkkB,CAAC;AAE/BjkB,UAAIwE,gBAAgB,CAAA;AAEpB,UAAIif,sBAAsBt2B,QAAQ;AAChC,iBAAS0iB,IAAI,GAAGA,IAAI4T,sBAAsBt2B,QAAQ0iB,KAAK;AACrDkU,gCAAsBN,sBAAsB5T,CAAC;AAC7C,gBAAMhgB,KAAKk0B,oBAAoBl0B;AAG/BmQ,cAAIwE,cAAc3U,EAAE,IAAIk0B,oBAAoBxf,SAC1CvE,KACAnQ,IACAk0B,oBAAoBzN,eACpB4N,gBAAc;AACZlkB,gBAAI0H,kBAAkB7X,EAAE,IAAIq0B;UAC9B,CACF;QACF;MACF;AAEA,UAAIR,sBAAsBv2B,QAAQ;AAChC,iBAAS0iB,IAAI,GAAGA,IAAI6T,sBAAsBv2B,QAAQ0iB,KAAK;AACrDmU,gCAAsBN,sBAAsB7T,CAAC;AAC7C,gBAAMhgB,KAAKm0B,oBAAoBn0B;AAE/B,cACEm0B,oBAAoBzf,SAClBvE,KACAnQ,IACAm0B,oBAAoB1N,eACpB4N,gBAAc;AACZlkB,gBAAI0H,kBAAkB7X,EAAE,IAAIq0B;UAC9B,CACF,GACA;AACAlkB,gBAAIwE,cAAc2f,aAAa;AAC/B;UACF;QACF;AAEA,YAAInkB,IAAIwE,cAAc2f,eAAe,MAAM;AACzCnkB,cAAIwE,cAAc2f,aAAa;QACjC;MACF;IACF;AAEA,UAAMC,iBAAkBpkB,SAAoB;AAE1C,eAAS6P,IAAI,GAAGA,IAAI+T,cAAcz2B,QAAQ0iB,KAAK;AAC7C,YAAI7P,IAAIwE,cAAcof,cAAc/T,CAAC,CAAC,MAAO,OAAO;AAClD,iBAAO;QACT;MACF;AACA,aAAO;;AAIT,WAAO+S,WAAWjM,SAASpG,MAAM6T,gBAAgBj1B,KAAK;EACxD,GACA;IACEtD,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;IAC/D1hB,UAAUA,MAAM;AACde,YAAMif,oBAAmB;IAC3B;EACF,CACF;AACJ;ACtJO,SAASjH,qBAGW;AACzB,SAAO,CAAChY,OAAOkE,aACbjG,KACE,MAAM,CACJ+B,MAAMiY,uBAAsB,GAC5BjY,MAAM0D,SAAQ,EAAG2R,eACjBrV,MAAM0D,SAAQ,EAAG4R,cACjBtV,MAAMkZ,oBAAmB,CAAE,GAE7B,CAACgc,aAAa7f,eAAeC,iBAAiB;AAC5C,QACE,CAAC4f,YAAY9T,KAAKpjB,UACjB,EAACqX,iBAAa,QAAbA,cAAerX,WAAU,CAACsX,cAC5B;AACA,aAAO4f;IACT;AAEA,UAAMT,gBAAgB,CACpB,GAAGpf,cAAclS,IAAIpG,OAAKA,EAAE2D,EAAE,EAAE0D,OAAOrH,OAAKA,MAAMmH,QAAQ,GAC1DoR,eAAe,eAAe1U,MAAS,EACvCwD,OAAOC,OAAO;AAEhB,UAAM4wB,iBAAkBpkB,SAAoB;AAE1C,eAAS6P,IAAI,GAAGA,IAAI+T,cAAcz2B,QAAQ0iB,KAAK;AAC7C,YAAI7P,IAAIwE,cAAcof,cAAc/T,CAAC,CAAC,MAAO,OAAO;AAClD,iBAAO;QACT;MACF;AACA,aAAO;;AAGT,WAAO+S,WAAWyB,YAAY9T,MAAM6T,gBAAgBj1B,KAAK;EAC3D,GACA;IACEtD,KAEE,wBAAwBwH;IAC1B1F,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;IAC/D1hB,UAAUA,MAAM;IAAA;EAClB,CACF;AACJ;AC9CO,SAASkZ,yBAGY;AAC1B,SAAO,CAACnY,OAAOkE,aACbjG,KACE,MAAA;AAAA,QAAAk3B;AAAA,WAAM,EAAAA,mBAACn1B,MAAM8L,UAAU5H,QAAQ,MAAC,OAAA,SAAzBixB,iBAA2Bnd,mBAAkB,CAAE;EAAC,GACvDod,qBAAmB;AACjB,QAAI,CAACA;AAAiB,aAAO,oBAAIhd,IAAG;AAEpC,QAAIid,sBAAsB,oBAAIjd,IAAG;AAEjC,aAASsI,IAAI,GAAGA,IAAI0U,gBAAgBxkB,SAAS5S,QAAQ0iB,KAAK;AACxD,YAAMxP,SACJkkB,gBAAgBxkB,SAAS8P,CAAC,EAAGiS,gBAAwBzuB,QAAQ;AAE/D,eAAS4wB,IAAI,GAAGA,IAAI5jB,OAAOlT,QAAQ82B,KAAK;AACtC,cAAMhf,QAAQ5E,OAAO4jB,CAAC;AAEtB,YAAIO,oBAAoB3R,IAAI5N,KAAK,GAAG;AAAA,cAAAwf;AAClCD,8BAAoBE,IAClBzf,SACAwf,wBAACD,oBAAoBG,IAAI1f,KAAK,MAACwf,OAAAA,wBAAI,KAAK,CAC1C;QACF,OAAO;AACLD,8BAAoBE,IAAIzf,OAAO,CAAC;QAClC;MACF;IACF;AAEA,WAAOuf;EACT,GACA;IACE34B,KAEE,4BAA4BwH;IAC9B1F,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;IAC/D1hB,UAAUA,MAAM;IAAA;EAClB,CACF;AACJ;ACxCO,SAASqZ,yBAGwB;AACtC,SAAO,CAACtY,OAAOkE,aACbjG,KACE,MAAA;AAAA,QAAAk3B;AAAA,WAAM,EAAAA,mBAACn1B,MAAM8L,UAAU5H,QAAQ,MAAC,OAAA,SAAzBixB,iBAA2Bnd,mBAAkB,CAAE;EAAC,GACvDod,qBAAmB;AAAA,QAAAK;AACjB,QAAI,CAACL;AAAiB,aAAOx0B;AAE7B,UAAM80B,cAAUD,wBACdL,gBAAgBxkB,SAAS,CAAC,MAAC,OAAA,SAA3B6kB,sBAA6B9C,gBAAgBzuB,QAAQ;AAEvD,QAAI,OAAOwxB,eAAe,aAAa;AACrC,aAAO90B;IACT;AAEA,QAAI+0B,sBAAkC,CAACD,YAAYA,UAAU;AAE7D,aAAShV,IAAI,GAAGA,IAAI0U,gBAAgBxkB,SAAS5S,QAAQ0iB,KAAK;AACxD,YAAMxP,SACJkkB,gBAAgBxkB,SAAS8P,CAAC,EAAGiS,gBAAwBzuB,QAAQ;AAE/D,eAAS4wB,IAAI,GAAGA,IAAI5jB,OAAOlT,QAAQ82B,KAAK;AACtC,cAAMhf,QAAQ5E,OAAO4jB,CAAC;AAEtB,YAAIhf,QAAQ6f,oBAAoB,CAAC,GAAG;AAClCA,8BAAoB,CAAC,IAAI7f;mBAChBA,QAAQ6f,oBAAoB,CAAC,GAAG;AACzCA,8BAAoB,CAAC,IAAI7f;QAC3B;MACF;IACF;AAEA,WAAO6f;EACT,GACA;IACEj5B,KAEE,4BAA4BwH;IAC9B1F,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;IAC/D1hB,UAAUA,MAAM;IAAA;EAClB,CACF;AACJ;AC3CO,SAAS0S,oBAEW;AACzB,SAAO3R,WACL/B,KACE,MAAM,CAAC+B,MAAM0D,SAAQ,EAAG6nB,SAASvrB,MAAMyuB,qBAAoB,CAAE,GAC7D,CAAClD,SAAS/D,aAAa;AACrB,QAAI,CAACA,SAASpG,KAAKpjB,UAAU,EAACutB,WAAO,QAAPA,QAASvtB,SAAQ;AAC7C,aAAOwpB;IACT;AAEA,UAAMoO,eAAe51B,MAAM0D,SAAQ,EAAG6nB;AAEtC,UAAMsK,iBAA+B,CAAA;AAGrC,UAAMC,mBAAmBF,aAAaxxB,OACpCmW,UAAI;AAAA,UAAA4a;AAAA,cAAAA,mBAAIn1B,MAAM8L,UAAUyO,KAAK7Z,EAAE,MAAvBy0B,OAAAA,SAAAA,iBAA0BvH,WAAU;IAAE,CAChD;AAEA,UAAMmI,iBAOF,CAAA;AAEJD,qBAAiBl4B,QAAQo4B,eAAa;AACpC,YAAM10B,SAAStB,MAAM8L,UAAUkqB,UAAUt1B,EAAE;AAC3C,UAAI,CAACY;AAAQ;AAEby0B,qBAAeC,UAAUt1B,EAAE,IAAI;QAC7B+qB,eAAenqB,OAAOrB,UAAUwrB;QAChCwK,eAAe30B,OAAOrB,UAAUg2B;QAChCzK,WAAWlqB,OAAO4qB,aAAY;;IAElC,CAAC;AAED,UAAMgK,WAAY9U,UAAuB;AAGvC,YAAM+U,aAAa/U,KAAKje,IAAI0N,UAAQ;QAAE,GAAGA;MAAI,EAAE;AAE/CslB,iBAAW5b,KAAK,CAAC6P,MAAMC,SAAS;AAC9B,iBAAS3J,IAAI,GAAGA,IAAIoV,iBAAiB93B,QAAQ0iB,KAAK,GAAG;AAAA,cAAA0V;AACnD,gBAAMJ,YAAYF,iBAAiBpV,CAAC;AACpC,gBAAM2V,aAAaN,eAAeC,UAAUt1B,EAAE;AAC9C,gBAAM41B,UAAMF,kBAAGJ,aAAS,OAAA,SAATA,UAAW1J,SAAI,OAAA8J,kBAAI;AAElC,cAAIG,UAAU;AAGd,cAAIF,WAAW5K,eAAe;AAC5B,kBAAM+K,SAASpM,KAAK7W,SAASyiB,UAAUt1B,EAAE;AACzC,kBAAM+1B,SAASpM,KAAK9W,SAASyiB,UAAUt1B,EAAE;AAEzC,kBAAMg2B,aAAaF,WAAW51B;AAC9B,kBAAM+1B,aAAaF,WAAW71B;AAE9B,gBAAI81B,cAAcC,YAAY;AAC5BJ,wBACEG,cAAcC,aACV,IACAD,aACEL,WAAW5K,gBACX,CAAC4K,WAAW5K;YACtB;UACF;AAEA,cAAI8K,YAAY,GAAG;AACjBA,sBAAUF,WAAW7K,UAAUpB,MAAMC,MAAM2L,UAAUt1B,EAAE;UACzD;AAGA,cAAI61B,YAAY,GAAG;AACjB,gBAAID,QAAQ;AACVC,yBAAW;YACb;AAEA,gBAAIF,WAAWJ,eAAe;AAC5BM,yBAAW;YACb;AAEA,mBAAOA;UACT;QACF;AAEA,eAAOnM,KAAKrrB,QAAQsrB,KAAKtrB;MAC3B,CAAC;AAGDo3B,iBAAWv4B,QAAQiT,SAAO;AAAA,YAAA0B;AACxBsjB,uBAAe/3B,KAAK+S,GAAG;AACvB,aAAA0B,eAAI1B,IAAI6B,YAAJH,QAAAA,aAAavU,QAAQ;AACvB6S,cAAI6B,UAAUwjB,SAASrlB,IAAI6B,OAAO;QACpC;MACF,CAAC;AAED,aAAOyjB;;AAGT,WAAO;MACL/U,MAAM8U,SAAS1O,SAASpG,IAAI;MAC5BxQ,UAAUilB;MACVrkB,UAAUgW,SAAShW;;EAEvB,GACA;IACE9U,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;IAC/D1hB,UAAUA,MAAM;AACde,YAAMif,oBAAmB;IAC3B;EACF,CACF;AACJ;ACrHO,SAASpC,qBAEW;AACzB,SAAO7c,WACL/B,KACE,MAAM,CAAC+B,MAAM0D,SAAQ,EAAG4X,UAAUtb,MAAM4c,sBAAqB,CAAE,GAC/D,CAACtB,UAAUkM,aAAa;AACtB,QAAI,CAACA,SAASpG,KAAKpjB,UAAU,CAACsd,SAAStd,QAAQ;AAC7C,aAAOwpB;IACT;AAGA,UAAMoP,mBAAmBtb,SAASlX,OAAOF,cACvClE,MAAM8L,UAAU5H,QAAQ,CAC1B;AAEA,UAAM2yB,kBAAgC,CAAA;AACtC,UAAMC,kBAA8C,CAAA;AAOpD,UAAMC,qBAAqB,SACzB3V,MACAlhB,OACA4S,UACG;AAAA,UAFH5S,UAAK,QAAA;AAALA,gBAAQ;MAAC;AAKT,UAAIA,SAAS02B,iBAAiB54B,QAAQ;AACpC,eAAOojB,KAAKje,IAAI0N,SAAO;AACrBA,cAAI3Q,QAAQA;AAEZ22B,0BAAgB/4B,KAAK+S,GAAG;AACxBimB,0BAAgBjmB,IAAInQ,EAAE,IAAImQ;AAE1B,cAAIA,IAAI6B,SAAS;AACf7B,gBAAI6B,UAAUqkB,mBAAmBlmB,IAAI6B,SAASxS,QAAQ,GAAG2Q,IAAInQ,EAAE;UACjE;AAEA,iBAAOmQ;QACT,CAAC;MACH;AAEA,YAAM3M,WAAmB0yB,iBAAiB12B,KAAK;AAG/C,YAAM82B,eAAeC,QAAQ7V,MAAMld,QAAQ;AAG3C,YAAMgzB,wBAAwBh6B,MAAMyd,KAAKqc,aAAaG,QAAO,CAAE,EAAEh0B,IAC/D,CAAA/C,MAA+BrB,UAAU;AAAA,YAAxC,CAACq4B,eAAeC,YAAW,IAACj3B;AAC3B,YAAIM,KAAM,GAAEwD,QAAS,IAAGkzB,aAAc;AACtC12B,aAAKoS,WAAY,GAAEA,QAAS,IAAGpS,EAAG,KAAIA;AAGtC,cAAMgS,UAAUqkB,mBAAmBM,cAAan3B,QAAQ,GAAGQ,EAAE;AAG7D,cAAMuZ,WAAW/Z,QACb5C,UAAU+5B,cAAaxmB,CAAAA,SAAOA,KAAI6B,OAAO,IACzC2kB;AAEJ,cAAMxmB,MAAMiB,UACV9R,OACAU,IACAuZ,SAAS,CAAC,EAAGmD,UACbre,OACAmB,OACAU,QACAkS,QACF;AAEA7B,eAAOof,OAAOxf,KAAK;UACjBmM,kBAAkB9Y;UAClBkzB;UACA1kB;UACAuH;UACA1G,UAAWrP,CAAAA,cAAqB;AAE9B,gBAAI0yB,iBAAiB71B,SAASmD,SAAQ,GAAG;AACvC,kBAAI2M,IAAI4hB,aAAatV,eAAejZ,SAAQ,GAAG;AAC7C,uBAAO2M,IAAI4hB,aAAavuB,SAAQ;cAClC;AAEA,kBAAImzB,aAAY,CAAC,GAAG;AAAA,oBAAAC;AAClBzmB,oBAAI4hB,aAAavuB,SAAQ,KAACozB,wBACxBD,aAAY,CAAC,EAAE9jB,SAASrP,SAAQ,MAACozB,OAAAA,wBAAI12B;cACzC;AAEA,qBAAOiQ,IAAI4hB,aAAavuB,SAAQ;YAClC;AAEA,gBAAI2M,IAAIqM,qBAAqBC,eAAejZ,SAAQ,GAAG;AACrD,qBAAO2M,IAAIqM,qBAAqBhZ,SAAQ;YAC1C;AAGA,kBAAM5C,SAAStB,MAAM8L,UAAU5H,SAAQ;AACvC,kBAAMqzB,cAAcj2B,UAAM,OAAA,SAANA,OAAQib,iBAAgB;AAE5C,gBAAIgb,aAAa;AACf1mB,kBAAIqM,qBAAqBhZ,SAAQ,IAAIqzB,YACnCrzB,WACA+V,UACAod,YACF;AAEA,qBAAOxmB,IAAIqM,qBAAqBhZ,SAAQ;YAC1C;UACF;QACF,CAAC;AAEDwO,gBAAQ9U,QAAQosB,YAAU;AACxB6M,0BAAgB/4B,KAAKksB,MAAM;AAC3B8M,0BAAgB9M,OAAOtpB,EAAE,IAAIspB;QAQ/B,CAAC;AAED,eAAOnZ;MACT,CACF;AAEA,aAAOqmB;;AAGT,UAAMG,cAAcN,mBAAmBvP,SAASpG,MAAM,CAAC;AAEvDiW,gBAAYz5B,QAAQosB,YAAU;AAC5B6M,sBAAgB/4B,KAAKksB,MAAM;AAC3B8M,sBAAgB9M,OAAOtpB,EAAE,IAAIspB;IAQ/B,CAAC;AAED,WAAO;MACL5I,MAAMiW;MACNzmB,UAAUimB;MACVrlB,UAAUslB;;EAEd,GACA;IACEp6B,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;IAC/D1hB,UAAUA,MAAM;AACde,YAAMgQ,OAAO,MAAM;AACjBhQ,cAAM8P,mBAAkB;AACxB9P,cAAMif,oBAAmB;MAC3B,CAAC;IACH;EACF,CACF;AACJ;AAEA,SAASgY,QAA+B7V,MAAoBld,UAAkB;AAC5E,QAAMszB,WAAW,oBAAIpf,IAAG;AAExB,SAAOgJ,KAAK3S,OAAO,CAACtL,KAAK0N,QAAQ;AAC/B,UAAM4mB,SAAU,GAAE5mB,IAAIoM,iBAAiB/Y,QAAQ,CAAE;AACjD,UAAMwzB,WAAWv0B,IAAIqyB,IAAIiC,MAAM;AAC/B,QAAI,CAACC,UAAU;AACbv0B,UAAIoyB,IAAIkC,QAAQ,CAAC5mB,GAAG,CAAC;IACvB,OAAO;AACL6mB,eAAS55B,KAAK+S,GAAG;IACnB;AACA,WAAO1N;KACNq0B,QAAQ;AACb;ACrLO,SAAS5lB,sBAEW;AACzB,SAAO5R,WACL/B,KACE,MAAM,CACJ+B,MAAM0D,SAAQ,EAAG+L,UACjBzP,MAAM0R,uBAAsB,GAC5B1R,MAAM4B,QAAQ+N,oBAAoB,GAEpC,CAACF,UAAU+X,UAAU7X,yBAAyB;AAC5C,QACE,CAAC6X,SAASpG,KAAKpjB,UACdyR,aAAa,QAAQ,CAACwB,OAAOE,KAAK1B,YAAAA,OAAAA,WAAY,CAAA,CAAE,EAAEzR,QACnD;AACA,aAAOwpB;IACT;AAEA,QAAI,CAAC7X,sBAAsB;AAEzB,aAAO6X;IACT;AAEA,WAAOmQ,WAAWnQ,QAAQ;EAC5B,GACA;IACE9qB,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;EACjE,CACF;AACJ;AAEO,SAASgX,WAAkCnQ,UAA2B;AAC3E,QAAMoQ,eAA6B,CAAA;AAEnC,QAAMC,YAAahnB,SAAoB;AAAA,QAAA0B;AACrCqlB,iBAAa95B,KAAK+S,GAAG;AAErB,SAAI0B,eAAA1B,IAAI6B,YAAJH,QAAAA,aAAavU,UAAU6S,IAAIQ,cAAa,GAAI;AAC9CR,UAAI6B,QAAQ9U,QAAQi6B,SAAS;IAC/B;;AAGFrQ,WAASpG,KAAKxjB,QAAQi6B,SAAS;AAE/B,SAAO;IACLzW,MAAMwW;IACNhnB,UAAU4W,SAAS5W;IACnBY,UAAUgW,SAAShW;;AAEvB;ACjDO,SAASwP,sBAA6C5iB,MAEV;AACjD,SAAO4B,WACL/B,KACE,MAAM,CACJ+B,MAAM0D,SAAQ,EAAGqb,YACjB/e,MAAM2Q,yBAAwB,GAC9B3Q,MAAM4B,QAAQ+N,uBACV/O,SACAZ,MAAM0D,SAAQ,EAAG+L,QAAQ,GAE/B,CAACsP,YAAYyI,aAAa;AACxB,QAAI,CAACA,SAASpG,KAAKpjB,QAAQ;AACzB,aAAOwpB;IACT;AAEA,UAAM;MAAE3I;MAAUD;IAAU,IAAIG;AAChC,QAAI;MAAEqC;MAAMxQ;MAAUY;IAAS,IAAIgW;AACnC,UAAMsQ,YAAYjZ,WAAWD;AAC7B,UAAMmZ,UAAUD,YAAYjZ;AAE5BuC,WAAOA,KAAK2K,MAAM+L,WAAWC,OAAO;AAEpC,QAAIC;AAEJ,QAAI,CAACh4B,MAAM4B,QAAQ+N,sBAAsB;AACvCqoB,0BAAoBL,WAAW;QAC7BvW;QACAxQ;QACAY;MACF,CAAC;IACH,OAAO;AACLwmB,0BAAoB;QAClB5W;QACAxQ;QACAY;;IAEJ;AAEAwmB,sBAAkBpnB,WAAW,CAAA;AAE7B,UAAMinB,YAAahnB,SAAoB;AACrCmnB,wBAAkBpnB,SAAS9S,KAAK+S,GAAG;AACnC,UAAIA,IAAI6B,QAAQ1U,QAAQ;AACtB6S,YAAI6B,QAAQ9U,QAAQi6B,SAAS;MAC/B;;AAGFG,sBAAkB5W,KAAKxjB,QAAQi6B,SAAS;AAExC,WAAOG;EACT,GACA;IACEt7B,KAA+C;IAC/C8B,OAAOA,MAAA;AAAA,UAAAmD;AAAA,cAAAA,wBAAM3B,MAAM4B,QAAQC,aAAQF,OAAAA,wBAAI3B,MAAM4B,QAAQ+e;IAAU;EACjE,CACF;AACJ;;;AC7CO,SAASsX,WACdC,MACAC,OAC+B;AAC/B,SAAO,CAACD,OAAO,OAAOE,iBAAyBF,IAAI,IACjDG,oBAACH,MAASC,KAAQ,IAElBD;AAEJ;AAEA,SAASE,iBACPE,WAC0C;AAC1C,SACEC,iBAAiBD,SAAS,KAC1B,OAAOA,cAAc,cACrBE,kBAAkBF,SAAS;AAE/B;AAEA,SAASC,iBAAiBD,WAAgB;AACxC,SACE,OAAOA,cAAc,eACpB,MAAM;AACL,UAAMG,QAAQC,OAAOC,eAAeL,SAAS;AAC7C,WAAOG,MAAMG,aAAaH,MAAMG,UAAUR;EAC5C,GAAC;AAEL;AAEA,SAASI,kBAAkBF,WAAgB;AACzC,SACE,OAAOA,cAAc,YACrB,OAAOA,UAAUO,aAAa,YAC9B,CAAC,cAAc,mBAAmB,EAAEC,SAASR,UAAUO,SAASE,WAAW;AAE/E;AAEO,SAASC,cACdC,SACA;AAEA,QAAMC,kBAA+C;IACnDC,OAAO,CAAA;;IACPC,eAAeA,MAAM;IAAA;;IACrBC,qBAAqB;IACrB,GAAGJ;;AAIL,QAAM,CAACK,QAAQ,IAAUC,eAAS,OAAO;IACvCC,SAASC,YAAmBP,eAAe;EAC7C,EAAE;AAGF,QAAM,CAACC,OAAOO,QAAQ,IAAUH,eAAS,MAAMD,SAASE,QAAQG,YAAY;AAI5EL,WAASE,QAAQI,WAAWC,WAAS;IACnC,GAAGA;IACH,GAAGZ;IACHE,OAAO;MACL,GAAGA;MACH,GAAGF,QAAQE;;;;IAIbC,eAAeU,aAAW;AACxBJ,eAASI,OAAO;AAChBb,cAAQG,iBAARH,QAAAA,QAAQG,cAAgBU,OAAO;IACjC;EACF,EAAE;AAEF,SAAOR,SAASE;AAClB;", "names": ["functionalUpdate", "updater", "input", "noop", "makeStateUpdater", "key", "instance", "setState", "old", "isFunction", "d", "Function", "isNumberArray", "Array", "isArray", "every", "val", "flattenBy", "arr", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "flat", "recurse", "subArr", "for<PERSON>ach", "item", "push", "children", "length", "memo", "getDeps", "fn", "opts", "deps", "result", "depTime", "debug", "Date", "now", "newDeps", "depsChanged", "some", "dep", "index", "resultTime", "onChange", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "createColumn", "table", "columnDef", "depth", "parent", "_ref", "_resolvedColumnDef$id", "defaultColumn", "_getDefaultColumnDef", "resolvedColumnDef", "accessorKey", "id", "replace", "undefined", "header", "accessorFn", "includes", "originalRow", "split", "_result", "warn", "process", "Error", "column", "columns", "getFlatColumns", "_column$columns", "flatMap", "_table$options$debugA", "options", "debugAll", "debugColumns", "getLeafColumns", "_getOrderColumnsFn", "orderColumns", "_column$columns2", "leafColumns", "_table$options$debugA2", "feature", "_features", "createHeader", "_options$id", "isPlaceholder", "placeholderId", "subHeaders", "colSpan", "rowSpan", "headerGroup", "getLeafHeaders", "leafHeaders", "recurse<PERSON><PERSON><PERSON>", "h", "map", "getContext", "Headers", "createTable", "getHeaderGroups", "getAllColumns", "getVisibleLeafColumns", "getState", "columnPinning", "left", "right", "allColumns", "_left$map$filter", "_right$map$filter", "leftColumns", "columnId", "find", "filter", "Boolean", "rightColumns", "centerColumns", "headerGroups", "buildHeaderGroups", "debugHeaders", "getCenterHeaderGroups", "getLeftHeaderGroups", "_left$map$filter2", "orderedLeafColumns", "_table$options$debugA3", "getRightHeaderGroups", "_right$map$filter2", "_table$options$debugA4", "getFooterGroups", "reverse", "_table$options$debugA5", "getLeftFooterGroups", "_table$options$debugA6", "getCenterFooterGroups", "_table$options$debugA7", "getRightFooterGroups", "_table$options$debugA8", "getFlatHeaders", "headers", "_table$options$debugA9", "getLeftFlatHeaders", "_table$options$debugA10", "getCenterFlatHeaders", "_table$options$debugA11", "getRightFlatHeaders", "_table$options$debugA12", "getCenterLeafHeaders", "flatHeaders", "_header$subHeaders", "_table$options$debugA13", "getLeftLeafHeaders", "_header$subHeaders2", "_table$options$debugA14", "getRightLeafHeaders", "_header$subHeaders3", "_table$options$debugA15", "center", "_left$0$headers", "_left$", "_center$0$headers", "_center$", "_right$0$headers", "_right$", "_table$options$debugA16", "columnsToGroup", "headerFamily", "_headerGroups$0$heade", "_headerGroups$", "max<PERSON><PERSON><PERSON>", "findMaxDepth", "getIsVisible", "createHeaderGroup", "headersToGroup", "join", "pendingParentHeaders", "headerToGroup", "latestPendingParentHeader", "isLeafHeader", "bottomHeaders", "recurseHeadersForSpans", "filteredHeaders", "childRowSpans", "childColSpan", "childRowSpan", "minChildRowSpan", "defaultColumnSizing", "size", "minSize", "maxSize", "Number", "MAX_SAFE_INTEGER", "getDefaultColumnSizingInfoState", "startOffset", "startSize", "deltaOffset", "deltaPercentage", "isResizingColumn", "columnSizingStart", "ColumnSizing", "getDefaultColumnDef", "getInitialState", "state", "columnSizing", "columnSizingInfo", "getDefaultOptions", "columnResizeMode", "columnResizeDirection", "onColumnSizingChange", "onColumnSizingInfoChange", "getSize", "_column$columnDef$min", "_column$columnDef$max", "columnSize", "getStart", "position", "getLeftVisibleLeafColumns", "getRightVisibleLeafColumns", "findIndex", "prevSiblingColumn", "resetSize", "setColumnSizing", "_ref2", "_", "rest", "getCanResize", "_column$columnDef$ena", "_table$options$enable", "enableResizing", "enableColumnResizing", "getIsResizing", "sum", "_header$column$getSiz", "prevSiblingHeader", "getResizeHandler", "_contextDocument", "getColumn", "canResize", "e", "persist", "isTouchStartEvent", "touches", "clientX", "newColumnSizing", "updateOffset", "eventType", "clientXPos", "setColumnSizingInfo", "_old$startOffset", "_old$startSize", "deltaDirection", "_ref3", "headerSize", "onMove", "onEnd", "contextDocument", "document", "mouseEvents", "<PERSON><PERSON><PERSON><PERSON>", "up<PERSON><PERSON><PERSON>", "removeEventListener", "touchEvents", "cancelable", "preventDefault", "stopPropagation", "_e$touches$", "passiveIfSupported", "passiveEventSupported", "passive", "addEventListener", "resetColumnSizing", "defaultState", "_table$initialState$c", "initialState", "resetHeaderSizeInfo", "_table$initialState$c2", "getTotalSize", "_table$getHeaderGroup", "_table$getHeaderGroup2", "reduce", "getLeftTotalSize", "_table$getLeftHeaderG", "_table$getLeftHeaderG2", "getCenterTotalSize", "_table$getCenterHeade", "_table$getCenterHeade2", "getRightTotalSize", "_table$getRightHeader", "_table$getRightHeader2", "passiveSupported", "supported", "window", "err", "type", "Expanding", "expanded", "onExpandedChange", "paginateExpandedRows", "registered", "queued", "_autoResetExpanded", "_table$options$autoRe", "_queue", "autoResetAll", "autoResetExpanded", "manualExpanding", "resetExpanded", "setExpanded", "toggleAllRowsExpanded", "getIsAllRowsExpanded", "_table$initialState$e", "_table$initialState", "getCanSomeRowsExpand", "getPrePaginationRowModel", "flatRows", "row", "getCanExpand", "getToggleAllRowsExpandedHandler", "getIsSomeRowsExpanded", "Object", "values", "keys", "getRowModel", "getIsExpanded", "getExpandedDepth", "rowIds", "rowsById", "splitId", "getPreExpandedRowModel", "getSortedRowModel", "getExpandedRowModel", "_getExpandedRowModel", "createRow", "toggleExpanded", "_expanded", "exists", "oldExpanded", "rowId", "_table$options$getIsR", "getIsRowExpanded", "_table$options$getRow", "_row$subRows", "getRowCanExpand", "enableExpanding", "subRows", "getIsAllParentsExpanded", "isFullyExpanded", "currentRow", "parentId", "getRow", "getToggleExpandedHandler", "canExpand", "includesString", "filterValue", "_row$getValue", "search", "toLowerCase", "getValue", "toString", "autoRemove", "<PERSON><PERSON><PERSON><PERSON>", "includesStringSensitive", "_row$getValue2", "equalsString", "_row$getValue3", "arrIncludes", "_row$getValue4", "arrIncludesAll", "_row$getValue5", "arrIncludesSome", "_row$getValue6", "equals", "weakEquals", "inNumberRange", "rowValue", "resolveFilterValue", "unsafeMin", "unsafeMax", "parsedMin", "parseFloat", "parsedMax", "isNaN", "Infinity", "temp", "filterFns", "Filters", "filterFn", "columnFilters", "globalFilter", "onColumnFiltersChange", "onGlobalFilterChange", "filterFromLeafRows", "maxLeafRowFilterDepth", "globalFilterFn", "getColumnCanGlobalFilter", "_table$getCoreRowMode", "value", "getCoreRowModel", "_getAllCellsByColumnId", "getAutoFilterFn", "firstRow", "getFilterFn", "_table$options$filter", "_table$options$filter2", "getCanFilter", "_table$options$enable2", "enableColumnFilter", "enableColumnFilters", "enableFilters", "getCanGlobalFilter", "_column$columnDef$ena2", "_table$options$enable3", "_table$options$enable4", "_table$options$getCol", "enableGlobalFilter", "getIsFiltered", "getFilterIndex", "getFilterValue", "_table$getState$colum", "_table$getState$colum2", "_table$getState$colum3", "setFilterValue", "setColumnFilters", "previousfilter", "newFilter", "shouldAutoRemoveFilter", "_old$filter", "newFilterObj", "_old$map", "_getFacetedRowModel", "getFacetedRowModel", "getPreFilteredRowModel", "_getFacetedUniqueValues", "getFacetedUniqueValues", "Map", "_getFacetedMinMaxValues", "getFacetedMinMaxValues", "columnFiltersMeta", "getGlobalAutoFilterFn", "getGlobalFilterFn", "_table$options$filter3", "_table$options$filter4", "getAllLeafColumns", "updateFn", "_functionalUpdate", "setGlobalFilter", "resetGlobalFilter", "resetColumnFilters", "getFilteredRowModel", "_getFilteredRowModel", "manualFiltering", "_getGlobalFacetedRowModel", "getGlobalFacetedRowModel", "_getGlobalFacetedUniqueValues", "getGlobalFacetedUniqueValues", "_getGlobalFacetedMinMaxValues", "getGlobalFacetedMinMaxValues", "_leafRows", "childRows", "next", "nextValue", "extent", "mean", "leafRows", "count", "median", "mid", "floor", "nums", "sort", "a", "b", "unique", "from", "Set", "uniqueCount", "_columnId", "aggregationFns", "Grouping", "aggregatedCell", "props", "_toString", "_props$getValue", "aggregationFn", "grouping", "onGroupingChange", "groupedColumnMode", "toggleGrouping", "setGrouping", "getCanGroup", "enableGrouping", "getIsGrouped", "_table$getState$group", "getGroupedIndex", "_table$getState$group2", "indexOf", "getToggleGroupingHandler", "canGroup", "getAutoAggregationFn", "prototype", "call", "getAggregationFn", "_table$options$aggreg", "_table$options$aggreg2", "resetGrouping", "_table$initialState$g", "getPreGroupedRowModel", "getGroupedRowModel", "_getGroupedRowModel", "manualGrouping", "groupingColumnId", "getGroupingValue", "_groupingValuesCache", "hasOwnProperty", "original", "createCell", "cell", "getIsPlaceholder", "getIsAggregated", "nonGroupingColumns", "col", "groupingColumns", "g", "Ordering", "columnOrder", "onColumnOrderChange", "setColumnOrder", "resetColumnOrder", "orderedColumns", "columnOrderCopy", "columnsCopy", "targetColumnId", "shift", "foundIndex", "splice", "defaultPageIndex", "defaultPageSize", "getDefaultPaginationState", "pageIndex", "pageSize", "Pagination", "pagination", "onPaginationChange", "_autoResetPageIndex", "autoResetPageIndex", "manualPagination", "resetPageIndex", "setPagination", "safeUpdater", "newState", "resetPagination", "_table$initialState$p", "setPageIndex", "maxPageIndex", "pageCount", "_table$initialState$p2", "resetPageSize", "_table$initialState$p3", "_table$initialState2", "setPageSize", "topRowIndex", "setPageCount", "_table$options$pageCo", "newPageCount", "getPageOptions", "getPageCount", "pageOptions", "fill", "i", "debugTable", "getCanPreviousPage", "getCanNextPage", "previousPage", "nextPage", "getPaginationRowModel", "_getPaginationRowModel", "_table$options$pageCo2", "ceil", "rows", "getDefaultColumnPinningState", "getDefaultRowPinningState", "top", "bottom", "<PERSON>nning", "rowPinning", "onColumnPinningChange", "onRowPinningChange", "pin", "columnIds", "setColumnPinning", "_old$left3", "_old$right3", "_old$left", "_old$right", "_old$left2", "_old$right2", "getCanPin", "_d$columnDef$enablePi", "enablePinning", "enableColumnPinning", "getIsPinned", "leafColumnIds", "isLeft", "isRight", "getPinnedIndex", "includeLeafRows", "includeParentRows", "leafRowIds", "getLeafRows", "parentRowIds", "getParentRows", "setRowPinning", "_old$top3", "_old$bottom3", "_old$top", "_old$bottom", "has", "_old$top2", "_old$bottom2", "_ref4", "enableRowPinning", "isTop", "isBottom", "_table$_getPinnedRows", "_visiblePinnedRowIds$", "visiblePinnedRowIds", "_getPinnedRows", "_ref5", "getCenterVisibleCells", "_getAllVisibleCells", "allCells", "leftAndRight", "debugRows", "getLeftVisibleCells", "cells", "getRightVisibleCells", "resetColumnPinning", "getIsSomeColumnsPinned", "_pinningState$positio", "pinningState", "_pinningState$left", "_pinningState$right", "getLeftLeafColumns", "getRightLeafColumns", "getCenterLeafColumns", "resetRowPinning", "_table$initialState$r", "getIsSomeRowsPinned", "_pinningState$positio2", "_pinningState$top", "_pinningState$bottom", "visibleRows", "pinnedRowIds", "_table$options$keepPi", "keepPinnedRows", "getTopRows", "getBottomRows", "getCenterRows", "allRows", "topAndBottom", "RowSelection", "rowSelection", "onRowSelectionChange", "enableRowSelection", "enableMultiRowSelection", "enableSubRowSelection", "setRowSelection", "resetRowSelection", "toggleAllRowsSelected", "getIsAllRowsSelected", "preGroupedFlatRows", "getCanSelect", "toggleAllPageRowsSelected", "resolvedValue", "getIsAllPageRowsSelected", "mutateRowIsSelected", "getPreSelectedRowModel", "getSelectedRowModel", "rowModel", "selectRowsFn", "getFilteredSelectedRowModel", "getGroupedSelectedRowModel", "isAllRowsSelected", "paginationFlatRows", "isAllPageRowsSelected", "getIsSomeRowsSelected", "_table$getState$rowSe", "totalSelected", "getIsSomePageRowsSelected", "getIsSelected", "getIsSomeSelected", "getToggleAllRowsSelectedHandler", "target", "checked", "getToggleAllPageRowsSelectedHandler", "toggleSelected", "isSelected", "_opts$selectChildren", "selectedRowIds", "select<PERSON><PERSON><PERSON><PERSON>", "isRowSelected", "isSubRowSelected", "getIsAllSubRowsSelected", "getCanSelectSubRows", "getCanMultiSelect", "getToggleSelectedHandler", "canSelect", "_target", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "newSelectedFlatRows", "newSelectedRowsById", "recurseRows", "_row$subRows2", "selection", "_selection$row$id", "_row$subRows3", "allChildrenSelected", "someSelected", "subRow", "subRowChildrenSelected", "reSplitAlphaNumeric", "alphanumeric", "rowA", "rowB", "compareAlphanumeric", "alphanumericCaseSensitive", "text", "compareBasic", "textCaseSensitive", "datetime", "basic", "aStr", "bStr", "aa", "bb", "an", "parseInt", "bn", "combo", "sortingFns", "Sorting", "sorting", "sortingFn", "sortUndefined", "onSortingChange", "isMultiSortEvent", "shift<PERSON>ey", "getAutoSortingFn", "firstRows", "slice", "isString", "getAutoSortDir", "getSortingFn", "_table$options$sortin", "_table$options$sortin2", "toggleSorting", "desc", "multi", "nextSortingOrder", "getNextSortingOrder", "hasManual<PERSON><PERSON>ue", "setSorting", "existingSorting", "existingIndex", "newSorting", "sortAction", "nextDesc", "getCanMultiSort", "_table$options$maxMul", "maxMultiSortColCount", "getFirstSortDir", "_column$columnDef$sor", "sortDescFirst", "firstSortDirection", "isSorted", "getIsSorted", "enableSortingRemoval", "enableMultiRemove", "getCanSort", "enableSorting", "enableMultiSort", "_table$getState$sorti", "columnSort", "getSortIndex", "_table$getState$sorti2", "_table$getState$sorti3", "clearSorting", "getToggleSortingHandler", "canSort", "resetSorting", "_table$initialState$s", "getPreSortedRowModel", "_getSortedRowModel", "manualSorting", "Visibility", "columnVisibility", "onColumnVisibilityChange", "toggleVisibility", "getCanHide", "setColumnVisibility", "enableHiding", "getToggleVisibilityHandler", "getAllCells", "getVisibleCells", "makeVisibleColumnsMethod", "getColumns", "getVisibleFlatColumns", "getAllFlatColumns", "getCenterVisibleLeafColumns", "resetColumnVisibility", "toggleAllColumnsVisible", "_value", "getIsAllColumnsVisible", "obj", "getIsSomeColumnsVisible", "getToggleAllColumnsVisibilityHandler", "features", "_options$initialState", "defaultOptions", "assign", "mergeOptions", "coreInitialState", "_feature$getInitialSt", "queuedTimeout", "coreInstance", "cb", "Promise", "resolve", "then", "catch", "error", "setTimeout", "reset", "setOptions", "newOptions", "onStateChange", "_getRowId", "getRowId", "_getCoreRowModel", "searchAll", "_defaultColumn", "_props$renderValue$to", "_props$renderValue", "renderValue", "_getColumnDefs", "columnDefs", "recurseColumns", "groupingColumnDef", "_getAllFlatColumnsById", "flatColumns", "acc", "getRenderValue", "_cell$getValue", "renderFallbackValue", "rowIndex", "_valuesCache", "_uniqueValuesCache", "getUniqueValues", "getParentRow", "parentRows", "parentRow", "createColumnHelper", "accessor", "display", "group", "data", "accessRows", "originalRows", "getSubRows", "_row$originalSubRows", "originalSubRows", "filterRows", "filterRowImpl", "filterRowModelFromLeafs", "filterRowModelFromRoot", "rowsToFilter", "filterRow", "_table$options$maxLea", "newFilteredFlatRows", "newFilteredRowsById", "recurseFilterRows", "newRow", "_table$options$maxLea2", "pass", "resolvedColumnFilters", "resolvedGlobalFilters", "_filterFn$resolveFilt", "filterableIds", "globallyFilterableColumns", "_globalFilterFn$resol", "currentColumnFilter", "currentGlobalFilter", "j", "filterMeta", "__global__", "filterRowsImpl", "preRowModel", "_table$getColumn", "facetedRowModel", "facetedUniqueValues", "_facetedUniqueValues$", "set", "get", "_facetedRowModel$flat", "firstValue", "facetedMinMaxValues", "sortingState", "sortedFlatRows", "availableSorting", "columnInfoById", "sortEntry", "invertSorting", "sortData", "sortedData", "_sortEntry$desc", "columnInfo", "isDesc", "sortInt", "aValue", "bValue", "aUndefined", "bUndefined", "existingGrouping", "groupedFlatRows", "groupedRowsById", "groupUpRecursively", "rowGroupsMap", "groupBy", "aggregatedGroupedRows", "entries", "groupingValue", "groupedRows", "_groupedRows$0$getVal", "aggregateFn", "groupMap", "res<PERSON>ey", "previous", "expandRows", "expandedRows", "handleRow", "pageStart", "pageEnd", "paginatedRowModel", "flexRender", "Comp", "props", "isReactComponent", "createElement", "component", "isClassComponent", "isExoticComponent", "proto", "Object", "getPrototypeOf", "prototype", "$$typeof", "includes", "description", "useReactTable", "options", "resolvedOptions", "state", "onStateChange", "renderFallbackValue", "tableRef", "useState", "current", "createTable", "setState", "initialState", "setOptions", "prev", "updater"]}