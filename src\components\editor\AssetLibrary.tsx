import React, { useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../store';
import { setSelectedAsset } from '../../store/editor';
import { Asset, AssetCategory, AssetType } from '../../types/editorTypes';
import { TileType } from '../../types/gameTypes';
import './AssetLibrary.scss';

// Default asset library with procedural desert-themed assets
const DEFAULT_ASSETS: Asset[] = [
  // Tile Assets
  {
    id: 'tile_basic',
    name: 'Basic Tile',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_basic.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 1, depth: 3 },
    tags: ['basic', 'tile', 'game'],
    description: 'Standard game tile for player movement',
  },
  {
    id: 'tile_healing',
    name: 'Healing Tile',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_healing.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 1, depth: 3 },
    tags: ['healing', 'tile', 'game', 'special'],
    description: 'Restores player health when landed on',
  },
  {
    id: 'tile_damage',
    name: 'Damage Tile',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_damage.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 1, depth: 3 },
    tags: ['damage', 'tile', 'game', 'special'],
    description: 'Damages player when landed on',
  },
  {
    id: 'tile_key',
    name: 'Key Tile',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_key.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 1, depth: 3 },
    tags: ['key', 'tile', 'game', 'special'],
    description: 'Grants keys to the player',
  },
  {
    id: 'tile_treasure_chest',
    name: 'Treasure Chest',
    category: AssetCategory.TILES,
    type: AssetType.TILE,
    thumbnail: '/assets/thumbnails/tile_treasure.png',
    defaultScale: [1, 1.5, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 2, depth: 3 },
    tags: ['treasure', 'chest', 'tile', 'game', 'win'],
    description: 'Win condition tile - requires keys to open',
  },

  // Rock Assets
  {
    id: 'rock_small',
    name: 'Small Rock',
    category: AssetCategory.ROCKS,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/rock_small.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 2, height: 1.5, depth: 2 },
    tags: ['rock', 'small', 'desert', 'decoration'],
    description: 'Small desert rock for environmental decoration',
  },
  {
    id: 'rock_medium',
    name: 'Medium Rock',
    category: AssetCategory.ROCKS,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/rock_medium.png',
    defaultScale: [1.5, 1.5, 1.5],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 3, height: 2.5, depth: 3 },
    tags: ['rock', 'medium', 'desert', 'decoration'],
    description: 'Medium-sized desert rock formation',
  },
  {
    id: 'rock_large',
    name: 'Large Rock',
    category: AssetCategory.ROCKS,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/rock_large.png',
    defaultScale: [2, 2, 2],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 4, height: 3.5, depth: 4 },
    tags: ['rock', 'large', 'desert', 'decoration'],
    description: 'Large desert rock outcropping',
  },

  // Vegetation Assets
  {
    id: 'cactus_small',
    name: 'Small Cactus',
    category: AssetCategory.VEGETATION,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/cactus_small.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 0.8, height: 2, depth: 0.8 },
    tags: ['cactus', 'small', 'desert', 'vegetation'],
    description: 'Small desert cactus plant',
  },
  {
    id: 'cactus_large',
    name: 'Large Cactus',
    category: AssetCategory.VEGETATION,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/cactus_large.png',
    defaultScale: [1.2, 1.5, 1.2],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 1, height: 4, depth: 1 },
    tags: ['cactus', 'large', 'desert', 'vegetation'],
    description: 'Large desert cactus with arms',
  },
  {
    id: 'palm_tree',
    name: 'Palm Tree',
    category: AssetCategory.VEGETATION,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/palm_tree.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 4, height: 8, depth: 4 },
    tags: ['palm', 'tree', 'oasis', 'vegetation'],
    description: 'Desert palm tree for oasis areas',
  },

  // Terrain Assets
  {
    id: 'sand_dune',
    name: 'Sand Dune',
    category: AssetCategory.TERRAIN,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/sand_dune.png',
    defaultScale: [3, 2, 3],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 6, height: 3, depth: 6 },
    tags: ['sand', 'dune', 'terrain', 'desert'],
    description: 'Natural sand dune formation',
  },
  {
    id: 'oasis_water',
    name: 'Oasis Water',
    category: AssetCategory.TERRAIN,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/oasis_water.png',
    defaultScale: [4, 0.2, 4],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 8, height: 0.5, depth: 8 },
    tags: ['oasis', 'water', 'terrain', 'desert'],
    description: 'Central oasis water feature',
  },

  // Decorative Assets
  {
    id: 'desert_grass',
    name: 'Desert Grass',
    category: AssetCategory.DECORATIVE,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/desert_grass.png',
    defaultScale: [0.5, 0.5, 0.5],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 0.5, height: 0.8, depth: 0.5 },
    tags: ['grass', 'desert', 'small', 'decoration'],
    description: 'Small desert grass clumps',
  },
  {
    id: 'bone_pile',
    name: 'Bone Pile',
    category: AssetCategory.DECORATIVE,
    type: AssetType.PROCEDURAL,
    thumbnail: '/assets/thumbnails/bone_pile.png',
    defaultScale: [1, 0.5, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 1.5, height: 0.8, depth: 1.5 },
    tags: ['bones', 'desert', 'decoration', 'spooky'],
    description: 'Desert animal bone remains',
  },

  // Lighting Assets
  {
    id: 'point_light',
    name: 'Point Light',
    category: AssetCategory.LIGHTING,
    type: AssetType.LIGHT,
    thumbnail: '/assets/thumbnails/point_light.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 0.5, height: 0.5, depth: 0.5 },
    tags: ['light', 'point', 'illumination'],
    description: 'Omnidirectional point light source',
  },
  {
    id: 'spot_light',
    name: 'Spot Light',
    category: AssetCategory.LIGHTING,
    type: AssetType.LIGHT,
    thumbnail: '/assets/thumbnails/spot_light.png',
    defaultScale: [1, 1, 1],
    defaultRotation: [0, 0, 0],
    boundingBox: { width: 0.5, height: 1, depth: 0.5 },
    tags: ['light', 'spot', 'directional', 'illumination'],
    description: 'Directional spotlight for focused illumination',
  },
];

interface AssetLibraryProps {
  className?: string;
}

export const AssetLibrary: React.FC<AssetLibraryProps> = ({ className }) => {
  const dispatch = useDispatch();
  const { selectedAsset, assets } = useSelector((state: RootState) => state.editor);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<AssetCategory | 'all'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Use default assets if none are loaded
  const availableAssets = assets.length > 0 ? assets : DEFAULT_ASSETS;

  const filteredAssets = useMemo(() => {
    return availableAssets.filter(asset => {
      const matchesSearch = asset.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           asset.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      const matchesCategory = selectedCategory === 'all' || asset.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [availableAssets, searchTerm, selectedCategory]);

  const categories = Object.values(AssetCategory);

  const handleAssetSelect = (asset: Asset) => {
    dispatch(setSelectedAsset(asset));
  };

  const handleAssetDoubleClick = (asset: Asset) => {
    // TODO: Implement quick placement at camera center
    console.log('Quick place asset:', asset.name);
  };

  return (
    <div className={`asset-library ${className || ''}`}>
      <div className="asset-library__header">
        <h3>Asset Library</h3>
        
        <div className="asset-library__controls">
          <div className="search-box">
            <input
              type="text"
              placeholder="Search assets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value as AssetCategory | 'all')}
            className="category-filter"
          >
            <option value="all">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </option>
            ))}
          </select>
          
          <div className="view-mode-toggle">
            <button
              className={viewMode === 'grid' ? 'active' : ''}
              onClick={() => setViewMode('grid')}
              title="Grid View"
            >
              ⊞
            </button>
            <button
              className={viewMode === 'list' ? 'active' : ''}
              onClick={() => setViewMode('list')}
              title="List View"
            >
              ☰
            </button>
          </div>
        </div>
      </div>

      <div className={`asset-library__content asset-library__content--${viewMode}`}>
        {filteredAssets.map(asset => (
          <div
            key={asset.id}
            className={`asset-item ${selectedAsset?.id === asset.id ? 'selected' : ''}`}
            onClick={() => handleAssetSelect(asset)}
            onDoubleClick={() => handleAssetDoubleClick(asset)}
            title={asset.description}
          >
            <div className="asset-item__thumbnail">
              <img
                src={asset.thumbnail}
                alt={asset.name}
                onError={(e) => {
                  // Fallback to a default thumbnail or colored square
                  (e.target as HTMLImageElement).src = `data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64"><rect width="64" height="64" fill="%23${Math.floor(Math.random()*16777215).toString(16)}"/><text x="32" y="32" text-anchor="middle" dy=".3em" fill="white" font-size="10">${asset.name.charAt(0)}</text></svg>`;
                }}
              />
              <div className="asset-item__category">{asset.category}</div>
            </div>
            
            <div className="asset-item__info">
              <div className="asset-item__name">{asset.name}</div>
              {viewMode === 'list' && (
                <div className="asset-item__details">
                  <div className="asset-item__description">{asset.description}</div>
                  <div className="asset-item__tags">
                    {asset.tags.map(tag => (
                      <span key={tag} className="tag">{tag}</span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {filteredAssets.length === 0 && (
        <div className="asset-library__empty">
          <p>No assets found matching your criteria.</p>
          <p>Try adjusting your search or category filter.</p>
        </div>
      )}
    </div>
  );
};
