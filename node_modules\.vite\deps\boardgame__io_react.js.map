{"version": 3, "sources": ["../../react-cookies/node_modules/cookie/index.js", "../../react-cookies/build/cookie.js", "../../boardgame.io/dist/esm/react.js"], "sourcesContent": ["/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */\n\n'use strict';\n\n/**\n * Module exports.\n * @public\n */\n\nexports.parse = parse;\nexports.serialize = serialize;\n\n/**\n * Module variables.\n * @private\n */\n\nvar decode = decodeURIComponent;\nvar encode = encodeURIComponent;\nvar pairSplitRegExp = /; */;\n\n/**\n * RegExp to match field-content in RFC 7230 sec 3.2\n *\n * field-content = field-vchar [ 1*( SP / HTAB ) field-vchar ]\n * field-vchar   = VCHAR / obs-text\n * obs-text      = %x80-FF\n */\n\nvar fieldContentRegExp = /^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;\n\n/**\n * Parse a cookie header.\n *\n * Parse the given cookie header string into an object\n * The object has the various cookies as keys(names) => values\n *\n * @param {string} str\n * @param {object} [options]\n * @return {object}\n * @public\n */\n\nfunction parse(str, options) {\n  if (typeof str !== 'string') {\n    throw new TypeError('argument str must be a string');\n  }\n\n  var obj = {}\n  var opt = options || {};\n  var pairs = str.split(pairSplitRegExp);\n  var dec = opt.decode || decode;\n\n  for (var i = 0; i < pairs.length; i++) {\n    var pair = pairs[i];\n    var eq_idx = pair.indexOf('=');\n\n    // skip things that don't look like key=value\n    if (eq_idx < 0) {\n      continue;\n    }\n\n    var key = pair.substr(0, eq_idx).trim()\n    var val = pair.substr(++eq_idx, pair.length).trim();\n\n    // quoted values\n    if ('\"' == val[0]) {\n      val = val.slice(1, -1);\n    }\n\n    // only assign once\n    if (undefined == obj[key]) {\n      obj[key] = tryDecode(val, dec);\n    }\n  }\n\n  return obj;\n}\n\n/**\n * Serialize data into a cookie header.\n *\n * Serialize the a name value pair into a cookie string suitable for\n * http headers. An optional options object specified cookie parameters.\n *\n * serialize('foo', 'bar', { httpOnly: true })\n *   => \"foo=bar; httpOnly\"\n *\n * @param {string} name\n * @param {string} val\n * @param {object} [options]\n * @return {string}\n * @public\n */\n\nfunction serialize(name, val, options) {\n  var opt = options || {};\n  var enc = opt.encode || encode;\n\n  if (typeof enc !== 'function') {\n    throw new TypeError('option encode is invalid');\n  }\n\n  if (!fieldContentRegExp.test(name)) {\n    throw new TypeError('argument name is invalid');\n  }\n\n  var value = enc(val);\n\n  if (value && !fieldContentRegExp.test(value)) {\n    throw new TypeError('argument val is invalid');\n  }\n\n  var str = name + '=' + value;\n\n  if (null != opt.maxAge) {\n    var maxAge = opt.maxAge - 0;\n    if (isNaN(maxAge)) throw new Error('maxAge should be a Number');\n    str += '; Max-Age=' + Math.floor(maxAge);\n  }\n\n  if (opt.domain) {\n    if (!fieldContentRegExp.test(opt.domain)) {\n      throw new TypeError('option domain is invalid');\n    }\n\n    str += '; Domain=' + opt.domain;\n  }\n\n  if (opt.path) {\n    if (!fieldContentRegExp.test(opt.path)) {\n      throw new TypeError('option path is invalid');\n    }\n\n    str += '; Path=' + opt.path;\n  }\n\n  if (opt.expires) {\n    if (typeof opt.expires.toUTCString !== 'function') {\n      throw new TypeError('option expires is invalid');\n    }\n\n    str += '; Expires=' + opt.expires.toUTCString();\n  }\n\n  if (opt.httpOnly) {\n    str += '; HttpOnly';\n  }\n\n  if (opt.secure) {\n    str += '; Secure';\n  }\n\n  if (opt.sameSite) {\n    var sameSite = typeof opt.sameSite === 'string'\n      ? opt.sameSite.toLowerCase() : opt.sameSite;\n\n    switch (sameSite) {\n      case true:\n        str += '; SameSite=Strict';\n        break;\n      case 'lax':\n        str += '; SameSite=Lax';\n        break;\n      case 'strict':\n        str += '; SameSite=Strict';\n        break;\n      default:\n        throw new TypeError('option sameSite is invalid');\n    }\n  }\n\n  return str;\n}\n\n/**\n * Try decoding a string using a decoding function.\n *\n * @param {string} str\n * @param {function} decode\n * @private\n */\n\nfunction tryDecode(str, decode) {\n  try {\n    return decode(str);\n  } catch (e) {\n    return str;\n  }\n}\n", "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _typeof = typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\" ? function (obj) { return typeof obj; } : function (obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; };\n\nexports.load = load;\nexports.loadAll = loadAll;\nexports.select = select;\nexports.save = save;\nexports.remove = remove;\nexports.setRawCookie = setRawCookie;\nexports.plugToRequest = plugToRequest;\n\nvar _cookie = require('cookie');\n\nvar _cookie2 = _interopRequireDefault(_cookie);\n\nvar _objectAssign = require('object-assign');\n\nvar _objectAssign2 = _interopRequireDefault(_objectAssign);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar IS_NODE = typeof document === 'undefined' || typeof process !== 'undefined' && process.env && process.env.NODE_ENV === 'test';\nvar _rawCookie = {};\nvar _res = void 0;\n\nfunction _isResWritable() {\n  return _res && !_res.headersSent;\n}\n\nfunction load(name, doNotParse) {\n  var cookies = IS_NODE ? _rawCookie : _cookie2.default.parse(document.cookie);\n  var cookieVal = cookies && cookies[name];\n\n  if (typeof doNotParse === 'undefined') {\n    doNotParse = !cookieVal || cookieVal[0] !== '{' && cookieVal[0] !== '[';\n  }\n\n  if (!doNotParse) {\n    try {\n      cookieVal = JSON.parse(cookieVal);\n    } catch (err) {\n      // Not serialized object\n    }\n  }\n\n  return cookieVal;\n}\n\nfunction loadAll(doNotParse) {\n  var cookies = IS_NODE ? _rawCookie : _cookie2.default.parse(document.cookie);\n  var cookieVal = cookies;\n\n  if (typeof doNotParse === 'undefined') {\n    doNotParse = !cookieVal || cookieVal[0] !== '{' && cookieVal[0] !== '[';\n  }\n\n  if (!doNotParse) {\n    try {\n      cookieVal = JSON.parse(cookieVal);\n    } catch (err) {\n      // Not serialized object\n    }\n  }\n\n  return cookieVal;\n}\n\nfunction select(regex) {\n  var cookies = IS_NODE ? _rawCookie : _cookie2.default.parse(document.cookie);\n\n  if (!cookies) {\n    return {};\n  }\n\n  if (!regex) {\n    return cookies;\n  }\n\n  return Object.keys(cookies).reduce(function (accumulator, name) {\n    if (!regex.test(name)) {\n      return accumulator;\n    }\n\n    var newCookie = {};\n    newCookie[name] = cookies[name];\n    return (0, _objectAssign2.default)({}, accumulator, newCookie);\n  }, {});\n}\n\nfunction save(name, val, opt) {\n  _rawCookie[name] = val;\n\n  // Allow you to work with cookies as objects.\n  if ((typeof val === 'undefined' ? 'undefined' : _typeof(val)) === 'object') {\n    _rawCookie[name] = JSON.stringify(val);\n  }\n\n  // Cookies only work in the browser\n  if (!IS_NODE) {\n    document.cookie = _cookie2.default.serialize(name, _rawCookie[name], opt);\n  }\n\n  if (_isResWritable() && _res.cookie) {\n    _res.cookie(name, val, opt);\n  }\n}\n\nfunction remove(name, opt) {\n  delete _rawCookie[name];\n\n  if (typeof opt === 'undefined') {\n    opt = {};\n  } else if (typeof opt === 'string') {\n    // Will be deprecated in future versions\n    opt = { path: opt };\n  } else {\n    // Prevent mutation of opt below\n    opt = (0, _objectAssign2.default)({}, opt);\n  }\n\n  if (typeof document !== 'undefined') {\n    opt.expires = new Date(1970, 1, 1, 0, 0, 1);\n    opt.maxAge = 0;\n    document.cookie = _cookie2.default.serialize(name, '', opt);\n  }\n\n  if (_isResWritable() && _res.clearCookie) {\n    _res.clearCookie(name, opt);\n  }\n}\n\nfunction setRawCookie(rawCookie) {\n  if (rawCookie) {\n    _rawCookie = _cookie2.default.parse(rawCookie);\n  } else {\n    _rawCookie = {};\n  }\n}\n\nfunction plugToRequest(req, res) {\n  if (req.cookie) {\n    _rawCookie = req.cookie;\n  } else if (req.cookies) {\n    _rawCookie = req.cookies;\n  } else if (req.headers && req.headers.cookie) {\n    setRawCookie(req.headers.cookie);\n  } else {\n    _rawCookie = {};\n  }\n\n  _res = res;\n\n  return function unplug() {\n    _res = null;\n    _rawCookie = {};\n  };\n}\n\nexports.default = {\n  setRawCookie: setRawCookie,\n  load: load,\n  loadAll: loadAll,\n  select: select,\n  save: save,\n  remove: remove,\n  plugToRequest: plugToRequest\n};", "import 'nanoid/non-secure';\nimport './Debug-8242c26e.js';\nimport 'redux';\nimport './turn-order-8cc4909b.js';\nimport 'immer';\nimport './plugin-random-087f861e.js';\nimport 'lodash.isplainobject';\nimport './reducer-24ea3e4c.js';\nimport 'rfc6902';\nimport './initialize-7316768f.js';\nimport './transport-ce07b771.js';\nimport { C as Client$1 } from './client-f7f02b82.js';\nimport 'flatted';\nimport 'setimmediate';\nimport { M as MCTSBot } from './ai-7998b00f.js';\nimport { L as LobbyClient } from './client-5f57c3f2.js';\nimport React from 'react';\nimport PropTypes from 'prop-types';\nimport Cookies from 'react-cookies';\nimport './util-991e76bb.js';\nimport { S as SocketIO, L as Local } from './socketio-a82b84e4.js';\nimport './master-17425f07.js';\nimport './filter-player-view-43ed49b0.js';\nimport 'socket.io-client';\n\n/*\r\n * Copyright 2017 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\n/**\r\n * Client\r\n *\r\n * boardgame.io React client.\r\n *\r\n * @param {...object} game - The return value of `Game`.\r\n * @param {...object} numPlayers - The number of players.\r\n * @param {...object} board - The React component for the game.\r\n * @param {...object} loading - (optional) The React component for the loading state.\r\n * @param {...object} multiplayer - Set to a falsy value or a transportFactory, e.g., SocketIO()\r\n * @param {...object} debug - Enables the Debug UI.\r\n * @param {...object} enhancer - Optional enhancer to send to the Redux store\r\n *\r\n * Returns:\r\n *   A React component that wraps board and provides an\r\n *   API through props for it to interact with the framework\r\n *   and dispatch actions such as MAKE_MOVE, GAME_EVENT, RESET,\r\n *   UNDO and REDO.\r\n */\r\nfunction Client(opts) {\r\n    var _a;\r\n    const { game, numPlayers, board, multiplayer, enhancer } = opts;\r\n    let { loading, debug } = opts;\r\n    // Component that is displayed before the client has synced\r\n    // with the game master.\r\n    if (loading === undefined) {\r\n        const Loading = () => React.createElement(\"div\", { className: \"bgio-loading\" }, \"connecting...\");\r\n        loading = Loading;\r\n    }\r\n    /*\r\n     * WrappedBoard\r\n     *\r\n     * The main React component that wraps the passed in\r\n     * board component and adds the API to its props.\r\n     */\r\n    return _a = class WrappedBoard extends React.Component {\r\n            constructor(props) {\r\n                super(props);\r\n                if (debug === undefined) {\r\n                    debug = props.debug;\r\n                }\r\n                this.client = Client$1({\r\n                    game,\r\n                    debug,\r\n                    numPlayers,\r\n                    multiplayer,\r\n                    matchID: props.matchID,\r\n                    playerID: props.playerID,\r\n                    credentials: props.credentials,\r\n                    enhancer,\r\n                });\r\n            }\r\n            componentDidMount() {\r\n                this.unsubscribe = this.client.subscribe(() => this.forceUpdate());\r\n                this.client.start();\r\n            }\r\n            componentWillUnmount() {\r\n                this.client.stop();\r\n                this.unsubscribe();\r\n            }\r\n            componentDidUpdate(prevProps) {\r\n                if (this.props.matchID != prevProps.matchID) {\r\n                    this.client.updateMatchID(this.props.matchID);\r\n                }\r\n                if (this.props.playerID != prevProps.playerID) {\r\n                    this.client.updatePlayerID(this.props.playerID);\r\n                }\r\n                if (this.props.credentials != prevProps.credentials) {\r\n                    this.client.updateCredentials(this.props.credentials);\r\n                }\r\n            }\r\n            render() {\r\n                const state = this.client.getState();\r\n                if (state === null) {\r\n                    return React.createElement(loading);\r\n                }\r\n                let _board = null;\r\n                if (board) {\r\n                    _board = React.createElement(board, {\r\n                        ...state,\r\n                        ...this.props,\r\n                        isMultiplayer: !!multiplayer,\r\n                        moves: this.client.moves,\r\n                        events: this.client.events,\r\n                        matchID: this.client.matchID,\r\n                        playerID: this.client.playerID,\r\n                        reset: this.client.reset,\r\n                        undo: this.client.undo,\r\n                        redo: this.client.redo,\r\n                        log: this.client.log,\r\n                        matchData: this.client.matchData,\r\n                        sendChatMessage: this.client.sendChatMessage,\r\n                        chatMessages: this.client.chatMessages,\r\n                    });\r\n                }\r\n                return React.createElement(\"div\", { className: \"bgio-client\" }, _board);\r\n            }\r\n        },\r\n        _a.propTypes = {\r\n            // The ID of a game to connect to.\r\n            // Only relevant in multiplayer.\r\n            matchID: PropTypes.string,\r\n            // The ID of the player associated with this client.\r\n            // Only relevant in multiplayer.\r\n            playerID: PropTypes.string,\r\n            // This client's authentication credentials.\r\n            // Only relevant in multiplayer.\r\n            credentials: PropTypes.string,\r\n            // Enable / disable the Debug UI.\r\n            debug: PropTypes.any,\r\n        },\r\n        _a.defaultProps = {\r\n            matchID: 'default',\r\n            playerID: null,\r\n            credentials: null,\r\n            debug: true,\r\n        },\r\n        _a;\r\n}\n\n/*\r\n * Copyright 2018 The boardgame.io Authors\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nclass _LobbyConnectionImpl {\r\n    constructor({ server, gameComponents, playerName, playerCredentials, }) {\r\n        this.client = new LobbyClient({ server });\r\n        this.gameComponents = gameComponents;\r\n        this.playerName = playerName || 'Visitor';\r\n        this.playerCredentials = playerCredentials;\r\n        this.matches = [];\r\n    }\r\n    async refresh() {\r\n        try {\r\n            this.matches = [];\r\n            const games = await this.client.listGames();\r\n            for (const game of games) {\r\n                if (!this._getGameComponents(game))\r\n                    continue;\r\n                const { matches } = await this.client.listMatches(game);\r\n                this.matches.push(...matches);\r\n            }\r\n        }\r\n        catch (error) {\r\n            throw new Error('failed to retrieve list of matches (' + error + ')');\r\n        }\r\n    }\r\n    _getMatchInstance(matchID) {\r\n        for (const inst of this.matches) {\r\n            if (inst['matchID'] === matchID)\r\n                return inst;\r\n        }\r\n    }\r\n    _getGameComponents(gameName) {\r\n        for (const comp of this.gameComponents) {\r\n            if (comp.game.name === gameName)\r\n                return comp;\r\n        }\r\n    }\r\n    _findPlayer(playerName) {\r\n        for (const inst of this.matches) {\r\n            if (inst.players.some((player) => player.name === playerName))\r\n                return inst;\r\n        }\r\n    }\r\n    async join(gameName, matchID, playerID) {\r\n        try {\r\n            let inst = this._findPlayer(this.playerName);\r\n            if (inst) {\r\n                throw new Error('player has already joined ' + inst.matchID);\r\n            }\r\n            inst = this._getMatchInstance(matchID);\r\n            if (!inst) {\r\n                throw new Error('game instance ' + matchID + ' not found');\r\n            }\r\n            const json = await this.client.joinMatch(gameName, matchID, {\r\n                playerID,\r\n                playerName: this.playerName,\r\n            });\r\n            inst.players[Number.parseInt(playerID)].name = this.playerName;\r\n            this.playerCredentials = json.playerCredentials;\r\n        }\r\n        catch (error) {\r\n            throw new Error('failed to join match ' + matchID + ' (' + error + ')');\r\n        }\r\n    }\r\n    async leave(gameName, matchID) {\r\n        try {\r\n            const inst = this._getMatchInstance(matchID);\r\n            if (!inst)\r\n                throw new Error('match instance not found');\r\n            for (const player of inst.players) {\r\n                if (player.name === this.playerName) {\r\n                    await this.client.leaveMatch(gameName, matchID, {\r\n                        playerID: player.id.toString(),\r\n                        credentials: this.playerCredentials,\r\n                    });\r\n                    delete player.name;\r\n                    delete this.playerCredentials;\r\n                    return;\r\n                }\r\n            }\r\n            throw new Error('player not found in match');\r\n        }\r\n        catch (error) {\r\n            throw new Error('failed to leave match ' + matchID + ' (' + error + ')');\r\n        }\r\n    }\r\n    async disconnect() {\r\n        const inst = this._findPlayer(this.playerName);\r\n        if (inst) {\r\n            await this.leave(inst.gameName, inst.matchID);\r\n        }\r\n        this.matches = [];\r\n        this.playerName = 'Visitor';\r\n    }\r\n    async create(gameName, numPlayers) {\r\n        try {\r\n            const comp = this._getGameComponents(gameName);\r\n            if (!comp)\r\n                throw new Error('game not found');\r\n            if (numPlayers < comp.game.minPlayers ||\r\n                numPlayers > comp.game.maxPlayers)\r\n                throw new Error('invalid number of players ' + numPlayers);\r\n            await this.client.createMatch(gameName, { numPlayers });\r\n        }\r\n        catch (error) {\r\n            throw new Error('failed to create match for ' + gameName + ' (' + error + ')');\r\n        }\r\n    }\r\n}\r\n/**\r\n * LobbyConnection\r\n *\r\n * Lobby model.\r\n *\r\n * @param {string}   server - '<host>:<port>' of the server.\r\n * @param {Array}    gameComponents - A map of Board and Game objects for the supported games.\r\n * @param {string}   playerName - The name of the player.\r\n * @param {string}   playerCredentials - The credentials currently used by the player, if any.\r\n *\r\n * Returns:\r\n *   A JS object that synchronizes the list of running game instances with the server and provides an API to create/join/start instances.\r\n */\r\nfunction LobbyConnection(opts) {\r\n    return new _LobbyConnectionImpl(opts);\r\n}\n\n/*\r\n * Copyright 2018 The boardgame.io Authors.\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nclass LobbyLoginForm extends React.Component {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.state = {\r\n            playerName: this.props.playerName,\r\n            nameErrorMsg: '',\r\n        };\r\n        this.onClickEnter = () => {\r\n            if (this.state.playerName === '')\r\n                return;\r\n            this.props.onEnter(this.state.playerName);\r\n        };\r\n        this.onKeyPress = (event) => {\r\n            if (event.key === 'Enter') {\r\n                this.onClickEnter();\r\n            }\r\n        };\r\n        this.onChangePlayerName = (event) => {\r\n            const name = event.target.value.trim();\r\n            this.setState({\r\n                playerName: name,\r\n                nameErrorMsg: name.length > 0 ? '' : 'empty player name',\r\n            });\r\n        };\r\n    }\r\n    render() {\r\n        return (React.createElement(\"div\", null,\r\n            React.createElement(\"p\", { className: \"phase-title\" }, \"Choose a player name:\"),\r\n            React.createElement(\"input\", { type: \"text\", value: this.state.playerName, onChange: this.onChangePlayerName, onKeyPress: this.onKeyPress }),\r\n            React.createElement(\"span\", { className: \"buttons\" },\r\n                React.createElement(\"button\", { className: \"buttons\", onClick: this.onClickEnter }, \"Enter\")),\r\n            React.createElement(\"br\", null),\r\n            React.createElement(\"span\", { className: \"error-msg\" },\r\n                this.state.nameErrorMsg,\r\n                React.createElement(\"br\", null))));\r\n    }\r\n}\r\nLobbyLoginForm.defaultProps = {\r\n    playerName: '',\r\n};\n\n/*\r\n * Copyright 2018 The boardgame.io Authors.\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nclass LobbyMatchInstance extends React.Component {\r\n    constructor() {\r\n        super(...arguments);\r\n        this._createSeat = (player) => {\r\n            return player.name || '[free]';\r\n        };\r\n        this._createButtonJoin = (inst, seatId) => (React.createElement(\"button\", { key: 'button-join-' + inst.matchID, onClick: () => this.props.onClickJoin(inst.gameName, inst.matchID, '' + seatId) }, \"Join\"));\r\n        this._createButtonLeave = (inst) => (React.createElement(\"button\", { key: 'button-leave-' + inst.matchID, onClick: () => this.props.onClickLeave(inst.gameName, inst.matchID) }, \"Leave\"));\r\n        this._createButtonPlay = (inst, seatId) => (React.createElement(\"button\", { key: 'button-play-' + inst.matchID, onClick: () => this.props.onClickPlay(inst.gameName, {\r\n                matchID: inst.matchID,\r\n                playerID: '' + seatId,\r\n                numPlayers: inst.players.length,\r\n            }) }, \"Play\"));\r\n        this._createButtonSpectate = (inst) => (React.createElement(\"button\", { key: 'button-spectate-' + inst.matchID, onClick: () => this.props.onClickPlay(inst.gameName, {\r\n                matchID: inst.matchID,\r\n                numPlayers: inst.players.length,\r\n            }) }, \"Spectate\"));\r\n        this._createInstanceButtons = (inst) => {\r\n            const playerSeat = inst.players.find((player) => player.name === this.props.playerName);\r\n            const freeSeat = inst.players.find((player) => !player.name);\r\n            if (playerSeat && freeSeat) {\r\n                // already seated: waiting for match to start\r\n                return this._createButtonLeave(inst);\r\n            }\r\n            if (freeSeat) {\r\n                // at least 1 seat is available\r\n                return this._createButtonJoin(inst, freeSeat.id);\r\n            }\r\n            // match is full\r\n            if (playerSeat) {\r\n                return (React.createElement(\"div\", null, [\r\n                    this._createButtonPlay(inst, playerSeat.id),\r\n                    this._createButtonLeave(inst),\r\n                ]));\r\n            }\r\n            // allow spectating\r\n            return this._createButtonSpectate(inst);\r\n        };\r\n    }\r\n    render() {\r\n        const match = this.props.match;\r\n        let status = 'OPEN';\r\n        if (!match.players.some((player) => !player.name)) {\r\n            status = 'RUNNING';\r\n        }\r\n        return (React.createElement(\"tr\", { key: 'line-' + match.matchID },\r\n            React.createElement(\"td\", { key: 'cell-name-' + match.matchID }, match.gameName),\r\n            React.createElement(\"td\", { key: 'cell-status-' + match.matchID }, status),\r\n            React.createElement(\"td\", { key: 'cell-seats-' + match.matchID }, match.players.map((player) => this._createSeat(player)).join(', ')),\r\n            React.createElement(\"td\", { key: 'cell-buttons-' + match.matchID }, this._createInstanceButtons(match))));\r\n    }\r\n}\n\n/*\r\n * Copyright 2018 The boardgame.io Authors.\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nclass LobbyCreateMatchForm extends React.Component {\r\n    constructor(props) {\r\n        super(props);\r\n        this.state = {\r\n            selectedGame: 0,\r\n            numPlayers: 2,\r\n        };\r\n        this._createGameNameOption = (game, idx) => {\r\n            return (React.createElement(\"option\", { key: 'name-option-' + idx, value: idx }, game.game.name));\r\n        };\r\n        this._createNumPlayersOption = (idx) => {\r\n            return (React.createElement(\"option\", { key: 'num-option-' + idx, value: idx }, idx));\r\n        };\r\n        this._createNumPlayersRange = (game) => {\r\n            return Array.from({ length: game.maxPlayers + 1 })\r\n                .map((_, i) => i)\r\n                .slice(game.minPlayers);\r\n        };\r\n        this.onChangeNumPlayers = (event) => {\r\n            this.setState({\r\n                numPlayers: Number.parseInt(event.target.value),\r\n            });\r\n        };\r\n        this.onChangeSelectedGame = (event) => {\r\n            const idx = Number.parseInt(event.target.value);\r\n            this.setState({\r\n                selectedGame: idx,\r\n                numPlayers: this.props.games[idx].game.minPlayers,\r\n            });\r\n        };\r\n        this.onClickCreate = () => {\r\n            this.props.createMatch(this.props.games[this.state.selectedGame].game.name, this.state.numPlayers);\r\n        };\r\n        /* fix min and max number of players */\r\n        for (const game of props.games) {\r\n            const matchDetails = game.game;\r\n            if (!matchDetails.minPlayers) {\r\n                matchDetails.minPlayers = 1;\r\n            }\r\n            if (!matchDetails.maxPlayers) {\r\n                matchDetails.maxPlayers = 4;\r\n            }\r\n            console.assert(matchDetails.maxPlayers >= matchDetails.minPlayers);\r\n        }\r\n        this.state = {\r\n            selectedGame: 0,\r\n            numPlayers: props.games[0].game.minPlayers,\r\n        };\r\n    }\r\n    render() {\r\n        return (React.createElement(\"div\", null,\r\n            React.createElement(\"select\", { value: this.state.selectedGame, onChange: (evt) => this.onChangeSelectedGame(evt) }, this.props.games.map((game, index) => this._createGameNameOption(game, index))),\r\n            React.createElement(\"span\", null, \"Players:\"),\r\n            React.createElement(\"select\", { value: this.state.numPlayers, onChange: this.onChangeNumPlayers }, this._createNumPlayersRange(this.props.games[this.state.selectedGame].game).map((number) => this._createNumPlayersOption(number))),\r\n            React.createElement(\"span\", { className: \"buttons\" },\r\n                React.createElement(\"button\", { onClick: this.onClickCreate }, \"Create\"))));\r\n    }\r\n}\n\n/*\r\n * Copyright 2018 The boardgame.io Authors.\r\n *\r\n * Use of this source code is governed by a MIT-style\r\n * license that can be found in the LICENSE file or at\r\n * https://opensource.org/licenses/MIT.\r\n */\r\nvar LobbyPhases;\r\n(function (LobbyPhases) {\r\n    LobbyPhases[\"ENTER\"] = \"enter\";\r\n    LobbyPhases[\"PLAY\"] = \"play\";\r\n    LobbyPhases[\"LIST\"] = \"list\";\r\n})(LobbyPhases || (LobbyPhases = {}));\r\n/**\r\n * Lobby\r\n *\r\n * React lobby component.\r\n *\r\n * @param {Array}  gameComponents - An array of Board and Game objects for the supported games.\r\n * @param {string} lobbyServer - Address of the lobby server (for example 'localhost:8000').\r\n *                               If not set, defaults to the server that served the page.\r\n * @param {string} gameServer - Address of the game server (for example 'localhost:8001').\r\n *                              If not set, defaults to the server that served the page.\r\n * @param {function} clientFactory - Function that is used to create the game clients.\r\n * @param {number} refreshInterval - Interval between server updates (default: 2000ms).\r\n * @param {bool}   debug - Enable debug information (default: false).\r\n *\r\n * Returns:\r\n *   A React component that provides a UI to create, list, join, leave, play or\r\n *   spectate matches (game instances).\r\n */\r\nclass Lobby extends React.Component {\r\n    constructor(props) {\r\n        super(props);\r\n        this.state = {\r\n            phase: LobbyPhases.ENTER,\r\n            playerName: 'Visitor',\r\n            runningMatch: null,\r\n            errorMsg: '',\r\n            credentialStore: {},\r\n        };\r\n        this._createConnection = (props) => {\r\n            const name = this.state.playerName;\r\n            this.connection = LobbyConnection({\r\n                server: props.lobbyServer,\r\n                gameComponents: props.gameComponents,\r\n                playerName: name,\r\n                playerCredentials: this.state.credentialStore[name],\r\n            });\r\n        };\r\n        this._updateCredentials = (playerName, credentials) => {\r\n            this.setState((prevState) => {\r\n                // clone store or componentDidUpdate will not be triggered\r\n                const store = Object.assign({}, prevState.credentialStore);\r\n                store[playerName] = credentials;\r\n                return { credentialStore: store };\r\n            });\r\n        };\r\n        this._updateConnection = async () => {\r\n            await this.connection.refresh();\r\n            this.forceUpdate();\r\n        };\r\n        this._enterLobby = (playerName) => {\r\n            this._startRefreshInterval();\r\n            this.setState({ playerName, phase: LobbyPhases.LIST });\r\n        };\r\n        this._exitLobby = async () => {\r\n            this._clearRefreshInterval();\r\n            await this.connection.disconnect();\r\n            this.setState({ phase: LobbyPhases.ENTER, errorMsg: '' });\r\n        };\r\n        this._createMatch = async (gameName, numPlayers) => {\r\n            try {\r\n                await this.connection.create(gameName, numPlayers);\r\n                await this.connection.refresh();\r\n                // rerender\r\n                this.setState({});\r\n            }\r\n            catch (error) {\r\n                this.setState({ errorMsg: error.message });\r\n            }\r\n        };\r\n        this._joinMatch = async (gameName, matchID, playerID) => {\r\n            try {\r\n                await this.connection.join(gameName, matchID, playerID);\r\n                await this.connection.refresh();\r\n                this._updateCredentials(this.connection.playerName, this.connection.playerCredentials);\r\n            }\r\n            catch (error) {\r\n                this.setState({ errorMsg: error.message });\r\n            }\r\n        };\r\n        this._leaveMatch = async (gameName, matchID) => {\r\n            try {\r\n                await this.connection.leave(gameName, matchID);\r\n                await this.connection.refresh();\r\n                this._updateCredentials(this.connection.playerName, this.connection.playerCredentials);\r\n            }\r\n            catch (error) {\r\n                this.setState({ errorMsg: error.message });\r\n            }\r\n        };\r\n        this._startMatch = (gameName, matchOpts) => {\r\n            const gameCode = this.connection._getGameComponents(gameName);\r\n            if (!gameCode) {\r\n                this.setState({\r\n                    errorMsg: 'game ' + gameName + ' not supported',\r\n                });\r\n                return;\r\n            }\r\n            let multiplayer = undefined;\r\n            if (matchOpts.numPlayers > 1) {\r\n                multiplayer = this.props.gameServer\r\n                    ? SocketIO({ server: this.props.gameServer })\r\n                    : SocketIO();\r\n            }\r\n            if (matchOpts.numPlayers == 1) {\r\n                const maxPlayers = gameCode.game.maxPlayers;\r\n                const bots = {};\r\n                for (let i = 1; i < maxPlayers; i++) {\r\n                    bots[i + ''] = MCTSBot;\r\n                }\r\n                multiplayer = Local({ bots });\r\n            }\r\n            const app = this.props.clientFactory({\r\n                game: gameCode.game,\r\n                board: gameCode.board,\r\n                debug: this.props.debug,\r\n                multiplayer,\r\n            });\r\n            const match = {\r\n                app: app,\r\n                matchID: matchOpts.matchID,\r\n                playerID: matchOpts.numPlayers > 1 ? matchOpts.playerID : '0',\r\n                credentials: this.connection.playerCredentials,\r\n            };\r\n            this._clearRefreshInterval();\r\n            this.setState({ phase: LobbyPhases.PLAY, runningMatch: match });\r\n        };\r\n        this._exitMatch = () => {\r\n            this._startRefreshInterval();\r\n            this.setState({ phase: LobbyPhases.LIST, runningMatch: null });\r\n        };\r\n        this._getPhaseVisibility = (phase) => {\r\n            return this.state.phase !== phase ? 'hidden' : 'phase';\r\n        };\r\n        this.renderMatches = (matches, playerName) => {\r\n            return matches.map((match) => {\r\n                const { matchID, gameName, players } = match;\r\n                return (React.createElement(LobbyMatchInstance, { key: 'instance-' + matchID, match: { matchID, gameName, players: Object.values(players) }, playerName: playerName, onClickJoin: this._joinMatch, onClickLeave: this._leaveMatch, onClickPlay: this._startMatch }));\r\n            });\r\n        };\r\n        this._createConnection(this.props);\r\n    }\r\n    componentDidMount() {\r\n        const cookie = Cookies.load('lobbyState') || {};\r\n        if (cookie.phase && cookie.phase === LobbyPhases.PLAY) {\r\n            cookie.phase = LobbyPhases.LIST;\r\n        }\r\n        if (cookie.phase && cookie.phase !== LobbyPhases.ENTER) {\r\n            this._startRefreshInterval();\r\n        }\r\n        this.setState({\r\n            phase: cookie.phase || LobbyPhases.ENTER,\r\n            playerName: cookie.playerName || 'Visitor',\r\n            credentialStore: cookie.credentialStore || {},\r\n        });\r\n    }\r\n    componentDidUpdate(prevProps, prevState) {\r\n        const name = this.state.playerName;\r\n        const creds = this.state.credentialStore[name];\r\n        if (prevState.phase !== this.state.phase ||\r\n            prevState.credentialStore[name] !== creds ||\r\n            prevState.playerName !== name) {\r\n            this._createConnection(this.props);\r\n            this._updateConnection();\r\n            const cookie = {\r\n                phase: this.state.phase,\r\n                playerName: name,\r\n                credentialStore: this.state.credentialStore,\r\n            };\r\n            Cookies.save('lobbyState', cookie, { path: '/' });\r\n        }\r\n        if (prevProps.refreshInterval !== this.props.refreshInterval) {\r\n            this._startRefreshInterval();\r\n        }\r\n    }\r\n    componentWillUnmount() {\r\n        this._clearRefreshInterval();\r\n    }\r\n    _startRefreshInterval() {\r\n        this._clearRefreshInterval();\r\n        this._currentInterval = setInterval(this._updateConnection, this.props.refreshInterval);\r\n    }\r\n    _clearRefreshInterval() {\r\n        clearInterval(this._currentInterval);\r\n    }\r\n    render() {\r\n        const { gameComponents, renderer } = this.props;\r\n        const { errorMsg, playerName, phase, runningMatch } = this.state;\r\n        if (renderer) {\r\n            return renderer({\r\n                errorMsg,\r\n                gameComponents,\r\n                matches: this.connection.matches,\r\n                phase,\r\n                playerName,\r\n                runningMatch,\r\n                handleEnterLobby: this._enterLobby,\r\n                handleExitLobby: this._exitLobby,\r\n                handleCreateMatch: this._createMatch,\r\n                handleJoinMatch: this._joinMatch,\r\n                handleLeaveMatch: this._leaveMatch,\r\n                handleExitMatch: this._exitMatch,\r\n                handleRefreshMatches: this._updateConnection,\r\n                handleStartMatch: this._startMatch,\r\n            });\r\n        }\r\n        return (React.createElement(\"div\", { id: \"lobby-view\", style: { padding: 50 } },\r\n            React.createElement(\"div\", { className: this._getPhaseVisibility(LobbyPhases.ENTER) },\r\n                React.createElement(LobbyLoginForm, { key: playerName, playerName: playerName, onEnter: this._enterLobby })),\r\n            React.createElement(\"div\", { className: this._getPhaseVisibility(LobbyPhases.LIST) },\r\n                React.createElement(\"p\", null,\r\n                    \"Welcome, \",\r\n                    playerName),\r\n                React.createElement(\"div\", { className: \"phase-title\", id: \"match-creation\" },\r\n                    React.createElement(\"span\", null, \"Create a match:\"),\r\n                    React.createElement(LobbyCreateMatchForm, { games: gameComponents, createMatch: this._createMatch })),\r\n                React.createElement(\"p\", { className: \"phase-title\" }, \"Join a match:\"),\r\n                React.createElement(\"div\", { id: \"instances\" },\r\n                    React.createElement(\"table\", null,\r\n                        React.createElement(\"tbody\", null, this.renderMatches(this.connection.matches, playerName))),\r\n                    React.createElement(\"span\", { className: \"error-msg\" },\r\n                        errorMsg,\r\n                        React.createElement(\"br\", null))),\r\n                React.createElement(\"p\", { className: \"phase-title\" }, \"Matches that become empty are automatically deleted.\")),\r\n            React.createElement(\"div\", { className: this._getPhaseVisibility(LobbyPhases.PLAY) },\r\n                runningMatch && (React.createElement(runningMatch.app, { matchID: runningMatch.matchID, playerID: runningMatch.playerID, credentials: runningMatch.credentials })),\r\n                React.createElement(\"div\", { className: \"buttons\", id: \"match-exit\" },\r\n                    React.createElement(\"button\", { onClick: this._exitMatch }, \"Exit match\"))),\r\n            React.createElement(\"div\", { className: \"buttons\", id: \"lobby-exit\" },\r\n                React.createElement(\"button\", { onClick: this._exitLobby }, \"Exit lobby\"))));\r\n    }\r\n}\r\nLobby.propTypes = {\r\n    gameComponents: PropTypes.array.isRequired,\r\n    lobbyServer: PropTypes.string,\r\n    gameServer: PropTypes.string,\r\n    debug: PropTypes.bool,\r\n    clientFactory: PropTypes.func,\r\n    refreshInterval: PropTypes.number,\r\n};\r\nLobby.defaultProps = {\r\n    debug: false,\r\n    clientFactory: Client,\r\n    refreshInterval: 2000,\r\n};\n\nexport { Client, Lobby };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAcA,YAAQ,QAAQ;AAChB,YAAQ,YAAY;AAOpB,QAAI,SAAS;AACb,QAAI,SAAS;AACb,QAAI,kBAAkB;AAUtB,QAAI,qBAAqB;AAczB,aAAS,MAAM,KAAK,SAAS;AAC3B,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM,IAAI,UAAU,+BAA+B;AAAA,MACrD;AAEA,UAAI,MAAM,CAAC;AACX,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,QAAQ,IAAI,MAAM,eAAe;AACrC,UAAI,MAAM,IAAI,UAAU;AAExB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,SAAS,KAAK,QAAQ,GAAG;AAG7B,YAAI,SAAS,GAAG;AACd;AAAA,QACF;AAEA,YAAI,MAAM,KAAK,OAAO,GAAG,MAAM,EAAE,KAAK;AACtC,YAAI,MAAM,KAAK,OAAO,EAAE,QAAQ,KAAK,MAAM,EAAE,KAAK;AAGlD,YAAI,OAAO,IAAI,CAAC,GAAG;AACjB,gBAAM,IAAI,MAAM,GAAG,EAAE;AAAA,QACvB;AAGA,YAAI,UAAa,IAAI,GAAG,GAAG;AACzB,cAAI,GAAG,IAAI,UAAU,KAAK,GAAG;AAAA,QAC/B;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAkBA,aAAS,UAAU,MAAM,KAAK,SAAS;AACrC,UAAI,MAAM,WAAW,CAAC;AACtB,UAAI,MAAM,IAAI,UAAU;AAExB,UAAI,OAAO,QAAQ,YAAY;AAC7B,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,UAAI,CAAC,mBAAmB,KAAK,IAAI,GAAG;AAClC,cAAM,IAAI,UAAU,0BAA0B;AAAA,MAChD;AAEA,UAAI,QAAQ,IAAI,GAAG;AAEnB,UAAI,SAAS,CAAC,mBAAmB,KAAK,KAAK,GAAG;AAC5C,cAAM,IAAI,UAAU,yBAAyB;AAAA,MAC/C;AAEA,UAAI,MAAM,OAAO,MAAM;AAEvB,UAAI,QAAQ,IAAI,QAAQ;AACtB,YAAI,SAAS,IAAI,SAAS;AAC1B,YAAI,MAAM,MAAM;AAAG,gBAAM,IAAI,MAAM,2BAA2B;AAC9D,eAAO,eAAe,KAAK,MAAM,MAAM;AAAA,MACzC;AAEA,UAAI,IAAI,QAAQ;AACd,YAAI,CAAC,mBAAmB,KAAK,IAAI,MAAM,GAAG;AACxC,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AAEA,eAAO,cAAc,IAAI;AAAA,MAC3B;AAEA,UAAI,IAAI,MAAM;AACZ,YAAI,CAAC,mBAAmB,KAAK,IAAI,IAAI,GAAG;AACtC,gBAAM,IAAI,UAAU,wBAAwB;AAAA,QAC9C;AAEA,eAAO,YAAY,IAAI;AAAA,MACzB;AAEA,UAAI,IAAI,SAAS;AACf,YAAI,OAAO,IAAI,QAAQ,gBAAgB,YAAY;AACjD,gBAAM,IAAI,UAAU,2BAA2B;AAAA,QACjD;AAEA,eAAO,eAAe,IAAI,QAAQ,YAAY;AAAA,MAChD;AAEA,UAAI,IAAI,UAAU;AAChB,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,QAAQ;AACd,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,UAAU;AAChB,YAAI,WAAW,OAAO,IAAI,aAAa,WACnC,IAAI,SAAS,YAAY,IAAI,IAAI;AAErC,gBAAQ,UAAU;AAAA,UAChB,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF,KAAK;AACH,mBAAO;AACP;AAAA,UACF;AACE,kBAAM,IAAI,UAAU,4BAA4B;AAAA,QACpD;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAUA,aAAS,UAAU,KAAKA,SAAQ;AAC9B,UAAI;AACF,eAAOA,QAAO,GAAG;AAAA,MACnB,SAAS,GAAG;AACV,eAAO;AAAA,MACT;AAAA,IACF;AAAA;AAAA;;;AClMA,IAAAC,kBAAA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AAED,QAAI,UAAU,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,WAAW,SAAU,KAAK;AAAE,aAAO,OAAO;AAAA,IAAK,IAAI,SAAU,KAAK;AAAE,aAAO,OAAO,OAAO,WAAW,cAAc,IAAI,gBAAgB,UAAU,QAAQ,OAAO,YAAY,WAAW,OAAO;AAAA,IAAK;AAE3Q,YAAQ,OAAO;AACf,YAAQ,UAAU;AAClB,YAAQ,SAAS;AACjB,YAAQ,OAAO;AACf,YAAQ,SAAS;AACjB,YAAQ,eAAe;AACvB,YAAQ,gBAAgB;AAExB,QAAI,UAAU;AAEd,QAAI,WAAW,uBAAuB,OAAO;AAE7C,QAAI,gBAAgB;AAEpB,QAAI,iBAAiB,uBAAuB,aAAa;AAEzD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAE9F,QAAI,UAAU,OAAO,aAAa,eAAe,OAAO,YAAY,eAAe,QAAQ,OAAO;AAClG,QAAI,aAAa,CAAC;AAClB,QAAI,OAAO;AAEX,aAAS,iBAAiB;AACxB,aAAO,QAAQ,CAAC,KAAK;AAAA,IACvB;AAEA,aAAS,KAAK,MAAM,YAAY;AAC9B,UAAI,UAAU,UAAU,aAAa,SAAS,QAAQ,MAAM,SAAS,MAAM;AAC3E,UAAI,YAAY,WAAW,QAAQ,IAAI;AAEvC,UAAI,OAAO,eAAe,aAAa;AACrC,qBAAa,CAAC,aAAa,UAAU,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM;AAAA,MACtE;AAEA,UAAI,CAAC,YAAY;AACf,YAAI;AACF,sBAAY,KAAK,MAAM,SAAS;AAAA,QAClC,SAAS,KAAK;AAAA,QAEd;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,QAAQ,YAAY;AAC3B,UAAI,UAAU,UAAU,aAAa,SAAS,QAAQ,MAAM,SAAS,MAAM;AAC3E,UAAI,YAAY;AAEhB,UAAI,OAAO,eAAe,aAAa;AACrC,qBAAa,CAAC,aAAa,UAAU,CAAC,MAAM,OAAO,UAAU,CAAC,MAAM;AAAA,MACtE;AAEA,UAAI,CAAC,YAAY;AACf,YAAI;AACF,sBAAY,KAAK,MAAM,SAAS;AAAA,QAClC,SAAS,KAAK;AAAA,QAEd;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,OAAO;AACrB,UAAI,UAAU,UAAU,aAAa,SAAS,QAAQ,MAAM,SAAS,MAAM;AAE3E,UAAI,CAAC,SAAS;AACZ,eAAO,CAAC;AAAA,MACV;AAEA,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AAEA,aAAO,OAAO,KAAK,OAAO,EAAE,OAAO,SAAU,aAAa,MAAM;AAC9D,YAAI,CAAC,MAAM,KAAK,IAAI,GAAG;AACrB,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY,CAAC;AACjB,kBAAU,IAAI,IAAI,QAAQ,IAAI;AAC9B,gBAAQ,GAAG,eAAe,SAAS,CAAC,GAAG,aAAa,SAAS;AAAA,MAC/D,GAAG,CAAC,CAAC;AAAA,IACP;AAEA,aAAS,KAAK,MAAM,KAAK,KAAK;AAC5B,iBAAW,IAAI,IAAI;AAGnB,WAAK,OAAO,QAAQ,cAAc,cAAc,QAAQ,GAAG,OAAO,UAAU;AAC1E,mBAAW,IAAI,IAAI,KAAK,UAAU,GAAG;AAAA,MACvC;AAGA,UAAI,CAAC,SAAS;AACZ,iBAAS,SAAS,SAAS,QAAQ,UAAU,MAAM,WAAW,IAAI,GAAG,GAAG;AAAA,MAC1E;AAEA,UAAI,eAAe,KAAK,KAAK,QAAQ;AACnC,aAAK,OAAO,MAAM,KAAK,GAAG;AAAA,MAC5B;AAAA,IACF;AAEA,aAAS,OAAO,MAAM,KAAK;AACzB,aAAO,WAAW,IAAI;AAEtB,UAAI,OAAO,QAAQ,aAAa;AAC9B,cAAM,CAAC;AAAA,MACT,WAAW,OAAO,QAAQ,UAAU;AAElC,cAAM,EAAE,MAAM,IAAI;AAAA,MACpB,OAAO;AAEL,eAAO,GAAG,eAAe,SAAS,CAAC,GAAG,GAAG;AAAA,MAC3C;AAEA,UAAI,OAAO,aAAa,aAAa;AACnC,YAAI,UAAU,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1C,YAAI,SAAS;AACb,iBAAS,SAAS,SAAS,QAAQ,UAAU,MAAM,IAAI,GAAG;AAAA,MAC5D;AAEA,UAAI,eAAe,KAAK,KAAK,aAAa;AACxC,aAAK,YAAY,MAAM,GAAG;AAAA,MAC5B;AAAA,IACF;AAEA,aAAS,aAAa,WAAW;AAC/B,UAAI,WAAW;AACb,qBAAa,SAAS,QAAQ,MAAM,SAAS;AAAA,MAC/C,OAAO;AACL,qBAAa,CAAC;AAAA,MAChB;AAAA,IACF;AAEA,aAAS,cAAc,KAAK,KAAK;AAC/B,UAAI,IAAI,QAAQ;AACd,qBAAa,IAAI;AAAA,MACnB,WAAW,IAAI,SAAS;AACtB,qBAAa,IAAI;AAAA,MACnB,WAAW,IAAI,WAAW,IAAI,QAAQ,QAAQ;AAC5C,qBAAa,IAAI,QAAQ,MAAM;AAAA,MACjC,OAAO;AACL,qBAAa,CAAC;AAAA,MAChB;AAEA,aAAO;AAEP,aAAO,SAAS,SAAS;AACvB,eAAO;AACP,qBAAa,CAAC;AAAA,MAChB;AAAA,IACF;AAEA,YAAQ,UAAU;AAAA,MAChB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA;AAAA;;;ACrKA,oBAAO;AAEP,qBAAO;AAKP,0BAAO;AAGP,mBAAkB;AAClB,wBAAsB;AACtB,2BAAoB;AAiCpB,SAASC,QAAO,MAAM;AAClB,MAAI;AACJ,QAAM,EAAE,MAAM,YAAY,OAAO,aAAa,SAAS,IAAI;AAC3D,MAAI,EAAE,SAAS,MAAM,IAAI;AAGzB,MAAI,YAAY,QAAW;AACvB,UAAM,UAAU,MAAM,aAAAC,QAAM,cAAc,OAAO,EAAE,WAAW,eAAe,GAAG,eAAe;AAC/F,cAAU;AAAA,EACd;AAOA,SAAO,KAAK,MAAM,qBAAqB,aAAAA,QAAM,UAAU;AAAA,IAC/C,YAAY,OAAO;AACf,YAAM,KAAK;AACX,UAAI,UAAU,QAAW;AACrB,gBAAQ,MAAM;AAAA,MAClB;AACA,WAAK,SAAS,OAAS;AAAA,QACnB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,SAAS,MAAM;AAAA,QACf,UAAU,MAAM;AAAA,QAChB,aAAa,MAAM;AAAA,QACnB;AAAA,MACJ,CAAC;AAAA,IACL;AAAA,IACA,oBAAoB;AAChB,WAAK,cAAc,KAAK,OAAO,UAAU,MAAM,KAAK,YAAY,CAAC;AACjE,WAAK,OAAO,MAAM;AAAA,IACtB;AAAA,IACA,uBAAuB;AACnB,WAAK,OAAO,KAAK;AACjB,WAAK,YAAY;AAAA,IACrB;AAAA,IACA,mBAAmB,WAAW;AAC1B,UAAI,KAAK,MAAM,WAAW,UAAU,SAAS;AACzC,aAAK,OAAO,cAAc,KAAK,MAAM,OAAO;AAAA,MAChD;AACA,UAAI,KAAK,MAAM,YAAY,UAAU,UAAU;AAC3C,aAAK,OAAO,eAAe,KAAK,MAAM,QAAQ;AAAA,MAClD;AACA,UAAI,KAAK,MAAM,eAAe,UAAU,aAAa;AACjD,aAAK,OAAO,kBAAkB,KAAK,MAAM,WAAW;AAAA,MACxD;AAAA,IACJ;AAAA,IACA,SAAS;AACL,YAAM,QAAQ,KAAK,OAAO,SAAS;AACnC,UAAI,UAAU,MAAM;AAChB,eAAO,aAAAA,QAAM,cAAc,OAAO;AAAA,MACtC;AACA,UAAI,SAAS;AACb,UAAI,OAAO;AACP,iBAAS,aAAAA,QAAM,cAAc,OAAO;AAAA,UAChC,GAAG;AAAA,UACH,GAAG,KAAK;AAAA,UACR,eAAe,CAAC,CAAC;AAAA,UACjB,OAAO,KAAK,OAAO;AAAA,UACnB,QAAQ,KAAK,OAAO;AAAA,UACpB,SAAS,KAAK,OAAO;AAAA,UACrB,UAAU,KAAK,OAAO;AAAA,UACtB,OAAO,KAAK,OAAO;AAAA,UACnB,MAAM,KAAK,OAAO;AAAA,UAClB,MAAM,KAAK,OAAO;AAAA,UAClB,KAAK,KAAK,OAAO;AAAA,UACjB,WAAW,KAAK,OAAO;AAAA,UACvB,iBAAiB,KAAK,OAAO;AAAA,UAC7B,cAAc,KAAK,OAAO;AAAA,QAC9B,CAAC;AAAA,MACL;AACA,aAAO,aAAAA,QAAM,cAAc,OAAO,EAAE,WAAW,cAAc,GAAG,MAAM;AAAA,IAC1E;AAAA,EACJ,GACA,GAAG,YAAY;AAAA;AAAA;AAAA,IAGX,SAAS,kBAAAC,QAAU;AAAA;AAAA;AAAA,IAGnB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA,IAGpB,aAAa,kBAAAA,QAAU;AAAA;AAAA,IAEvB,OAAO,kBAAAA,QAAU;AAAA,EACrB,GACA,GAAG,eAAe;AAAA,IACd,SAAS;AAAA,IACT,UAAU;AAAA,IACV,aAAa;AAAA,IACb,OAAO;AAAA,EACX,GACA;AACR;AASA,IAAM,uBAAN,MAA2B;AAAA,EACvB,YAAY,EAAE,QAAQ,gBAAgB,YAAY,kBAAmB,GAAG;AACpE,SAAK,SAAS,IAAI,YAAY,EAAE,OAAO,CAAC;AACxC,SAAK,iBAAiB;AACtB,SAAK,aAAa,cAAc;AAChC,SAAK,oBAAoB;AACzB,SAAK,UAAU,CAAC;AAAA,EACpB;AAAA,EACA,MAAM,UAAU;AACZ,QAAI;AACA,WAAK,UAAU,CAAC;AAChB,YAAM,QAAQ,MAAM,KAAK,OAAO,UAAU;AAC1C,iBAAW,QAAQ,OAAO;AACtB,YAAI,CAAC,KAAK,mBAAmB,IAAI;AAC7B;AACJ,cAAM,EAAE,QAAQ,IAAI,MAAM,KAAK,OAAO,YAAY,IAAI;AACtD,aAAK,QAAQ,KAAK,GAAG,OAAO;AAAA,MAChC;AAAA,IACJ,SACO,OAAO;AACV,YAAM,IAAI,MAAM,yCAAyC,QAAQ,GAAG;AAAA,IACxE;AAAA,EACJ;AAAA,EACA,kBAAkB,SAAS;AACvB,eAAW,QAAQ,KAAK,SAAS;AAC7B,UAAI,KAAK,SAAS,MAAM;AACpB,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EACA,mBAAmB,UAAU;AACzB,eAAW,QAAQ,KAAK,gBAAgB;AACpC,UAAI,KAAK,KAAK,SAAS;AACnB,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EACA,YAAY,YAAY;AACpB,eAAW,QAAQ,KAAK,SAAS;AAC7B,UAAI,KAAK,QAAQ,KAAK,CAAC,WAAW,OAAO,SAAS,UAAU;AACxD,eAAO;AAAA,IACf;AAAA,EACJ;AAAA,EACA,MAAM,KAAK,UAAU,SAAS,UAAU;AACpC,QAAI;AACA,UAAI,OAAO,KAAK,YAAY,KAAK,UAAU;AAC3C,UAAI,MAAM;AACN,cAAM,IAAI,MAAM,+BAA+B,KAAK,OAAO;AAAA,MAC/D;AACA,aAAO,KAAK,kBAAkB,OAAO;AACrC,UAAI,CAAC,MAAM;AACP,cAAM,IAAI,MAAM,mBAAmB,UAAU,YAAY;AAAA,MAC7D;AACA,YAAM,OAAO,MAAM,KAAK,OAAO,UAAU,UAAU,SAAS;AAAA,QACxD;AAAA,QACA,YAAY,KAAK;AAAA,MACrB,CAAC;AACD,WAAK,QAAQ,OAAO,SAAS,QAAQ,CAAC,EAAE,OAAO,KAAK;AACpD,WAAK,oBAAoB,KAAK;AAAA,IAClC,SACO,OAAO;AACV,YAAM,IAAI,MAAM,0BAA0B,UAAU,OAAO,QAAQ,GAAG;AAAA,IAC1E;AAAA,EACJ;AAAA,EACA,MAAM,MAAM,UAAU,SAAS;AAC3B,QAAI;AACA,YAAM,OAAO,KAAK,kBAAkB,OAAO;AAC3C,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,0BAA0B;AAC9C,iBAAW,UAAU,KAAK,SAAS;AAC/B,YAAI,OAAO,SAAS,KAAK,YAAY;AACjC,gBAAM,KAAK,OAAO,WAAW,UAAU,SAAS;AAAA,YAC5C,UAAU,OAAO,GAAG,SAAS;AAAA,YAC7B,aAAa,KAAK;AAAA,UACtB,CAAC;AACD,iBAAO,OAAO;AACd,iBAAO,KAAK;AACZ;AAAA,QACJ;AAAA,MACJ;AACA,YAAM,IAAI,MAAM,2BAA2B;AAAA,IAC/C,SACO,OAAO;AACV,YAAM,IAAI,MAAM,2BAA2B,UAAU,OAAO,QAAQ,GAAG;AAAA,IAC3E;AAAA,EACJ;AAAA,EACA,MAAM,aAAa;AACf,UAAM,OAAO,KAAK,YAAY,KAAK,UAAU;AAC7C,QAAI,MAAM;AACN,YAAM,KAAK,MAAM,KAAK,UAAU,KAAK,OAAO;AAAA,IAChD;AACA,SAAK,UAAU,CAAC;AAChB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,MAAM,OAAO,UAAU,YAAY;AAC/B,QAAI;AACA,YAAM,OAAO,KAAK,mBAAmB,QAAQ;AAC7C,UAAI,CAAC;AACD,cAAM,IAAI,MAAM,gBAAgB;AACpC,UAAI,aAAa,KAAK,KAAK,cACvB,aAAa,KAAK,KAAK;AACvB,cAAM,IAAI,MAAM,+BAA+B,UAAU;AAC7D,YAAM,KAAK,OAAO,YAAY,UAAU,EAAE,WAAW,CAAC;AAAA,IAC1D,SACO,OAAO;AACV,YAAM,IAAI,MAAM,gCAAgC,WAAW,OAAO,QAAQ,GAAG;AAAA,IACjF;AAAA,EACJ;AACJ;AAcA,SAAS,gBAAgB,MAAM;AAC3B,SAAO,IAAI,qBAAqB,IAAI;AACxC;AASA,IAAM,iBAAN,cAA6B,aAAAD,QAAM,UAAU;AAAA,EACzC,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,QAAQ;AAAA,MACT,YAAY,KAAK,MAAM;AAAA,MACvB,cAAc;AAAA,IAClB;AACA,SAAK,eAAe,MAAM;AACtB,UAAI,KAAK,MAAM,eAAe;AAC1B;AACJ,WAAK,MAAM,QAAQ,KAAK,MAAM,UAAU;AAAA,IAC5C;AACA,SAAK,aAAa,CAAC,UAAU;AACzB,UAAI,MAAM,QAAQ,SAAS;AACvB,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AACA,SAAK,qBAAqB,CAAC,UAAU;AACjC,YAAM,OAAO,MAAM,OAAO,MAAM,KAAK;AACrC,WAAK,SAAS;AAAA,QACV,YAAY;AAAA,QACZ,cAAc,KAAK,SAAS,IAAI,KAAK;AAAA,MACzC,CAAC;AAAA,IACL;AAAA,EACJ;AAAA,EACA,SAAS;AACL,WAAQ,aAAAA,QAAM;AAAA,MAAc;AAAA,MAAO;AAAA,MAC/B,aAAAA,QAAM,cAAc,KAAK,EAAE,WAAW,cAAc,GAAG,uBAAuB;AAAA,MAC9E,aAAAA,QAAM,cAAc,SAAS,EAAE,MAAM,QAAQ,OAAO,KAAK,MAAM,YAAY,UAAU,KAAK,oBAAoB,YAAY,KAAK,WAAW,CAAC;AAAA,MAC3I,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAQ,EAAE,WAAW,UAAU;AAAA,QAC/C,aAAAA,QAAM,cAAc,UAAU,EAAE,WAAW,WAAW,SAAS,KAAK,aAAa,GAAG,OAAO;AAAA,MAAC;AAAA,MAChG,aAAAA,QAAM,cAAc,MAAM,IAAI;AAAA,MAC9B,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAQ,EAAE,WAAW,YAAY;AAAA,QACjD,KAAK,MAAM;AAAA,QACX,aAAAA,QAAM,cAAc,MAAM,IAAI;AAAA,MAAC;AAAA,IAAC;AAAA,EAC5C;AACJ;AACA,eAAe,eAAe;AAAA,EAC1B,YAAY;AAChB;AASA,IAAM,qBAAN,cAAiC,aAAAA,QAAM,UAAU;AAAA,EAC7C,cAAc;AACV,UAAM,GAAG,SAAS;AAClB,SAAK,cAAc,CAAC,WAAW;AAC3B,aAAO,OAAO,QAAQ;AAAA,IAC1B;AACA,SAAK,oBAAoB,CAAC,MAAM,WAAY,aAAAA,QAAM,cAAc,UAAU,EAAE,KAAK,iBAAiB,KAAK,SAAS,SAAS,MAAM,KAAK,MAAM,YAAY,KAAK,UAAU,KAAK,SAAS,KAAK,MAAM,EAAE,GAAG,MAAM;AACzM,SAAK,qBAAqB,CAAC,SAAU,aAAAA,QAAM,cAAc,UAAU,EAAE,KAAK,kBAAkB,KAAK,SAAS,SAAS,MAAM,KAAK,MAAM,aAAa,KAAK,UAAU,KAAK,OAAO,EAAE,GAAG,OAAO;AACxL,SAAK,oBAAoB,CAAC,MAAM,WAAY,aAAAA,QAAM,cAAc,UAAU,EAAE,KAAK,iBAAiB,KAAK,SAAS,SAAS,MAAM,KAAK,MAAM,YAAY,KAAK,UAAU;AAAA,MAC7J,SAAS,KAAK;AAAA,MACd,UAAU,KAAK;AAAA,MACf,YAAY,KAAK,QAAQ;AAAA,IAC7B,CAAC,EAAE,GAAG,MAAM;AAChB,SAAK,wBAAwB,CAAC,SAAU,aAAAA,QAAM,cAAc,UAAU,EAAE,KAAK,qBAAqB,KAAK,SAAS,SAAS,MAAM,KAAK,MAAM,YAAY,KAAK,UAAU;AAAA,MAC7J,SAAS,KAAK;AAAA,MACd,YAAY,KAAK,QAAQ;AAAA,IAC7B,CAAC,EAAE,GAAG,UAAU;AACpB,SAAK,yBAAyB,CAAC,SAAS;AACpC,YAAM,aAAa,KAAK,QAAQ,KAAK,CAAC,WAAW,OAAO,SAAS,KAAK,MAAM,UAAU;AACtF,YAAM,WAAW,KAAK,QAAQ,KAAK,CAAC,WAAW,CAAC,OAAO,IAAI;AAC3D,UAAI,cAAc,UAAU;AAExB,eAAO,KAAK,mBAAmB,IAAI;AAAA,MACvC;AACA,UAAI,UAAU;AAEV,eAAO,KAAK,kBAAkB,MAAM,SAAS,EAAE;AAAA,MACnD;AAEA,UAAI,YAAY;AACZ,eAAQ,aAAAA,QAAM,cAAc,OAAO,MAAM;AAAA,UACrC,KAAK,kBAAkB,MAAM,WAAW,EAAE;AAAA,UAC1C,KAAK,mBAAmB,IAAI;AAAA,QAChC,CAAC;AAAA,MACL;AAEA,aAAO,KAAK,sBAAsB,IAAI;AAAA,IAC1C;AAAA,EACJ;AAAA,EACA,SAAS;AACL,UAAM,QAAQ,KAAK,MAAM;AACzB,QAAI,SAAS;AACb,QAAI,CAAC,MAAM,QAAQ,KAAK,CAAC,WAAW,CAAC,OAAO,IAAI,GAAG;AAC/C,eAAS;AAAA,IACb;AACA,WAAQ,aAAAA,QAAM;AAAA,MAAc;AAAA,MAAM,EAAE,KAAK,UAAU,MAAM,QAAQ;AAAA,MAC7D,aAAAA,QAAM,cAAc,MAAM,EAAE,KAAK,eAAe,MAAM,QAAQ,GAAG,MAAM,QAAQ;AAAA,MAC/E,aAAAA,QAAM,cAAc,MAAM,EAAE,KAAK,iBAAiB,MAAM,QAAQ,GAAG,MAAM;AAAA,MACzE,aAAAA,QAAM,cAAc,MAAM,EAAE,KAAK,gBAAgB,MAAM,QAAQ,GAAG,MAAM,QAAQ,IAAI,CAAC,WAAW,KAAK,YAAY,MAAM,CAAC,EAAE,KAAK,IAAI,CAAC;AAAA,MACpI,aAAAA,QAAM,cAAc,MAAM,EAAE,KAAK,kBAAkB,MAAM,QAAQ,GAAG,KAAK,uBAAuB,KAAK,CAAC;AAAA,IAAC;AAAA,EAC/G;AACJ;AASA,IAAM,uBAAN,cAAmC,aAAAA,QAAM,UAAU;AAAA,EAC/C,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACT,cAAc;AAAA,MACd,YAAY;AAAA,IAChB;AACA,SAAK,wBAAwB,CAAC,MAAM,QAAQ;AACxC,aAAQ,aAAAA,QAAM,cAAc,UAAU,EAAE,KAAK,iBAAiB,KAAK,OAAO,IAAI,GAAG,KAAK,KAAK,IAAI;AAAA,IACnG;AACA,SAAK,0BAA0B,CAAC,QAAQ;AACpC,aAAQ,aAAAA,QAAM,cAAc,UAAU,EAAE,KAAK,gBAAgB,KAAK,OAAO,IAAI,GAAG,GAAG;AAAA,IACvF;AACA,SAAK,yBAAyB,CAAC,SAAS;AACpC,aAAO,MAAM,KAAK,EAAE,QAAQ,KAAK,aAAa,EAAE,CAAC,EAC5C,IAAI,CAAC,GAAG,MAAM,CAAC,EACf,MAAM,KAAK,UAAU;AAAA,IAC9B;AACA,SAAK,qBAAqB,CAAC,UAAU;AACjC,WAAK,SAAS;AAAA,QACV,YAAY,OAAO,SAAS,MAAM,OAAO,KAAK;AAAA,MAClD,CAAC;AAAA,IACL;AACA,SAAK,uBAAuB,CAAC,UAAU;AACnC,YAAM,MAAM,OAAO,SAAS,MAAM,OAAO,KAAK;AAC9C,WAAK,SAAS;AAAA,QACV,cAAc;AAAA,QACd,YAAY,KAAK,MAAM,MAAM,GAAG,EAAE,KAAK;AAAA,MAC3C,CAAC;AAAA,IACL;AACA,SAAK,gBAAgB,MAAM;AACvB,WAAK,MAAM,YAAY,KAAK,MAAM,MAAM,KAAK,MAAM,YAAY,EAAE,KAAK,MAAM,KAAK,MAAM,UAAU;AAAA,IACrG;AAEA,eAAW,QAAQ,MAAM,OAAO;AAC5B,YAAM,eAAe,KAAK;AAC1B,UAAI,CAAC,aAAa,YAAY;AAC1B,qBAAa,aAAa;AAAA,MAC9B;AACA,UAAI,CAAC,aAAa,YAAY;AAC1B,qBAAa,aAAa;AAAA,MAC9B;AACA,cAAQ,OAAO,aAAa,cAAc,aAAa,UAAU;AAAA,IACrE;AACA,SAAK,QAAQ;AAAA,MACT,cAAc;AAAA,MACd,YAAY,MAAM,MAAM,CAAC,EAAE,KAAK;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,WAAQ,aAAAA,QAAM;AAAA,MAAc;AAAA,MAAO;AAAA,MAC/B,aAAAA,QAAM,cAAc,UAAU,EAAE,OAAO,KAAK,MAAM,cAAc,UAAU,CAAC,QAAQ,KAAK,qBAAqB,GAAG,EAAE,GAAG,KAAK,MAAM,MAAM,IAAI,CAAC,MAAM,UAAU,KAAK,sBAAsB,MAAM,KAAK,CAAC,CAAC;AAAA,MACnM,aAAAA,QAAM,cAAc,QAAQ,MAAM,UAAU;AAAA,MAC5C,aAAAA,QAAM,cAAc,UAAU,EAAE,OAAO,KAAK,MAAM,YAAY,UAAU,KAAK,mBAAmB,GAAG,KAAK,uBAAuB,KAAK,MAAM,MAAM,KAAK,MAAM,YAAY,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,KAAK,wBAAwB,MAAM,CAAC,CAAC;AAAA,MACpO,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAQ,EAAE,WAAW,UAAU;AAAA,QAC/C,aAAAA,QAAM,cAAc,UAAU,EAAE,SAAS,KAAK,cAAc,GAAG,QAAQ;AAAA,MAAC;AAAA,IAAC;AAAA,EACrF;AACJ;AASA,IAAI;AAAA,CACH,SAAUE,cAAa;AACpB,EAAAA,aAAY,OAAO,IAAI;AACvB,EAAAA,aAAY,MAAM,IAAI;AACtB,EAAAA,aAAY,MAAM,IAAI;AAC1B,GAAG,gBAAgB,cAAc,CAAC,EAAE;AAmBpC,IAAM,QAAN,cAAoB,aAAAF,QAAM,UAAU;AAAA,EAChC,YAAY,OAAO;AACf,UAAM,KAAK;AACX,SAAK,QAAQ;AAAA,MACT,OAAO,YAAY;AAAA,MACnB,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,MACV,iBAAiB,CAAC;AAAA,IACtB;AACA,SAAK,oBAAoB,CAACG,WAAU;AAChC,YAAM,OAAO,KAAK,MAAM;AACxB,WAAK,aAAa,gBAAgB;AAAA,QAC9B,QAAQA,OAAM;AAAA,QACd,gBAAgBA,OAAM;AAAA,QACtB,YAAY;AAAA,QACZ,mBAAmB,KAAK,MAAM,gBAAgB,IAAI;AAAA,MACtD,CAAC;AAAA,IACL;AACA,SAAK,qBAAqB,CAAC,YAAY,gBAAgB;AACnD,WAAK,SAAS,CAAC,cAAc;AAEzB,cAAM,QAAQ,OAAO,OAAO,CAAC,GAAG,UAAU,eAAe;AACzD,cAAM,UAAU,IAAI;AACpB,eAAO,EAAE,iBAAiB,MAAM;AAAA,MACpC,CAAC;AAAA,IACL;AACA,SAAK,oBAAoB,YAAY;AACjC,YAAM,KAAK,WAAW,QAAQ;AAC9B,WAAK,YAAY;AAAA,IACrB;AACA,SAAK,cAAc,CAAC,eAAe;AAC/B,WAAK,sBAAsB;AAC3B,WAAK,SAAS,EAAE,YAAY,OAAO,YAAY,KAAK,CAAC;AAAA,IACzD;AACA,SAAK,aAAa,YAAY;AAC1B,WAAK,sBAAsB;AAC3B,YAAM,KAAK,WAAW,WAAW;AACjC,WAAK,SAAS,EAAE,OAAO,YAAY,OAAO,UAAU,GAAG,CAAC;AAAA,IAC5D;AACA,SAAK,eAAe,OAAO,UAAU,eAAe;AAChD,UAAI;AACA,cAAM,KAAK,WAAW,OAAO,UAAU,UAAU;AACjD,cAAM,KAAK,WAAW,QAAQ;AAE9B,aAAK,SAAS,CAAC,CAAC;AAAA,MACpB,SACO,OAAO;AACV,aAAK,SAAS,EAAE,UAAU,MAAM,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACJ;AACA,SAAK,aAAa,OAAO,UAAU,SAAS,aAAa;AACrD,UAAI;AACA,cAAM,KAAK,WAAW,KAAK,UAAU,SAAS,QAAQ;AACtD,cAAM,KAAK,WAAW,QAAQ;AAC9B,aAAK,mBAAmB,KAAK,WAAW,YAAY,KAAK,WAAW,iBAAiB;AAAA,MACzF,SACO,OAAO;AACV,aAAK,SAAS,EAAE,UAAU,MAAM,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACJ;AACA,SAAK,cAAc,OAAO,UAAU,YAAY;AAC5C,UAAI;AACA,cAAM,KAAK,WAAW,MAAM,UAAU,OAAO;AAC7C,cAAM,KAAK,WAAW,QAAQ;AAC9B,aAAK,mBAAmB,KAAK,WAAW,YAAY,KAAK,WAAW,iBAAiB;AAAA,MACzF,SACO,OAAO;AACV,aAAK,SAAS,EAAE,UAAU,MAAM,QAAQ,CAAC;AAAA,MAC7C;AAAA,IACJ;AACA,SAAK,cAAc,CAAC,UAAU,cAAc;AACxC,YAAM,WAAW,KAAK,WAAW,mBAAmB,QAAQ;AAC5D,UAAI,CAAC,UAAU;AACX,aAAK,SAAS;AAAA,UACV,UAAU,UAAU,WAAW;AAAA,QACnC,CAAC;AACD;AAAA,MACJ;AACA,UAAI,cAAc;AAClB,UAAI,UAAU,aAAa,GAAG;AAC1B,sBAAc,KAAK,MAAM,aACnB,SAAS,EAAE,QAAQ,KAAK,MAAM,WAAW,CAAC,IAC1C,SAAS;AAAA,MACnB;AACA,UAAI,UAAU,cAAc,GAAG;AAC3B,cAAM,aAAa,SAAS,KAAK;AACjC,cAAM,OAAO,CAAC;AACd,iBAAS,IAAI,GAAG,IAAI,YAAY,KAAK;AACjC,eAAK,IAAI,EAAE,IAAI;AAAA,QACnB;AACA,sBAAc,MAAM,EAAE,KAAK,CAAC;AAAA,MAChC;AACA,YAAM,MAAM,KAAK,MAAM,cAAc;AAAA,QACjC,MAAM,SAAS;AAAA,QACf,OAAO,SAAS;AAAA,QAChB,OAAO,KAAK,MAAM;AAAA,QAClB;AAAA,MACJ,CAAC;AACD,YAAM,QAAQ;AAAA,QACV;AAAA,QACA,SAAS,UAAU;AAAA,QACnB,UAAU,UAAU,aAAa,IAAI,UAAU,WAAW;AAAA,QAC1D,aAAa,KAAK,WAAW;AAAA,MACjC;AACA,WAAK,sBAAsB;AAC3B,WAAK,SAAS,EAAE,OAAO,YAAY,MAAM,cAAc,MAAM,CAAC;AAAA,IAClE;AACA,SAAK,aAAa,MAAM;AACpB,WAAK,sBAAsB;AAC3B,WAAK,SAAS,EAAE,OAAO,YAAY,MAAM,cAAc,KAAK,CAAC;AAAA,IACjE;AACA,SAAK,sBAAsB,CAAC,UAAU;AAClC,aAAO,KAAK,MAAM,UAAU,QAAQ,WAAW;AAAA,IACnD;AACA,SAAK,gBAAgB,CAAC,SAAS,eAAe;AAC1C,aAAO,QAAQ,IAAI,CAAC,UAAU;AAC1B,cAAM,EAAE,SAAS,UAAU,QAAQ,IAAI;AACvC,eAAQ,aAAAH,QAAM,cAAc,oBAAoB,EAAE,KAAK,cAAc,SAAS,OAAO,EAAE,SAAS,UAAU,SAAS,OAAO,OAAO,OAAO,EAAE,GAAG,YAAwB,aAAa,KAAK,YAAY,cAAc,KAAK,aAAa,aAAa,KAAK,YAAY,CAAC;AAAA,MACtQ,CAAC;AAAA,IACL;AACA,SAAK,kBAAkB,KAAK,KAAK;AAAA,EACrC;AAAA,EACA,oBAAoB;AAChB,UAAM,SAAS,qBAAAI,QAAQ,KAAK,YAAY,KAAK,CAAC;AAC9C,QAAI,OAAO,SAAS,OAAO,UAAU,YAAY,MAAM;AACnD,aAAO,QAAQ,YAAY;AAAA,IAC/B;AACA,QAAI,OAAO,SAAS,OAAO,UAAU,YAAY,OAAO;AACpD,WAAK,sBAAsB;AAAA,IAC/B;AACA,SAAK,SAAS;AAAA,MACV,OAAO,OAAO,SAAS,YAAY;AAAA,MACnC,YAAY,OAAO,cAAc;AAAA,MACjC,iBAAiB,OAAO,mBAAmB,CAAC;AAAA,IAChD,CAAC;AAAA,EACL;AAAA,EACA,mBAAmB,WAAW,WAAW;AACrC,UAAM,OAAO,KAAK,MAAM;AACxB,UAAM,QAAQ,KAAK,MAAM,gBAAgB,IAAI;AAC7C,QAAI,UAAU,UAAU,KAAK,MAAM,SAC/B,UAAU,gBAAgB,IAAI,MAAM,SACpC,UAAU,eAAe,MAAM;AAC/B,WAAK,kBAAkB,KAAK,KAAK;AACjC,WAAK,kBAAkB;AACvB,YAAM,SAAS;AAAA,QACX,OAAO,KAAK,MAAM;AAAA,QAClB,YAAY;AAAA,QACZ,iBAAiB,KAAK,MAAM;AAAA,MAChC;AACA,2BAAAA,QAAQ,KAAK,cAAc,QAAQ,EAAE,MAAM,IAAI,CAAC;AAAA,IACpD;AACA,QAAI,UAAU,oBAAoB,KAAK,MAAM,iBAAiB;AAC1D,WAAK,sBAAsB;AAAA,IAC/B;AAAA,EACJ;AAAA,EACA,uBAAuB;AACnB,SAAK,sBAAsB;AAAA,EAC/B;AAAA,EACA,wBAAwB;AACpB,SAAK,sBAAsB;AAC3B,SAAK,mBAAmB,YAAY,KAAK,mBAAmB,KAAK,MAAM,eAAe;AAAA,EAC1F;AAAA,EACA,wBAAwB;AACpB,kBAAc,KAAK,gBAAgB;AAAA,EACvC;AAAA,EACA,SAAS;AACL,UAAM,EAAE,gBAAgB,SAAS,IAAI,KAAK;AAC1C,UAAM,EAAE,UAAU,YAAY,OAAO,aAAa,IAAI,KAAK;AAC3D,QAAI,UAAU;AACV,aAAO,SAAS;AAAA,QACZ;AAAA,QACA;AAAA,QACA,SAAS,KAAK,WAAW;AAAA,QACzB;AAAA,QACA;AAAA,QACA;AAAA,QACA,kBAAkB,KAAK;AAAA,QACvB,iBAAiB,KAAK;AAAA,QACtB,mBAAmB,KAAK;AAAA,QACxB,iBAAiB,KAAK;AAAA,QACtB,kBAAkB,KAAK;AAAA,QACvB,iBAAiB,KAAK;AAAA,QACtB,sBAAsB,KAAK;AAAA,QAC3B,kBAAkB,KAAK;AAAA,MAC3B,CAAC;AAAA,IACL;AACA,WAAQ,aAAAJ,QAAM;AAAA,MAAc;AAAA,MAAO,EAAE,IAAI,cAAc,OAAO,EAAE,SAAS,GAAG,EAAE;AAAA,MAC1E,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,KAAK,oBAAoB,YAAY,KAAK,EAAE;AAAA,QAChF,aAAAA,QAAM,cAAc,gBAAgB,EAAE,KAAK,YAAY,YAAwB,SAAS,KAAK,YAAY,CAAC;AAAA,MAAC;AAAA,MAC/G,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,KAAK,oBAAoB,YAAY,IAAI,EAAE;AAAA,QAC/E,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAK;AAAA,UACrB;AAAA,UACA;AAAA,QAAU;AAAA,QACd,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,eAAe,IAAI,iBAAiB;AAAA,UACxE,aAAAA,QAAM,cAAc,QAAQ,MAAM,iBAAiB;AAAA,UACnD,aAAAA,QAAM,cAAc,sBAAsB,EAAE,OAAO,gBAAgB,aAAa,KAAK,aAAa,CAAC;AAAA,QAAC;AAAA,QACxG,aAAAA,QAAM,cAAc,KAAK,EAAE,WAAW,cAAc,GAAG,eAAe;AAAA,QACtE,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,IAAI,YAAY;AAAA,UACzC,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAS;AAAA,YACzB,aAAAA,QAAM,cAAc,SAAS,MAAM,KAAK,cAAc,KAAK,WAAW,SAAS,UAAU,CAAC;AAAA,UAAC;AAAA,UAC/F,aAAAA,QAAM;AAAA,YAAc;AAAA,YAAQ,EAAE,WAAW,YAAY;AAAA,YACjD;AAAA,YACA,aAAAA,QAAM,cAAc,MAAM,IAAI;AAAA,UAAC;AAAA,QAAC;AAAA,QACxC,aAAAA,QAAM,cAAc,KAAK,EAAE,WAAW,cAAc,GAAG,sDAAsD;AAAA,MAAC;AAAA,MAClH,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,KAAK,oBAAoB,YAAY,IAAI,EAAE;AAAA,QAC/E,gBAAiB,aAAAA,QAAM,cAAc,aAAa,KAAK,EAAE,SAAS,aAAa,SAAS,UAAU,aAAa,UAAU,aAAa,aAAa,YAAY,CAAC;AAAA,QAChK,aAAAA,QAAM;AAAA,UAAc;AAAA,UAAO,EAAE,WAAW,WAAW,IAAI,aAAa;AAAA,UAChE,aAAAA,QAAM,cAAc,UAAU,EAAE,SAAS,KAAK,WAAW,GAAG,YAAY;AAAA,QAAC;AAAA,MAAC;AAAA,MAClF,aAAAA,QAAM;AAAA,QAAc;AAAA,QAAO,EAAE,WAAW,WAAW,IAAI,aAAa;AAAA,QAChE,aAAAA,QAAM,cAAc,UAAU,EAAE,SAAS,KAAK,WAAW,GAAG,YAAY;AAAA,MAAC;AAAA,IAAC;AAAA,EACtF;AACJ;AACA,MAAM,YAAY;AAAA,EACd,gBAAgB,kBAAAC,QAAU,MAAM;AAAA,EAChC,aAAa,kBAAAA,QAAU;AAAA,EACvB,YAAY,kBAAAA,QAAU;AAAA,EACtB,OAAO,kBAAAA,QAAU;AAAA,EACjB,eAAe,kBAAAA,QAAU;AAAA,EACzB,iBAAiB,kBAAAA,QAAU;AAC/B;AACA,MAAM,eAAe;AAAA,EACjB,OAAO;AAAA,EACP,eAAeF;AAAA,EACf,iBAAiB;AACrB;", "names": ["decode", "require_cookie", "Client", "React", "PropTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "Cookies"]}