import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { RootState } from '../../store';
import { updateObject, updateTile, clearSelection, selectTiles, setGridSize, toggleGrid, toggleSnapToGrid } from '../../store/editor';
import { PlacedObject } from '../../types/editorTypes';
import { TileType } from '../../types/gameTypes';
import './EditorSidebar.scss';

export const EditorSidebar: React.FC = () => {
  const dispatch = useDispatch();
  const { currentMap, selectedObjects, editorState } = useSelector((state: RootState) => state.editor);
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['properties', 'tile-properties']));

  if (!currentMap) return null;

  const selectedObject = selectedObjects.length === 1
    ? currentMap.placedObjects.find(obj => obj.id === selectedObjects[0])
    : null;

  const selectedTile = editorState.selectedTiles.length === 1
    ? currentMap.tiles.find(tile => tile.id === editorState.selectedTiles[0])
    : null;

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  const handlePropertyChange = (property: keyof PlacedObject, value: any) => {
    if (selectedObject) {
      dispatch(updateObject({
        id: selectedObject.id,
        updates: { [property]: value }
      }));
    }
  };

  const handlePositionChange = (axis: 'x' | 'y' | 'z', value: number) => {
    if (selectedObject) {
      const newPosition = [...selectedObject.position] as [number, number, number];
      const axisIndex = axis === 'x' ? 0 : axis === 'y' ? 1 : 2;
      newPosition[axisIndex] = value;
      handlePropertyChange('position', newPosition);
    }
  };

  const handleRotationChange = (axis: 'x' | 'y' | 'z', value: number) => {
    if (selectedObject) {
      const newRotation = [...selectedObject.rotation] as [number, number, number];
      const axisIndex = axis === 'x' ? 0 : axis === 'y' ? 1 : 2;
      newRotation[axisIndex] = (value * Math.PI) / 180; // Convert degrees to radians
      handlePropertyChange('rotation', newRotation);
    }
  };

  const handleScaleChange = (axis: 'x' | 'y' | 'z', value: number) => {
    if (selectedObject) {
      const newScale = [...selectedObject.scale] as [number, number, number];
      const axisIndex = axis === 'x' ? 0 : axis === 'y' ? 1 : 2;
      newScale[axisIndex] = value;
      handlePropertyChange('scale', newScale);
    }
  };

  const handleUniformScaleChange = (value: number) => {
    if (selectedObject) {
      const newScale: [number, number, number] = [value, value, value];
      handlePropertyChange('scale', newScale);
    }
  };

  // Tile property handlers
  const handleTilePropertyChange = (property: string, value: any) => {
    if (selectedTile) {
      dispatch(updateTile({
        id: selectedTile.id,
        updates: { [property]: value }
      }));
    }
  };

  const handleTilePositionChange = (axis: 'x' | 'y' | 'z', value: number) => {
    if (selectedTile) {
      const newPosition = [...selectedTile.position] as [number, number, number];
      const axisIndex = axis === 'x' ? 0 : axis === 'y' ? 1 : 2;
      newPosition[axisIndex] = value;
      handleTilePropertyChange('position', newPosition);
    }
  };

  const handleTileTypeChange = (newType: TileType) => {
    if (selectedTile) {
      handleTilePropertyChange('type', newType);
    }
  };

  const handleTileCustomPropertyChange = (property: string, value: any) => {
    if (selectedTile) {
      const customProperties = selectedTile.customProperties || {};
      dispatch(updateTile({
        id: selectedTile.id,
        updates: {
          customProperties: {
            ...customProperties,
            [property]: value
          }
        }
      }));
    }
  };

  return (
    <div className="editor-sidebar">
      {/* Scene Information */}
      <div className="sidebar-section">
        <div
          className="section-header"
          onClick={() => toggleSection('scene')}
        >
          <span>Scene Information</span>
          <span className={`expand-icon ${expandedSections.has('scene') ? 'expanded' : ''}`}>
            ▼
          </span>
        </div>

        {expandedSections.has('scene') && (
          <div className="section-content">
            <div className="info-grid">
              <div className="info-item">
                <span className="info-label">Map Name:</span>
                <span className="info-value">{currentMap.name}</span>
              </div>
              <div className="info-item">
                <span className="info-label">Board Size:</span>
                <span className="info-value">{currentMap.boardSize} tiles</span>
              </div>
              <div className="info-item">
                <span className="info-label">Tiles Placed:</span>
                <span className="info-value">{currentMap.tiles.length}</span>
              </div>
              <div className="info-item">
                <span className="info-label">Objects:</span>
                <span className="info-value">{currentMap.placedObjects.length}</span>
              </div>
              <div className="info-item">
                <span className="info-label">Selected:</span>
                <span className="info-value">{selectedObjects.length} object(s)</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Object Properties */}
      {selectedObject && (
        <div className="sidebar-section">
          <div
            className="section-header"
            onClick={() => toggleSection('properties')}
          >
            <span>Object Properties</span>
            <span className={`expand-icon ${expandedSections.has('properties') ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>

          {expandedSections.has('properties') && (
            <div className="section-content">
              <div className="property-group">
                <label>Asset ID:</label>
                <span className="property-value">{selectedObject.assetId}</span>
              </div>

              {/* Position Controls */}
              <div className="property-group">
                <label>Position:</label>
                <div className="vector-input">
                  <div className="vector-component">
                    <label>X:</label>
                    <input
                      type="number"
                      step="0.1"
                      value={selectedObject.position[0].toFixed(2)}
                      onChange={(e) => handlePositionChange('x', parseFloat(e.target.value))}
                    />
                  </div>
                  <div className="vector-component">
                    <label>Y:</label>
                    <input
                      type="number"
                      step="0.1"
                      value={selectedObject.position[1].toFixed(2)}
                      onChange={(e) => handlePositionChange('y', parseFloat(e.target.value))}
                    />
                  </div>
                  <div className="vector-component">
                    <label>Z:</label>
                    <input
                      type="number"
                      step="0.1"
                      value={selectedObject.position[2].toFixed(2)}
                      onChange={(e) => handlePositionChange('z', parseFloat(e.target.value))}
                    />
                  </div>
                </div>
              </div>

              {/* Rotation Controls */}
              <div className="property-group">
                <label>Rotation (degrees):</label>
                <div className="vector-input">
                  <div className="vector-component">
                    <label>X:</label>
                    <input
                      type="number"
                      step="1"
                      value={Math.round((selectedObject.rotation[0] * 180) / Math.PI)}
                      onChange={(e) => handleRotationChange('x', parseFloat(e.target.value))}
                    />
                  </div>
                  <div className="vector-component">
                    <label>Y:</label>
                    <input
                      type="number"
                      step="1"
                      value={Math.round((selectedObject.rotation[1] * 180) / Math.PI)}
                      onChange={(e) => handleRotationChange('y', parseFloat(e.target.value))}
                    />
                  </div>
                  <div className="vector-component">
                    <label>Z:</label>
                    <input
                      type="number"
                      step="1"
                      value={Math.round((selectedObject.rotation[2] * 180) / Math.PI)}
                      onChange={(e) => handleRotationChange('z', parseFloat(e.target.value))}
                    />
                  </div>
                </div>
              </div>

              {/* Scale Controls */}
              <div className="property-group">
                <label>Scale:</label>
                <div className="scale-controls">
                  <div className="uniform-scale">
                    <label>Uniform:</label>
                    <input
                      type="number"
                      step="0.1"
                      min="0.1"
                      value={selectedObject.scale[0].toFixed(2)}
                      onChange={(e) => handleUniformScaleChange(parseFloat(e.target.value))}
                    />
                  </div>
                  <div className="vector-input">
                    <div className="vector-component">
                      <label>X:</label>
                      <input
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={selectedObject.scale[0].toFixed(2)}
                        onChange={(e) => handleScaleChange('x', parseFloat(e.target.value))}
                      />
                    </div>
                    <div className="vector-component">
                      <label>Y:</label>
                      <input
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={selectedObject.scale[1].toFixed(2)}
                        onChange={(e) => handleScaleChange('y', parseFloat(e.target.value))}
                      />
                    </div>
                    <div className="vector-component">
                      <label>Z:</label>
                      <input
                        type="number"
                        step="0.1"
                        min="0.1"
                        value={selectedObject.scale[2].toFixed(2)}
                        onChange={(e) => handleScaleChange('z', parseFloat(e.target.value))}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="property-group">
                <label>Quick Actions:</label>
                <div className="quick-actions">
                  <button
                    className="action-btn"
                    onClick={() => handlePropertyChange('rotation', [0, 0, 0])}
                  >
                    Reset Rotation
                  </button>
                  <button
                    className="action-btn"
                    onClick={() => handlePropertyChange('scale', [1, 1, 1])}
                  >
                    Reset Scale
                  </button>
                  <button
                    className="action-btn danger"
                    onClick={() => dispatch(clearSelection())}
                  >
                    Deselect
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Tile Properties */}
      {selectedTile && (
        <div className="sidebar-section">
          <div
            className="section-header"
            onClick={() => toggleSection('tile-properties')}
          >
            <span>Tile Properties</span>
            <span className={`expand-icon ${expandedSections.has('tile-properties') ? 'expanded' : ''}`}>
              ▼
            </span>
          </div>

          {expandedSections.has('tile-properties') && (
            <div className="section-content">
              <div className="property-group">
                <label>Tile ID:</label>
                <span className="property-value">{selectedTile.id}</span>
              </div>

              {/* Tile Type */}
              <div className="property-group">
                <label>Tile Type:</label>
                <select
                  value={selectedTile.type}
                  onChange={(e) => handleTileTypeChange(e.target.value as TileType)}
                >
                  <option value={TileType.BASIC}>Basic</option>
                  <option value={TileType.HEALING}>Healing</option>
                  <option value={TileType.DAMAGE}>Damage</option>
                  <option value={TileType.KEY}>Key</option>
                  <option value={TileType.TREASURE_CHEST}>Treasure Chest</option>
                </select>
              </div>

              {/* Position Controls */}
              <div className="property-group">
                <label>Position:</label>
                <div className="vector-input">
                  <div className="vector-component">
                    <label>X:</label>
                    <input
                      type="number"
                      step="0.1"
                      value={selectedTile.position[0].toFixed(2)}
                      onChange={(e) => handleTilePositionChange('x', parseFloat(e.target.value))}
                    />
                  </div>
                  <div className="vector-component">
                    <label>Y:</label>
                    <input
                      type="number"
                      step="0.1"
                      value={selectedTile.position[1].toFixed(2)}
                      onChange={(e) => handleTilePositionChange('y', parseFloat(e.target.value))}
                    />
                  </div>
                  <div className="vector-component">
                    <label>Z:</label>
                    <input
                      type="number"
                      step="0.1"
                      value={selectedTile.position[2].toFixed(2)}
                      onChange={(e) => handleTilePositionChange('z', parseFloat(e.target.value))}
                    />
                  </div>
                </div>
              </div>

              {/* Height Offset */}
              <div className="property-group">
                <label>Height Offset:</label>
                <input
                  type="number"
                  step="0.1"
                  value={selectedTile.customProperties?.heightOffset || 0}
                  onChange={(e) => handleTileCustomPropertyChange('heightOffset', parseFloat(e.target.value))}
                />
              </div>

              {/* Material Color */}
              <div className="property-group">
                <label>Color:</label>
                <input
                  type="color"
                  value={selectedTile.customProperties?.material?.color || '#ffffff'}
                  onChange={(e) => handleTileCustomPropertyChange('material', {
                    ...selectedTile.customProperties?.material,
                    color: e.target.value
                  })}
                />
              </div>

              {/* Quick Actions */}
              <div className="property-group">
                <label>Quick Actions:</label>
                <div className="quick-actions">
                  <button
                    className="action-btn"
                    onClick={() => handleTileCustomPropertyChange('heightOffset', 0)}
                  >
                    Reset Height
                  </button>
                  <button
                    className="action-btn danger"
                    onClick={() => dispatch(selectTiles([]))}
                  >
                    Deselect
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Camera Information */}
      <div className="sidebar-section">
        <div
          className="section-header"
          onClick={() => toggleSection('camera')}
        >
          <span>Camera</span>
          <span className={`expand-icon ${expandedSections.has('camera') ? 'expanded' : ''}`}>
            ▼
          </span>
        </div>

        {expandedSections.has('camera') && (
          <div className="section-content">
            <div className="info-grid">
              <div className="info-item">
                <span className="info-label">Position:</span>
                <span className="info-value">
                  {editorState.cameraPosition.map(v => v.toFixed(1)).join(', ')}
                </span>
              </div>
              <div className="info-item">
                <span className="info-label">Target:</span>
                <span className="info-value">
                  {editorState.cameraTarget.map(v => v.toFixed(1)).join(', ')}
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Editor Settings */}
      <div className="sidebar-section">
        <div
          className="section-header"
          onClick={() => toggleSection('settings')}
        >
          <span>Editor Settings</span>
          <span className={`expand-icon ${expandedSections.has('settings') ? 'expanded' : ''}`}>
            ▼
          </span>
        </div>

        {expandedSections.has('settings') && (
          <div className="section-content">
            <div className="setting-item">
              <label>Grid Size:</label>
              <input
                type="number"
                min="0.1"
                step="0.1"
                value={editorState.gridSize}
                onChange={(e) => {
                  const value = parseFloat(e.target.value);
                  if (!isNaN(value) && value > 0) {
                    dispatch(setGridSize(value));
                    console.log('Grid size changed to:', value);
                  }
                }}
              />
            </div>

            <div className="setting-item">
              <label>
                <input
                  type="checkbox"
                  checked={editorState.gridVisible}
                  onChange={() => {
                    dispatch(toggleGrid());
                    console.log('Grid visibility toggled:', !editorState.gridVisible);
                  }}
                />
                Show Grid
              </label>
            </div>

            <div className="setting-item">
              <label>
                <input
                  type="checkbox"
                  checked={editorState.snapToGrid}
                  onChange={() => {
                    dispatch(toggleSnapToGrid());
                    console.log('Snap to grid toggled:', !editorState.snapToGrid);
                  }}
                />
                Snap to Grid
              </label>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
