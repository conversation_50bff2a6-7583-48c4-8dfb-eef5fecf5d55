// Simple test to verify our game logic works
import { BrawlPartyGame } from './index';
import { Client } from 'boardgame.io/client';
import { Local } from 'boardgame.io/multiplayer';

export const testGame = () => {
  console.log('🎮 Testing Brawl Party Game Logic...');
  
  // Create a local game with 2 players
  const client1 = Client({
    game: BrawlPartyGame,
    multiplayer: Local(),
    playerID: '0',
  });
  
  const client2 = Client({
    game: BrawlPartyGame,
    multiplayer: Local(),
    playerID: '1',
  });
  
  client1.start();
  client2.start();
  
  // Get initial state
  let state = client1.getState();
  console.log('📊 Initial Game State:');
  console.log('Players:', Object.keys(state?.G.players || {}));
  console.log('Board size:', state?.G.tiles?.length || 0);
  console.log('Player 1 health:', state?.G.players['0']?.health);
  console.log('Player 1 keys:', state?.G.players['0']?.keys);
  console.log('Player 1 position:', state?.G.players['0']?.position);
  
  // Test dice roll for player 1
  console.log('\n🎲 Player 1 rolling dice...');
  client1.moves.rollDice();
  
  state = client1.getState();
  console.log('Dice result:', state?.G.currentDiceRoll);
  console.log('Player 1 new position:', state?.G.players['0']?.position);
  console.log('Player 1 health after move:', state?.G.players['0']?.health);
  console.log('Player 1 keys after move:', state?.G.players['0']?.keys);
  console.log('Current turn:', state?.ctx.currentPlayer);
  
  // Test dice roll for player 2
  console.log('\n🎲 Player 2 rolling dice...');
  client2.moves.rollDice();
  
  state = client2.getState();
  console.log('Dice result:', state?.G.currentDiceRoll);
  console.log('Player 2 new position:', state?.G.players['1']?.position);
  console.log('Player 2 health after move:', state?.G.players['1']?.health);
  console.log('Player 2 keys after move:', state?.G.players['1']?.keys);
  console.log('Current turn:', state?.ctx.currentPlayer);
  
  // Show game log
  console.log('\n📝 Game Log:');
  state?.G.gameLog.forEach((entry, index) => {
    console.log(`${index + 1}. ${entry.details}`);
  });
  
  console.log('\n✅ Game test completed!');
  
  return { client1, client2, state };
};

// Run test if this file is executed directly
if (typeof window !== 'undefined') {
  (window as any).testGame = testGame;
  console.log('🔧 Game test function available as window.testGame()');
}
